import { DateInfo, DateStatus, ScheduleItem, TimeSlot, SelectedAppointment, DateInfoStatus } from './types';
import moment, { Moment } from 'moment';

/**
 * 格式化日期为 YYYY-MM-DD 格式
 */
export const formatDate = (date: Date): string => {
  return moment(date).format('YYYY-MM-DD');
};

/**
 * 格式化时间段为 HH:mm-HH:mm 格式
 */
export const formatTimeSlot = (timeSlot: TimeSlot): string => {
  return `${timeSlot.startTime}-${timeSlot.endTime}`;
};

/**
 * 格式化预约信息为字符串
 */
export const formatAppointment = (appointment: SelectedAppointment): string => {
  const { date, period, timeSlot } = appointment;
  const dateStr = formatDate(date);
  const periodText = period === 'morning' ? '上午场' : '下午场';
  const timeSlotStr = formatTimeSlot(timeSlot);

  return `${dateStr}${periodText}${timeSlotStr}`;
};

/**
 * 检查时间段是否可用
 */
export const isTimeSlotAvailable = (timeSlot: TimeSlot): boolean => {
  return (timeSlot?.reserveNum || 0) > (timeSlot?.useReserveNum || 0);
};

/**
 * 检查日期是否已过期（早于今天）
 */
export const isDateExpired = (date: Moment): boolean => {
  return date.clone().startOf('day').isBefore(moment().startOf('day'));
};

/**
 * 获取日期信息
 */
export const getDateStatus = (momentDate: Moment, scheduleData: ScheduleItem[]): DateInfoStatus => {
  // 查找对应的排班项
  const scheduleItem = scheduleData.find(item => momentDate.isSame(item.scheduleDate, 'day')) || null;

  // 默认状态为未开放
  let status = DateStatus.CLOSED;
  // let remainingCount = 0;
  // let isMorningAvailable = false;
  // let isAfternoonAvailable = false;

  // 如果日期已过期
  if (isDateExpired(momentDate)) {
    status = DateStatus.EXPIRED;
  }
  // 如果有排班项且有scheduleId
  else if (scheduleItem && scheduleItem.scheduleId) {
    // 计算剩余号源数量
    const remainingCount = scheduleItem.reserveNum - scheduleItem.useReserveNum;

    // 检查上午和下午的可用性
    // const morningAvailable = scheduleItem.morningScheduleList.some(isTimeSlotAvailable);
    // const afternoonAvailable = scheduleItem.afternoonScheduleList.some(isTimeSlotAvailable);

    // isMorningAvailable = morningAvailable;
    // isAfternoonAvailable = afternoonAvailable;

    // 确定状态
    if (remainingCount > 0) {
      status = DateStatus.AVAILABLE;
    } else {
      status = DateStatus.FULL;
    }
  } else {
    // 如果没有排班项或没有scheduleId，则设置为未开放
    status = DateStatus.CLOSED;
  }

  return {
    status,
    scheduleItem,
  };
};

/**
 *
 * @param momentDate
 * @param scheduleData
 * @returns
 */
export const getDateInfo = (momentDate: Moment, scheduleData: ScheduleItem[]): DateInfo => {
  const { status, scheduleItem } = getDateStatus(momentDate, scheduleData);
  const remainingCount = (scheduleItem?.reserveNum || 0) - (scheduleItem?.useReserveNum || 0);

  return {
    status,
    scheduleItem,
    remainingCount,
    medicalDate: momentDate.date(),
  };
};

/** 获取格式化的数据 */
export const getDateFormatText = (momentDate: Moment, scheduleData: ScheduleItem[]): string => {
  if (!momentDate) {
    return undefined;
  }
  let formatText = `${momentDate.format('YYYY-MM-DD')}`;
  const { scheduleItem } = getDateStatus(momentDate, scheduleData);
  if (scheduleItem) {
    const detail = scheduleItem.morningScheduleList.find(isTimeSlotAvailable);
    if (detail) {
      formatText = formatText.concat('上午', detail.timeRangeString);
    } else {
      const afterDetail = scheduleItem.afternoonScheduleList.find(isTimeSlotAvailable);
      formatText = formatText.concat('下午', afterDetail.timeRangeString);
    }
  }

  return formatText;
};

/**
 * 获取指定时段的可用时间段
 */
export const getAvailableTimeSlots = (scheduleItem: ScheduleItem, period: 'morning' | 'afternoon'): TimeSlot[] => {
  const timeSlots = period === 'morning' ? scheduleItem.morningScheduleList : scheduleItem.afternoonScheduleList;

  return timeSlots.filter(isTimeSlotAvailable);
};

/**
 * 获取日期单元格的类名
 */
export const getDateCellClassName = (status: DateStatus): string => {
  switch (status) {
    case DateStatus.AVAILABLE:
      return 'date-cell-available';
    case DateStatus.FULL:
      return 'date-cell-full';
    case DateStatus.EXPIRED:
      return 'date-cell-expired';
    case DateStatus.CLOSED:
      return 'date-cell-closed';
    default:
      return '';
  }
};

/**
 * 获取日期单元格的提示文本
 */
export const getDateCellTooltip = (dateInfo: DateInfo): string => {
  switch (dateInfo.status) {
    case DateStatus.AVAILABLE:
      return `剩余${dateInfo.remainingCount}个号源`;
    case DateStatus.FULL:
      return '当日号源已满';
    case DateStatus.EXPIRED:
      return '已过期';
    case DateStatus.CLOSED:
      return '未开放预约';
    default:
      return '';
  }
};
