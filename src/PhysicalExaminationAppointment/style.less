.dhr-medical-date-picker {
  width: 100%;
  .dhr-medical-date-picker-input {
    width: 100%;
  }
}

.dhr-medical-date-picker-popup {
  .ant-picker-date-panel {
    width: initial;
    min-width: 280px;
    .ant-picker-content {
      width: initial;
      min-width: 252px;
      // .ant-picker-cell-disabled::before {
      //   height: 40px;
      // }
      .ant-picker-cell::before {
        top: 14%;
        transform: translateY(-14%);
      }
    }
  }
}

// 响应式调整
// @media (max-width: 768px) {
//   .dhr-medical-date-picker {
//     .medical-date-picker-clear {
//       position: static;
//       margin-top: 8px;
//       transform: none;
//       display: block;
//       text-align: center;
//     }
//   }
// }
