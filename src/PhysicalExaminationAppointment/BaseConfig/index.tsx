import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { addressKey, formatTextKey, empTypeKey } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigAddressKey',
      curFormData?.dictConfigAddressKey || defFormData?.dictConfigAddressKey || addressKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigEmpTypeKey',
      curFormData?.dictConfigEmpTypeKey || defFormData?.dictConfigEmpTypeKey || empTypeKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFormatTextKey',
      curFormData?.dictConfigFormatTextKey || defFormData?.dictConfigFormatTextKey || formatTextKey
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigFormatTextKey',
      label: '体检日期文本储存字段',
      configs: {
        allowClear: true,
        placeholder: '请输入',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigFormatTextKey');
          context?.onConfirm?.('dictConfigFormatTextKey', value);
          setDictConfig(formRef, 'formatTextKey', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigEmpTypeKey',
      label: '人员类型字段',
      configs: {
        allowClear: true,
        placeholder: '请输入',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigEmpTypeKey');
          context?.onConfirm?.('dictConfigEmpTypeKey', value);
          setDictConfig(formRef, 'empTypeKey', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigAddressKey',
      label: '体检地点字段',
      configs: {
        allowClear: true,
        placeholder: '请输入',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigAddressKey');
          context?.onConfirm?.('dictConfigAddressKey', value);
          setDictConfig(formRef, 'addressKey', value, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
