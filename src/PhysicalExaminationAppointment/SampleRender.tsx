import { DatePicker } from 'antd';
import React from 'react';

export interface IAppProps {
  configs: {
    config: {
      baseConfig: {
        dictConfig: {
          label: string;
          controlType: string;
        };
      };
    };
  };
}

const BaseInfo: React.FC<IAppProps> = props => {
  return (
    <div>
      <DatePicker style={{ width: '100%' }} />
    </div>
  );
};

export default BaseInfo;
