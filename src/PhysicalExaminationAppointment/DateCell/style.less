.dhr-date-cell {
  padding-left: 6px;
  padding-right: 6px;
  .dhr-date-cell-status {
    font-size: 12px;
    line-height: 12px;
    white-space: nowrap;
  }
}

// 可用状态样式
.dhr-date-cell-available {
  color: #52c41a;
  &:hover {
    background-color: rgba(82, 196, 26, 0.1);
  }
}

// 已满状态样式
.dhr-date-cell-full {
  color: #f5222d;

  &:hover {
    background-color: rgba(245, 34, 45, 0.1);
  }
}

// 过期状态样式
.dhr-date-cell-expired {
  color: #bfbfbf;
  cursor: not-allowed;
}

// 未开放状态样式
.dhr-date-cell-closed {
  color: #f5222d;
  cursor: not-allowed;
}

// 响应式调整
// @media (max-width: 768px) {
//   .dhr-date-cell {
//     .dhr-date-cell-content {
//       padding: 2px;
//     }

//     .dhr-date-cell-periods {
//       margin-top: 1px;

//       .dhr-date-cell-period {
//         font-size: 9px;
//       }
//     }

//     .dhr-date-cell-status {
//       font-size: 9px;
//       margin-top: 1px;
//     }
//   }
// }
