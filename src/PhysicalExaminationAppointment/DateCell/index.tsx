import React, { useMemo } from 'react';
import classnames from 'classnames';

import { DateInfo, DateStatus } from '../types';

import './style.less';

interface DateCellProps {
  dateInfo: DateInfo;
}

// 将状态文本和类名的计算逻辑提取到组件外部
const getStatusInfo = (status: DateStatus, remainingCount: number) => {
  switch (status) {
    case DateStatus.AVAILABLE:
      return {
        text: `剩余${remainingCount}个`,
        className: 'dhr-date-cell-available',
      };
    case DateStatus.FULL:
      return {
        text: '已满',
        className: 'dhr-date-cell-full',
      };
    case DateStatus.EXPIRED:
      return {
        text: '已过期',
        className: 'dhr-date-cell-expired',
      };
    case DateStatus.CLOSED:
      return {
        text: '未开放',
        className: 'dhr-date-cell-closed',
      };
    default:
      return {
        text: '',
        className: '',
      };
  }
};

const DateCell: React.FC<DateCellProps> = React.memo(({ dateInfo }) => {
  const { status, remainingCount, medicalDate } = dateInfo;

  // 使用 useMemo 缓存状态信息的计算结果
  const statusInfo = useMemo(() => getStatusInfo(status, remainingCount), [status, remainingCount]);

  // 使用 useMemo 缓存徽章渲染
  // const periodBadges = useMemo(() => {
  //   if (status !== DateStatus.AVAILABLE) return null;

  //   return (
  //     <div className="dhr-date-cell-periods">
  //       {isMorningAvailable && <Badge color="#52c41a" text="上午" className="dhr-date-cell-period" />}
  //       {isAfternoonAvailable && <Badge color="#1890ff" text="下午" className="dhr-date-cell-period" />}
  //     </div>
  //   );
  // }, [status, isMorningAvailable, isAfternoonAvailable]);

  return (
    <>
      <div className="dhr-date-cell ant-picker-cell-inner">
        <div>{medicalDate}</div>
        <div className={classnames('dhr-date-cell-status', statusInfo.className)}>{statusInfo.text}</div>
      </div>
    </>
  );
});

// 添加组件显示名称，便于调试
DateCell.displayName = 'DateCell';

export default DateCell;
