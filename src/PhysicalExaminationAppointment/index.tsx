import React, { useCallback, useEffect, useRef, useState } from 'react';
import moment, { Moment } from 'moment';
import { DatePicker } from 'antd';

import DateCell from './DateCell';

import entryApis from '@apis/entry';
import { toStartDate } from '@utils/tools';
import { request as fetchApi } from '@utils/http';
import { getDateInfo, getDateStatus, getDateFormatText } from './utils';

import { WULI_MODE } from '@constants/common';
import { MedicalDatePickerProps, DateStatus, ScheduleItem } from './types';

import './style.less';

const PhysicalExaminationAppointment: React.FC<MedicalDatePickerProps> = props => {
  const { configs, value, onChange, disabled } = props;
  const dateRenderRef = useRef<number>(0);
  const wuliMode = configs.wuliMode;
  const dictConfig: any = configs?.config?.baseConfig?.dictConfig || {};
  const { empTypeKey, addressKey, formatTextKey } = dictConfig;
  const formData = configs.context.getFormData() || {};

  const physicalAddress = formData[addressKey] || '';
  const empTypeCategory = formData[empTypeKey] || '';
  const viewText = formData[formatTextKey] || '';
  const isView = wuliMode === WULI_MODE.VIEW;
  const [scheduleData, setScheduleData] = useState<ScheduleItem[]>([]);
  const handleChange = useCallback(
    (date: Moment | null) => {
      // if (!date) {
      //   formatTextKey &&
      //     configs.context.setFormData({
      //       [formatTextKey]: undefined,
      //     });
      //   return onChange(undefined);
      // }
      formatTextKey &&
        configs.context.setFormData({
          [formatTextKey]: getDateFormatText(date, scheduleData),
        });
      onChange(date && toStartDate(date));
    },
    [configs, scheduleData]
  );

  /* 获取当月号源 **/
  const onFetchScheduleList = useCallback(
    (year, month) => {
      /** 没有员工类型和体检地址不发请求 */
      if (!physicalAddress || !empTypeCategory) {
        return;
      }
      fetchApi({
        ...entryApis.entryPhysical,
        params: {
          year,
          month,
          physicalAddress,
          empTypeCategory,
        },
        onSuccess: list => {
          try {
            setScheduleData(list || []);
          } catch (error) {
            setScheduleData([]);
          }
        },
      });
    },
    [physicalAddress, empTypeCategory]
  );

  const onFetchScheduleData = useCallback(
    (date: Moment) => {
      const year = date.year();
      const month = date.month() + 1;
      onFetchScheduleList?.(year, month);
    },
    [physicalAddress, empTypeCategory]
  );

  /** 打开面板时 获取接口 */
  const handleOpenChange = useCallback(
    open => {
      if (open) {
        onFetchScheduleData(moment(value));
      }
    },
    [value, physicalAddress, empTypeCategory]
  );

  /** 禁用日期 */
  const handleDisabledDate = useCallback(
    (current: Moment) => {
      if (scheduleData.length === 0) {
        return true;
      }
      const dateInfo = getDateStatus(current.clone(), scheduleData);
      // // 如果状态是已过期或未开放，则禁用
      const isDisabled = [DateStatus.EXPIRED, DateStatus.CLOSED].includes(dateInfo.status);
      return isDisabled;
    },
    [scheduleData]
  );

  const dateRender = (current: Moment) => {
    const medicalDate = current.clone().date();
    medicalDate === 1 && (dateRenderRef.current = dateRenderRef.current + 1);
    if (dateRenderRef.current !== 1) {
      dateRenderRef.current = 0;
      return <div className="ant-picker-cell-inner "></div>;
    }
    const dateInfo = getDateInfo(current, scheduleData);
    return <DateCell dateInfo={dateInfo} />;
  };

  const format = useCallback(
    (current?: Moment, isView?: string) => {
      if (!current) {
        return isView === 'view' ? '' : undefined;
      }
      if (formatTextKey) {
        return viewText || current.format('YYYY-MM-DD');
      }
      return current.format('YYYY-MM-DD');
    },
    [viewText, formatTextKey]
  );
  return (
    <div className="dhr-medical-date-picker">
      {isView ? (
        format(value && moment(value), 'view')
      ) : (
        <DatePicker
          format={format}
          showToday={false}
          disabled={disabled}
          placeholder="请选择"
          style={{ width: '100%' }}
          dateRender={dateRender}
          onChange={handleChange}
          value={value && moment(value)}
          onOpenChange={handleOpenChange}
          disabledDate={handleDisabledDate}
          onPanelChange={onFetchScheduleData}
          className="dhr-medical-date-picker-input"
          popupClassName="dhr-medical-date-picker-popup"
        />
      )}
    </div>
  );
};

export default PhysicalExaminationAppointment;
