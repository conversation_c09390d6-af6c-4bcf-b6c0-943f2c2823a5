// 时间段接口
export interface TimeSlot {
  scheduleTimeId: string;
  startTime: string;
  endTime: string;
  reserveNum: number;
  useReserveNum: number;
  timeRangeString: string;
}

// 排班项接口
export interface ScheduleItem {
  scheduleDate: string;
  scheduleId: string;
  scheduleType: string;
  reserveNum: number;
  useReserveNum: number;
  morningScheduleList: TimeSlot[];
  afternoonScheduleList: TimeSlot[];
}

// 日期状态枚举
export enum DateStatus {
  /** 已满 */
  FULL = 'full',
  /** 可用 */
  AVAILABLE = 'available',
  /** 过期 */
  EXPIRED = 'expired',
  /** 未开放 */
  CLOSED = 'closed',
}

export interface DateInfoStatus {
  status: DateStatus;
  scheduleItem: ScheduleItem | null;
}

// 日期信息接口
export interface DateInfo {
  status: DateStatus;
  medicalDate: number;
  remainingCount: number;
  scheduleItem: ScheduleItem | null;
}

// 选中的预约信息
export interface SelectedAppointment {
  date: Date;
  period: 'morning' | 'afternoon';
  timeSlot: TimeSlot;
}

// 组件属性接口
export interface MedicalDatePickerProps {
  value?: number;
  format?: string;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  allowClear?: boolean;
  style?: React.CSSProperties;
  scheduleData?: ScheduleItem[];
  disabledDate?: (current: any) => boolean;
  onChange?: (value: number | null) => void;
  configs: {
    wuliMode: 'view' | 'edit';
    context: {
      setFormData: (values) => void;
      getFormData: () => Record<string, any>;
    };
    config: {
      baseConfig: {
        dictConfig: {
          addressKey: string;
          empTypeKey: string;
        };
      };
    };
  };
}
