import React, { useEffect, useMemo, useRef, useState } from 'react';
import { IOriginalProps } from '@src/types';
import { Row, Col, ConfigProvider, Spin, DatePicker } from 'antd';
import axios from 'axios';

// import { DatePicker } from '@cvte/wuli-antd';
// 导入 antd 中文
import zhCN from 'antd/lib/locale/zh_CN';

import { request } from '@utils/http';
import tmgApis from '@apis/tmg';
import { Moment } from 'moment';
import moment from 'moment-timezone';

import './style.less';
import classnames from 'classnames';
import { showErrNotification } from '@utils/tools';
import { DAY_TYPES } from '@constants/absence';

// 设置默认时区为北京时区 (Asia/Shanghai)
moment.tz.setDefault('Asia/Shanghai');

/**
 * 设置东八时区
 */

const ITimeFrame = {
  FORENOON: 'FORENOON',
  AFTERNOON: 'AFTERNOON',
  WHOLE_DAY: 'WHOLE_DAY',
  SELF_SELECT: 'SELF_SELECT',
};

type ITimeFrameValue = typeof ITimeFrame[keyof typeof ITimeFrame];

interface IActionItem {
  title: string;
  code: ITimeFrameValue; // 使用 keyof 来确保 code 是 ITimeFrame 对象的键
  beginTime?: string;
  endTime?: string;
  visible?: boolean;
}

interface IAppProps extends IOriginalProps {
  onChange: (val) => void;
}

const BaseInfo: React.FC<IAppProps> = props => {
  const { onChange, utils, configs, value, id: curField } = props;
  console.log('~~~AbsenceDatePicker-props', props);
  const { dictConfig = {}, isEnabled } = configs.config?.baseConfig || {};
  const [timeFrame, setTimeFrame] = React.useState<ITimeFrameValue>(ITimeFrame.SELF_SELECT);
  const [scheduleList, setScheduleList] = React.useState([]);
  const [isPickerLayerOpen, setIsPickerLayerOpen] = useState<boolean>(false);
  const [tempSelectDate, setTempSelectDate] = useState<Moment>();
  const [loading, setLoading] = useState(false);
  const [configData, setConfigData] = useState<Record<string, any>>();
  const [buttonList, setButtonList] = useState<string[]>([]);
  // 当前选择日程配置
  const [selectedScdule, setSelectedScdule] = useState<string>();
  // 临时选择日程配置
  const [tempSelectedScdule, setTempSelectedScdule] = useState<string>();
  const detailData = configs.context.globalContext.PageTools.getDetailEngineProps() || {};
  const contextData = detailData?.metaConfig?.contextData || {};
  const formData = configs.context?.getFormData();
  const cancelTokenSourceRef = useRef(null);

  console.log('~~~AbsenceDatePicker-formData', formData);
  const dateRef = React.useRef(null);
  const { beginTimeKey, endTimeKey } = dictConfig || {};
  // 是否结束时间选择
  const isEndDate = curField === endTimeKey;
  const { durationCalType } = configData || {};
  // 是否按班次计算
  const isShift = durationCalType === 'SHIFT';

  useEffect(() => {
    if (formData.C_RULE_CONFIG_ID) {
      const _configData = utils.cache.getValue(`config-${contextData.empId}-${formData.C_RULE_CONFIG_ID}`);
      setConfigData(_configData || {});
    }
  }, [formData.C_RULE_CONFIG_ID]);

  const getDayScheduleConfig = (queryDate: number) => {
    const _queryDate = moment(queryDate).startOf('day').valueOf();
    const todaySchecule = scheduleList.find(k => k.date === _queryDate);
    return todaySchecule;
  };

  const getDaySchedule = todaySchecule => {
    if (!todaySchecule) return null;
    const { shiftView } = todaySchecule || {};
    const shiftPeriods = shiftView?.shiftPeriods || [];
    const timeList = shiftPeriods
      .filter(k => k.type === 'WORK')
      ?.map(k => [k.beginPoint, k.endPoint])
      .flat()
      .sort((a, b) => a - b);
    const timeTransform = list => list.sort((a, b) => a - b).map(time => time * 1000 + todaySchecule.date);
    const restPeriod = shiftPeriods.find(k => k.type === 'REST');
    // 非法休息时段
    let illegalRestPeriod = false;
    // 有配置休息时段
    if (!restPeriod || restPeriod?.beginPoint >= timeList[timeList.length - 1]) {
      illegalRestPeriod = true;
    }
    const restStartPoint =
      (illegalRestPeriod ? shiftView.splitPoint : restPeriod.beginPoint) * 1000 + todaySchecule.date;
    const restEndPoint = (illegalRestPeriod ? shiftView.splitPoint : restPeriod.endPoint) * 1000 + todaySchecule.date;
    // 根据分割点切分时段
    const forenoonList = timeTransform(timeList.filter(time => time <= shiftView.splitPoint));
    const afternoonList = timeTransform(timeList.filter(time => time > shiftView.splitPoint));
    return {
      forenoonStartTime: forenoonList[0],
      afternoonEndTime: afternoonList[afternoonList.length - 1],
      splitPoint: shiftView.splitPoint * 1000 + todaySchecule.date,
      restStartPoint,
      restEndPoint,
    };
  };

  const _actionList: IActionItem[] = useMemo(() => {
    const actionList: IActionItem[] = [
      {
        title: '上半天',
        code: ITimeFrame.FORENOON,
        visible: buttonList?.includes(ITimeFrame.FORENOON),
      },
      {
        title: '下半天',
        code: ITimeFrame.AFTERNOON,
        visible: buttonList?.includes(ITimeFrame.AFTERNOON),
      },
      {
        title: '全天',
        code: ITimeFrame.WHOLE_DAY,
        visible: buttonList?.includes(ITimeFrame.WHOLE_DAY),
      },
      {
        title: '自选时段',
        code: ITimeFrame.SELF_SELECT,
        visible: buttonList?.includes(ITimeFrame.SELF_SELECT),
      },
    ];
    return actionList.filter(k => k.visible);
  }, [isShift, buttonList]);

  const scheduleListMap = useMemo(() => {
    return (scheduleList || []).reduce((pre, cur) => {
      pre[cur.date] = cur;
      return pre;
    }, {});
  }, [scheduleList]);
  /** 获取当月班次分割点 */
  const handleGetScheduleEmp = (queryDate: number = Date.now()) => {
    if (cancelTokenSourceRef.current) {
      cancelTokenSourceRef.current.cancel('取消上次请求');
      cancelTokenSourceRef.current = null;
    }
    cancelTokenSourceRef.current = axios.CancelToken.source();
    setLoading(true);
    request({
      ...tmgApis.scheduleEmp,
      params: {
        empId: contextData.empId,
        queryDate,
      },
      cancelToken: cancelTokenSourceRef.current.token,
    })
      .then(res => {
        cancelTokenSourceRef.current = null;
        if (Array.isArray(res)) {
          setScheduleList(res);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /** 选择分割点 */
  const handleAtivate = (perid: IActionItem) => {
    const { code } = perid;
    const todaySchecule = getDayScheduleConfig(tempSelectDate.valueOf() || value);
    const todayScheduleTimesMapings = getDaySchedule(todaySchecule);
    if (!todayScheduleTimesMapings) {
      return showErrNotification('当前日期没有班次,请重新选择您要请假的日期');
    }
    console.log('todaySchecule', todaySchecule);
    setTimeFrame(perid.code);
    // 映射关系简化条件逻辑
    const timeFrameMap: { [key in ITimeFrame]: number[] } = {
      [ITimeFrame.FORENOON]: [todayScheduleTimesMapings.forenoonStartTime, todayScheduleTimesMapings.restStartPoint],
      [ITimeFrame.AFTERNOON]: [todayScheduleTimesMapings.restEndPoint, todayScheduleTimesMapings.afternoonEndTime],
      [ITimeFrame.WHOLE_DAY]: [todayScheduleTimesMapings.forenoonStartTime, todayScheduleTimesMapings.afternoonEndTime],
    };

    // 根据code直接获取时间范围，若为SELF_SELECT则直接返回
    if (code === ITimeFrame.SELF_SELECT) return;
    const [_beginDate, _endDate] = timeFrameMap[code];
    if (isEndDate) {
      configs.context.setFormData({
        [endTimeKey]: _endDate,
      });
    } else {
      // 设置表单数据
      configs.context.setFormData({
        [beginTimeKey]: _beginDate,
        [endTimeKey]: _endDate,
      });
    }
    setSelectedScdule(todaySchecule);
    setIsPickerLayerOpen(false);
    /** 触发字段联动，只需要触发一个就行 */
    props.configs.relation?.emit({
      attrCode: endTimeKey,
      formCode: 'BASIC_INFO_GROUP',
      actionCode: 'change',
    });
  };

  const handleChange = (current: Moment) => {
    if (!current) {
      showErrNotification('请正确选择请假时间');
    }
    const newValue = current?.millisecond(0);
    // setInternalValue(newValue);
    onChange(newValue ? newValue.valueOf() : undefined);
    setSelectedScdule(tempSelectedScdule);
  };
  // 初始化当前日期版次版次配置
  const initSchedule = () => {
    if (value && scheduleList.length > 0) {
      const curSchedule = getDayScheduleConfig(value);
      if (curSchedule) {
        const { shiftView } = curSchedule;
        // 设置操作栏
        setButtonList(isShift ? shiftView?.shiftButton?.split(',') || [] : shiftView?.calendarButton?.split(',') || []);
        setSelectedScdule(curSchedule);
        setTempSelectedScdule(curSchedule);
      }
    }
  };
  useEffect(() => {
    if (!scheduleList.length || (value && getDayScheduleConfig(value) === null)) {
      // 获取班次配置
      console.log('获取班次配置获取班次配置');
      handleGetScheduleEmp(value);
    } else {
      initSchedule();
    }
  }, [value, isShift, scheduleList, isPickerLayerOpen]);

  // 关闭的时候触发
  useEffect(() => {
    if (isPickerLayerOpen === false) {
      if (tempSelectDate && tempSelectDate?.valueOf() !== value) {
        setTempSelectDate(moment(value));
        handleGetScheduleEmp(value);
        setTempSelectedScdule(undefined);
      }
    }
  }, [isPickerLayerOpen, tempSelectDate, value]);

  // 点击单元格时间
  const handleDateCellClick = current => {
    if (loading) {
      return showErrNotification('请等待当前班次加载完毕再操作');
    }
    const todaySchecule = getDayScheduleConfig(current.valueOf());
    if (!todaySchecule) {
      return showErrNotification('当前日期没有班次,请重新选择您要请假的日期');
    }
    const { shiftView } = todaySchecule;
    const selectedScheduleTime = getDaySchedule(todaySchecule);

    const newValue = isEndDate ? selectedScheduleTime.afternoonEndTime : selectedScheduleTime.forenoonStartTime;
    // 设置操作栏
    setButtonList(isShift ? shiftView?.shiftButton?.split(',') || [] : shiftView?.calendarButton?.split(',') || []);
    setTempSelectDate(moment(newValue));
    setTempSelectedScdule(todaySchecule);
  };

  useEffect(() => {
    setTempSelectDate(value ? moment(value) : undefined);
  }, [value]);

  const onPanelChange = current => {
    // setInternalValue(current);
    handleGetScheduleEmp(current.valueOf());
  };
  const renderExtraFooter = () => {
    return (
      <Row>
        {_actionList.map(k => (
          <Col
            key={k.code}
            style={{
              marginLeft: 10,
            }}
          >
            <span
              onClick={() => handleAtivate(k)}
              className={classnames('itemSelect', k.code === timeFrame ? 'hightlight' : '')}
            >
              {k.title}
            </span>
          </Col>
        ))}
      </Row>
    );
  };
  const getTargetScheule = (curDate: Moment) => {
    const timedate = curDate.startOf('day').valueOf();
    return scheduleListMap[timedate] || {};
  };

  const range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };
  const disabledDateTime = () => ({
    disabledHours: () => (!isShift ? range(0, 24) : []),
    disabledMinutes: () => (!isShift ? range(0, 60) : []),
  });

  // useEffect(() => {
  //   const schdule = tempSelectedScdule || selectedScdule;
  //   curField && schdule && utils.cache.setValue(`${curField}-schdule`, schdule);
  // }, [tempSelectedScdule, selectedScdule, curField]);

  const genDisplayName = selectedScdule => {
    const { shiftView = {} } = selectedScdule || {};
    const splitTime = shiftView.splitPoint * 1000 + selectedScdule.date;
    // console.log('splitTime===selectedScdule', isEndDate, value, splitTime, selectedScdule);
    // 开始时间
    if (!isEndDate) {
      if (value === splitTime) {
        return 'YYYY-MM-DD 下半天';
      }
      if (value < splitTime) {
        return 'YYYY-MM-DD 上半天';
      }
      return 'YYYY-MM-DD 下半天';
    }
    if (value > splitTime) {
      return 'YYYY-MM-DD 下半天';
    }
    return 'YYYY-MM-DD 上半天';
  };

  return (
    <ConfigProvider locale={zhCN}>
      <Spin spinning={loading}>
        <div className="dhrAbsenceDatePicker">
          <DatePicker
            showTime={{
              format: 'hh:mm',
            }}
            className="dhrAbsenceDatePickerWrap"
            minuteStep={15}
            disabledTime={disabledDateTime}
            dateRender={current => {
              const targetSchdule = getTargetScheule(current);
              const isRestDay = [DAY_TYPES.HOLIDAYS, DAY_TYPES.REST_DAY].includes(targetSchdule.dateAttr);
              return (
                <div
                  className={classnames('ant-picker-cell-inner cellStyle', isRestDay && 'highlight-color')}
                  onClick={() => {
                    handleDateCellClick(current);
                  }}
                >
                  {isRestDay && <span className="cell-extra">{'休' || targetSchdule.dateAttrName}</span>}
                  <span>{current.date()}</span>
                  {/* dayTypeName为 节假日_法定 的代表当天是某个节日当天, */}
                  {targetSchdule?.holiday?.dayTypeName === '节假日_法定' && (
                    <span className="holidayLabel">{targetSchdule.holiday.holidayName?.split?.('_')?.[0]}</span>
                  )}
                </div>
              );
            }}
            ref={dateRef}
            format={`${isShift || !durationCalType ? 'YYYY-MM-DD HH:mm' : genDisplayName(selectedScdule || {})}`}
            // format="YYYY-MM-DD HH:mm"
            open={isPickerLayerOpen}
            showNow={false}
            renderExtraFooter={renderExtraFooter}
            popupClassName={classnames('dhrAbsenceDatePickerPopup', loading ? 'dhrAbsenceDatePickerPopupLoading' : '')}
            onChange={handleChange}
            onOk={handleChange}
            onPanelChange={onPanelChange}
            // 如果不选假期，那不给选时间
            disabled={isEnabled === '0' || !formData.C_CATEGORY_ID}
            onOpenChange={newOpen => setIsPickerLayerOpen(newOpen)} // 监听弹层打开关闭状态变化
            value={tempSelectDate}
            inputReadOnly
            style={{
              width: '100%',
            }}
          />
        </div>
      </Spin>
    </ConfigProvider>
  );
};

export default BaseInfo;
