// .dhrAbsenceDatePicker {

// }
.dhrAbsenceDatePickerPopup {
  .hightlight {
    color: var(--adm-color-primary);
  }
  .itemSelect {
    cursor: pointer;
  }
  .ant-picker-date-panel {
    width: auto;
    .ant-picker-content {
      width: 350px;
      .ant-picker-cell {
        &:not(.ant-picker-cell-in-view) {
          pointer-events: none;
          cursor: not-allowed;
          .cellStyle {
            pointer-events: none;
            cursor: not-allowed;
          }
        }
      }
    }
  }
  // 移动端样式
  @media screen and (max-width: 768px) {
    top: 80px !important;
    position: fixed !important;
    .ant-picker-panel-container {
      .ant-picker-content {
        .ant-picker-time-panel-column {
          max-width: 40px;
        }
        // td {
        //   max-width: 6vw;
        // }
      }
      .ant-picker-date-panel {
        width: auto;
        .ant-picker-content {
          width: 70vw;
        }
      }
      .ant-picker-time-panel {
        width: auto;
        .ant-picker-content {
          width: 30vw;
        }
      }
      .ant-picker-footer {
        padding: 0 20px;
      }
    }
    .cellStyle {
      max-width: 50px;
      height: 50px;
      font-size: 14px;
      font-weight: 800;
      line-height: 50px;
      margin: 0px 4px;
      position: relative;
      cursor: pointer;
      .cell-extra {
        position: absolute;
        top: 0px;
        right: -10px;
        font-size: 10px;
        width: 14px;
        height: 14px;
        line-height: 14px;
        border-radius: 4px;
        background-color: #dc5853;
        color: #fff;
      }
      &.highlight-color {
        color: #dc5853;
      }
      .holidayLabel {
        font-size: 10px;
        display: block;
        position: absolute;
        bottom: -20px;
        left: 0;
        right: 0;
        min-width: 30px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
      }
    }
  }
  @media screen and (min-width: 768px) {
    .cellStyle {
      width: 50px;
      height: 50px;
      font-size: 16px;
      font-weight: 800;
      line-height: 50px;
      margin: 0px 8px;
      position: relative;
      cursor: pointer;
      .cell-extra {
        position: absolute;
        top: 0px;
        right: 0px;
        font-size: 10px;
        width: 14px;
        height: 14px;
        line-height: 14px;
        border-radius: 4px;
        background-color: #dc5853;
        color: #fff;
      }
      &.highlight-color {
        color: #dc5853;
      }
      .holidayLabel {
        font-size: 10px;
        display: block;
        position: absolute;
        bottom: -20px;
        left: 0;
        right: 0;
      }
    }
  }
  .ant-picker-cell-selected {
    .cellStyle {
      color: #fff;
      .cell-extra {
        color: #fff;
      }
    }
  }
  .ant-picker-time-panel-cell {
    font-weight: 800;
  }
  &.dhrAbsenceDatePickerPopupLoading {
    &::before {
      content: '工作日历加载中...';
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 800;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 999;
    }
  }
}
