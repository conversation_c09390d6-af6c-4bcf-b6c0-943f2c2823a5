import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { beginTimeKey, endTimeKey } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );

    formRef?.current?.setFormItem?.(
      'dictConfigBeginTimeKey',
      curFormData?.dictConfigBeginTimeKey || defFormData?.dictConfigBeginTimeKey || beginTimeKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigEndTimeKey',
      curFormData?.dictConfigEndTimeKey || defFormData?.dictConfigEndTimeKey || endTimeKey
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigBeginTimeKey',
      label: '开始时间表单字段',
      configs: {
        onBlur: () => {
          const dictConfigBeginTimeKey = formRef?.current?.getFormItem?.('dictConfigBeginTimeKey');
          context?.onConfirm?.('dictConfigBeginTimeKey', dictConfigBeginTimeKey);
          setDictConfig(formRef, 'beginTimeKey', dictConfigBeginTimeKey, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigEndTimeKey',
      label: '请假结束表单字段',
      configs: {
        onBlur: () => {
          const dictConfigEndTimeKey = formRef?.current?.getFormItem?.('dictConfigEndTimeKey');
          context?.onConfirm?.('dictConfigEndTimeKey', dictConfigEndTimeKey);
          setDictConfig(formRef, 'endTimeKey', dictConfigEndTimeKey, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
