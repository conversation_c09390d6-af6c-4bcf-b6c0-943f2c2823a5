@list: 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58,
  60, 62, 64, 66, 68, 70, 72, 74, 76, 78, 80;
@fontList: 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50;
@borderRadiusList: 2, 4, 6, 8, 10, 12, 14, 16;

//定义  width/ height / padding / margin /line-height
each(@list, {
  //padding
  .pt-@{value} {
    padding-top: (@value * 1px);
  }
  .pb-@{value} {
    padding-bottom: (@value * 1px);
  }
  .pl-@{value} {
    padding-left: (@value * 1px);
  }
  .pr-@{value} {
    padding-right: (@value * 1px);
  }
  .p-@{value} {
    padding: (@value * 1px);
  }
  .px-@{value} {
    padding-left: (@value * 1px);
    padding-right: (@value * 1px);
  }
  .py-@{value} {
    padding-top: (@value * 1px);
    padding-bottom: (@value * 1px);
  }

  //margin
  .mt-@{value} {
    margin-top: (@value * 1px);
  }
  .mb-@{value} {
    margin-bottom: (@value * 1px);
  }
  .ml-@{value} {
    margin-left: (@value * 1px);
  }
  .mr-@{value} {
    margin-right: (@value * 1px);
  }
  .m-@{value} {
    margin: (@value * 1px);
  }
  .mx-@{value} {
    margin-left: (@value * 1px);
    margin-right: (@value * 1px);
  }
  .my-@{value} {
    margin-top: (@value * 1px);
    margin-bottom: (@value * 1px);
  }

  //width
  .w-@{value} {
    width: (@value * 1px);
  }
  //height
  .h-@{value} {
    height: (@value * 1px);
  }
  //line-height 
  .lh-@{value} {
    line-height: (@value * 1px);
  }

//定位-left
.left-@{value} {
  left: (@value * 1px);
}
//right
.right-@{value} {
  right: (@value * 1px);
}
//top
.top-@{value} {
  top: (@value * 1px);
}
//bottom
.bottom-@{value} {
  bottom: (@value * 1px);
}
});

each(@fontList, {
  .text-@{value} {
    font-size: (@value * 1px);
  }
});

//全圆角
.border-full {
  border-radius: 50%;
}

// 定义relative
.relative {
  position: relative;
}

// 定义absolute
.absolute {
  position: absolute;
}

.invisible {
  visibility: hidden;
}

//定义fixed
.fixed {
  position: fixed;
}

//定义flex
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}

.flex-direction-row {
  flex-direction: row;
}

.flex-direction-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}
.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-center {
  justify-content: center;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.justify-evenly {
  justify-content: space-evenly;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}

//定义text-align
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

//颜色
.text-black {
  color: #000;
}
.text-white {
  color: #fff;
}
.text-gray {
  color: #999;
}
.text-danger {
  color: #f5222d;
}
.text-primary {
  color: var(--antd-dynamic-primary-color);
}
.text-success {
  color: #52c41a;
}

.border-bottom {
  border-bottom: 1px solid #ededed;
}

// display block
.inline-block {
  display: inline-block;
}
.block {
  display: block;
}

.inline {
  display: inline;
}

// 宽度百分百
.w-full {
  width: 100%;
}

//   高度百分百
.h-full {
  height: 100%;
}

//手指样式
.pointer {
  cursor: pointer;
}
.pointer-events-none {
  pointer-events: none;
}
//背景白
.bg-white {
  background-color: white;
}

//禁止点击
.c-disabled {
  pointer-events: none;
  color: #ccc;
  cursor: default;
}

//隐藏
.hidden {
  display: none;
}

.overflow-hidden {
  overflow: hidden;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

//单行超出省略
.text-ellipsis {
  display: -webkit-box;
  display: -moz-box;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
//加粗
.text-bold {
  font-weight: bold;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}
