export const targetOptions = [
  {
    key: '80aa0b6fd7924d9fa5c2faa3d9eb2335',
    value: '80aa0b6fd7924d9fa5c2faa3d9eb2335',
    label: '主表单',
    disabled: true,
    children: [
      {
        id: 'c317ad7b8056442c98d1a49a62ef79d8',
        name: '主键',
        apiName: 'ID',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 1,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.ID',
        value: 'obj.BASIC_INFO_GROUP.ID',
        label: '主键',
      },
      {
        id: '96f45de8e22240949be18741c90b4abc',
        name: '编号',
        apiName: 'SERIAL_NUMBER',
        attrAlias: null,
        attrType: 'SERIAL_NUMBER',
        sortNo: 2,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.SERIAL_NUMBER',
        value: 'obj.BASIC_INFO_GROUP.SERIAL_NUMBER',
        label: '编号',
      },
      {
        id: '9f4ccb6a1f854fd7ad8942ea3f102123',
        name: '数据版本',
        apiName: 'DVERSION_NO',
        attrAlias: null,
        attrType: 'INT',
        sortNo: 3,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.DVERSION_NO',
        value: 'obj.BASIC_INFO_GROUP.DVERSION_NO',
        label: '数据版本',
      },
      {
        id: '507a5f0596254214a12c585db5e3568c',
        name: '分类',
        apiName: 'CLASS_ID',
        attrAlias: null,
        attrType: 'TREE',
        sortNo: 4,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.CLASS_ID',
        value: 'obj.BASIC_INFO_GROUP.CLASS_ID',
        label: '分类',
      },
      {
        id: '3b21323a0b074a0ab207366ccf24ad87',
        name: '生命周期阶段',
        apiName: 'LIFECYCLE',
        attrAlias: null,
        attrType: 'SELECT',
        sortNo: 5,
        dictId: 'LCP_CONF_WFSTATUS',
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.LIFECYCLE',
        value: 'obj.BASIC_INFO_GROUP.LIFECYCLE',
        label: '生命周期阶段',
      },
      {
        id: '66a9f505e6fb46e6b83ae5b7246f0c71',
        name: '创建用户',
        apiName: 'CRT_USER',
        attrAlias: null,
        attrType: 'PERSON',
        sortNo: 6,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.CRT_USER',
        value: 'obj.BASIC_INFO_GROUP.CRT_USER',
        label: '创建用户',
      },
      {
        id: '35c168a754e04675b7763f2c045b1759',
        name: '创建时间',
        apiName: 'CRT_TIME',
        attrAlias: null,
        attrType: 'DATE',
        sortNo: 7,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.CRT_TIME',
        value: 'obj.BASIC_INFO_GROUP.CRT_TIME',
        label: '创建时间',
      },
      {
        id: 'c8aec834841e44d1b6a96d010c5627cf',
        name: '更新用户',
        apiName: 'UPD_USER',
        attrAlias: null,
        attrType: 'PERSON',
        sortNo: 8,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.UPD_USER',
        value: 'obj.BASIC_INFO_GROUP.UPD_USER',
        label: '更新用户',
      },
      {
        id: 'df656e21c493410881103e6dff43452c',
        name: '更新时间',
        apiName: 'UPD_TIME',
        attrAlias: null,
        attrType: 'DATE',
        sortNo: 9,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.BASIC_INFO_GROUP.UPD_TIME',
        value: 'obj.BASIC_INFO_GROUP.UPD_TIME',
        label: '更新时间',
      },
      {
        id: 'c92494e075344e3c8e97a70c432a9094',
        name: '连接产品名称',
        apiName: 'PRODUCT_NAME',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 10,
        dictId: null,
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"PRODUCT_NAME","dictConfigType":"","formCode":"BASIC_INFO_GROUP","defaultValue":{"type":"value","value":""},"mouldName":"基础信息","hyphen":"","isCrtVisible":"1","isVisible":"1","attrType":"TEXT","mouldId":"BASIC_INFO_GROUP","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"连接产品名称","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.BASIC_INFO_GROUP.PRODUCT_NAME',
        value: 'obj.BASIC_INFO_GROUP.PRODUCT_NAME',
        label: '连接产品名称',
      },
      {
        id: '60e04dab94bc437182445e42c54a0b08',
        name: '连接产品编码',
        apiName: 'CODE',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 11,
        dictId: null,
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"CODE","dictConfigType":"","formCode":"BASIC_INFO_GROUP","defaultValue":{"type":"value","value":""},"mouldName":"基础信息","hyphen":"","isCrtVisible":"1","isVisible":"1","attrType":"TEXT","mouldId":"BASIC_INFO_GROUP","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"连接产品编码","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.BASIC_INFO_GROUP.CODE',
        value: 'obj.BASIC_INFO_GROUP.CODE',
        label: '连接产品编码',
      },
      {
        id: '46904a019f6840839580bd3139989b6f',
        name: '连接产品描述',
        apiName: 'DESCRIPTION',
        attrAlias: null,
        attrType: 'TEXTAREA',
        sortNo: 12,
        dictId: null,
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"DESCRIPTION","dictConfigType":"","formCode":"BASIC_INFO_GROUP","defaultValue":{"type":"value","value":""},"mouldName":"基础信息","hyphen":"","isCrtVisible":"1","isVisible":"1","attrType":"TEXTAREA","mouldId":"BASIC_INFO_GROUP","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"连接产品描述","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.BASIC_INFO_GROUP.DESCRIPTION',
        value: 'obj.BASIC_INFO_GROUP.DESCRIPTION',
        label: '连接产品描述',
      },
      {
        id: 'd40bbe60ef0e4bdab5f42565648302f1',
        name: '是否系统内置',
        apiName: 'IS_SYS',
        attrAlias: null,
        attrType: 'RADIO',
        sortNo: 13,
        dictId: 'LCP_CONF_YES_NO',
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"IS_SYS","dictConfigType":"dict","formCode":"BASIC_INFO_GROUP","defaultValue":{"type":"value","value":"0"},"availableList":[],"mouldName":"基础信息","hyphen":"","isCrtVisible":"1","isVisible":"1","dictId":"LCP_CONF_YES_NO","attrType":"RADIO","mouldId":"BASIC_INFO_GROUP","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"是否系统内置","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.BASIC_INFO_GROUP.IS_SYS',
        value: 'obj.BASIC_INFO_GROUP.IS_SYS',
        label: '是否系统内置',
      },
    ],
  },
  {
    key: 'a9405c7b8b0948d48263bb183909f932',
    value: 'a9405c7b8b0948d48263bb183909f932',
    label: '标题',
    disabled: true,
    children: [
      {
        id: 'be90076db3954ed681c7b583b253ba2d',
        name: '主键',
        apiName: 'ID',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 1,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.ID',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.ID',
        label: '主键',
      },
      {
        id: '65f1a4a924d943d0bddfa0417fbf8fd6',
        name: '表单实例ID',
        apiName: 'OBJ_ID',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 2,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.OBJ_ID',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.OBJ_ID',
        label: '表单实例ID',
      },
      {
        id: '79e92db9f71d4e22bbc4a208add81e80',
        name: '父级数据ID',
        apiName: 'PARENT_ID',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 3,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.PARENT_ID',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.PARENT_ID',
        label: '父级数据ID',
      },
      {
        id: 'd2e0ad9734e44002ae06d284817c9d9f',
        name: '数据版本',
        apiName: 'DVERSION_NO',
        attrAlias: null,
        attrType: 'INT',
        sortNo: 4,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.DVERSION_NO',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.DVERSION_NO',
        label: '数据版本',
      },
      {
        id: 'fbedbf95c0ca4c6c8b48ff0542a6e984',
        name: '创建用户',
        apiName: 'CRT_USER',
        attrAlias: null,
        attrType: 'PERSON',
        sortNo: 5,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.CRT_USER',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.CRT_USER',
        label: '创建用户',
      },
      {
        id: '34265a7ad0e4438e9a9b79fbca9bc2ed',
        name: '创建时间',
        apiName: 'CRT_TIME',
        attrAlias: null,
        attrType: 'DATE',
        sortNo: 6,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.CRT_TIME',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.CRT_TIME',
        label: '创建时间',
      },
      {
        id: 'a933b39a2f274c6db540f97d8a235d88',
        name: '更新用户',
        apiName: 'UPD_USER',
        attrAlias: null,
        attrType: 'PERSON',
        sortNo: 7,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.UPD_USER',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.UPD_USER',
        label: '更新用户',
      },
      {
        id: '05b71f15ff0c456885ea47debfa89867',
        name: '更新时间',
        apiName: 'UPD_TIME',
        attrAlias: null,
        attrType: 'DATE',
        sortNo: 8,
        dictId: null,
        dictConfig: null,
        uiConfig: null,
        baseConfig: null,
        type: null,
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.UPD_TIME',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.UPD_TIME',
        label: '更新时间',
      },
      {
        id: 'b6b354830dfa44019d70dc972a14e9bd',
        name: '参数名称',
        apiName: 'PARAM_NAME',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 9,
        dictId: null,
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"PARAM_NAME","dictConfigType":"","formCode":"OBJ_CONNECT_PRODUCT_PARAM","defaultValue":{"type":"value","value":""},"mouldName":"标题","hyphen":"","isCrtVisible":"1","isVisible":"1","attrType":"TEXT","mouldId":"401e70cc27794f8e945d682b0f59ba09","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"参数名称","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.PARAM_NAME',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.PARAM_NAME',
        label: '参数名称',
      },
      {
        id: 'c68fe45039aa4450889b96ae948d2d90',
        name: '参数字段',
        apiName: 'FIELD',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 10,
        dictId: null,
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"FIELD","dictConfigType":"","formCode":"OBJ_CONNECT_PRODUCT_PARAM","defaultValue":{"type":"value","value":""},"mouldName":"标题","hyphen":"","isCrtVisible":"1","isVisible":"1","attrType":"TEXT","mouldId":"401e70cc27794f8e945d682b0f59ba09","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"参数字段","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.FIELD',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.FIELD',
        label: '参数字段',
      },
      {
        id: '024707b927d14f8a8f117249203ec849',
        name: '参数描述',
        apiName: 'DESCRIPTION',
        attrAlias: null,
        attrType: 'TEXT',
        sortNo: 11,
        dictId: null,
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"DESCRIPTION","dictConfigType":"","formCode":"OBJ_CONNECT_PRODUCT_PARAM","defaultValue":{"type":"value","value":""},"mouldName":"标题","hyphen":"","isCrtVisible":"1","isVisible":"1","attrType":"TEXT","mouldId":"401e70cc27794f8e945d682b0f59ba09","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"参数描述","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.DESCRIPTION',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.DESCRIPTION',
        label: '参数描述',
      },
      {
        id: '2bbfe2f5b6934893938dc794b1f664c1',
        name: '展示类型',
        apiName: 'PARAM_TYPE',
        attrAlias: null,
        attrType: 'SELECT',
        sortNo: 12,
        dictId: 'LCP_FORM_DESGIN_SHOW_TYPE',
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"PARAM_TYPE","dictConfigType":"dict","formCode":"OBJ_CONNECT_PRODUCT_PARAM","defaultValue":{"type":"value","value":"input"},"availableList":[],"mouldName":"标题","hyphen":"","isCrtVisible":"1","isVisible":"1","dictId":"LCP_FORM_DESGIN_SHOW_TYPE","attrType":"SELECT","mouldId":"401e70cc27794f8e945d682b0f59ba09","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"展示类型","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.PARAM_TYPE',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.PARAM_TYPE',
        label: '展示类型',
      },
      {
        id: '0d5cb7a0c00644c28f17e4ca23e2da0b',
        name: '是否必填',
        apiName: 'IS_REQUIRED',
        attrAlias: null,
        attrType: 'RADIO',
        sortNo: 13,
        dictId: 'LCP_CONF_YES_NO',
        dictConfig: '{}',
        uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
        baseConfig:
          '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"IS_REQUIRED","dictConfigType":"dict","formCode":"OBJ_CONNECT_PRODUCT_PARAM","defaultValue":{"type":"value","value":"1"},"availableList":[],"mouldName":"标题","hyphen":"","isCrtVisible":"1","isVisible":"1","dictId":"LCP_CONF_YES_NO","attrType":"RADIO","mouldId":"401e70cc27794f8e945d682b0f59ba09","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"是否必填","isCopiable":"0"}',
        type: 'BASE',
        key: 'obj.OBJ_CONNECT_PRODUCT_PARAM.IS_REQUIRED',
        value: 'obj.OBJ_CONNECT_PRODUCT_PARAM.IS_REQUIRED',
        label: '是否必填',
      },
    ],
  },
];

// const a = {
//   id: '0b8ca0ef2977495c86b4c81b057bed6d',
//   classifyId: 'd3df3d78d81f4d55af18057009825d5b',
//   name: '管理层住房补助发放记录',
//   code: null,
//   tableName: 'tb_cnb_fixed_subsidy_record',
//   columnName: 'c_emp_id',
//   desc: '用来引用管理层住房补助发放记录发放的信息集',
// };
// {
//   id: 'c92494e075344e3c8e97a70c432a9094',
//   name: '连接产品名称',
//   apiName: 'PRODUCT_NAME',
//   attrAlias: null,
//   attrType: 'TEXT',
//   sortNo: 10,
//   dictId: null,
//   dictConfig: '{}',
//   uiConfig: '{"extra":{},"attr":{"labelAlign":"right","wrapperCol":{"span":20},"labelCol":{"span":4}}}',
//   baseConfig:
//     '{"commitType":"always","isChangeOn":"0","isRequired":"0","apiName":"PRODUCT_NAME","dictConfigType":"","formCode":"BASIC_INFO_GROUP","defaultValue":{"type":"value","value":""},"mouldName":"基础信息","hyphen":"","isCrtVisible":"1","isVisible":"1","attrType":"TEXT","mouldId":"BASIC_INFO_GROUP","isVersionOn":"0","isEditable":"1","dictConfig":{},"isEnabled":"1","name":"连接产品名称","isCopiable":"0"}',
//   type: 'BASE',
//   key: 'obj.BASIC_INFO_GROUP.PRODUCT_NAME',
//   value: 'obj.BASIC_INFO_GROUP.PRODUCT_NAME',
//   label: '连接产品名称',
// },

// {
//   "id": "1c0d2b4fbf3b4fe8a459ea7387c2287e",
//   "classifyId": "e701d4d6aaf04e26a2668d383ee9bfce",
//   "type": "JAVA",
//   "classPath": "com.cvte.dhr.lcp.hcm.core.module.cnb.operator.salary.CurrentMonthTravelSubsidyOperator",
//   "name": "当月出差补贴发放",
//   "method": null,
//   "sql": null,
//   "desc": null,
//   "code": "CURRENT_MONTH_TRAUL_SUBSIDY",
//   "returnType": "Double",
//   "params": []
// },

export const dataSource = [
  {
    id: '入参ID', // 场景参数ID
    name: '信息集参数', // 场景参数名称
    code: 'scene_code', // 场景参数编码
    type: 'INFO_SET', // 入参类型
    referenceId: '0b8ca0ef2977495c86b4c81b057bed6d', // 关联信息集ID/函数ID，其他类型为空
    referenceCode: '', // 关联编码
    column: [
      {
        columnId: '', // 信息集返回数据列字段ID
        columnCode: '', // 信息集返回数据列字段code
        columnName: '', // 信息集返回数据字段列名称
        columnType: 'SELECT', // 天舟云字段元数据类型，SELECT,TEXT等
        columnDictId: '', // 数据字典编码
        columnDictConfig: '', // 数据字典配置JSON串
      },
    ],
  },
  {
    id: 'c92494e075344e3c8e97a70c432a9094', // 场景参数ID
    name: '字符串参数', // 场景参数名称
    code: 'c92494e075344e3c8e97a70c432a9094', // 场景参数编码
    type: 'TEXT', // 入参类型
    referenceId: '', // 关联信息集ID/函数ID，其他类型为空
    referenceCode: '', // 关联编码
  },
  {
    id: '78787878656', // 场景参数ID
    name: '函数参数', // 场景参数名称
    code: 'fun_code', // 场景参数编码
    type: 'FUNCTION', // 入参类型
    referenceId: '1c0d2b4fbf3b4fe8a459ea7387c2287e', // 关联函数ID，其他类型为空
    referenceCode: 'CURRENT_MONTH_TRAUL_SUBSIDY', // 关联函数编码
    returnType: 'Double', // 函数返回类型
  },
];
