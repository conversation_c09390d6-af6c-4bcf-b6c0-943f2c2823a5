import React, { Suspense } from 'react';
import Loader, { ReactRemoteLoaderComponent } from '@cvte/resource-center-sdk';

import { APP_ID } from '@constants/system';

import { preloadCustomComp } from '@components/PreloadCustomComp';

// todo 需要改环境配置
const ConditionTreeNodeRender = ReactRemoteLoaderComponent(
  new Loader({
    // appName: '资源宿主对应在资源中心中的资源名称',
    name: 'tz-design',
    env: 'fat',
    // version: '1.0.0.9',
  }),
  'cirFormtransfer.conditionTreeNodeRender',
  {
    useShared: true,
    mode: 'page',
  }
);
export interface IAppProps {
  customCompList?: Record<string, any>[];
  targetFieldList: Record<string, any>[];
  outputTargetFieldList: Record<string, any>[];
  customTool: (item?: any) => React.ReactNode;
  customConditionOptions: Record<string, any>[];
  conditionId?: string;
  onGetData: (data: any, type: string) => void;
  onAddItem: (type: string) => void;
  onDeleteItem: (data: any, type: string) => void;
  data: any;
  type: string;
}
const BaseInfo: React.FC<IAppProps> = props => {
  const { data, type, customCompList, targetFieldList, onGetData, customTool, customConditionOptions } = props;
  return (
    <Suspense fallback={<div>loading</div>}>
      <ConditionTreeNodeRender
        // {...props}
        customConditionOptions={customConditionOptions}
        targetFieldOptions={targetFieldList}
        key={data.id}
        isNeedValidate={false}
        data={data}
        appId={APP_ID}
        onAddSubItem={() => {}}
        customTool={customTool}
        customCompList={customCompList || []}
        onGetData={onGetData(data.id, type)}
      />
    </Suspense>
  );
};

export default preloadCustomComp(BaseInfo);
