import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  // let dictConfigObj = JSON.parse(dictConfig || '{}');
  // if (!dictConfigObj) {
  //   dictConfigObj = {};
  // }
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { detailTableCode, moveType } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );

    formRef?.current?.setFormItem?.(
      'dictConfigDetailTableCode',
      curFormData?.dictConfigDetailTableCode || defFormData?.dictConfigDetailTableCode || detailTableCode
    );
    formRef?.current?.setFormItem?.(
      'dictConfigMoveType',
      curFormData?.dictConfigMoveType || defFormData?.dictConfigMoveType || moveType
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigDetailTableCode',
      label: '明细表编码',
      configs: {
        onBlur: () => {
          const dictConfigDetailTableCode = formRef?.current?.getFormItem?.('dictConfigDetailTableCode');
          context?.onConfirm?.('dictConfigDetailTableCode', dictConfigDetailTableCode);
          setDictConfig(formRef, 'detailTableCode', dictConfigDetailTableCode, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigMoveType',
      label: '移动类型',
      configs: {
        options: [
          {
            key: 'up',
            label: '上移',
            value: 'up',
          },
          {
            key: 'down',
            label: '下移',
            value: 'down',
          },
        ],
        onSelect: () => {
          const dictConfigMoveType = formRef?.current?.getFormItem?.('dictConfigMoveType');
          context?.onConfirm?.('dictConfigMoveType', dictConfigMoveType);
          setDictConfig(formRef, 'moveType', dictConfigMoveType, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
