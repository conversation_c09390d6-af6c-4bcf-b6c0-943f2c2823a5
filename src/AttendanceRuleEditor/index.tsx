import React, { forwardRef, useEffect } from 'react';
import useFetch from '@hooks/useFetch';
import sceneApis from '@apis/scene';
import cnbApis from '@apis/cnb';
import { defaultObj } from '@utils/tools';
import { RULE_TYPES, RETURAN_TYPES } from '@constants/rule';
import { AttributeType } from '@constants/tzAttrTypes';
import RuleEditor from './RuleEditor';

// const DEFAULT_SCENEID = '513a3d9748484381b32c0ae0823612bb';

export interface IAppProps { }
const AttendanceRuleEditor: React.FC<IAppProps> = forwardRef((props, ref) => {
  const [dataSourceInput, setDataSourceInput] = React.useState<any[]>([]);
  const [dataSourceOutput, setDataSourceOuput] = React.useState<any[]>([]);
  const [isInputSouceFinish, setIsInputSouceFinish] = React.useState<boolean>(false);
  const [isOutputSouceFinish, setIsOutputSouceFinish] = React.useState<boolean>(false);

  const metaParams = props.configs.context.globalContext?.pageTools?.getDetailEngineProps();
  const { sceneId } = metaParams;

  const { Data: sceneInputTreeData = [], Loading: sceneInputTreeLoading } = useFetch({
    ...sceneApis.sceneInputTree,
    params: {
      sceneId,
    },
  }) as {
    Data: any[];
    Loading: boolean;
  };

  const { Data: sceneOutputTreeData = [], Loading: sceneOuputTreeLoading } = useFetch({
    ...sceneApis.sceneOutputTree,
    params: {
      sceneId,
    },
  }) as {
    Data: any[];
    Loading: boolean;
  };

  const { Data: orgList = [] } = useFetch({
    ...cnbApis.dimList,
    // params: {
    //   excludeCategories: 'COMPANY', // 过滤法人公司
    // },
  }) as {
    Data: any[];
    Loading: boolean;
  };

  const { list: sceneInputTreeList = [] } = defaultObj(sceneInputTreeData);
  const { list: sceneOutputTreeList = [] } = defaultObj(sceneOutputTreeData);

  /** 数据拉取并转换 */
  const handleFetchDataSource = (list, cb) => {
    const typeMappings = {
      [RULE_TYPES.NUMBER]: AttributeType.INT,
      [RULE_TYPES.DICTIONARY]: AttributeType.SELECT,
      [RULE_TYPES.VARCHAR]: AttributeType.TEXT,
      [RULE_TYPES.BOOLEAN]: AttributeType.SELECT,
    };

    const getAttype = data => {
      if (data.returnType === RETURAN_TYPES.BOOLEAN) {
        return AttributeType.SELECT;
      }
      return data.columnType || data.returnType || typeMappings[data.type];
    };
    const getDictId = data => {
      if (data.returnType === RETURAN_TYPES.BOOLEAN) {
        return 'LCP_CONF_YES_NO';
      }
      return data.type === RULE_TYPES.DICTIONARY ? data.referenceCode : undefined;
    };

    /** 特殊规则,  */
    const newDataSource = list.map(k => {
      const newItems = {
        ...k,
        key: k.code,
        code: k.code,
        value: k.id,
        label: k.name,
        disabled: k.column?.length > 0,
        attrType: getAttype(k),
        source: k.type,
        dictConfig: k.dictConfig || '{}',
        dictId: getDictId(k),
        children:
          k.column?.map(childColumn => ({
            code: childColumn.columnCode,
            key: childColumn.columnCode,
            value: childColumn.columnId,
            label: childColumn.columnName,
            attrType: childColumn.columnType,
            dictConfig: childColumn.columnDictConfig,
            dictId: childColumn.columnDictId,
            uiConfig: childColumn.uiConfig,
            baseConfig: childColumn.baseConfig,
            source: k.type,
            parentId: k.referenceId,
            columnId: childColumn.columnId,
            // 只有type为CUSTOM_FIELD才会渲染自定义组件
            type: childColumn.layoutType === 'BASE' ? 'CUSTOM_FIELD' : childColumn.layoutType,
          })) || [],
      };
      return newItems;
    });
    cb(newDataSource);
  };

  /** 入参数 */
  useEffect(() => {
    handleFetchDataSource(sceneInputTreeList, data => {
      setDataSourceInput(data);
      setIsInputSouceFinish(true);
    });
  }, [sceneInputTreeList?.length]);

  /** 出参 */
  useEffect(() => {
    handleFetchDataSource(sceneOutputTreeList, data => {
      setDataSourceOuput(data);
      setIsOutputSouceFinish(true);
    });
  }, [sceneOutputTreeList?.length]);

  console.log('dataSource=====', dataSourceInput, dataSourceOutput);
  return (
    <RuleEditor
      targetFieldList={dataSourceInput || []}
      outputTargetFieldList={dataSourceOutput || []}
      orgList={orgList}
      soueceDataReady={isInputSouceFinish && isOutputSouceFinish}
      ref={ref}
      {...props}
    />
  );
});

AttendanceRuleEditor.displayName = 'AttendanceRuleEditor';
export default AttendanceRuleEditor;
