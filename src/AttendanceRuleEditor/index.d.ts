/**
 * 规则行数据
 */
export interface IRow {
  id: string;
  code: string;
  condition: string; // 条件
  varType: string; // 字段类型
  valueType: string; // 值类型， 有值与参数
  value: string; // 字段值
}

export interface IAppProps extends IOriginalProps {
  sceneId: string;
  hideCondition?: boolean;
  customCompList?: Record<string, any>[];
  targetFieldList: Record<string, any>[];
  outputTargetFieldList: Record<string, any>[];
  conditionId?: string;
  orgList: Record<string, any>[];
}
