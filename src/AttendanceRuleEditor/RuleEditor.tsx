import React, { useEffect, useRef, forwardRef, useImperativeHandle, useState } from 'react';

import { Col, Row, Card, Radio, Tag, Alert, Button } from 'antd';
import CodeEditor, { moveToPosition } from '@components/CodeEditor';
import useDebounce from '@hooks/useDebounce';
import {
  OPERATION_TYPES,
  OPERATE_TYPES,
  OPERATE_TYPES_CODES,
  RULE_TYPES,
  TZ_TYPES_TO_RULE_TYPES,
  EDIT_TYPES,
} from '@constants/rule';

import { AttributeType } from '@constants/tzAttrTypes';
import commomApis from '@apis/common';
import { defaultArray, findNodeWithValue } from '@utils/tools';
import { request } from '@utils/http';
import { PlusCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { APP_ID } from '@constants/system';
import ConditionTreeNodeRender from './ConditionTreeNodeRender';

import { IOriginalProps } from '../types';
import { IRow } from './index.d';
import './style.less';
import '../styles/atom.less';
import { OPERATE_LIST, CONDITION_TYPES, conditionTypesList } from './data';

/**
 * 该组件产生的规则均需要保存到 规则表单中去
 */
/** 规则表单classId */
const RULE_FORM_CLASS_ID = '9c9b3fbfab9247839d051fd1020f23fe';
/** 规则表单appId */
const RULE_FORM_APP_ID = 'bac9d8edadd14879b4de05d519ecd086';

const genResultEmptyRow = () => ({
  id: Math.random().toString(16),
  // condition: '$eq',
  valueType: 'value',
});
const genEmptyRow = () => ({
  id: Math.random().toString(16),
  valueType: 'value',
});

interface IAppProps extends IOriginalProps {
  customCompList?: Record<string, any>[];
  targetFieldList: Record<string, any>[];
  outputTargetFieldList: Record<string, any>[];
  conditionId?: string;
  orgList: Record<string, any>[];
  soueceDataReady?: boolean;
}

const BaseInfo: React.FC<IAppProps> = forwardRef((props, ref) => {
  const { customCompList, targetFieldList, outputTargetFieldList, orgList, soueceDataReady } = props;
  const [ruleFormData, setRuleFormData] = useState<Record<string, any>>({});
  const [isFinishInit, setIsFinishInit] = useState<boolean>(false); // 是否完成初始化
  const [warnText, setWarnText] = useState<string>(''); // 错误信息
  console.log('~~~~attendanceRuleEditor props~~~~~', props);
  const metaParams = props.configs.context.globalContext?.pageTools?.getDetailEngineProps();
  console.log('metaParams', metaParams);
  const { sceneId, sceneCode, conditionId, hideCondition, hideResult } = metaParams;

  /** 编辑器ref */
  const editorRef = useRef<any>();

  /** 条件公式ref */
  const conditionRef = useRef<any>();

  /** 条件 */
  const [rows, setRows] = React.useState([
    {
      ...genEmptyRow(),
    },
  ]);

  /** 结果 */
  const [resultRows, setResultRows] = React.useState([
    {
      ...genResultEmptyRow(),
    },
  ]);

  const [formulaType, setFormulaType] = React.useState<keyof typeof CONDITION_TYPES>(CONDITION_TYPES.ALL);

  /** 统一设置行 */
  const handleSetConditionRows = data => {
    setRows(data);
    conditionRef.current = data;
  };

  /**
   * @description 获取表单数据
   * @param value
   * @returns void
   */
  const handleSetEditorValue = (value: string) => {
    console.log('设置编辑器值', value);
    editorRef?.current?.setValue(value);
  };

  const warnTipsValidate = (ruleRows: IObject[]) => {
    try {
      const andorRelationText = ruleRows.length > 0 ? editorRef?.current?.getValue() || '' : '';
      const matchLine = andorRelationText?.match(/T\d{1,3}/g) || [];
      // 防止引用到不存在的地方
      const unExistRuleText = Array.from(new Set(matchLine))
        .filter(k => {
          return +k.replace('T', '') > ruleRows.length;
        })
        ?.join(',');

      // 防止有其他字符
      const innerChartsList = OPERATE_LIST.map(k => k.name);
      // 去除空白字符
      const pureText = andorRelationText.replace(/\s|T\d{1,3}/g, '');
      // 筛选不合法字符
      let unLegalCharaterText = pureText;
      innerChartsList.forEach(unit => {
        const escapedUnit = unit.replace(/[\\^$*+?.()|[\]{}]/g, '\\$&');
        unLegalCharaterText = unLegalCharaterText.replace(new RegExp(escapedUnit, 'g'), '');
      });
      const _text = unLegalCharaterText ? `包含不合法字符:${unLegalCharaterText};` : '';
      const wrongTText = unExistRuleText ? `当前规则编写有误: ${unExistRuleText}在左侧规则中没定义` : '';
      setWarnText(_text + wrongTText);
    } catch (error) {
      console.log('规则校验报错啦啦啦', error);
    }
  };

  // 代码编辑器
  const onCodeEditorChange = useDebounce(
    () => {
      warnTipsValidate(rows);
    },
    1000,
    [rows]
  );

  useEffect(() => {
    let _formala = '';
    // 如果不是CUSTOM类型, 则自动拼接
    if (formulaType !== CONDITION_TYPES.CUSTOM) {
      _formala = rows.map((k, index) => `T${index + 1}`).join(formulaType === CONDITION_TYPES.ALL ? '并且' : '或者');
      handleSetEditorValue(_formala);
    } else {
      // 如果是是CUSTOM类型, 需要检测是否合规;
      warnTipsValidate(rows);
    }
  }, [rows.length, formulaType]);

  useEffect(() => {
    editorRef.current?.setOption('readOnly', formulaType !== CONDITION_TYPES.CUSTOM);
  }, [formulaType]);

  /** 请求规则表单数据详情 */
  const onFetchScheduleDetail = id => {
    const params = {
      id,
      // onlyMain: true,
      keyType: 'UPPER',
      appId: RULE_FORM_APP_ID,
      formClassId: RULE_FORM_CLASS_ID,
    };
    request({
      ...commomApis.formDetailInfo,
      params,
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        console.log('规则表单详情', res);
        const { bizDataMap = {}, mainData = {} } = defaultArray(res);
        const TP_CONDITION_OUTPUT = bizDataMap?.TP_CONDITION_OUTPUT || [];
        const TP_CONDITION_INPUT = bizDataMap?.TP_CONDITION_INPUT || [];
        setRuleFormData(res);

        /** 设置行数据 */
        const newRows = [...TP_CONDITION_INPUT]
          .sort((a, b) => {
            // 比较两个数字并返回结果
            return parseInt(a.C_LINE_CODE?.substring(1) || 1) - parseInt(b?.C_LINE_CODE?.substring(1) || 1);
          })
          .map(k => {
            const targetField = findNodeWithValue(targetFieldList, k.C_CODE, 'code');
            return {
              id: k.ID,
              targetField: targetField.value,
              condition: `$${k.C_OPT_TYPE}`,
              valueType: k.C_VALUE_TYPE,
              value: typeof k.C_VALUE === 'string' && k.C_VALUE?.indexOf(',') > -1 ? k.C_VALUE?.split(',') : k.C_VALUE,
              varType: targetField.attrType,
            };
          });
        console.log('newRows', newRows);
        const _newRows = newRows.length > 0 ? newRows : [{ ...genEmptyRow() }];
        // setRows(_newRows);
        handleSetConditionRows(_newRows);

        /** 设置结果行数据 */
        const newResultRows = TP_CONDITION_OUTPUT.map(k => {
          const targetField = findNodeWithValue(outputTargetFieldList, k.C_CODE, 'code');
          return {
            id: k.ID,
            targetField: targetField.value,
            condition: `$${k.C_OPT_TYPE}`,
            valueType: k.C_VALUE_TYPE,
            value:
              typeof k.C_OUTPUT_VALUE === 'string' && k.C_OUTPUT_VALUE?.indexOf(',') > -1
                ? k.C_OUTPUT_VALUE?.split(',')
                : k.C_OUTPUT_VALUE,
            varType: targetField.attrType,
          };
        });
        const _newResultRows = newResultRows.length > 0 ? newResultRows : [{ ...genResultEmptyRow() }];
        setResultRows(_newResultRows);

        /** 设置条件逻辑 */
        setFormulaType(mainData.C_ANDOR_TYPE || CONDITION_TYPES.ALL);
        /** 设置编辑器值 */
        if (mainData.C_ANDOR_TYPE === CONDITION_TYPES.CUSTOM) {
          handleSetEditorValue(mainData.C_ANDOR_RELATION);
        }

        /** 标记初始化完成 */
        setIsFinishInit(true);
      },
    });
  };
  useEffect(() => {
    /** 拉取表单详情 */
    !isFinishInit &&
      conditionId &&
      soueceDataReady &&
      (targetFieldList.length > 0 || outputTargetFieldList.length > 0) &&
      onFetchScheduleDetail(conditionId);
  }, [conditionId, soueceDataReady, targetFieldList.length, outputTargetFieldList.length]);

  /** 插入高亮文本  */
  const insertTextWithHighlight = (editor, { title, text, className, code }) => {
    const cursor = editor.getCursor(); // 获取当前光标位置
    editor.replaceSelection(text); // 插入文本
    const end = editor.getCursor(); // 获取插入文本后的光标位置
    const hightlightEnd = {
      ...end,
      ch: end.ch,
    };
    console.log('====插入文本后的光标位置====', end, hightlightEnd);
    const replaceSpan = `<span class="cm-field ${className}" data-code="${code}">${title}</span>`;
    const outDiv = document.createElement('div'); // 创建一个div
    outDiv.innerHTML = replaceSpan; // 将dom字符串赋给div
    editor.markText(cursor, hightlightEnd, {
      handleMouseEvents: true, // handleMouseEvents: 设置为true，这意味着这个标记（mark）会处理鼠标事件
      atomic: true, // atomic: 同样设置为true，这表示这个标记是不可分割的，即当用户进行文本操作时，该标记作为一个整体来处理。
      // 这是一个函数，它返回一个DOM元素，该元素会被用来替换标记的位置。replacedWith函数内部执行了以下操作：
      replacedWith: outDiv.firstChild,
      code,
      className,
      title,
    }); // 高亮插入的文本
    moveToPosition(editor, end.line, end.ch); // 将光标移动到插入文本之后
  };
  /**
   * @description 转换公式
   * @param param0
   */
  const handleInsertFormula = ({ name, code }) => {
    insertTextWithHighlight(editorRef.current, {
      title: name,
      text: name,
      className: 'cm-keyword',
      code,
    });
  };

  const valueTransform = (row, originTarget, type?: string) => {
    /** 非简单类型 */
    const NOT_SIMPLE_TYPES = [
      AttributeType.MULTI_ORGANIZTION,
      AttributeType.MULTI_PERSON,
      AttributeType.MULTI_SEARCH,
      AttributeType.MULTI_SELECT,
      AttributeType.MULTI_TREE,
      AttributeType.ORGANIZTION,
      AttributeType.PERSON,
      AttributeType.SEARCH,
      AttributeType.SELECT,
      AttributeType.RELATE_DATA,

      // 'CUSTOM_FIELD',
    ];
    const { attrType } = originTarget;
    const { value, extra = {} } = row;
    if (attrType === AttributeType.ORG) {
      const org = orgList?.find(k => k.hid === value);
      return org?.name || value;
    }
    if (extra.valueTranslate) {
      if (!Array.isArray(extra.valueTranslate)) {
        return extra.valueTranslate;
      }
      // 多选情况下value可能为数组
      const genSigleName = _data => extra.valueTranslate?.find(k => k.key === _data)?.label || value;
      if (Array.isArray(value)) {
        return value.map(v => genSigleName(v)).join(',');
      }
      return genSigleName(value);
    }
    /** 都没有，看下接口缓存的翻译 */
    if (NOT_SIMPLE_TYPES.includes(attrType)) {
      const isCondition = type === EDIT_TYPES.CONDITION;
      const detailList = isCondition
        ? ruleFormData?.bizDataMap?.TP_CONDITION_INPUT
        : ruleFormData?.bizDataMap?.TP_CONDITION_OUTPUT;
      if (!detailList) return value;
      const target = detailList.find(k => k.C_CODE === originTarget.code);
      return target?.[isCondition ? 'C_VALUE_NAME' : 'C_OUTPUT_VALUE_NAME'] || value;
    }
    return value;
  };

  /**
   * @description 数据转换逻辑表达
   * 如：用工关系状态等于在职
   * @param row 行数据
   */
  const generateRelationText = (row: IRow, originTarget: Record<string, any>, type: string) => {
    const { condition } = row;
    if (!originTarget) return '';
    const _value = valueTransform(row, originTarget, type);

    return `${originTarget.label}${OPERATION_TYPES[condition]}${_value}`;
  };

  /** 行数据转化为代码 */
  const handleTextToCode = (row: IRow, originTarget: Record<string, any> = {}, type?: string) => {
    const { valueType, condition, value } = row;
    const { code, attrType, source, parentId } = originTarget;
    const transformMappings = {
      [RULE_TYPES.INFO_SET]: () => {
        const _code = `${parentId}#${originTarget.columnId}`;
        return `DS("${_code}", "")`;
      },
      [RULE_TYPES.FUNCTION]: () => `${originTarget.referenceCode}()`,
    };
    const params1 = transformMappings[source] ? transformMappings[source]() : code;
    const params2 = valueType === 'value' ? `"${value}"` : value || null;
    const params3 = condition;
    const params4 = TZ_TYPES_TO_RULE_TYPES[attrType] || 'STRING';
    return `COMPARE_FUNC(${params1}, ${params2}, "${params3}", "${params4}")`;
  };

  /**
   * @description 数据组装
   * @returns  {conditionList, condition, resultList}
   * @todo 函数返回类似检查
   */
  const handleAssemblyFormula = () => {
    const omit$Prefix = list =>
      [...list].map(k => {
        k.condition?.startsWith('$') && (k.condition = k.condition.slice(1));
        return k;
      });
    // 除去条件操作符$
    const newRows = omit$Prefix(rows);
    const newResultRows = omit$Prefix(resultRows)?.filter(k => k.targetField) || [];

    /** 数据组装依赖 */
    const genDetailRow = (k, lineIndex) => {
      /**  字段源数据目标对象 */
      const originTarget = findNodeWithValue(targetFieldList, k.targetField);
      if (!originTarget) return {};
      return {
        ID: k.id,
        C_CODE: originTarget.code,
        C_LINE_CODE: `T${lineIndex + 1}`, // 行编码
        C_SCENE_CODE: sceneCode,
        C_OPT_TYPE: k.condition,
        C_VALUE_TYPE: k.valueType,
        C_VALUE: k.value,
        C_VALUE_NAME: valueTransform(k, originTarget, EDIT_TYPES.CONDITION),
        C_INPUT_EXPRESS: handleTextToCode(k, originTarget, EDIT_TYPES.CONDITION), // 入参表达式
        C_INPUT_SHOW: generateRelationText(k, originTarget, EDIT_TYPES.CONDITION), // 入参展示
      };
    };
    /**
     * @description 转换规则
     * 1、字段的类型在COMPARE_FUNC(参数1,参数2,参数3,参数4)的第四个参数
     * 2、conditionList写在condition对象的一个字段里
     */
    const conditionList = newRows.map((k: IRow, lineIndex: number) => genDetailRow(k, lineIndex)).filter(k => k.C_CODE);
    // 规则list
    const resultList = newResultRows.map((k: IRow) => {
      const originTarget = findNodeWithValue(outputTargetFieldList, k.targetField);
      return {
        ID: k.id,
        C_CODE: originTarget.code,
        C_SCENE_CODE: sceneCode,
        C_OPT_TYPE: k.condition,
        C_VALUE_TYPE: k.valueType,
        C_OUTPUT_VALUE: k.value,
        C_OUTPUT_VALUE_NAME: valueTransform(k, originTarget, EDIT_TYPES.RESULT),
        // C_INPUT_EXPRESS: handleTextToCode(k), // 入参表达式
        C_OUTPUT_SHOW: generateRelationText(k, originTarget, EDIT_TYPES.RESULT), // 入参展示
      };
    });

    /** 条件逻辑text */
    const andorRelationText = conditionList.length > 0 ? editorRef?.current?.getValue() || '' : '';

    /** 最终计算公式代码组装 */
    let inputFormala: string = andorRelationText;
    let inputShow: string = andorRelationText;
    /** 替换表达式 */
    for (let i = conditionList.length - 1; i >= 0; i--) {
      const k = conditionList[i];
      const tNum = k.C_LINE_CODE.substring(1); // 提取 T 后面的数字部分
      inputFormala = inputFormala.replaceAll(new RegExp(`T${tNum}`, 'g'), k.C_INPUT_EXPRESS);
      inputShow = inputShow.replaceAll(new RegExp(`T${tNum}`, 'g'), k.C_INPUT_SHOW);
    }

    /**  输出接轨哦 */
    const outputShow = resultList.map(k => k.C_OUTPUT_SHOW)?.join(';');

    ['AND', 'OR'].forEach(k => {
      inputFormala = inputFormala.replaceAll(new RegExp(`${OPERATE_TYPES[k]}`, 'g'), OPERATE_TYPES_CODES[k]);
    });

    const condition = {
      C_SCENE_ID: sceneId,
      C_INPUT_FORMULA: inputFormala, // 最终计算公式代码
      C_INPUT_SHOW: inputShow, // 入参展示翻译， 如，用工关系状态等于在职，就是T1 T2对应的文字翻译
      C_OUTPUT_SHOW: outputShow, // 出参展示翻译, 返回结果，非必需
      C_ANDOR_RELATION: andorRelationText, // 逻辑关系
      C_ANDOR_TYPE: formulaType, // 逻辑类型
    };
    console.log('inputFormala====', condition, conditionList, resultList);
    return {
      conditionList,
      condition,
      resultList,
    };
  };

  /** 增加条件 */
  const onAddItem = (type: EDIT_TYPES) => {
    // console.log('newdata', data, rows);
    const isCondition = type === EDIT_TYPES.CONDITION;
    const actionData = isCondition ? conditionRef.current || rows : resultRows;
    const newData = [
      ...actionData,
      {
        ...(isCondition ? genEmptyRow() : genResultEmptyRow()),
      },
    ];
    if (isCondition) {
      // rowsRef.current = newData;
      // setRows(newData);
      handleSetConditionRows(newData);
    } else {
      setResultRows(newData);
    }
  };

  /**
   * 删除条件
   */
  const handleDeleteItem = (row: Record<string, any>) => {
    const _list = conditionRef.current || [];
    let newRows: Record<string, any>[] = [];
    if (_list.length === 1) {
      newRows = [{ ...genEmptyRow() }];
    } else {
      newRows = _list?.filter(k => k.id !== row.id);
    }
    handleSetConditionRows(newRows);
  };

  /**
   *
   * @param resData
   * @param formData 表单数据
   */
  const handleGetData = (id: string, type: EDIT_TYPES) => resData => {
    const actionData = type === EDIT_TYPES.CONDITION ? [...(conditionRef.current || rows)] : [...resultRows];

    const targetIndex = actionData.findIndex(k => k.id === id);
    targetIndex >= 0 && (actionData[targetIndex] = { ...resData, id });
    if (type === EDIT_TYPES.CONDITION) {
      handleSetConditionRows(actionData);
    } else {
      setResultRows(actionData);
    }
  };

  /** 对外暴露方法 */
  useImperativeHandle(ref, () => ({
    getData: handleAssemblyFormula,
    saveRule: async () => {
      console.log('执行保存规则');
      const rules = handleAssemblyFormula();
      console.log('rules===========', rules);
      const data = {
        mainData: {
          ...rules.condition,
        },
        bizDataMap: {
          TP_CONDITION_INPUT: rules.conditionList, // 条件公式入参
          TP_CONDITION_OUTPUT: rules.resultList, // 条件公式出参
        },
      };
      const params = {
        keyType: 'UPPER',
        appId: RULE_FORM_APP_ID,
        formClassId: '9c9b3fbfab9247839d051fd1020f23fe',
        id: conditionId,
      };

      const requestApi = conditionId ? commomApis.updateFormData : commomApis.newFormData;
      const result = await request({
        ...requestApi,
        data,
        params,
        headers: {
          'x-app-id': params.appId,
        },
      });
      console.log('规则保存结果', result);
      return {
        result,
        data,
        params,
      };
    },
    updateRule: () => { },
    validateRule: () => { },
  }));

  const customToolNodes = item => {
    return (
      <div
        className="ml-20"
        style={{
          minWidth: '50px',
        }}
      >
        <PlusCircleOutlined onClick={() => onAddItem(EDIT_TYPES.CONDITION)} className="mr-10" />
        <DeleteOutlined onClick={() => handleDeleteItem(item)} />
      </div>
    );
  };
  console.log('rows========', rows, resultRows, conditionRef.current);
  return (
    <div className="attendanceRuleEditor">
      {!sceneId && <Alert message="缺少场景参数!请勿直接操作" type="warning" className="mb-20" />}
      {/* <Row>
        <Button onClick={handleAssemblyFormula}>收集数据</Button>
      </Row> */}
      {!hideCondition && (
        <Row>
          <Col span={15}>
            <Card title="条件" size="small">
              {/* antd v4表单 */}
              {/* <Form form={formRef}> */}
              {(rows || []).map((rowData, index) => (
                <div className="flex items-center" key={rowData.id}>
                  <Tag className="m-6">T{index + 1}</Tag>
                  <ConditionTreeNodeRender
                    targetFieldOptions={targetFieldList}
                    targetFieldList={targetFieldList}
                    appId={APP_ID}
                    data={rowData}
                    onAddSubItem={() => { }}
                    customCompList={customCompList || []}
                    onGetData={handleGetData}
                    type={EDIT_TYPES.CONDITION}
                    customTool={() => customToolNodes(rowData)}
                    customConditionOptions={innerConditionList => {
                      if (rowData?.varType === AttributeType.ORG) {
                        return [
                          ...innerConditionList,
                          {
                            key: '$childInAny',
                            label: OPERATION_TYPES.childInAny,
                            value: '$childInAny',
                          },
                          {
                            key: '$childNotInAny',
                            label: OPERATION_TYPES.childNotInAny,
                            value: '$childNotInAny',
                          },
                        ];
                      }
                      return innerConditionList;
                    }}
                  />
                </div>
              ))}
              {/* </Form> */}
            </Card>
          </Col>
          <Col span={8} offset={1}>
            <Card title="条件逻辑" size="small">
              <div className="p-20">
                <Radio.Group
                  onChange={e => {
                    setFormulaType(e.target.value);
                  }}
                  value={formulaType}
                >
                  {conditionTypesList.map(k => (
                    <Radio value={k.code} className="block mb-10">
                      {k.name}
                    </Radio>
                  ))}
                </Radio.Group>
                <CodeEditor
                  codeMirrorConfig={{
                    theme: 'mdn-like',
                    lineNumbers: false,
                  }}
                  onChange={onCodeEditorChange}
                  onDidMount={editor => {
                    editorRef.current = editor;
                  }}
                  className="ruleEditor"
                />
                {warnText && <div className="text-danger">{warnText}</div>}
                {formulaType === CONDITION_TYPES.CUSTOM && (
                  <div className="mt-20">
                    {OPERATE_LIST.map(k => (
                      <Tag key={k.code} onClick={() => handleInsertFormula(k)} className="operateBtn">
                        {k.name}
                      </Tag>
                    ))}
                  </div>
                )}
              </div>
            </Card>
          </Col>
        </Row>
      )}
      {!hideResult && (
        <Row>
          <Card title="结果" size="small">
            {/* <Form form={formRef}> */}
            {resultRows.map((rowData, index) => (
              <div className="flex items-center" key={rowData.id}>
                <Tag className="m-6">T{index + 1}</Tag>
                <ConditionTreeNodeRender
                  targetFieldList={outputTargetFieldList}
                  key={rowData.id}
                  customTool={() => <div />}
                  data={rowData}
                  customCompList={customCompList || []}
                  onAddSubItem={() => { }}
                  onDeleteItem={handleDeleteItem}
                  onGetData={handleGetData}
                  type={EDIT_TYPES.RESULT}
                  customConditionOptions={innerConditionList => {
                    if (rowData?.varType === AttributeType.ORG) {
                      return [
                        ...innerConditionList,
                        {
                          key: '$childInAny',
                          label: OPERATION_TYPES.childInAny,
                          value: '$childInAny',
                        },
                        {
                          key: '$childNotInAny',
                          label: OPERATION_TYPES.childNotInAny,
                          value: '$childNotInAny',
                        },
                      ];
                    }
                    return innerConditionList;
                  }}
                />
              </div>
            ))}
            {/* </Form> */}
          </Card>
        </Row>
      )}
    </div>
  );
});

export default BaseInfo;
