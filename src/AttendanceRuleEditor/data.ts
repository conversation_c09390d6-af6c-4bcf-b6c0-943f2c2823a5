import { OPERATE_TYPES, OPERATE_TYPES_CODES } from '@constants/rule';

export const OPERATE_LIST = [
  {
    name: OPERATE_TYPES.AND,
    code: OPERATE_TYPES_CODES.AND,
  },
  {
    name: OPERATE_TYPES.OR,
    code: OPERATE_TYPES_CODES.OR,
  },
  {
    name: '(',
    code: '(',
  },
  {
    name: ')',
    code: ')',
  },
];

export const CONDITION_TYPES = {
  ALL: '1',
  ANY: '2',
  CUSTOM: '3',
};

export const conditionTypesList = [
  {
    name: '满足所有条件（并且）',
    code: CONDITION_TYPES.ALL,
  },
  {
    name: ' 满足任意条件（或者）',
    code: CONDITION_TYPES.ANY,
  },
  {
    name: '自定义逻辑',
    code: CONDITION_TYPES.CUSTOM,
  },
];
