import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { TIME_PICKER_FORMAT_TYPE } from '@constants/common';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { timeType, fieldFormatType } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );

    formRef?.current?.setFormItem?.(
      'dictConfigTimeType',
      curFormData?.dictConfigTimeType || defFormData?.dictConfigTimeType || timeType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFieldFormatType',
      curFormData?.dictConfigFieldFormatType || defFormData?.dictConfigFieldFormatType || fieldFormatType
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'select',
      key: 'dictConfigTimeType',
      label: '时间格式',
      required: true,
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请选择',
          },
        ],
      },
      configs: {
        allowClear: true,
        placeholder: '请选择时间格式',
        options: [
          {
            key: 'HH:mm:ss',
            label: '时：分：秒',
            value: 'HH:mm:ss',
          },
          {
            key: 'HH:mm',
            label: '时：分',
            value: 'HH:mm',
          },
        ],
        onSelect: val => {
          context?.onConfirm?.('dictConfigTimeType', val);
          setDictConfig(formRef, 'timeType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigFieldFormatType',
      label: '储存时间格式',
      required: true,
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请选择',
          },
        ],
      },
      configs: {
        allowClear: true,
        placeholder: '请选择储存时间字段的格式',
        options: [
          {
            label: '时间戳',
            key: TIME_PICKER_FORMAT_TYPE.TIMESTAMP,
            value: TIME_PICKER_FORMAT_TYPE.TIMESTAMP,
          },
          {
            label: '时间转化到分',
            key: TIME_PICKER_FORMAT_TYPE.MINUTE,
            value: TIME_PICKER_FORMAT_TYPE.MINUTE,
          },
          {
            label: '时间转化到秒',
            key: TIME_PICKER_FORMAT_TYPE.SECOND,
            value: TIME_PICKER_FORMAT_TYPE.SECOND,
          },
          {
            label: '时间转化到毫秒',
            key: TIME_PICKER_FORMAT_TYPE.MILLISECOND,
            value: TIME_PICKER_FORMAT_TYPE.MILLISECOND,
          },
        ],
        onSelect: val => {
          context?.onConfirm?.('dictConfigFieldFormatType', val);
          setDictConfig(formRef, 'fieldFormatType', val, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
