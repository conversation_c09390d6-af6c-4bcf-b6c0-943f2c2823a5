import React, { useCallback, useEffect, useMemo, useState } from 'react';
import type { Moment } from 'moment';
import { TimePicker } from 'antd';
import moment from 'moment';

import { toDateFormat, toStartDate } from '@utils/tools';

import { TIME_PICKER_FORMAT_TYPE } from '@constants/common';

import './style.less';

const defaultFormat = 'HH:mm:ss';

// 表单类型
const CONTAINER_TYPE_MAP = {
  TABLE: 'table',
  FORM: 'form',
};

// ag-grid 模式类型
const WULI_MODE_TYPE_MAP = {
  VIEW: 'view',
  EDIT: 'edit',
};

const startOfTypeMap = {
  'HH:mm': 'second',
};

interface IAppProps {
  value: any;
  onChange: (val, valStr) => void;
  configs: {
    wuliMode: 'view' | 'edit';
    containerType: 'form' | 'table';
    config: {
      baseConfig: {
        dictConfig: {
          timeType: string;
        };
      };
    };
  };
}
const BaseInfo: React.FC<IAppProps> = props => {
  const [open, setOpen] = useState<boolean>(false);
  const { value, configs, onChange } = props;
  const wuliMode = configs?.wuliMode;
  // 当前是form还是table表单
  const containerType = configs?.containerType;
  const dictConfig: any = configs?.config?.baseConfig?.dictConfig || {};
  const fieldFormatType = dictConfig.fieldFormatType;
  const timeType = dictConfig.timeType || defaultFormat;
  console.log('时间选择===', dictConfig, timeType, wuliMode, value, containerType, props);

  const handleChange = useCallback(
    (val: Moment, b) => {
      const timeClone = val.clone();
      const formatFn = {
        [TIME_PICKER_FORMAT_TYPE.MILLISECOND]: () => handleFormatMillisecond(timeClone),
        [TIME_PICKER_FORMAT_TYPE.SECOND]: () => handleFormatSecond(timeClone),
        [TIME_PICKER_FORMAT_TYPE.TIMESTAMP]: () => handleFormatTimestamp(timeClone),
        [TIME_PICKER_FORMAT_TYPE.MINUTE]: () => handleFormatMinute(timeClone),
      };

      const result = formatFn[fieldFormatType]();
      onChange(result, b);
    },
    [timeType, fieldFormatType]
  );

  useEffect(() => {
    const isOpen = containerType && containerType === CONTAINER_TYPE_MAP.TABLE && wuliMode === WULI_MODE_TYPE_MAP.EDIT;
    isOpen && setOpen(true);
  }, [wuliMode, containerType]);

  // 分钟
  const handleFormatMinute = useCallback((val: Moment) => {
    const hoursVal = val.hours();
    const minutesVal = val.minutes();
    const result = hoursVal * 60 + minutesVal;
    return result;
  }, []);

  // 格式化到秒
  const handleFormatSecond = useCallback((val: Moment) => {
    const secondsVal = val.seconds();
    const minuteVal = handleFormatMinute(val);
    const result = minuteVal * 60 + secondsVal;
    return result;
  }, []);

  // 格式化到毫秒
  const handleFormatMillisecond = useCallback((val: Moment) => {
    const seconds = handleFormatSecond(val);
    const result = seconds * 1000;
    return result;
  }, []);

  // 时间戳
  const handleFormatTimestamp = useCallback((val: Moment) => {
    let newVal = val.clone().valueOf();
    const type = startOfTypeMap[timeType];
    type && (newVal = toStartDate(val.clone(), type));
    return newVal;
  }, []);

  const initValues: Moment = useMemo(() => {
    // const isNumber = Object.prototype.toString.call(value) === '[object Number]';
    const isHasData = ![undefined, null, ''].includes(value);
    if (isHasData) {
      const numberVal = Number(value);
      if (fieldFormatType === TIME_PICKER_FORMAT_TYPE.TIMESTAMP) {
        return moment(numberVal);
      }
      const formatTimeType = {
        [TIME_PICKER_FORMAT_TYPE.SECOND]: 'seconds',
        [TIME_PICKER_FORMAT_TYPE.MINUTE]: 'minutes',
        [TIME_PICKER_FORMAT_TYPE.MILLISECOND]: 'milliseconds',
      };

      const result = moment().startOf('day').add(value, formatTimeType[fieldFormatType]);
      return result;
    }
    return value;
  }, [fieldFormatType, value]);

  return wuliMode === WULI_MODE_TYPE_MAP.VIEW ? (
    <p className="value-p">{toDateFormat(initValues, timeType)}</p>
  ) : (
    <TimePicker
      open={open}
      format={timeType}
      value={initValues}
      onChange={handleChange}
      className="customTimePicker"
      onOpenChange={val => setOpen(val)}
    />
  );
};

export default BaseInfo;
