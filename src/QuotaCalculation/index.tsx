import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button, Tabs, Dropdown, Space } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';

import tmgApis from '@apis/tmg';
import { request as fetchApi } from '@utils/http';
import { exportMultiExcel, IExport } from '@utils/excel';

import useDictCode from '@hooks/useDictCode';
import TaskSlot from '@components/TaskSlot';
import excelTask from '@components/ExportLargeExcel';
import { LCPDetailTemplate } from '@components/LcpTemplate';
import WULIWholeTable, { DEFAULT_PAGE_SIZE } from '@components/WholeTable/AgGrid';

import { DICT_CODE_MAP_ID } from '@constants/common';

import './style.less';

let timeout = null;

export interface IAppProps {
  processId: string;
}
const QuotaCalculation: React.FC<IAppProps> = props => {
  const { processId } = props;

  const taskRef = useRef(null);
  const templateRef = useRef(null);
  const filterParamsRef = useRef({});
  const [selectRows, setSelectRows] = useState<any[]>([]);
  const [replayLoading, setReplayLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any>({
    list: [],
    pagination: {
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  const { list: tableList = [], pagination = {} } = tableData;

  const { DHR_TMG_CALCULATE_CAL_STATUS = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_TMG_CALCULATE_CAL_STATUS]);

  const statusOptions = useMemo(
    () =>
      (DHR_TMG_CALCULATE_CAL_STATUS || []).map(codeItem => ({
        label: codeItem.name,
        value: codeItem.itemValue,
        ...codeItem,
      })),
    [DHR_TMG_CALCULATE_CAL_STATUS]
  );

  const formTemplateConfig = useMemo(
    () => ({
      processId,
      pageId: processId,
      pageFlag: processId,
      appId: '05f5f32003cc41f896b334e526e59de9',
      classId: '4a3de4d1f40449adb28b208c88ccf840',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        contextData: {
          viewType: 'read',
        },
      },
    }),
    [processId]
  );

  const columns = useMemo(
    () => [
      {
        key: 'statusName',
        title: '核算状态',
        dataIndex: 'statusName',
        filterKey: 'status',
        filterType: 'select',
        headerComponentParams: {
          options: statusOptions,
        },
      },
      {
        key: 'empName',
        title: '姓名',
        dataIndex: 'empName',
        filterType: 'input',
      },
      {
        key: 'empAccount',
        title: '域账号',
        dataIndex: 'empAccount',
      },
      {
        title: '异常原因',
        dataIndex: ' errorMsg',
        key: 'errorMsg',
        render: record => {
          const errorMsg = record?.data?.errorMsg;
          return errorMsg || '-';
        },
      },
    ],
    [statusOptions]
  );

  const onFetchQuotaProcess = useCallback(params => {
    fetchApi({
      ...tmgApis.quotaProcessEmpList,
      params,
      onSuccess: res => {
        setTableData(res || {});
      },
    });
  }, []);

  const handleSearch = useCallback(
    (params = {}) => {
      const filterParams = filterParamsRef.current || {};
      onFetchQuotaProcess({
        processId,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...filterParams,
        ...params,
      });
    },
    [processId]
  );

  const handleExport = useCallback(() => {
    if (selectRows.length > 0) {
      const exportArr: IExport[] = [
        {
          columns,
          data: selectRows,
          sheetName: 'Sheet1',
        },
      ];
      return exportMultiExcel(exportArr, '人员核算信息列表.xlsx');
    }
    const filterParams = filterParamsRef.current || {};
    const params = {
      fetchParams: {
        ...tmgApis.quotaProcessEmpList,
        params: {
          processId,
          ...filterParams,
        },
      },
      xlsxName: '人员核算信息列表',
      configs: {
        columns,
        maxPageSize: 500,
      },
    };
    excelTask.add(params);
  }, [selectRows, processId]);

  const actionBtnItems = useMemo(
    () => [
      {
        key: 'export',
        content: '导出',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleExport,
          disabled: tableList.length === 0,
        },
      },
    ],
    [selectRows, processId]
  );

  const paginationConfig = useMemo(
    () => ({
      showSizeChanger: true,
      showQuickJumper: true,
      total: pagination.total,
      current: pagination.pageNum || 1,
      pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
      onChange: (pageNum, pageSize) => handleSearch({ pageNum, pageSize }),
    }),
    [pagination, processId]
  );

  const handleFilterSearch = values => {
    setSelectRows([]);
    filterParamsRef.current = values;
    handleSearch(values);
  };

  const tabItems = useMemo(
    () => [
      {
        label: '人员核算信息',
        key: '1',
        children: (
          <WULIWholeTable
            canSelect
            pagination
            multiSelect
            rowKey="id"
            height={500}
            data={tableList}
            columns={columns}
            action={actionBtnItems}
            onHeaderChange={handleFilterSearch}
            paginationConfig={paginationConfig}
            onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => setSelectRows(_selectedRows)}
          />
        ),
      },
    ],
    [tableList, pagination, processId, columns, selectRows]
  );

  // 重新计算
  const handleReplay = useCallback(
    (params = {}) => {
      const empIds = [...selectRows].map(({ empId }) => empId);
      setReplayLoading(true);
      fetchApi({
        ...tmgApis.quotaProcessAgain,
        data: {
          processId,
          empIds: empIds.length > 0 ? empIds : undefined,
          ...params,
        },
        onSuccess: res => {
          taskRef.current?.onReplayTask?.();
        },
      }).finally(() => setReplayLoading(false));
    },
    [processId, selectRows]
  );

  // 刷新资源组件的数据
  const onRereshTemplateData = useCallback(() => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    timeout = setTimeout(() => {
      templateRef.current?.GlobalContext?.pageTools?.refresh();
      clearTimeout(timeout);
      timeout = null;
    }, 500);
  }, []);

  const handleTaskEnd = useCallback(() => {
    handleSearch();
    onRereshTemplateData();
  }, [processId]);

  const items = useMemo(
    () => [
      {
        label: '重新执行全部',
        key: 'all',
        disabled: replayLoading,
      },
      {
        label: '重新执行已选',
        key: 'selected',
        disabled: selectRows.length === 0 || replayLoading,
      },
      {
        label: '重新执行失败数据',
        key: 'error',
        disabled: replayLoading,
      },
    ],
    [processId, selectRows, replayLoading]
  );

  const handleMenuClick: MenuProps['onClick'] = e => {
    const key = e.key;
    const fnMap = {
      all: () => handleReplay(),
      selected: () => handleReplay(),
      error: () => handleReplay({ status: 'FAIL' }),
    };
    fnMap[key]?.();
  };

  const menuProps = useMemo(
    () => ({
      items,
      onClick: handleMenuClick,
    }),
    [processId, selectRows, replayLoading]
  );

  return (
    <TaskSlot processId={processId} onOk={handleTaskEnd} ref={taskRef}>
      <div className="dhr-quota-calculation-container">
        <div className="dhr-quota-calculation-btn-action">
          <Dropdown menu={menuProps}>
            <Button type="primary" loading={replayLoading}>
              <Space>
                重新执行
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </div>
        <LCPDetailTemplate {...formTemplateConfig} ref={templateRef} />
        <Tabs items={tabItems} />
      </div>
    </TaskSlot>
  );
};

export default QuotaCalculation;
