
.dhr-image-preview-container {
  /* A4 paper aspect ratio is 1:1.414 (width:height) */
  @a4-aspect-ratio: 1.414;
  @preview-width: 100%;
  @preview-height: 842px;
  @nav-button-size: 40px;
  @nav-button-icon-size: 18px;
  @pagination-font-size: 14px;

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: @preview-width;
  height: @preview-height;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  .dhr-image-preview-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    // height: 100%;
    // padding: 20px;
  }
  
  .dhr-image-preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    /* Maintain A4 aspect ratio */
    max-height: calc(~"min(100%, @{preview-height} - 40px)");
    max-width: calc(~"min(100%, (@{preview-height} - 40px) / @{a4-aspect-ratio})");
  }
  
  .dhr-image-preview-nav {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: @nav-button-size;
    height: @nav-button-size;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s;
  
    &:hover {
      background-color: rgba(0, 0, 0, 0.5);
    }
  
    &.disabled {
      background-color: rgba(0, 0, 0, 0.1);
      color: rgba(255, 255, 255, 0.5);
      cursor: not-allowed;
    }
  
    .nav-icon {
      font-size: @nav-button-icon-size;
    }
  }
  
  .dhr-image-preview-prev {
    left: 20px;
  }
  
  .dhr-image-preview-next {
    right: 20px;
  }
  
  .dhr-image-preview-pagination {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: @pagination-font-size;
    font-weight: 500;
  }
  
  .dhr-image-preview-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    width: @preview-width;
    height: @preview-height;
    background-color: #f5f5f5;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .preview-icon {
    font-size: 32px;
    color: #1890ff;
  }
}


