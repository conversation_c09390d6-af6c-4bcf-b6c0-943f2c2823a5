import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Empty, Spin } from 'antd';

// import { mockImages } from './mockData';

import './style.less';

export interface ImageItem {
  page: number;
  preview_url: string;
}

export interface IAppProps {
  images?: ImageItem[];
}

const DHRImagePreview: React.FC<IAppProps> = props => {
  const { images } = props;
  console.log('images==========', images, props);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);

  // Reset to first image when images array changes
  useEffect(() => {
    if (images && images.length > 0) {
      setCurrentIndex(0);
      setLoading(true);
    }
  }, [images]);

  // 当前显示的图片
  const currentImage = useMemo(() => {
    return images[currentIndex] || null;
  }, [images, currentIndex]);

  // 处理图片加载
  const handleImageLoad = useCallback(() => {
    setLoading(false);
  }, []);

  // 处理图片加载错误
  const handleImageError = useCallback(() => {
    setLoading(false);
  }, []);

  // 上一张图片
  const handlePrev = useCallback(() => {
    if (currentIndex > 0) {
      setLoading(true);
      setCurrentIndex(currentIndex - 1);
    }
  }, [currentIndex]);

  // 下一张图片
  const handleNext = useCallback(() => {
    if (currentIndex < images.length - 1) {
      setLoading(true);
      setCurrentIndex(currentIndex + 1);
    }
  }, [currentIndex, images.length]);

  // 如果没有图片，显示空状态
  if (!images || images.length === 0) {
    return (
      <div className="dhr-image-preview-empty">
        <Empty description="暂无图片" />
      </div>
    );
  }

  const isLastImage = currentIndex === images.length - 1;
  const isFirstImage = currentIndex === 0;

  return (
    <div className="dhr-image-preview-container">
      {/* 左侧导航按钮 */}
      <div
        className={`dhr-image-preview-nav dhr-image-preview-prev ${isFirstImage ? 'disabled' : ''}`}
        onClick={handlePrev}
      >
        <LeftOutlined className="nav-icon" />
      </div>

      {/* 图片显示区域 */}
      <div className="dhr-image-preview-content">
        <Spin spinning={loading} size="large">
          {currentImage && (
            <img
              className="dhr-image-preview-img"
              src={currentImage.preview_url}
              alt={`图片 ${currentImage.page}`}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          )}
        </Spin>

        {/* 页码显示 */}
        {images.length > 0 && (
          <div className="dhr-image-preview-pagination">
            {currentIndex + 1} / {images.length}
          </div>
        )}
      </div>

      {/* 右侧导航按钮 */}
      <div
        className={`dhr-image-preview-nav dhr-image-preview-next ${isLastImage ? 'disabled' : ''}`}
        onClick={handleNext}
      >
        <RightOutlined className="nav-icon" />
      </div>
    </div>
  );
};

export default DHRImagePreview;
