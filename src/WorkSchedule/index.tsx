import { Form, Input, Row, Col, Select, DatePicker, <PERSON>, Button, Tag } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import queryString from 'query-string';
import type { Moment } from 'moment';
import moment from 'moment';
import dayjs from 'dayjs';

import tmgApis from '@apis/tmg';
import commomApis from '@apis/common';
import { showErrNotification, transformFormTZValues } from '@utils/tools';
import useDebounce from '@hooks/useDebounce';
import { request as fetchApi } from '@utils/http';

import Block from '@components/Block';
import FreeCard from '@components/FreeCard';
import useDictCode from '@hooks/useDictCode';
import CalendarConfig from './containers/CalendarConfig';
import { LCPDetailTemplate } from '@components/LcpTemplate';

import { DICT_CODE_MAP_ID, DATE_ATTR_TYPE, DATE_ATTR_TYPE_MAP_COLORS } from '@constants/common';

import './style.less';

const formLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};
const { Item: FormItem } = Form;

export interface IAppProps {
  scheduleId: string;
  systemHelper: any;
  viewType: string;
}
// 基础表单配置
const baseFormConfig = {
  appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
  classId: '8a3ab1599fbd43c1ac8450bef1cff934',
  tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
};
const WorkSchedule: React.FC<IAppProps> = props => {
  const { scheduleId, viewType, systemHelper } = props;
  console.log('全局props', props);
  const routerQueryParams = queryString.parse(window.location.search);
  const calendarRef = useRef(null);
  const [initValues, setInitValues] = useState<any>({});
  const [saveLoading, setSaveLoading] = useState<boolean>(false);
  const [pageLoading, setPageLoading] = useState<boolean>(false);
  const [formLoad, setFormLoad] = React.useState<boolean>(false);
  const templateRef = useRef(null);
  const isEdit = !!scheduleId;
  const context = templateRef.current?.RenderRef?.current?.getContext() ?? {};
  const { form } = context;

  // const validateRes = await context.form.validateFields();

  // const statusOptions = useMemo(
  //   () =>
  //     DHR_COMMON_STATUS_TYPE.map(codeItem => ({
  //       ...codeItem,
  //       label: codeItem.name,
  //       key: codeItem.itemValue,
  //       value: codeItem.itemValue,
  //     })),
  //   [DHR_COMMON_STATUS_TYPE]
  // );

  // const rulesMap = useMemo(
  //   () => ({
  //     textRequired: [{ required: true, message: '请填写' }],
  //     selectRequired: [{ required: true, message: '请填写' }],
  //   }),
  //   []
  // );

  // useEffect(() => {
  //   onSearchShiftRuleList();
  //   onSearchCalendarRuleList();
  // }, []);

  // 获取数据
  // const onFetchScheduleDetail = id => {
  //   setPageLoading(true);
  //   const params = {
  //     id,
  //     onlyMain: true,
  //     keyType: 'CAMEL',
  //     appId: baseFormConfig.appId,
  //     formClassId: baseFormConfig.classId,
  //   };
  //   fetchApi({
  //     ...commomApis.formDetailInfo,
  //     params,
  //     headers: {
  //       'x-app-id': params.appId,
  //     },
  //     onSuccess: res => {
  //       const mainData = (res || {}).mainData || {};
  //       const newInitValues = {
  //         name: mainData.cName,
  //         desc: mainData.cDesc,
  //         status: mainData.cStatus,
  //         shiftRuleId: mainData.cShiftRuleId,
  //         calendarRuleId: mainData.cCalendarRuleId,
  //         endDate: mainData.cEndDate ? moment(mainData.cEndDate) : mainData.cEndDate,
  //         baseDate: mainData.cBaseDate ? moment(mainData.cBaseDate) : mainData.cBaseDate,
  //         beginDate: mainData.cBeginDate ? moment(mainData.cBeginDate) : mainData.cBeginDate,
  //       };
  //       debugger;
  //       context.setFormData({
  //         ...newInitValues,
  //       });
  //       setInitValues(newInitValues);
  //       const selectMonth = calendarRef.current?.onGetCurrentMonth();
  //       calendarRef.current?.onGetCalendarData({
  //         previewDate: selectMonth,
  //         scheduleId: id,
  //       });
  //     },
  //   }).finally(() => setPageLoading(false));
  // };

  const initScheduleTable = (scheduleId: string) => {
    const selectMonth = calendarRef.current?.onGetCurrentMonth();
    calendarRef.current?.onGetCalendarData({
      previewDate: selectMonth,
      scheduleId,
    });
  };

  // 等待表单加载完毕后再
  useEffect(() => {
    scheduleId && form && formLoad && initScheduleTable(scheduleId);
  }, [scheduleId, form, formLoad]);

  // 获取工作日历规则列表
  // const onSearchCalendarRuleList = useDebounce((value = '') => {
  //   setCalendarRuleLoading(true);
  //   const params = {
  //     appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
  //     formClassId: 'b81b51fe59ea4f988fd02ea6df7ef588',
  //     onlyMain: true,
  //     keyType: 'CAMEL',
  //   };
  //   const mainParamsGroups = [
  //     {
  //       paramsList: [
  //         {
  //           attrApi: 'C_NAME',
  //           operator: 'like',
  //           value,
  //         },
  //       ],
  //     },
  //   ];
  //   value && (params['mainParamsGroups'] = mainParamsGroups);
  //   fetchApi({
  //     ...commomApis.formTableList,
  //     data: {
  //       ...params,
  //     },
  //     headers: {
  //       'x-app-id': params.appId,
  //     },
  //     onSuccess: res => {
  //       const list = res?.content || [];
  //       const options = list.map(({ mainData }) => ({
  //         ...mainData,
  //         label: mainData?.cName,
  //         value: mainData?.id,
  //         key: mainData?.id,
  //       }));
  //       setCalendarRuleList(options);
  //     },
  //     onError: () => {
  //       setCalendarRuleList([]);
  //     },
  //   }).finally(() => setCalendarRuleLoading(false));
  // }, 300);

  // 获取轮班规则列表
  // const onSearchShiftRuleList = useDebounce((value = '') => {
  //   setShiftRuleLoading(true);
  //   const params = {
  //     onlyMain: true,
  //     keyType: 'CAMEL',
  //     appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
  //     formClassId: '2a8728d34b6b4861b17c96435ebfbe59',
  //   };
  //   const mainParamsGroups = [
  //     {
  //       paramsList: [
  //         {
  //           attrApi: 'C_NAME',
  //           operator: 'like',
  //           value,
  //         },
  //       ],
  //     },
  //   ];
  //   value && (params['mainParamsGroups'] = mainParamsGroups);
  //   fetchApi({
  //     ...commomApis.formTableList,
  //     data: {
  //       ...params,
  //     },
  //     headers: {
  //       'x-app-id': params.appId,
  //     },
  //     onSuccess: res => {
  //       const list = res?.content || [];
  //       const options = list.map(({ mainData }) => ({
  //         ...mainData,
  //         label: mainData?.cName,
  //         value: mainData?.id,
  //         key: mainData?.id,
  //       }));
  //       setShiftRuleList(options);
  //     },
  //     onError: () => {
  //       setShiftRuleList([]);
  //     },
  //   }).finally(() => setShiftRuleLoading(false));
  // }, 300);

  // 禁用基准日期
  // const handleDisabledBaseDate = useCallback(
  //   (startValue: Moment) => {
  //     if (!startValue || !beginDateVal) {
  //       return false;
  //     }
  //     return startValue.isAfter(beginDateVal);
  //   },
  //   [beginDateVal]
  // );

  // // 禁用生成开始日期
  // const handleDisabledBeginDate = useCallback(
  //   (startValue: Moment) => {
  //     let isStartDisabled = false;
  //     let isEndDisabled = false;
  //     if (endDateVal && startValue) {
  //       isEndDisabled = startValue.isAfter(endDateVal, 'd');
  //     }
  //     if (baseDateVal && startValue) {
  //       isStartDisabled = startValue.isBefore(baseDateVal, 'd');
  //     }
  //     return isStartDisabled || isEndDisabled;
  //   },
  //   [baseDateVal, endDateVal]
  // );

  // // 禁用结束时间
  // const handleDisabledEndDate = useCallback(
  //   (startValue: Moment) => {
  //     if (!startValue || !beginDateVal) {
  //       return false;
  //     }
  //     return startValue.isBefore(beginDateVal, 'd');
  //   },
  //   [beginDateVal]
  // );

  const handleJumpToEditPage = (updateQuery = {}) => {
    systemHelper.history.push(
      `/portal/jg8gj4sq/cmPage?${queryString.stringify({
        ...routerQueryParams,
        ...updateQuery,
      })}`
    );
  };

  const handleSave = () => {
    debugger;
    form.validateFields().then(formRes => {
      if (!formRes.success) {
        return showErrNotification('请完善字段信息');
      }
      const formValues = formRes.value || {};
      const result = transformFormTZValues(formValues);
      // const result = {
      //   name: formValues['C_NAME'],
      //   desc: formValues['C_DESC'],
      //   type: formValues['C_TYPE'],
      //   status: formValues['C_STATUS'],
      //   shiftRuleId: formValues['C_SHIFT_RULE_ID'],
      //   calendarRuleId: formValues['C_CALENDAR_RULE_ID'],
      //   endDate: formValues['C_END_DATE'],
      //   baseDate: formValues['C_BASE_DATE'],
      //   beginDate: formValues['C_BEGIN_DATE'],
      // };
      // ['beginDate', 'baseDate', 'endDate'].map(key => {
      //   result[key] = toStartDate(formValues[key]);
      // });
      setSaveLoading(true);
      fetchApi({
        ...tmgApis.scheduleDetailSave,
        data: {
          ...result,
          id: scheduleId,
        },
        onSuccess: newScheduleId => {
          if (!isEdit) {
            return handleJumpToEditPage({
              pageId: newScheduleId,
              scheduleId: newScheduleId,
              pageFlag: newScheduleId,
              pageName: `工作日程表-${result.name}`,
            });
          }
          // onFetchScheduleDetail(newScheduleId);
        },
      }).finally(() => setSaveLoading(false));
    });
  };

  const calendarStatusList = useMemo(
    () => [
      {
        name: '工作日',
        key: DATE_ATTR_TYPE.WORK_DAY,
        color: DATE_ATTR_TYPE_MAP_COLORS[DATE_ATTR_TYPE.WORK_DAY],
      },
      {
        name: '休息日',
        key: DATE_ATTR_TYPE.REST_DAY,
        color: DATE_ATTR_TYPE_MAP_COLORS[DATE_ATTR_TYPE.REST_DAY],
      },
      {
        name: '节假日',
        key: DATE_ATTR_TYPE.HOLIDAYS,
        color: DATE_ATTR_TYPE_MAP_COLORS[DATE_ATTR_TYPE.HOLIDAYS],
      },
    ],
    []
  );

  const formTemplateConfig = useMemo(
    () => ({
      ...baseFormConfig,
      metaConfig: {
        contextData: {
          viewType: viewType || 'WORK', // 默认工作日历
        },
      },
    }),
    [viewType]
  );

  return (
    <Spin spinning={pageLoading}>
      <div className="workScheduleContainer">
        <Button type="primary" loading={saveLoading} className="workScheduleSave" onClick={handleSave}>
          保存
        </Button>
        <LCPDetailTemplate
          {...formTemplateConfig}
          ref={templateRef}
          onCompleted={ref => {
            setFormLoad(true);
          }}
        />

        {/* <Form form={form} {...formLayout}>
          <FreeCard title="基本信息">
            <Row>
              <Col span={8}>
                <FormItem name="name" label="工作日程名称" rules={rulesMap.textRequired}>
                  <Input placeholder="请填写" />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name="status" label="生效状态" rules={rulesMap.selectRequired}>
                  <Select placeholder="请选择" options={statusOptions} />
                </FormItem>
              </Col>
              <Col span={16}>
                <FormItem name="desc" label="描述" labelCol={{ span: 3 }} wrapperCol={{ span: 20 }}>
                  <Input.TextArea placeholder="请填写" autoSize={{ minRows: 4, maxRows: 6 }} />
                </FormItem>
              </Col>
            </Row>
          </FreeCard>
          <FreeCard title="日程表信息">
            <Row>
              <Col span={8}>
                <FormItem name="calendarRuleId" label="工作日历规则" rules={rulesMap.selectRequired}>
                  <Select
                    showSearch
                    allowClear
                    placeholder="请搜索"
                    filterOption={false}
                    options={calendarRuleList}
                    onSearch={onSearchCalendarRuleList}
                    onChange={val => !val && onSearchCalendarRuleList()}
                    notFoundContent={calendarRuleLoading ? <Spin size="small" /> : null}
                  />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name="shiftRuleId" label="轮班规则" rules={rulesMap.selectRequired}>
                  <Select
                    showSearch
                    allowClear
                    placeholder="请搜索"
                    filterOption={false}
                    options={shiftRuleList}
                    onSearch={onSearchShiftRuleList}
                    onChange={val => !val && onSearchShiftRuleList()}
                    notFoundContent={shiftRuleLoading ? <Spin size="small" /> : null}
                  />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name="baseDate" label="基准日期" rules={rulesMap.selectRequired}>
                  <DatePicker placeholder="请选择" style={{ width: '100%' }} disabledDate={handleDisabledBaseDate} />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name="beginDate" label="生成开始日期" rules={rulesMap.selectRequired}>
                  <DatePicker placeholder="请选择" style={{ width: '100%' }} disabledDate={handleDisabledBeginDate} />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name="endDate" label="生成结束日期" rules={rulesMap.selectRequired}>
                  <DatePicker placeholder="请选择" style={{ width: '100%' }} disabledDate={handleDisabledEndDate} />
                </FormItem>
              </Col>
            </Row>
          </FreeCard>
        </Form> */}
        <Block title="工作日程表">
          <div className="dhr-work-calendar-status">
            {calendarStatusList.map(statusDetail => (
              <span key={statusDetail.key} className="dhr-calendar-status-item-container">
                <Tag color={statusDetail.color} />
                <span>{statusDetail.name}</span>
              </span>
            ))}
          </div>
          <CalendarConfig form={form} ref={calendarRef} scheduleId={scheduleId} isEdit={isEdit} />
        </Block>
      </div>
    </Spin>
  );
};

export default WorkSchedule;
