import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import React, { useCallback } from 'react';
import { DatePicker, Button } from 'antd';
import moment from 'moment';

import './style.less';

export interface IAppProps {
  value: any;
  isEdit: boolean;
  isPreview: boolean;
  onPreview: () => void;
  previewLoading: boolean;
  onChange: (val) => void;
  onCancelPreview: () => void;
}

const CustomCalendarHeader: React.FC<IAppProps> = props => {
  const { value, isPreview, previewLoading, onPreview, onChange, onCancelPreview } = props;

  // 上一个日期
  const handlePrev = useCallback(() => {
    const nexMonth = moment(value).subtract(1, 'months');
    onChange(nexMonth);
  }, [value]);

  // 下一个日期
  const handleNext = useCallback(() => {
    const nexMonth = moment(value).add(1, 'months');
    onChange(nexMonth);
  }, [value]);

  return (
    <div className="dhr-custom-calendar-header">
      <div className="calendarHeaderLeftAction">
        <LeftOutlined onClick={handlePrev} className="calendarPrev" />
        <DatePicker value={value} picker="month" onChange={onChange} className="calendarDatePicker" />
        <RightOutlined onClick={handleNext} className="calendarNext" />
      </div>
      <div className="calendarHeaderRightAction">
        {!isPreview ? (
          <Button type="link" loading={previewLoading} onClick={onPreview}>
            预览
          </Button>
        ) : (
          <Button type="link" onClick={onCancelPreview}>
            取消预览
          </Button>
        )}
      </div>
    </div>
  );
};

export default CustomCalendarHeader;
