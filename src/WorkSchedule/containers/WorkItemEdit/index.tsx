import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Modal, message } from 'antd';

import { request as fetchApi } from '@utils/http';

import { LCPPageTemplate } from '@components/LcpTemplate';

import './style.less';

export interface ModalProps {
  fetchParams: any;
  onOk?: () => void;
}

export interface IAppProps extends ModalProps {
  visible: boolean;
  onAfterClose: () => void;
}
const WorkItemEdit: React.FC<IAppProps> = ({ visible, fetchParams, onOk, onAfterClose }) => {
  const tableRef = useRef(null);
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setOpen(visible);
  }, [visible]);

  const listProps = useMemo(
    () => ({
      pageType: 'LCP_VIEW',
      apiName: 'TB_TMG_SHIFT',
      appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
      classId: 'bd201921a6704b1db531a62aa91e6d16',
      pageCode: 'bd201921a6704b1db531a62aa91e6d16',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
    }),
    []
  );

  const handleOk = async () => {
    const context = tableRef.current?.getRef()?.RenderRef?.current?.getContext?.();
    const selectedRows = context?.getCompRef?.({ code: 'VIEW_AREA' })?.selectedRows || [];
    if (selectedRows.length === 0) {
      return message.warning('请勾选数据');
    }

    if (selectedRows.length > 1) {
      return message.warning('请勾选一条数据哦～');
    }
    const shiftId = selectedRows.map(({ objId }) => objId).join(',');
    setLoading(true);
    fetchApi({
      ...fetchParams,
      data: {
        ...fetchParams.data,
        shiftId,
      },
      onSuccess: () => {
        message.success('操作成功～');
        setOpen(false);
        onOk?.();
      },
    }).finally(() => setLoading(false));
  };

  return (
    <Modal
      title="班次"
      width={950}
      open={open}
      onOk={handleOk}
      confirmLoading={loading}
      afterClose={onAfterClose}
      className="workItemEditModal"
      onCancel={() => setOpen(false)}
    >
      <div className="workItemEditContainer">
        <LCPPageTemplate {...listProps} ref={tableRef} />
      </div>
    </Modal>
  );
};

function withParams(config: ModalProps): IAppProps {
  return {
    visible: true,
    onAfterClose: () => {},
    ...config,
  };
}

function confirm(props: IAppProps) {
  const containers = document.createDocumentFragment();
  const handleAfterClose = () => {
    reactUnmount(containers);
  };
  reactRender(<WorkItemEdit {...props} onAfterClose={handleAfterClose} />, containers);
}

const WorkItemEditModal = WorkItemEdit as any;

WorkItemEditModal.edit = function (config: ModalProps) {
  return confirm(withParams(config));
};

export default WorkItemEditModal;
