import { TagOutlined, FormOutlined, EyeOutlined } from '@ant-design/icons';
import { Calendar, Tag, Tooltip, Spin, message, Modal } from 'antd';
import React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';
import { CalendarProps } from 'antd/es/calendar';
import { FormInstance } from 'antd/lib/form';
import type { Moment } from 'moment';
import dayjs from 'dayjs';

import tmgApis from '@apis/tmg';
import { showErrNotification, toStartDate, transformFormTZValues } from '@utils/tools';
import { request as fetchApi } from '@utils/http';

import WorkItemEdit from '../WorkItemEdit';
import CustomCalendarHeader from '../CustomCalendarHeader';

import { DATE_ATTR_TYPE, DATE_ATTR_TYPE_MAP_COLORS } from '@constants/common';

import './style.less';

const dayStart = dayjs().startOf('day');
export interface IAppProps extends CalendarProps<Moment> {
  ref?: any;
  isEdit: boolean;
  scheduleId: string;
  form: FormInstance;
}

const CalendarConfig: React.FC<IAppProps> = forwardRef((props, ref) => {
  const { form, isEdit, scheduleId, ...resetPros } = props;

  const previewStatusRef = useRef(false);
  const [previewData, setPreviewData] = useState<any>({});
  const [, setIsPreview] = useState<boolean>(false);
  const [previewLoading, setPreviewLoading] = useState<boolean>(false);
  const [selectedMonth, setSelectedMonth] = useState(toStartDate(dayjs()));

  useImperativeHandle(ref, () => ({
    onGetCalendarData: params => onFetchCalendarData(params),
    onGetCurrentMonth: () => selectedMonth,
  }));

  const handleDayTimeFormat = useCallback(
    val => {
      if (!val) {
        return '';
      }
      const result = dayStart.clone().add(val, 's').format('HH:mm');
      return result;
    },
    [dayStart]
  );

  // 小眼睛 - 显示
  const ToolTipTitle = useCallback(
    ({ title, list }) => {
      return (
        <div>
          <div>{title}</div>
          {list.map(listItem => (
            <div>
              <span>{listItem.name}</span>
              {/* <span style={{ marginLeft: '4px' }}>{handleDayTimeFormat(listItem.beginPoint)}</span>
              <span> - </span>
              <span>{handleDayTimeFormat(listItem.endPoint)}</span> */}
            </div>
          ))}
        </div>
      );
    },
    [dayStart]
  );

  const handleEdit = params => {
    if (!isEdit) {
      return message.warning('请先保存工作日程表才能修改哦~');
    }
    const isPreview = previewStatusRef.current;
    if (isEdit && isPreview) {
      return message.warning('请取消预览才能修改数据哦~');
    }
    WorkItemEdit.edit({
      fetchParams: {
        ...tmgApis.scheduleDetailUpdate,
        data: params,
      },
      onOk: () => onFetchCalendarData(),
    });
  };

  // 显示的时间数据
  const handleWorkStartTime = useCallback(list => {
    const newList = [...list];
    const detail = newList.shift() || {};
    const beginPoint = detail.beginPoint;
    const result = handleDayTimeFormat(beginPoint);
    return result;
  }, []);

  // 显示的时间数据
  const handleWorkEndTime = useCallback(list => {
    const newList = [...list];
    const detail = newList.pop() || {};
    const endPoint = detail.endPoint;
    const result = handleDayTimeFormat(endPoint);
    return result;
  }, []);

  // 取消手动更新标识
  const handleCancelMark = useCallback(
    record => {
      Modal.confirm({
        title: '提醒',
        content: '是否取消手动标识？取消后，班次按设置重新初始化后即被覆盖',
        onOk: () =>
          new Promise((resolve, reject) => {
            fetchApi({
              ...tmgApis.calendarDetailStatusUpdate,
              data: {
                scheduleDetailId: record.id,
              },
              onSuccess: () => {
                resolve(true);
                onFetchCalendarData();
              },
              onError: reject,
            });
          }),
      });
    },
    [selectedMonth, scheduleId]
  );

  const handleDateFullCellRender = useCallback(
    (value: Moment) => {
      const dateClone = value.clone();
      const isSameMonth = dateClone.isSame(selectedMonth, 'month');
      if (!isSameMonth) {
        return (
          <div className="ant-picker-cell-inner ant-picker-calendar-date">
            <div className="ant-picker-calendar-date-value dhr-default-calendar-item">
              <span>{selectedMonth ? '' : `${value.date()}日`}</span>
            </div>
          </div>
        );
      }

      const monthVal = toStartDate(dateClone, 'day');
      const dateInfo = previewData[monthVal] || {};
      const holidayName = (dateInfo.holiday || {}).holidayName || '';
      const dateAttr = dateInfo.dateAttr;
      const shiftView = dateInfo.shiftView || {};
      const dateAttrName = dateInfo.dateAttrName || '';
      const dateName = dateAttr === DATE_ATTR_TYPE.HOLIDAYS ? holidayName : dateAttrName;

      return (
        <div className="ant-picker-cell-inner ant-picker-calendar-date work-calendar-date">
          <div className="ant-picker-calendar-date-value work-calendar-date-header">
            <div className="work-calendar-date-header-content-left">
              <span>{`${value.date()}日`}</span>
              {dateAttr && (
                <Tag color={DATE_ATTR_TYPE_MAP_COLORS[dateAttr]} className="work-calendar-date-status">
                  {dateName}
                </Tag>
              )}
            </div>
            <div className="work-calendar-date-header-content-right">
              {dateInfo.pageEdit === '1' && (
                <Tooltip title="已手动更新标识" placement="bottom">
                  <TagOutlined
                    style={{ fontSize: '16px' }}
                    onClick={() => handleCancelMark(dateInfo)}
                    className="work-calendar-date-icon work-calendar-sign-icon"
                  />
                </Tooltip>
              )}
              <Tooltip
                title={<ToolTipTitle title={shiftView.name || '暂无数据'} list={shiftView.shiftPeriods || []} />}
              >
                <EyeOutlined className="work-calendar-date-icon" style={{ fontSize: '16px' }} />
              </Tooltip>
              {dateInfo.id && (
                <FormOutlined
                  className="work-calendar-date-icon"
                  style={{ fontSize: '16px', marginLeft: '2px' }}
                  onClick={() => handleEdit({ id: dateInfo.id, scheduleDetailId: dateInfo.id })}
                />
              )}
            </div>
          </div>
          <div className="ant-picker-calendar-date-content">
            {shiftView.id && (
              <div className="work-calendar-date-title">
                <span>{shiftView.name}</span>
                <span style={{ marginLeft: '4px' }}>
                  <span>{handleWorkStartTime(shiftView.shiftPeriods || [])}</span>
                  <span>-</span>
                  <span>{handleWorkEndTime(shiftView.shiftPeriods || [])}</span>
                </span>
              </div>
            )}
          </div>
        </div>
      );
    },
    [selectedMonth, previewData]
  );

  // 禁用非选择月的日期
  const handleDisabledDate = useCallback(
    (current: Moment) => {
      const currentTimeout = toStartDate(current.clone(), 'day');
      return !previewData[currentTimeout];
      // return selectedMonth ? !current.isSame(selectedMonth, 'month') : true;
    },
    [previewData]
  );

  // 获取实际的日期数据
  const onFetchCalendarData = useCallback(
    (
      params = {
        scheduleId,
        previewDate: selectedMonth,
      }
    ) => {
      setPreviewLoading(true);
      fetchApi({
        ...tmgApis.scheduleDetailInfo,
        params,
        onSuccess: (list: any[]) => {
          const previewData = (list || []).reduce(
            (pre, cur) => ({
              ...pre,
              [cur.date]: { ...cur },
            }),
            {}
          );
          setPreviewData(previewData);
        },
      }).finally(() => setPreviewLoading(false));
    },
    [selectedMonth, scheduleId]
  );

  // 获取预览数据
  const onFetchCalendarPreviewData = useCallback(
    (params = {}) => {
      const newParams = { ...params };
      setPreviewLoading(true);
      ['baseDate', 'beginDate', 'endDate', 'previewDate'].forEach(key => {
        newParams[key] = toStartDate(params[key]);
      });
      fetchApi({
        ...tmgApis.calendarDetailPreview,
        data: {
          ...newParams,
        },
        onSuccess: (list: any[]) => {
          const previewData = (list || []).reduce(
            (pre, cur) => ({
              ...pre,
              [cur.date]: { ...cur },
            }),
            {}
          );
          setPreviewData(previewData);
        },
      }).finally(() => {
        setPreviewLoading(false);
      });
    },
    [selectedMonth, scheduleId]
  );

  // 更新预览状态
  const onUpdatePreviewStatus = useCallback(newIsPreview => {
    setIsPreview(newIsPreview);
    previewStatusRef.current = newIsPreview;
  }, []);

  // 预览
  const handlePreview = async (otherParams = {}) => {
    if (!selectedMonth) {
      return message.warning('请先选择预览月份哦～');
    }
    const validateRes = await form.validateFields();
    if (!validateRes.success) {
      return showErrNotification('请完善当前表单字段');
    }

    const formValues = transformFormTZValues(validateRes.value || {});
    // console.log('selectedMonth===', selectedMonth, formValues);
    // 更新时 - 进入预览模式
    // 创建时 - 只有预览日期数据 - 没有实际日期数据
    isEdit && onUpdatePreviewStatus(true);
    const params = {
      scheduleId: 'create',
      ...formValues,
      previewDate: selectedMonth,
      ...otherParams,
    };
    onFetchCalendarPreviewData(params);
    // form
    //   .validateFields(['calendarRuleId', 'shiftRuleId', 'endDate', 'baseDate', 'beginDate'])
    //   .then(formValues => {
    //   .catch(err => {
    //     console.log('预览点击事件报错===', err);
    //     message.warning('请先完善日程表信息哦～');
    //   });
  };

  // 切换日期
  const handlePanelChange = useCallback(
    (val: Moment, mode) => {
      const isPreview = previewStatusRef.current;
      const newSelectedMonth = val ? toStartDate(val.clone(), 'M') : undefined;
      if (newSelectedMonth) {
        // 更新且非预览模式下 -> 获取实际数据
        isEdit && !isPreview
          ? onFetchCalendarData({
              scheduleId,
              previewDate: newSelectedMonth,
            })
          : handlePreview({ previewDate: newSelectedMonth });
      } else {
        setPreviewData({});
      }

      setSelectedMonth(newSelectedMonth);
    },
    [isEdit, scheduleId]
  );
  const handleCancelPreview = () => {
    onUpdatePreviewStatus(false);
    onFetchCalendarData();
  };

  return (
    <Spin spinning={previewLoading}>
      <div className="dhr-calendar-config-container">
        <Calendar
          className="dhr-work-calendar"
          onPanelChange={handlePanelChange}
          disabledDate={handleDisabledDate}
          dateFullCellRender={handleDateFullCellRender}
          headerRender={({ value, onChange }) => (
            <CustomCalendarHeader
              value={value}
              isEdit={isEdit}
              onChange={onChange}
              onPreview={() => handlePreview()}
              previewLoading={previewLoading}
              isPreview={previewStatusRef.current}
              onCancelPreview={handleCancelPreview}
            />
          )}
          {...resetPros}
        />
      </div>
    </Spin>
  );
});

export default CalendarConfig;
