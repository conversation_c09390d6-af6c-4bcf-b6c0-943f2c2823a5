.dhr-calendar-config-container {
  .dhr-work-calendar {
    & > .ant-picker-panel {
      .ant-picker-content {
        & > thead {
          & > tr {
            border-top: 1px solid #f0f0f0;
            border-left: 1px solid #f0f0f0;
            & > th {
              text-align: left;
              padding-top: 6px;
              padding-left: 10px;
              background-color: #f0f0f080;
              border-right: 1px solid #f0f0f0;
            }
          }
        }
        & > tbody {
          & > tr {
            border-left: 1px solid #f0f0f0;
            border-right: 1px solid #f0f0f0;
            & > .ant-picker-cell {
              &:not(:last-of-type) {
                border-right: 1px solid #f0f0f0;
              }
              & > .ant-picker-calendar-date {
                margin: 0;
                border-width: 1px;
              }
            }
          }
        }
      }
    }
  }

  .ant-picker-cell-today {
    .work-calendar-date {
      &::before {
        z-index: -1;
      }
    }
  }
  .dhr-default-calendar-item {
    height: 96px !important;
  }
  .work-calendar-date {
    .work-calendar-date-header {
      height: 26px;
      display: flex;
      padding-top: 2px;
      align-items: center;
      justify-content: space-between;
      .work-calendar-date-header-content-left {
        .work-calendar-date-status {
          margin-left: 4px;
        }
      }
      .work-calendar-date-header-content-right {
        position: relative;
        .work-calendar-sign-icon {
          position: absolute;
          bottom: -14px;
          right: 0;
          color: var(--antd-dynamic-primary-hover-color);
        }
      }
      .work-calendar-date-icon {
        &:hover {
          color: var(--antd-dynamic-primary-color);
        }
      }
    }

    .ant-picker-calendar-date-content {
      padding-top: 20px;
      font-size: 12px;
      line-height: 18px;
      height: 70px;
      .work-calendar-date-title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
