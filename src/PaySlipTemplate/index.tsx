import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Steps, Form, Modal } from 'antd';
import queryString from 'query-string';

import salarySlip from '@apis/salarySlip';
import useDictCode from '@hooks/useDictCode';
import { request as fetchApi } from '@utils/http';
import { showSucNotification, showWarnNotification } from '@utils/tools';

import BasicInfo from './containers/BasicInfo';
import FooterAction from './containers/FooterAction';
import PaySlipDisplay from './containers/PaySlipDisplay';
import PaySlipConfigModal from './containers/PaySlipConfigTable';
import AssociationAndNotification from './containers/AssociationAndNotification';

import { DICT_CODE_MAP_ID, PUBLISH_TYPE } from '@constants/common';

import './style.less';

const formLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

export interface IAppProps {
  templateId: string;
  systemHelper: any;
}

const PaySlipTemplate: React.FC<IAppProps> = (props: IAppProps) => {
  const routerQueryParams = queryString.parse(window.location.search);

  const [form] = Form.useForm();

  const baseComponentRef = useRef(null);
  const projectComponentRef = useRef(null);
  const associationComponentRef = useRef(null);

  const [current, setCurrent] = useState<number>(0);
  const [templateId, setTemplateId] = useState<string>('');
  const [loadMap, setLoadMap] = useState<Record<string, any>>({});
  const [completeLoading, setCompleteLoading] = useState<boolean>(false);
  const [tablePreviewLoading, setTablePreviewLoading] = useState<boolean>(false);

  const dictCodes = useDictCode([
    DICT_CODE_MAP_ID.DHR_COMMON_SWITCH,
    DICT_CODE_MAP_ID.DHR_COMMON_WHETHER,
    DICT_CODE_MAP_ID.DHR_CNB_CARRY_RULE,
    DICT_CODE_MAP_ID.DHR_CNB_NUM_PRECISION,
    DICT_CODE_MAP_ID.DHR_COMMON_SWITCH_CASE,
    DICT_CODE_MAP_ID.DHR_CNB_SALARY_SLIP_ANDOR,
    DICT_CODE_MAP_ID.DHR_CNB_SALARY_SLIP_ENTRANT_TIME_TYPE,
    DICT_CODE_MAP_ID.DHR_CNB_SALARY_SLIP_TABLE_RELATION_TYPE,
    DICT_CODE_MAP_ID.DHR_CNB_SALARY_SLIP_NOTIFY_CONDITION_TYPE,
  ]);

  const stepItems = useMemo(
    () => [
      {
        title: '基础信息设置',
        key: 'basic',
      },
      {
        title: '工资条项目显示设置',
        key: 'paySlipDisplay',
      },
      {
        title: '人员关联及通知设置',
        key: 'relation',
      },
    ],
    []
  );

  useEffect(() => {
    props.templateId && setTemplateId(props.templateId);
    if (templateId !== props.templateId && templateId) {
      handleUpdateRouterParams(templateId);
    }
  }, [props, templateId]);

  // 表预览
  const handlePreviewPaySlipTable = () => {
    setTablePreviewLoading(true);
    const tableAllList = projectComponentRef.current?.getAllTablePreviewList?.() || [];
    const tableIds = tableAllList.map(({ id }) => id);
    fetchApi({
      ...salarySlip.payrollTplAllPreview,
      data: {
        templateId,
        tableIds,
      },
      onSuccess: list => {
        const newList = (list || []).map(listItem => ({
          ...list,
          tableDisplayContent: (listItem.tableDisplayContent || '').replace(/\${[\w#]+}/g, '-'),
        }));
        PaySlipConfigModal.preview({
          tableAllList,
          previewList: newList,
        });
      },
    }).finally(() => setTablePreviewLoading(false));
  };

  // 更新loading映射状态
  const handleUpdateLoadStatus = (key, val) => {
    setLoadMap({
      ...loadMap,
      [key]: val,
    });
  };

  // 更新路由参数
  const handleUpdateRouterParams = templateId => {
    const query = queryString.stringify({
      ...routerQueryParams,
      templateId,
      // pageFlag: templateId,
    });
    props.systemHelper.history.replace({
      search: `?${query}`,
    });
  };

  // 第一步 - 保存 - 基本信息
  const handleBaseInfoSave = params => {
    handleUpdateLoadStatus('base', true);
    const baseInitData = baseComponentRef.current?.onGetBaseInfo?.() || {};

    const { publishMode } = params;
    const result = {
      ...baseInitData,
      ...params,
      publishMode: undefined,
      onEmail: (publishMode || []).includes(PUBLISH_TYPE.onEmail) ? '1' : '0',
      onMobileTerminal: (publishMode || []).includes(PUBLISH_TYPE.onMobileTerminal) ? '1' : '0',
      onQywx: (publishMode || []).includes(PUBLISH_TYPE.onQywx) ? '1' : '0',
    };

    fetchApi({
      ...salarySlip.salarySlipBaseSave,
      data: result,
      onSuccess: res => {
        if (!props.templateId) {
          const newTemplateId = (res || {}).id;
          setTemplateId(newTemplateId);
          handleUpdateRouterParams(newTemplateId);
        }

        setCurrent(current + 1);
      },
    }).finally(() => handleUpdateLoadStatus('base', false));
  };

  // 第二步 - 保存 - 工资条项目显示设置
  const handleProjectConfigSave = () => {
    const isAllSave = projectComponentRef.current?.isAllSave?.();
    // 未保存节点 提醒
    if (!isAllSave) {
      return Modal.confirm({
        title: '提醒',
        content: '存在未保存的项目，未保存的项目会丢失，请确认是否进行下一步？',
        onOk: () => {
          setCurrent(current + 1);
        },
      });
    }
    setCurrent(current + 1);
  };

  // 下一步
  const handleNext = () => {
    form.validateFields().then(formValues => {
      const saveMapFn = {
        0: handleBaseInfoSave,
        1: handleProjectConfigSave,
      };

      saveMapFn[current](formValues);
    });
  };

  // 上一步
  const handleBack = () => {
    setCurrent(current - 1);
  };

  // 完成
  const handleOk = () => {
    form.validateFields().then(formValues => {
      const { groups } = formValues;
      // const notifyConfigs = associationComponentRef.current?.onGetNotifyConfigs?.();
      const empNotifyConfigs = associationComponentRef.current?.onGetEmpNotifyConfigs?.();
      const newGroup = [];
      let isPass = true;
      (groups || []).forEach((groupItem, index) => {
        const detail = empNotifyConfigs[index] || {};
        const notifyConfig = detail.notifyConfigs;
        // 不存在 说明没配置通知模板
        if (!notifyConfig) {
          isPass = false;
          return showWarnNotification(`请先完成通知人员组${index + 1}通知的配置哦~`);
        }
        const conditions = (groupItem.conditions || []).map((conditionItem, index) => ({
          templateId,
          ...conditionItem,
          sort: index,
          relation: index === 0 ? 'and' : conditionItem.relation,
        }));

        const newGroupItem = {
          ...detail,
          expression: undefined,
          templateId,
          name: `通知人员组${index + 1}`,
          ...groupItem,
          notifyConfigs: notifyConfig,
          conditions,
        };
        newGroup.push(newGroupItem);
      });
      if (isPass) {
        setCompleteLoading(true);
        fetchApi({
          ...salarySlip.salarySlipConditionUpdate,
          data: {
            groups: newGroup,
            templateId,
          },
          onSuccess: () => {
            showSucNotification('操作成功');
            props.systemHelper.history.push('/portal/ddcl84ou/ddcl84ou_qnrfemq2_jmy7kl0v_wqsju33t/list');
          },
        }).finally(() => setCompleteLoading(false));
      }
    });
  };

  const nextLoading = useMemo(() => ['base'].some(loadName => loadMap[loadName]), [loadMap]);

  return (
    <div className="paySlipTemplateContainer">
      <div className="step-container">
        <Steps current={current} items={stepItems} />
      </div>
      <Form
        form={form}
        className="pay-slip-step-form-container"
        initialValues={{
          groups: [
            {
              conditions: [
                {
                  itemType: undefined,
                  itemValue: undefined,
                },
              ],
            },
          ],
        }}
        {...formLayout}
      >
        {current === 0 && (
          <BasicInfo {...props} templateId={templateId} form={form} dictCodes={dictCodes} ref={baseComponentRef} />
        )}
        {current === 1 && (
          <PaySlipDisplay
            {...props}
            form={form}
            dictCodes={dictCodes}
            templateId={templateId}
            ref={projectComponentRef}
          />
        )}
        {current === 2 && (
          <AssociationAndNotification
            {...props}
            form={form}
            dictCodes={dictCodes}
            templateId={templateId}
            ref={associationComponentRef}
          />
        )}
      </Form>
      <FooterAction
        onOk={handleOk}
        stepCount={current}
        onNext={handleNext}
        onBack={handleBack}
        nextLoading={nextLoading}
        completeLoading={completeLoading}
        previewLoading={tablePreviewLoading}
        onPreviewPaySlipTable={handlePreviewPaySlipTable}
      />
    </div>
  );
};

export default PaySlipTemplate;
