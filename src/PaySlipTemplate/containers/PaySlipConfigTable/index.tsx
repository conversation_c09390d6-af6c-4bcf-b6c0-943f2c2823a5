import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Modal, Table } from 'antd';

import './style.less';

interface PreviewItem {
  id: string;
  name: string;
  tableDisplayContent: string | null;
}

export interface ModalProps {
  tableAllList: any[];
  previewList: PreviewItem[];
}

export interface ExtraProps {
  onAfterClose: () => void;
}

export interface IAppProps extends ModalProps, ExtraProps {
  open: boolean;
}
const PaySlipConfigTable: React.FC<IAppProps> = ({ open, tableAllList, previewList, onAfterClose }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  return (
    <Modal
      centered
      open={visible}
      title="表预览"
      footer={null}
      width={1100}
      className="paySlipTablePreviewModal"
      afterClose={onAfterClose}
      onCancel={handleCancel}
    >
      <div className="paySlipTablePreviewContainer">
        {previewList.map(previewItem => {
          return (
            <div key={previewItem.id} className="previewTableItem">
              <p className="tableTitle">{previewItem.name}</p>
              <div
                className="previewTable"
                dangerouslySetInnerHTML={{ __html: previewItem.tableDisplayContent || '暂无表格数据' }}
              ></div>
            </div>
          );
        })}
      </div>
    </Modal>
  );
};

const withParams = (params: ModalProps) => {
  return {
    ...params,
    open: true,
  };
};

const previewModal = (params: any) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
  };
  reactRender(<PaySlipConfigTable {...params} onAfterClose={onAfterClose} />, container);
};

const PaySlipConfigModal = PaySlipConfigTable as any;

PaySlipConfigModal.preview = function previewFn(props: ModalProps) {
  return previewModal(withParams(props));
};

export default PaySlipConfigModal;
