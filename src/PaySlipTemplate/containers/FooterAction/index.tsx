import React, { useCallback } from 'react';
import { Button } from 'antd';

import './style.less';

export interface IAppProps {
  stepCount: number;
  onOk: () => void;
  onNext: () => void;
  onBack: () => void;
  nextLoading: boolean;
  previewLoading: boolean;
  completeLoading: boolean;
  onPreviewPaySlipTable: () => void;
}
const FooterAction: React.FC<IAppProps> = ({
  stepCount,
  nextLoading,
  completeLoading,
  previewLoading,
  onOk,
  onNext,
  onBack,
  onPreviewPaySlipTable,
}) => {
  const handleNext = useCallback(() => {
    onNext();
  }, [stepCount]);

  const handleBack = useCallback(() => {
    onBack();
  }, [stepCount]);

  return (
    <div className="paySlipFooterActionBtn">
      {stepCount === 1 && (
        <>
          {/* <Button>PC端预览</Button>
            <Button className="intervalBtn">移动端预览</Button> */}
          <Button onClick={onPreviewPaySlipTable} loading={previewLoading}>
            表预览
          </Button>
        </>
      )}
      {/* <Button>暂存</Button> */}
      <Button className="intervalBtn" disabled={stepCount === 0} onClick={handleBack}>
        上一步
      </Button>
      {stepCount < 2 ? (
        <Button type="primary" onClick={handleNext} loading={nextLoading}>
          下一步
        </Button>
      ) : (
        <Button type="primary" loading={completeLoading} onClick={onOk}>
          完成
        </Button>
      )}
    </div>
  );
};

export default FooterAction;
