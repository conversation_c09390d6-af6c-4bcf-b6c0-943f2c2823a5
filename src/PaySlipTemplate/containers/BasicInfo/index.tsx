import { Form, Row, Col, Input, Checkbox, Radio, Spin, Select } from 'antd';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { FormInstance } from 'antd/lib/form/Form';

import salarySlipApi from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';

import { PUBLISH_TYPE } from '@constants/common';

import './style.less';

const { Item: FormItem } = Form;
export interface IAppProps {
  ref?: any;
  templateId: string;
  dictCodes: any;
  form: FormInstance;
}
const BasicInfo: React.FC<IAppProps> = forwardRef(({ templateId, form, dictCodes }, ref) => {
  const baseInfoRef = useRef(null);
  const [payrollLoading, setPayrollLoading] = useState<boolean>(false);
  const [baseInfoLoading, setBaseInfoLoading] = useState<boolean>(false);
  const [salarySetList, setSalarySetList] = useState<Record<string, any>[]>([]);

  const { DHR_COMMON_SWITCH = [], DHR_COMMON_SWITCH_CASE = [], DHR_COMMON_WHETHER = [] } = dictCodes;
  useImperativeHandle(ref, () => ({
    onGetBaseInfo() {
      return baseInfoRef.current;
    },
  }));

  const rules = useMemo(
    () => ({
      name: [
        {
          required: true,
          message: '请输入',
        },
      ],
      relation: [
        {
          required: true,
          message: '请输入',
        },
      ],
      publishMode: [
        {
          required: true,
          message: '请选择',
        },
      ],
      summarySearch: [
        {
          required: true,
          message: '请选择',
        },
      ],
      pwdCheck: [
        {
          required: true,
          message: '请选择',
        },
      ],
      ifZeroSendSlip: [
        {
          required: true,
          message: '请选择',
        },
      ],
    }),
    []
  );

  const publishOptions = useMemo(
    () => [
      { label: '邮件', value: PUBLISH_TYPE.onEmail },
      { label: '自助端', value: PUBLISH_TYPE.onMobileTerminal },
      { label: '企微', value: PUBLISH_TYPE.onQywx },
    ],
    []
  );

  const startOptions = useMemo(
    () =>
      (DHR_COMMON_SWITCH_CASE || []).map(({ name, itemValue }) => ({
        label: name,
        value: itemValue,
      })),
    [DHR_COMMON_SWITCH_CASE]
  );

  const statusOptions = useMemo(
    () =>
      (DHR_COMMON_SWITCH || []).map(({ name, itemValue }) => ({
        label: name,
        value: itemValue,
      })),
    [DHR_COMMON_SWITCH]
  );

  const ifZeroSendSlipOptions = useMemo(
    () =>
      (DHR_COMMON_WHETHER || []).map(({ name, itemValue }) => ({
        label: name,
        value: itemValue,
      })),
    [DHR_COMMON_WHETHER]
  );

  const onFetchBaseInfo = useCallback(() => {
    setBaseInfoLoading(true);
    fetchApi({
      ...salarySlipApi.salarySlipBaseInfo,
      params: {
        id: templateId,
      },
      onSuccess: res => {
        baseInfoRef.current = res || {};
        const { name, remark, salarySet, summarySearch, onQywx, pwdCheck, onMobileTerminal, onEmail, status, ifZeroSendSlip } = res;
        const publishMode = [];
        // 邮件
        onEmail === '1' && publishMode.push(PUBLISH_TYPE.onEmail);
        // 自助端
        onMobileTerminal === '1' && publishMode.push(PUBLISH_TYPE.onMobileTerminal);
        // 企微
        onQywx === '1' && publishMode.push(PUBLISH_TYPE.onQywx);
        form.setFieldsValue({
          name,
          status,
          remark,
          pwdCheck,
          salarySet,
          publishMode,
          summarySearch,
          ifZeroSendSlip,
        });
      },
    }).finally(() => setBaseInfoLoading(false));
  }, [templateId]);

  /** 查找关联工作套 */
  const onFetchPayrollList = useCallback(() => {
    setPayrollLoading(true);
    fetchApi({
      ...salarySlipApi.payrollList,
      onSuccess: list => {
        const newSalarySetList = (list || []).map(listItem => ({
          ...listItem,
          label: listItem.name,
          value: listItem.hid,
        }));
        setSalarySetList(newSalarySetList);
      },
    }).finally(() => setPayrollLoading(false));
  }, []);

  useEffect(() => {
    onFetchPayrollList();
  }, []);

  useEffect(() => {
    templateId && onFetchBaseInfo();
  }, [templateId]);

  const loading = useMemo(
    () => [payrollLoading, baseInfoLoading].some(loadVal => loadVal),
    [payrollLoading, baseInfoLoading]
  );
  return (
    <Spin spinning={loading}>
      <div className="basicInfo">
        <section>
          <p className="pay-slip-title">基本信息</p>
          <Row>
            <Col span={10}>
              <FormItem name="name" label="名称" rules={rules.name}>
                <Input placeholder="请填写" />
              </FormItem>
            </Col>
            <Col span={10}>
              <FormItem name="salarySet" label="关联工资套" rules={rules.relation}>
                <Select
                  showSearch
                  options={salarySetList}
                  optionFilterProp="label"
                  placeholder="请输入工资套名称搜索"
                />
              </FormItem>
            </Col>
            <Col span={20}>
              <FormItem name="remark" label="描述" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                <Input.TextArea rows={4} placeholder="请填写" />
              </FormItem>
            </Col>
          </Row>
        </section>
        <section>
          <p className="pay-slip-title">发布方式</p>
          <Row>
            <Col span={8}>
              <FormItem
                name="publishMode"
                label="发布方式"
                rules={rules.publishMode}
                labelCol={{ span: 12 }}
                wrapperCol={{ span: 12 }}
              >
                <Checkbox.Group options={publishOptions} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem
                name="summarySearch"
                label="多期汇总查询"
                rules={rules.summarySearch}
                labelCol={{ span: 10 }}
                wrapperCol={{ span: 12 }}
              >
                <Radio.Group options={startOptions} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem name="pwdCheck" label="密码二次确认" rules={rules.pwdCheck}>
                <Radio.Group options={startOptions} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem
                name="status"
                label="工资条状态"
                rules={rules.pwdCheck}
                labelCol={{ span: 12 }}
                wrapperCol={{ span: 12 }}
              >
                <Radio.Group options={statusOptions} />
              </FormItem>
            </Col>
          </Row>
        </section>
        <section>
          <p className="pay-slip-title">配置</p>
          <Row>
            <Col span={14}>
              <FormItem name="ifZeroSendSlip" label="应发合计项目为0是否发放工资条" rules={rules.ifZeroSendSlip} labelCol={{ span: 12 }} wrapperCol={{ span: 12 }}>
                <Radio.Group options={ifZeroSendSlipOptions} />
              </FormItem>
            </Col>
          </Row>
        </section>
      </div>
    </Spin>
  );
});

export default BasicInfo;
