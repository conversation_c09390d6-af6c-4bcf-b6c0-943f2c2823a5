import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Select, Row, Col, Modal, Spin } from 'antd';
import { FormInstance } from 'antd/lib/form/Form';

import commomApis from '@apis/common';
import salarySlip from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';

import NotificationTemplateDrawer from './containers/NotificationTemplateDrawer';

import { NOTIFY_CONDITION_TYPE } from '@constants/common';

import './style.less';

const { Item: FormItem } = Form;

const baseGroups = [
  {
    conditions: [
      {
        relation: undefined,
        itemType: undefined,
        itemValue: undefined,
      },
    ],
  },
];

export interface IAppProps {
  ref?: any;
  templateId: string;
  form: FormInstance;
  dictCodes: Record<string, any>;
}

const AssociationAndNotification: React.FC<IAppProps> = forwardRef(({ form, dictCodes, templateId }, ref) => {
  const optionsRef = useRef({});
  const noticationCompRef = useRef(null);

  const [previewLoads, setPreviewLoads] = useState<boolean[]>([]);
  const [empNotifyConfigs, setEmpNotifyConfigs] = useState<any[]>([]);
  // 通知模版暂存
  const [payTypeLoading, setPayTypeLoading] = useState<boolean>(false);
  const [conditionLoading, setConditionLoading] = useState<boolean>(false);
  const [empStatusLoading, setEmpStatusLoading] = useState<boolean>(false);
  const [payrollGroupLoading, setPayrollGroupLoading] = useState<boolean>(false);
  const [payrollStructureLoading, setPayrollStructureLoading] = useState<boolean>(false);

  const {
    DHR_CNB_SALARY_SLIP_ANDOR = [],
    DHR_CNB_SALARY_SLIP_ENTRANT_TIME_TYPE = [],
    DHR_CNB_SALARY_SLIP_NOTIFY_CONDITION_TYPE = [],
  } = dictCodes;

  useImperativeHandle(ref, () => ({
    // 获取通知模板配置列表
    onGetEmpNotifyConfigs: () => empNotifyConfigs,
  }));

  // 邮件通知配置
  const handleNotificationConfig = (key: number) => {
    NotificationTemplateDrawer.confirm({
      templateId,
      title: `通知模版配置（通知人员组${key + 1}）`,
      initData: empNotifyConfigs[key]?.notifyConfigs || [],
      onOk: values => {
        const newEmpNoticationConfigs = [...empNotifyConfigs];
        newEmpNoticationConfigs[key] = {
          ...(newEmpNoticationConfigs[key] || {}),
          notifyConfigs: values,
        };
        setEmpNotifyConfigs(newEmpNoticationConfigs);
      },
    });
  };

  // 预览通知
  const handlePreviewNotication = useCallback(
    async index => {
      const noticationConfig = empNotifyConfigs[index]?.notifyConfigs;
      if (!noticationConfig) return;
      const newPreviewLoad = [...previewLoads];
      newPreviewLoad[index] = true;
      setPreviewLoads(newPreviewLoad);
      const previewData = [...(noticationConfig || [])].reduce(
        (pre, cur) => ({
          ...pre,
          [cur.notifyChannel]: { ...cur },
        }),
        []
      );
      noticationCompRef.current.onPreviewTpl({ ...previewData }).then(() => {
        newPreviewLoad[index] = false;
        setPreviewLoads(newPreviewLoad);
      });
    },
    [empNotifyConfigs]
  );

  // 条件关系 - 下拉
  const relationOptions = useMemo(
    () =>
      (DHR_CNB_SALARY_SLIP_ANDOR || []).map(codeItem => ({
        ...codeItem,
        label: codeItem.name,
        key: codeItem.itemValue,
        value: codeItem.itemValue,
      })),
    [DHR_CNB_SALARY_SLIP_ANDOR]
  );

  // 关联条件 - 下拉
  const itemTypeOptions = useMemo(
    () =>
      (DHR_CNB_SALARY_SLIP_NOTIFY_CONDITION_TYPE || []).map(codeItem => ({
        ...codeItem,
        label: codeItem.name,
        key: codeItem.itemValue,
        value: codeItem.itemValue,
      })),
    [DHR_CNB_SALARY_SLIP_NOTIFY_CONDITION_TYPE]
  );

  // 获取社保缴纳方式
  const onFetchPayType = useCallback(() => {
    setPayTypeLoading(true);
    const params = {
      appId: '9edbb14c3ebe4d6b99fc7d3ac288311c',
      formClassId: '93587afa17f44b35ab646c833bb681a7',
      onlyMain: true,
      keyType: 'CAMEL',
      pageSize: 500,
    };
    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.id,
          label: mainData?.cName,
          value: mainData?.id,
        }));
        optionsRef.current[NOTIFY_CONDITION_TYPE.SI_PAY_TYPE] = options;
      },
    }).finally(() => setPayTypeLoading(false));
  }, []);

  // 固定薪资下拉列表
  const onFetchPayrollStructure = useCallback(() => {
    setPayrollStructureLoading(true);
    fetchApi({
      ...salarySlip.payrollStructureList,
      onSuccess: list => {
        const options = (list || []).map(listItem => ({
          ...listItem,
          key: listItem.id,
          value: listItem.id,
          label: listItem.name,
        }));
        optionsRef.current[NOTIFY_CONDITION_TYPE.SALARY_STRUCTURE] = options;
      },
    }).finally(() => setPayrollStructureLoading(false));
  }, []);

  // 薪资组下拉列表
  const onFetchPayrollGroup = useCallback(() => {
    setPayrollGroupLoading(true);
    fetchApi({
      ...salarySlip.payrollGroupList,
      onSuccess: list => {
        const options = (list || []).map(listItem => ({
          ...listItem,
          key: listItem.id,
          value: listItem.id,
          label: listItem.name,
        }));
        optionsRef.current[NOTIFY_CONDITION_TYPE.SALARY_GROUP] = options;
      },
    }).finally(() => setPayrollGroupLoading(false));
  }, []);

  // 获取员工状态列表
  const onFetchPayrollEmpStatus = useCallback(() => {
    setEmpStatusLoading(true);
    fetchApi({
      ...salarySlip.payrollEmpStatusList,
      onSuccess: list => {
        const options = (list || []).map(listItem => ({
          ...listItem,
          key: listItem.id,
          value: listItem.id,
          label: listItem.name,
        }));
        optionsRef.current[NOTIFY_CONDITION_TYPE.EMP_STATUS] = options;
      },
    }).finally(() => setEmpStatusLoading(false));
  }, []);

  // 获取-回显数据
  const onFetchConditionList = useCallback(() => {
    setConditionLoading(true);
    fetchApi({
      ...salarySlip.salarySlipConditionList,
      params: {
        templateId,
      },
      onSuccess: list => {
        setEmpNotifyConfigs([...(list || [])]);
        const newGroups = (list || []).map(({ conditions }) => ({ conditions }));
        form.setFieldsValue({
          groups: newGroups.length > 0 ? newGroups : baseGroups,
        });
      },
    }).finally(() => setConditionLoading(false));
  }, [templateId]);

  // 获取入职时间类型下拉
  useEffect(() => {
    if (DHR_CNB_SALARY_SLIP_ENTRANT_TIME_TYPE) {
      const options = (DHR_CNB_SALARY_SLIP_ENTRANT_TIME_TYPE || []).map(optionItem => ({
        ...optionItem,
        label: optionItem.name,
        key: optionItem.itemValue,
        value: optionItem.itemValue,
      }));
      optionsRef.current[NOTIFY_CONDITION_TYPE.ENTRANT_TIME] = options;
    }
  }, [DHR_CNB_SALARY_SLIP_ENTRANT_TIME_TYPE]);

  useEffect(() => {
    // 获取initData
    templateId && onFetchConditionList();
  }, [templateId]);

  useEffect(() => {
    // 薪资组下拉列表
    onFetchPayrollGroup();
    // 固定薪资下拉列表
    onFetchPayrollStructure();
    // 获取员工状态列表
    onFetchPayrollEmpStatus();
    // 获取缴纳方式列表
    onFetchPayType();
  }, []);

  const handleRemoveItem = (delIndex, len, delFn) => {
    if (len > 1) {
      const notifyConfig = empNotifyConfigs[delIndex]?.notifyConfigs;

      if (notifyConfig) {
        return Modal.confirm({
          title: '提醒',
          content: `通知人员组${delIndex + 1}存在已配置通知模板，请确认是否删除该通知人员组？`,
          onOk: () => {
            const newEmpNoticationConfigs = [...empNotifyConfigs];
            newEmpNoticationConfigs.splice(delIndex, 1);
            delFn(delIndex);
            setEmpNotifyConfigs(newEmpNoticationConfigs);
          },
        });
      }
      delFn(delIndex);
    }
  };

  const pageLoading = [
    payTypeLoading,
    conditionLoading,
    empStatusLoading,
    payrollGroupLoading,
    payrollStructureLoading,
  ].some(loading => loading);
  console.log('下拉数据===', optionsRef);
  return (
    <div className="associationAndNotification">
      <Spin spinning={pageLoading}>
        <Form.List name="groups">
          {(fields, { add, remove }) => (
            <>
              <div className="joinConditionAddBtn">
                <Button
                  type="primary"
                  onClick={() =>
                    add({
                      conditions: [
                        {
                          relation: undefined,
                          itemType: undefined,
                          itemValue: undefined,
                        },
                      ],
                    })
                  }
                >
                  新增通知人员组
                </Button>
              </div>
              <div className="relevancyFormListContainer">
                {fields.map(({ key, name, ...resetField }, index) => (
                  <div key={`relevance${key}`} className="relevanceContainer">
                    <div className="relevanceTitleContainer">
                      <span className="notificationText">{`通知人员组${index + 1}：`}</span>
                      <div>
                        <Button type="link" onClick={() => handleNotificationConfig(index)}>
                          配置通知
                        </Button>
                        <Button
                          type="link"
                          disabled={!empNotifyConfigs[index]?.notifyConfigs}
                          onClick={() => handlePreviewNotication(index)}
                          className="lcp-margin-left-10 lcp-margin-right-10"
                        >
                          预览通知
                        </Button>
                        <Button
                          type="link"
                          disabled={fields.length <= 1}
                          onClick={() => handleRemoveItem(index, fields.length, remove)}
                        >
                          删除通知人员组
                        </Button>
                      </div>
                    </div>
                    <section className="relevanceFormContent">
                      <Form.List name={[name, 'conditions']}>
                        {(childFields, { add: childAdd, remove: childRemove }) => (
                          <>
                            {childFields.map((field, childIndex) => (
                              <Row key={`conditionItem${field.key}`}>
                                <Col span={2}>
                                  {childIndex > 0 && (
                                    <FormItem
                                      {...field}
                                      wrapperCol={{ span: 22 }}
                                      name={[field.name, 'relation']}
                                      rules={[{ required: true, message: '请选择' }]}
                                    >
                                      <Select placeholder="且/或" options={relationOptions} />
                                    </FormItem>
                                  )}
                                </Col>
                                <Col span={6}>
                                  <FormItem
                                    {...resetField}
                                    wrapperCol={{ span: 22 }}
                                    name={[field.name, 'itemType']}
                                    rules={[{ required: true, message: '请选择' }]}
                                  >
                                    <Select
                                      options={itemTypeOptions}
                                      placeholder="请选择关联条件"
                                      onChange={() =>
                                        form.setFieldValue(
                                          ['groups', index, 'conditions', childIndex, 'itemValue'],
                                          undefined
                                        )
                                      }
                                    />
                                  </FormItem>
                                </Col>
                                <Col span={6}>
                                  <FormItem
                                    noStyle
                                    shouldUpdate={(pre, cur) =>
                                      pre.groups?.[index]?.conditions?.[childIndex]?.itemType !==
                                      cur.groups?.[index]?.conditions?.[childIndex]?.itemType
                                    }
                                  >
                                    {({ getFieldValue }) => {
                                      const groupsVal = getFieldValue('groups');
                                      const currentItemType = groupsVal?.[index]?.conditions?.[childIndex]?.itemType;
                                      const options = optionsRef.current[currentItemType] || [];
                                      return (
                                        <Form.Item
                                          {...field}
                                          wrapperCol={{ span: 22 }}
                                          name={[field.name, 'itemValue']}
                                          rules={[{ required: true, message: '请选择' }]}
                                        >
                                          <Select
                                            showSearch
                                            options={options}
                                            placeholder="请选择值"
                                            optionFilterProp="label"
                                          />
                                        </Form.Item>
                                      );
                                    }}
                                  </FormItem>
                                </Col>
                                <Col className="actionIcon">
                                  <PlusCircleOutlined className="addIcon" onClick={() => childAdd()} />
                                  <MinusCircleOutlined
                                    className="delIcon"
                                    onClick={() => childFields.length > 1 && childRemove(field.name)}
                                  />
                                </Col>
                              </Row>
                            ))}
                          </>
                        )}
                      </Form.List>
                    </section>
                  </div>
                ))}
              </div>
            </>
          )}
        </Form.List>
        <NotificationTemplateDrawer templateId={templateId} ref={noticationCompRef} />
      </Spin>
    </div>
  );
});

export default AssociationAndNotification;
