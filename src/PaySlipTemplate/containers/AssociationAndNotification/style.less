.associationAndNotification {
  height: 100%;
  overflow: scroll;
  .joinConditionAddBtn {
    padding-right: 20px;
    text-align: right;
    height: 26px;
  }

  .relevancyFormListContainer {
    height: calc(100% - 24px);
    overflow: scroll;
    padding-right: 10px;
    padding-bottom: 10px;
    .relevanceContainer {
      margin-top: 20px;
      .relevanceTitleContainer {
        display: flex;
        justify-content: space-between;
        .notificationText {
          &::before {
            content: '*';
            line-height: 1;
            font-size: 14px;
            color: #ff4d4f;
            margin-right: 4px;
            display: inline-block;
            font-family: SimSun, sans-serif;
          }
        }
      }
      .relevanceFormContent {
        margin-top: 6px;
        border: 1px solid #eee;
        padding: 20px 30px;
        border-radius: 4px;
        .ant-form-item {
          margin-bottom: 16px;
        }
        .actionIcon {
          font-size: 22px;
          line-height: 30px;
          .addIcon {
            margin-right: 10px;
            cursor: pointer;
            color: var(--antd-dynamic-primary-color);
            &:hover {
              color: var(--antd-dynamic-primary-hover-color);
            }
            &:active {
              color: var(--antd-dynamic-primary-active-color);
            }
          }
          .delIcon {
            cursor: pointer;
            color: var(--adm-color-text-secondary);
            &:hover {
              opacity: 0.7;
            }
            &:active {
              opacity: 0.8;
            }
          }
        }
      }
    }
  }
}
