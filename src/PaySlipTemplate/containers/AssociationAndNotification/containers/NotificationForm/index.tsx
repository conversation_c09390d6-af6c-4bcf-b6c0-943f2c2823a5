import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useRef } from 'react';
import { FormInstance } from 'antd/lib/form/Form';
import { Form, Input, Button } from 'antd';

import { showWarnNotification } from '@utils/tools';

import QuillEditor from '@components/QuillEditor';

import { NOTIFICATION_CHANNEL_TYPE } from '@constants/common';

const { Item: FormItem } = Form;

export interface IAppProps {
  channelKey: string;
  showAction?: boolean;
  tplTableOptions: any[];
  form: FormInstance;
  ref?: any;
}

const NotificationForm: React.FC<IAppProps> = forwardRef(
  ({ channelKey, tplTableOptions, showAction = true, form }, ref) => {
    const quillRef = useRef(null);
    const titleRef = useRef(null);
    const currentInputRef = useRef(null);

    useImperativeHandle(ref, () => ({
      quillHandle: () => quillRef.current?.getQuill(),
    }));

    // 变量arr
    const variateArr = useMemo(
      () => [
        {
          title: '通知人姓名',
          key: 'empName',
        },
        {
          title: '当年',
          key: 'currentYear',
        },
        {
          title: '上一年',
          key: 'lastYear',
        },
        {
          title: '当月',
          key: 'currentMonth',
        },
        {
          title: '上个月',
          key: 'lastMonth',
        },
        {
          title: '当季度',
          key: 'currentQuarter',
        },
        {
          title: '上一季度',
          key: 'lastQuarter',
        },
        {
          title: '薪酬HR',
          key: 'salaryHrName',
        },
        {
          title: '薪酬HR邮箱',
          key: 'salaryHrEmail',
        },
        {
          title: '实际发薪日期',
          key: 'actualPayDate',
        },
        {
          title: '薪资期间开始日期',
          key: 'periodDateBeginStr',
        },
        {
          title: '薪资期间结束日期',
          key: 'periodDateEndStr',
        },
      ],
      []
    );

    const rulesMap = useMemo(
      () => ({
        title: [{ required: true, message: '请输入' }],
        content: [
          {
            validator: () => {
              const quill = quillRef.current?.getQuill();
              const quillText = quill?.getText() || '';
              const currentContent = quillText.trim().replace(/\n/g, '');
              if (!currentContent) {
                return Promise.reject(new Error('请填写内容'));
              }
              return Promise.resolve();
            },
          },
        ],
      }),
      [quillRef]
    );

    // 插入变量
    const handleInsertVariate = useCallback(
      key => {
        const focusType = currentInputRef.current;
        if (focusType === 'title') {
          const titleEle = titleRef.current?.input || {};
          const cursorIndex = titleEle.selectionStart || 0;
          const title = titleEle.value || '';
          const insertText = `\${${key}}`;
          const newTitle = title.slice(0, cursorIndex) + insertText + title.slice(cursorIndex);
          form.setFieldValue('title', newTitle);
          return;
        }
        const quill = quillRef.current?.getQuill();
        const rang = quill.getSelection();
        if (!rang) {
          return showWarnNotification('先把鼠标光标定到对应位置再插入字段哦～');
        }
        quill.insertText(rang.index, `\${${key}}`);
      },
      [quillRef.current, currentInputRef.current, titleRef.current]
    );

    // 插入表
    const handleInsertTabler = useCallback(
      key => {
        const quill = quillRef.current?.getQuill();
        const rang = quill.getSelection();
        if (!rang) {
          return showWarnNotification('先把鼠标光标定到对应位置再插入字段哦～');
        }
        quill.insertText(rang.index, `\n[TABLE#${key}]\n`);
      },
      [quillRef.current]
    );
    return (
      <div>
        <FormItem label="插入变量">
          {variateArr.map(variateItem => (
            <Button
              size="small"
              key={variateItem.key}
              className="variateInserBtn"
              onClick={() => handleInsertVariate(variateItem.key)}
            >
              {variateItem.title}
            </Button>
          ))}
        </FormItem>
        <FormItem name="title" label="通知标题" rules={rulesMap.title}>
          <Input
            placeholder="请输入"
            ref={titleRef}
            onFocus={() => {
              currentInputRef.current = 'title';
            }}
          />
        </FormItem>
        {channelKey === NOTIFICATION_CHANNEL_TYPE.EMAIL && (
          <FormItem label="插入表">
            {(tplTableOptions || []).length > 0 ? (
              tplTableOptions.map(insertTableItem => (
                <Button
                  size="small"
                  key={insertTableItem.id}
                  className="variateInserBtn"
                  onClick={() => handleInsertTabler(insertTableItem.id)}
                >
                  {insertTableItem.name}
                </Button>
              ))
            ) : (
              <Button size="small" key="noneData" className="variateInserBtn" disabled>
                暂无表数据
              </Button>
            )}
          </FormItem>
        )}
        <FormItem label="文本内容" name="content" required rules={rulesMap.content} wrapperCol={{ span: 20 }}>
          <QuillEditor
            ref={quillRef}
            showToolbarOptions={showAction}
            onFocus={() => {
              currentInputRef.current = 'quill';
            }}
          />
        </FormItem>
      </div>
    );
  }
);

export default NotificationForm;
