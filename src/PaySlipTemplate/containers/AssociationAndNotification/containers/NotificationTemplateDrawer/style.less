.notificationTemplate {
  min-height: calc(100vh - 104px);
  .variateInserBtn {
    color: rgba(0, 0, 0, 0.85);
    margin-right: 10px;
    font-size: 12px;
    border-radius: 2px;
    padding: 0 8px;
    line-height: 20px;
    background-color: #fafafa;
    margin-bottom: 4px;
    &:hover {
      color: var(--antd-dynamic-primary-color);
    }
  }
  .ant-form-item {
    margin-bottom: 16px;
  }

  .footerActionBtn {
    text-align: right;
    position: absolute;
    bottom: 20px;
    right: 24px;
    button {
      margin-right: 10px;
      font-size: var(--dynamic-primary-font-size, var(--default-font-size));
      height: calc(var(--dynamic-primary-font-size, var(--default-font-size)) * 2);
      border-radius: 4px;
      padding: 0 14px;
    }
  }
}
.qlbt-operation-menu {
  z-index: 1001;
}
