import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import { Form, Input, Button, Drawer, Spin, Modal, Tabs } from 'antd';

import salarySlip from '@apis/salarySlip';
import useDictCode from '@hooks/useDictCode';
import { request as fetchApi } from '@utils/http';

import { DICT_CODE_MAP_ID, NOTIFICATION_CHANNEL_TYPE } from '@constants/common';
import NotificationPreview from '../NotificationPreview';
import EmailNotificationForm from '../EmailNotificationForm';
import WechatNotificationForm from '../WechatNotificationForm';

import './style.less';

const formLayout = {
  labelCol: { span: 2 },
  wrapperCol: { span: 16 },
};

const baseFormValues = {
  title: undefined,
  content: '',
};

const tableIdReg = /\[TABLE#(.*?)]/g;
export interface WithParams {
  title: string;
  templateId: string;
  onOk?: (values) => void;
  initData?: Record<string, any>;
}

export interface IAppProps extends WithParams {
  ref?: any;
  open: boolean;
  onDestroyDrawer: () => void;
}

const NotificationTemplateDrawer: React.FC<IAppProps> = forwardRef(
  ({ title, templateId, open, onDestroyDrawer, onOk, initData }, ref) => {
    const initDataStr = JSON.stringify(initData);

    const [form] = Form.useForm();

    const cacheFormValuesRef = useRef({});
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const [tplTableOptions, setTplTableOptions] = useState<any[]>([]);
    const [previewLoading, setPreviewLoading] = useState<boolean>(false);
    const [notifyChannel, setNotifyChannel] = useState<string>(NOTIFICATION_CHANNEL_TYPE.EMAIL);

    const { DHR_NOTIFICATION_CHANNEL: channelOptions = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_NOTIFICATION_CHANNEL]);

    useImperativeHandle(ref, () => ({
      onPreviewTpl: values => handlePreviewTpl(values),
    }));

    // 获取表
    const onFetchTplTableList = useCallback(() => {
      setLoading(true);
      fetchApi({
        ...salarySlip.salarySlipTplList,
        params: {
          onNotification: '1',
          templateId,
        },
        onSuccess: list => {
          setTplTableOptions(list || []);
        },
      }).finally(() => setLoading(false));
    }, [templateId]);

    // 初始化数据
    const onInitData = useCallback(() => {
      (initData || []).forEach(data => {
        const { notifyChannel, ...otherData } = data;
        cacheFormValuesRef.current[notifyChannel] = {
          ...data,
        };
        // 刚进来只需要初始化email的数据
        if (notifyChannel === NOTIFICATION_CHANNEL_TYPE.EMAIL) {
          form.setFieldsValue({
            ...otherData,
          });
        }
      });
    }, [initData]);

    useEffect(() => {
      initData && onInitData();
    }, [initDataStr]);

    useEffect(() => {
      templateId && onFetchTplTableList();
    }, [templateId]);

    useEffect(() => {
      setVisible(open);
    }, [open]);

    const handleAfterOpenChange = useCallback((afterOpen: boolean) => {
      !afterOpen && onDestroyDrawer();
    }, []);

    // 预览
    const handlePreviewTpl = params =>
      new Promise(async (resolve, reject) => {
        const tabItems = [];
        for (const channelItem of channelOptions || []) {
          const { name, itemValue } = channelItem;
          const { title, content } = params[itemValue] || {};
          let previewContent = content || '';
          const tableIdsMatch = [...previewContent.matchAll(tableIdReg)];
          if (tableIdsMatch.length > 0) {
            const ids = tableIdsMatch.map(matchItem => matchItem[1]);
            // 去重
            const tableIds = [...new Set(ids)];
            setPreviewLoading(true);
            try {
              const list: any[] = await fetchApi({
                ...salarySlip.payrollTplAllPreview,
                data: {
                  templateId,
                  tableIds,
                },
              });
              (list || []).forEach(listItem => {
                const reg = new RegExp(`\\[TABLE#${listItem.id}\\]`, 'g');
                previewContent = previewContent.replace(
                  reg,
                  `<div style="overflow-x:auto">${listItem.tableDisplayContent || ''}</div>`
                );
              });
            } catch (error) {}
          }
          tabItems.push({
            key: itemValue,
            label: name,
            data: {
              title,
              content: previewContent,
            },
          });
        }
        resolve(true);
        setPreviewLoading(false);
        NotificationPreview.preview({ tabItems });
      });

    // 点击预览
    const handlePreview = () => {
      const formValues = form.getFieldsValue();
      cacheFormValuesRef.current[notifyChannel] = formValues;
      handlePreviewTpl(cacheFormValuesRef.current);
    };

    // 切换渠道
    const handleChangeTabs = useCallback(
      activeKey => {
        // 缓存上一步的数据
        const oldFormValues = form.getFieldsValue();
        setNotifyChannel(activeKey);
        cacheFormValuesRef.current[notifyChannel] = { ...oldFormValues };
        const updateFormValues = cacheFormValuesRef.current[activeKey];
        form.setFieldsValue({
          ...baseFormValues,
          ...updateFormValues,
        });
      },
      [notifyChannel]
    );

    const handleOk = () => {
      const formValues = form.getFieldsValue();
      cacheFormValuesRef.current[notifyChannel] = formValues;
      const notifyKeys = Object.keys(cacheFormValuesRef.current);
      if (notifyKeys.length > 0) {
        const formAllValues = { ...cacheFormValuesRef.current };
        const notifyConfigs = notifyKeys.map(key => ({
          notifyChannel: key,
          ...formAllValues[key],
        }));

        const isNotAllContent = notifyConfigs.some(
          ({ title, content }) => !(title && content && (content || '') !== '<p><br></p>')
        );
        if (isNotAllContent) {
          return Modal.confirm({
            title: '提醒',
            content: '存在Tab下的内容未填写完整，请确认是否删除未填写完Tab的内容？',
            okText: '删除并确认',
            cancelText: '返回填写',
            onOk: () => {
              const newNotifyConfigs = notifyConfigs.filter(({ title, content }) => !!title && !!content);
              onOk?.(newNotifyConfigs);
              setVisible(false);
            },
          });
        }
        onOk?.(notifyConfigs);
        setVisible(false);
      }
    };

    const items = useMemo(() => {
      const tabItems = (channelOptions || []).map(channelItem => ({
        label: channelItem.name,
        key: channelItem.itemValue,
        children:
          channelItem.itemValue !== NOTIFICATION_CHANNEL_TYPE.WECHAT ? (
            <EmailNotificationForm form={form} key={channelItem.itemValue} tplTableOptions={tplTableOptions} />
          ) : (
            <WechatNotificationForm form={form} key={channelItem.itemValue} />
          ),
      }));
      return tabItems;
    }, [channelOptions, tplTableOptions]);

    return (
      <Drawer
        width="70%"
        title={title}
        open={visible}
        onClose={() => setVisible(false)}
        afterOpenChange={handleAfterOpenChange}
      >
        <Spin spinning={loading}>
          <div className="notificationTemplate">
            <Form form={form} {...formLayout}>
              <Tabs items={items} onChange={handleChangeTabs} />
            </Form>
            <div className="footerActionBtn">
              <Button onClick={() => setVisible(false)}>取消</Button>
              <Button type="primary" loading={previewLoading} onClick={handlePreview}>
                预览
              </Button>
              <Button type="primary" onClick={handleOk}>
                确认
              </Button>
            </div>
          </div>
        </Spin>
      </Drawer>
    );
  }
);

const withParams = (props: WithParams): IAppProps => {
  return {
    open: true,
    onDestroyDrawer: undefined,
    ...props,
  };
};

const confirm = props => {
  const containers = document.createDocumentFragment();
  const handleDestroyDrawer = () => {
    reactUnmount(containers);
  };
  reactRender(<NotificationTemplateDrawer {...props} onDestroyDrawer={handleDestroyDrawer} />, containers);
};

const NotificationTemplateDrawerModal = NotificationTemplateDrawer as any;

NotificationTemplateDrawerModal.confirm = function confirmFn(props: WithParams) {
  return confirm(withParams(props));
};

export default NotificationTemplateDrawerModal;
