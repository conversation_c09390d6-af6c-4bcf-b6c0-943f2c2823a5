import { render as reactRender, unmount as reactUnmount } from 'react-dom';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Modal, Row, Col, Tabs } from 'antd';

import './style.less';

interface TabItemType {
  label: string;
  key: string;
  data: {
    title: string;
    content: string;
  };
}

export interface ModalProps {
  tabItems: TabItemType[];
}

export interface ExtraProps {
  onAfterClose: () => void;
}

export interface IAppProps extends ModalProps, ExtraProps {
  open: boolean;
}
const NotificationPreview: React.FC<IAppProps> = ({ open, tabItems, onAfterClose }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  const PreviewCom = useCallback(({ title, content }) => {
    return (
      <div className="noticationPreviewContainer">
        <Row gutter={[0, 6]}>
          <Col>通知标题：</Col>
          <Col span={22}>{title || '-'}</Col>
          <Col>通知内容：</Col>
          <Col span={22}>
            <div dangerouslySetInnerHTML={{ __html: content || '-' }}></div>
          </Col>
        </Row>
      </div>
    );
  }, []);

  const items = useMemo(
    () =>
      (tabItems || []).map(tabItem => ({
        ...tabItem,
        children: <PreviewCom title={tabItem.data.title} content={tabItem.data.content} />,
      })),
    [tabItems]
  );
  return (
    <Modal
      centered
      width={1000}
      footer={null}
      open={visible}
      title="预览模板"
      onCancel={handleCancel}
      afterClose={onAfterClose}
      className="noticationPreviewModal"
    >
      <Tabs items={items} />
    </Modal>
  );
};

const withParams = (params: ModalProps) => {
  return {
    ...params,
    open: true,
  };
};

const previewModal = (params: any) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
  };
  reactRender(<NotificationPreview {...params} onAfterClose={onAfterClose} />, container);
};

const NotificationPreviewModal = NotificationPreview as any;

NotificationPreviewModal.preview = function previewFn(props: ModalProps) {
  return previewModal(withParams(props));
};

export default NotificationPreviewModal;
