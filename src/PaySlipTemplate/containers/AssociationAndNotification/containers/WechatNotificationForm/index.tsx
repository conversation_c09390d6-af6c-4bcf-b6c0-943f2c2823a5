import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useRef } from 'react';
import { FormInstance } from 'antd/lib/form/Form';
import { Form, Input, Button } from 'antd';

import { showWarnNotification } from '@utils/tools';

import QuillEditor from '@components/QuillEditor';

import { NOTIFICATION_CHANNEL_TYPE } from '@constants/common';

const { Item: FormItem } = Form;

const { TextArea } = Input;

export interface IAppProps {
  form: FormInstance;
}

const NotificationForm: React.FC<IAppProps> = ({ form }) => {
  const titleRef = useRef(null);
  const contentRef = useRef(null);
  const currentInputRef = useRef(null);

  // 变量arr
  const variateArr = useMemo(
    () => [
      {
        title: '通知人姓名',
        key: 'empName',
      },
      {
        title: '当年',
        key: 'currentYear',
      },
      {
        title: '上一年',
        key: 'lastYear',
      },
      {
        title: '当月',
        key: 'currentMonth',
      },
      {
        title: '上个月',
        key: 'lastMonth',
      },
      {
        title: '当季度',
        key: 'currentQuarter',
      },
      {
        title: '上一季度',
        key: 'lastQuarter',
      },
      {
        title: '薪酬HR',
        key: 'salaryHrName',
      },
      {
        title: '薪酬HR邮箱',
        key: 'salaryHrEmail',
      },
      {
        title: '实际发薪日期',
        key: 'actualPayDate',
      },
      {
        title: '薪资期间开始日期',
        key: 'periodDateBeginStr',
      },
      {
        title: '薪资期间结束日期',
        key: 'periodDateEndStr',
      },
    ],
    []
  );

  const rulesMap = useMemo(
    () => ({
      title: [{ required: true, message: '请输入' }],
      content: [{ required: true, message: '请输入' }],
    }),
    []
  );

  // 插入变量
  const handleInsertVariate = useCallback(
    key => {
      const focusType = currentInputRef.current;
      if (!focusType) {
        return showWarnNotification('先把鼠标光标定位到对应位置再插入字段哦～');
      }
      const insertText = `\${${key}}`;
      if (focusType === 'title') {
        const titleEle = titleRef.current?.input || {};
        const cursorIndex = titleEle.selectionStart || 0;
        const title = titleEle.value || '';
        const newTitle = title.slice(0, cursorIndex) + insertText + title.slice(cursorIndex);
        form.setFieldValue('title', newTitle);
        return;
      }
      const contentEle = contentRef.current?.resizableTextArea?.textArea || {};
      const cursorIndex = contentEle.selectionStart || 0;
      const content = contentEle.value || '';
      const newContent = content.slice(0, cursorIndex) + insertText + content.slice(cursorIndex);
      form.setFieldValue('content', newContent);
    },
    [contentRef.current, titleRef.current, currentInputRef.current]
  );

  return (
    <div>
      <FormItem label="插入变量">
        {variateArr.map(variateItem => (
          <Button
            size="small"
            key={variateItem.key}
            className="variateInserBtn"
            onClick={() => handleInsertVariate(variateItem.key)}
          >
            {variateItem.title}
          </Button>
        ))}
      </FormItem>
      <FormItem name="title" label="通知标题" rules={rulesMap.title}>
        <Input
          placeholder="请输入"
          ref={titleRef}
          onFocus={() => {
            currentInputRef.current = 'title';
          }}
        />
      </FormItem>
      <FormItem label="文本内容" name="content" required rules={rulesMap.content} wrapperCol={{ span: 20 }}>
        <TextArea
          ref={contentRef}
          autoSize={{
            minRows: 14,
            maxRows: 16,
          }}
          onFocus={() => {
            currentInputRef.current = 'content';
          }}
        />
      </FormItem>
    </div>
  );
};

export default NotificationForm;
