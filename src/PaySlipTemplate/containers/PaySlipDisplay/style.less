.paySlipDisplay {
  height: 100%;
  .pay-slip-display-row {
    height: inherit;
    // overflow: hidden;
    .left-pay-slip-col {
      height: inherit;
      width: 100%;
      .left-title {
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 2px;
        padding-right: 2px;
        .paySlipAddIcon {
          font-size: 16px;
          cursor: pointer;
          &:hover {
            color: var(--antd-dynamic-primary-color);
          }
        }
      }
      .left-pay-slip-tree {
        padding: 10px;
        padding-left: 6px;
        overflow: scroll;
        border: 1px solid #eee;
        height: calc(100% - 2px);
        border-radius: 4px;
        .tree-item-container {
          white-space: nowrap;
          position: relative;
          padding-right: 24px;
          .moreIcon {
            position: absolute;
            width: 18px;
            height: 24px;
            right: -4px;
            font-size: 14px;
            line-height: 26px;
          }
        }
      }
    }
    .right-pay-slip-col {
      height: inherit;
      padding-left: 20px;
      overflow: hidden;
      .right-title {
        height: 28px;
        display: flex;
        padding-left: 2px;
        padding-right: 20px;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
}
