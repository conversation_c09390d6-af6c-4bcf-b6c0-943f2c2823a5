import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Form, Row, Col, Input, Radio, Select } from 'antd';
import { FormInstance } from 'antd/lib/form';

import cnb from '@apis/cnb';
import databaseApis from '@apis/database';

import salarySlip from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';

import { REFERENCE_TYPE_CODE } from '@constants/common';

import './style.less';

const { Item: FormItem } = Form;

export const TREE_NODE_KEY = {
  // 初始化状态
  NOT_DATA: '-1',
  // 项目
  PROJECT_TYPE: '0',
  // 表
  TABLE_TYPE: '1',
};

export interface IAppProps {
  form: FormInstance;
  treeNodeType: string;
  initData: Record<string, any>;
  dictCodes: Record<string, any>;
}
const RightPaySlipContent: React.FC<IAppProps> = ({ form, treeNodeType, dictCodes, initData }) => {
  const optionCacheRef = useRef({});
  const {
    DHR_COMMON_WHETHER = [],
    DHR_CNB_SALARY_SLIP_TABLE_RELATION_TYPE = [],
    DHR_CNB_CARRY_RULE = [],
    DHR_CNB_NUM_PRECISION = [],
  } = dictCodes;

  const [precision, setPrecision] = useState<string>('--');
  const [carryRule, setCarryRule] = useState<string>('--');
  const [dsColumnOptions, setDsColumnOptions] = useState([]);
  const [dsColumnLoading, setDsColumnLoading] = useState(false);

  const referenceTypeVal = Form.useWatch('referenceType', form);

  /** 进位规则 */
  const carryRuleMap = useMemo(
    () => (DHR_CNB_CARRY_RULE || []).reduce((pre, cur) => ({ ...pre, [cur.itemValue]: cur.name }), {}),
    [DHR_CNB_CARRY_RULE]
  );

  /** 数值精度 */
  const numPrecisionMap = useMemo(
    () => (DHR_CNB_NUM_PRECISION || []).reduce((pre, cur) => ({ ...pre, [cur.itemValue]: cur.name }), {}),
    [DHR_CNB_NUM_PRECISION]
  );

  const rules = useMemo(
    () => ({
      name: [
        {
          required: true,
          message: '请输入',
        },
      ],
      onMobileTerminal: [
        {
          required: true,
          message: '请选择',
        },
      ],
      onNotification: [
        {
          required: true,
          message: '请选择',
        },
      ],
      projectName: [
        {
          required: true,
          message: '请输入',
        },
      ],
      baseEmployee: [
        {
          required: true,
          message: '请选择',
        },
      ],
      onSummary: [
        {
          required: true,
          message: '请选择',
        },
      ],
      ifZeroDisplay: [
        {
          required: true,
          message: '请选择',
        },
      ],
      ifNullDisplay: [
        {
          required: true,
          message: '请选择',
        },
      ],
      referenceType: [
        {
          required: true,
          message: '请选择',
        },
      ],
      referenceId: [
        {
          required: true,
          message: '请选择',
        },
      ],
      dsColumn: [
        {
          required: true,
          message: '请选择',
        },
      ],
    }),
    []
  );

  const startOptions = useMemo(
    () =>
      (DHR_COMMON_WHETHER || []).map(({ itemValue, name }) => ({
        label: name,
        key: itemValue,
        value: itemValue,
      })),
    [DHR_COMMON_WHETHER]
  );

  const referenceOptions = useMemo(
    () =>
      (DHR_CNB_SALARY_SLIP_TABLE_RELATION_TYPE || []).map(({ itemValue, name }) => ({
        label: name,
        key: itemValue,
        value: itemValue,
      })),
    [DHR_CNB_SALARY_SLIP_TABLE_RELATION_TYPE]
  );

  useEffect(() => {
    setPrecision(initData.precision || '---');
  }, [initData.precision]);

  useEffect(() => {
    setCarryRule(initData.carryRule || '---');
  }, [initData.carryRule]);

  useEffect(() => {
    handleInfoSetOptions(REFERENCE_TYPE_CODE.INFO_SET);
    handleSalaryProjectOptions(REFERENCE_TYPE_CODE.SALARY_PROJECT);
  }, []);

  useEffect(() => {
    initData.referenceId &&
      initData.referenceType === REFERENCE_TYPE_CODE.INFO_SET &&
      onGetInfoChildOptions(initData.referenceId);
  }, [initData.referenceId, initData.referenceType]);

  // 信息集列表
  const handleInfoSetOptions = key => {
    const optionsMap = optionCacheRef.current || {};
    if (optionsMap[key]) return;
    fetchApi({
      ...salarySlip.payrollInfosetList,
      onSuccess: list => {
        const options = (list || []).map(listItem => ({
          ...listItem,
          key: listItem.id,
          value: listItem.id,
          label: listItem.name,
        }));
        optionCacheRef.current[key] = options;
      },
    });
  };

  // 薪资项目列表
  const handleSalaryProjectOptions = key => {
    const optionsMap = optionCacheRef.current || {};
    if (optionsMap[key]) return;
    fetchApi({
      ...salarySlip.payrollItemList,
      onSuccess: list => {
        const options = (list || []).map(listItem => ({
          ...listItem,
          key: listItem.id,
          value: listItem.id,
          label: listItem.name,
        }));
        optionCacheRef.current[key] = options;
      },
    });
  };

  // 获取信息集下拉
  const onGetInfoChildOptions = id => {
    setDsColumnLoading(true);
    const referenceIdOptions = optionCacheRef.current[REFERENCE_TYPE_CODE.INFO_SET] || [];
    const detail = referenceIdOptions.find(({ key }) => key === id) || {};
    const { tableName, id: infosetId } = detail;
    fetchApi({
      ...databaseApis.metaDataTable,
      params: {
        tableMetaId: tableName,
        infosetId,
      },
      onSuccess: list => {
        console.log('list===', list);

        const options = list.map(listItem => ({
          ...listItem,
          key: listItem.metaId,
          value: listItem.metaId,
          label: listItem.metaName,
        }));
        setDsColumnOptions(options);
      },
      onError: () => setDsColumnOptions([]),
    }).finally(() => setDsColumnLoading(false));
  };

  const handleChangeReferenceType = useCallback(val => {
    // 重置referenceId
    form.setFieldsValue({ referenceId: undefined });
  }, []);

  /** 切换引用字段 */
  const handleChangeReferenceId = (val, selectRow) => {
    form.setFieldsValue({
      dsColumn: undefined,
    });
    if (val) {
      referenceTypeVal === REFERENCE_TYPE_CODE.INFO_SET && onGetInfoChildOptions(val);
      const { precision: newPrecision, carryRule: newCarryRule } = selectRow;
      setPrecision(newPrecision);
      setCarryRule(newCarryRule);
      return;
    }
    setPrecision('---');
    setCarryRule('---');
    setDsColumnOptions([]);
  };

  return (
    <div className="rightPaySlipContainer">
      {treeNodeType === TREE_NODE_KEY.TABLE_TYPE && (
        <section>
          <p className="pay-slip-title">工资条表设置</p>
          <Row>
            <Col span={10} offset={1}>
              <FormItem name="name" label="显示名称" rules={rules.name}>
                <Input placeholder="请填写" />
              </FormItem>
            </Col>
            <Col span={14} />
            <Col span={10} offset={1}>
              <FormItem name="onMobileTerminal" label="自助端显示" rules={rules.onMobileTerminal}>
                <Radio.Group options={startOptions} />
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem name="onNotification" label="通知引用显示" rules={rules.onNotification}>
                <Radio.Group options={startOptions} />
              </FormItem>
            </Col>
          </Row>
        </section>
      )}
      {treeNodeType === TREE_NODE_KEY.PROJECT_TYPE && (
        <>
          <section>
            <p className="pay-slip-title">工资条项目基础设置</p>
            <Row>
              <Col span={10} offset={1}>
                <FormItem name="name" label="项目显示名称" rules={rules.projectName}>
                  <Input placeholder="请填写" />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="上级项目">
                  {/* <Input placeholder="请填写" /> */}
                  <span>{initData.parentName || '---'}</span>
                </FormItem>
              </Col>
              <Col span={10} offset={1}>
                <FormItem name="onMobileTerminal" label="自助端显示" rules={rules.baseEmployee}>
                  <Radio.Group options={startOptions} />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem name="onSummary" label="自助端顶部汇总显示" rules={rules.onSummary}>
                  <Radio.Group options={startOptions} />
                </FormItem>
              </Col>
              <Col span={10} offset={1}>
                <FormItem name="ifZeroDisplay" label="数据为0时显示" rules={rules.ifZeroDisplay}>
                  <Radio.Group options={startOptions} />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem name="ifNullDisplay" label="数据为空时显示" rules={rules.ifNullDisplay}>
                  <Radio.Group options={startOptions} />
                </FormItem>
              </Col>
              <Col span={10} offset={1}>
                <FormItem name="onNotification" label="通知引用显示" rules={rules.onNotification}>
                  <Radio.Group options={startOptions} />
                </FormItem>
              </Col>
            </Row>
          </section>
          <section>
            <p className="pay-slip-title">项目值引用设置</p>
            <Row>
              <Col span={10} offset={1}>
                <FormItem name="referenceType" label="引用字段类型">
                  <Select
                    allowClear
                    placeholder="请选择"
                    options={referenceOptions}
                    onChange={handleChangeReferenceType}
                  />
                </FormItem>
              </Col>
              <Col span={14} />
              <Col span={10} offset={1}>
                <FormItem
                  label="引用字段/信息集"
                  required={!!referenceTypeVal}
                  shouldUpdate={(pre, cur) => pre.referenceType !== cur.referenceType}
                >
                  {({ getFieldValue }) => {
                    const referenceType = getFieldValue('referenceType');
                    const options = optionCacheRef.current[referenceType] || [];
                    return (
                      <FormItem name="referenceId" rules={referenceType ? rules.referenceId : []}>
                        <Select
                          showSearch
                          allowClear
                          options={options}
                          placeholder="请选择"
                          optionFilterProp="label"
                          onChange={handleChangeReferenceId}
                        />
                      </FormItem>
                    );
                  }}
                </FormItem>
              </Col>
              {referenceTypeVal === REFERENCE_TYPE_CODE.INFO_SET && (
                <Col span={10}>
                  <FormItem label="信息集字段" name="dsColumn" rules={referenceTypeVal ? rules.dsColumn : []}>
                    <Select
                      showSearch
                      allowClear
                      placeholder="请选择"
                      optionFilterProp="label"
                      loading={dsColumnLoading}
                      options={dsColumnOptions}
                    />
                  </FormItem>
                </Col>
              )}
              <Col span={12}>
                <FormItem label="数值精度">
                  <span>{numPrecisionMap[precision] || '---'}</span>
                </FormItem>
              </Col>
              <Col span={10} offset={1}>
                <FormItem label="进位规则">
                  <span>{carryRuleMap[carryRule] || '---'}</span>
                </FormItem>
              </Col>
            </Row>
          </section>
        </>
      )}
    </div>
  );
};

export default RightPaySlipContent;
