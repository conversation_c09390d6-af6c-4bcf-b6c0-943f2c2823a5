import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Tree, Row, Col, Dropdown, notification, Button, Modal } from 'antd';
import { MoreOutlined, PlusOutlined } from '@ant-design/icons';
import type { DataNode, TreeProps } from 'antd/es/tree';
import type { MenuProps } from 'antd';
import * as uuid from 'uuid';

import list2tree from '@cvte/list2tree';
import salarySlip from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';

import RightPaySlipContent, { TREE_NODE_KEY } from './containers/RightPaySlipContent';

import './style.less';

import { TreeType, IAppProps } from './interface';
import { showErrNotification, showSucNotification } from '@utils/tools';

const PaySlipDisplay: React.FC<IAppProps> = forwardRef(({ form, templateId, dictCodes }, ref) => {
  // 校验是否存在未保存的node
  const newNodeRef = useRef(null);

  const parentNodeIdRef = useRef(null);

  const [treeList, setTreeList] = useState<TreeType[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [saveLoading, setSaveLoading] = useState<boolean>(false);
  const [selectTreeKeys, setSelectTreeKeys] = useState<string[]>([]);
  const [initData, setInitData] = useState<Record<string, any>>({});
  const [treeNodeType, setTreeNodeType] = useState<string>(TREE_NODE_KEY.NOT_DATA);

  useImperativeHandle(ref, () => ({
    // 是否全部保存了
    isAllSave: () => !newNodeRef.current,
    // 获取所有预览的表
    getAllTablePreviewList: () => treeList.filter(({ onNotification }) => onNotification === '1'),
  }));

  const items: MenuProps['items'] = useMemo(
    () => [
      {
        label: '新增',
        key: 'add',
      },
      {
        label: '删除',
        key: 'delete',
      },
    ],
    []
  );

  // 判断点击是否是顶层
  const handleUpdateIsTopNode = useCallback((key, list) => {
    const isTreeTopNode = list.some(({ id }) => id === key);
    setTreeNodeType(isTreeTopNode ? TREE_NODE_KEY.TABLE_TYPE : TREE_NODE_KEY.PROJECT_TYPE);
  }, []);

  // 获取项目表列表
  const onFetchAllTreeList = (isReset = true, selectKey = undefined) => {
    fetchApi({
      ...salarySlip.salaryTplAllList,
      params: {
        templateId,
      },
      onSuccess: detail => {
        const { salarySlipTableList, salarySlipTableItemViewList } = detail;
        const list = [...(salarySlipTableList || []), ...(salarySlipTableItemViewList || [])]
          .map(listItem => ({
            ...listItem,
            key: listItem.id,
            value: listItem.id,
            title: listItem.name,
            name: listItem.name,
          }))
          .sort((a, b) => a.sort - b.sort);

        if (list.length > 0) {
          const treeList = list2tree({
            idKey: 'id',
            parentIdKey: 'parentId',
            newKey: {
              key: 'id',
              value: 'id',
              title: 'name',
              name: 'name',
            },
          })(list);
          const tableItemMap = {};
          treeList.forEach(treeItem => {
            if (treeItem.parentId) {
              return (tableItemMap[`${treeItem.tableId}`] = [
                { ...treeItem },
                ...(tableItemMap[`${treeItem.tableId}`] || []),
              ]);
            }
          });
          const newTreeList = treeList
            .filter(({ parentId }) => !parentId)
            .map(tableListItem => {
              if (tableItemMap[tableListItem.id]) {
                return {
                  ...tableListItem,
                  children: [...tableItemMap[tableListItem.id]].sort((a, b) => a.sort - b.sort),
                };
              }
              return tableListItem;
            });
          setTreeList(newTreeList);
          if (isReset) {
            // 默认选中第一条数据
            const defaultInitData = newTreeList[0] || {};
            const defaultSelectKey = selectKey || defaultInitData.key;
            setInitData({ ...defaultInitData });
            setSelectTreeKeys([defaultSelectKey]);
            handleUpdateIsTopNode(defaultSelectKey, newTreeList);
            handleUpdateFormValues(defaultInitData);
          }
        }
      },
    });
  };

  useEffect(() => {
    templateId && onFetchAllTreeList();
  }, [templateId]);

  // 生成node节点
  const handleGenerateNode = useCallback((parentId = undefined) => {
    const insertId = uuid.v4().replace(/-/g, '').slice(0, 12);
    const insertNode = {
      parentId,
      id: insertId,
      key: insertId,
      value: insertId,
      title: `新建节点${insertId}`,
      isAdd: true, // 标识新增的节点
    };
    return insertNode;
  }, []);

  /**
   * node 当前节点
   * searchId 搜索的id
   * isInsert 是否新增
   *
   */
  const handleActionNode = useCallback((node, searchId: string, isInsert: boolean) => {
    // 当前节点 -> 新增子节点
    if (isInsert) {
      if (node.key === searchId) {
        const baseInsertItem = handleGenerateNode(searchId);
        const insertDetail = {
          ...baseInsertItem,
          parentName: node.name,
          level: (node.level || 0) + 1,
          tableId: node.tableId || node.id,
          parentId: node.parentId ? baseInsertItem.parentId : '0',
        };
        newNodeRef.current = insertDetail.key;
        node.children ? node.children.push(insertDetail) : (node.children = [insertDetail]);
        // 新增 需要展开的key
        return {
          isInsert,
          expandId: node.key,
          initData: insertDetail,
          selectId: insertDetail.key,
        };
      }
    } else {
      // 删除当前节点
      // 存在children不给删 需先删子节点
      // if (node.key === searchId && (node.children || []).length > 0) {
      //   notification.warning({
      //     message: '提醒',
      //     description: '请先删除子节点',
      //   });
      //   return {};
      // }
      const searchIndex = (node.children || []).findIndex(childrenDetail => childrenDetail.key === searchId);
      if (searchIndex > -1) {
        node.children.splice(searchIndex, 1);
        // children没内容了 需要去掉展开的key
        return node.children.length <= 0
          ? {
              isInsert,
              expandId: node.key,
            }
          : {};
      }
    }
  }, []);

  /**
   *  深度优先遍历
   * searchId 当前点击节点的id
   * isInsert 是否新增
   *
   */
  const handleActionDfsTree = useCallback((tree: any[], isInsert: boolean, searchId: string) => {
    if (!tree || tree.length <= 0) {
      return tree;
    }

    let result;
    const stack = [...tree];
    /**删除顶层 */
    if (!isInsert) {
      const delIndex = tree.findIndex(({ key }) => key === searchId);
      if (delIndex > -1) {
        tree.splice(delIndex, 1);
        return {};
      }
    }
    while (stack.length > 0) {
      const node = stack.pop();
      const stopAction = handleActionNode(node, searchId, isInsert);
      result = stopAction;
      if (stopAction) break;
      if ((node.children || []).length > 0) {
        for (let index = node.children.length - 1; index >= 0; index--) {
          stack.push(node.children[index]);
        }
      }
    }

    return result;
  }, []);

  // 更新formValues
  const handleUpdateFormValues = useCallback(params => {
    const {
      name,
      onMobileTerminal,
      onNotification,
      onSummary,
      ifZeroDisplay,
      ifNullDisplay,
      referenceType,
      referenceId,
      parentId,
      dsColumn,
    } = params || {};
    let updateFormValues: any = {
      name,
      onMobileTerminal,
      onNotification,
    };

    // 有parentId 说明
    if (parentId) {
      updateFormValues = {
        ...updateFormValues,
        onSummary,
        ifZeroDisplay,
        ifNullDisplay,
        referenceId: referenceId || undefined,
        referenceType: referenceType || undefined,
      };
      dsColumn && (updateFormValues['dsColumn'] = dsColumn);
    }

    form.setFieldsValue({
      ...updateFormValues,
    });
  }, []);

  // 选中树
  const handleSelectTree = useCallback(
    (
      selectKeys,
      {
        node: {
          props: { data },
        },
      }
    ) => {
      setInitData(data);
      // 更新formValues
      handleUpdateFormValues(data);
      if (selectKeys.length <= 0) {
        return;
      }
      const currentKey = selectKeys[0];
      setSelectTreeKeys(selectKeys);
      handleUpdateIsTopNode(currentKey, treeList);
    },
    [treeList]
  );

  const handleSelectMenu: MenuProps['onClick'] = useCallback(
    e => {
      e.domEvent.stopPropagation();
      const newTreeList = [...treeList];
      const isAdd = e.key === 'add';
      // 新增&未保存新增的节点 -> 提醒 不给新增
      if (newNodeRef.current && isAdd) {
        return notification.warning({
          message: '提醒',
          description: '请先保存新增的项目哦～',
        });
      }
      const selectKey = selectTreeKeys[0];
      const newExpandedKeys = [...expandedKeys];

      // 新增
      if (isAdd) {
        // 缓存选中的父节点 - 保存新增的子节点的时候 -> 选中父节点
        parentNodeIdRef.current = selectKey;
        // 插入或删除返回展开收起
        const result = handleActionDfsTree(newTreeList, isAdd, selectKey);
        result.expandId && !newExpandedKeys.includes(result.expandId) && newExpandedKeys.push(result.expandId);
        setExpandedKeys(newExpandedKeys);

        // 选中节点
        result.selectId && setSelectTreeKeys([result.selectId]);
        result.selectId && handleUpdateIsTopNode(result.selectId, newTreeList);
        if (result.initData) {
          form.resetFields();
          setInitData({ ...result.initData });
        }
        return setTreeList(newTreeList);
      }

      // 删除是新增的节点
      if (selectKey === newNodeRef.current) {
        const { parentId, tableId } = initData;
        const currentParentId = parentId === '0' ? tableId : parentId;
        const result = handleActionDfsTree(newTreeList, isAdd, selectKey);
        const isDel = newExpandedKeys.includes(result.expandId);
        if (isDel) {
          const delIndex = newExpandedKeys.indexOf(result.id);
          newExpandedKeys.splice(delIndex, 1);
        }
        setSelectTreeKeys([currentParentId]);
        setExpandedKeys(newExpandedKeys);
        setTreeList(newTreeList);
        newNodeRef.current = null;
        return;
      }
      // // 删除
      Modal.confirm({
        title: `请确认是否删除${initData.name}？`,
        content: (initData.children || []).length > 0 ? '存在子项目，请检查是否需要一起删除' : '',
        onOk: () =>
          new Promise((resolve, reject) => {
            const { parentId, id, tableId } = initData;
            // 有parentId字段 是项目
            const delAction = parentId ? salarySlip.salarySlipTplTableItemDel : salarySlip.salarySlipTplTableDel;
            const currentParentId = parentId === '0' ? tableId : parentId;
            fetchApi({
              ...delAction,
              params: {
                id,
              },
              onSuccess: res => {
                showSucNotification('操作成功！');
                resolve(true);
                onFetchAllTreeList(true, currentParentId);
              },
              onError: reject,
            });
          }),
      });
    },
    [newNodeRef.current, selectTreeKeys, expandedKeys, treeList]
  );

  // 节点渲染
  const titleRender = useCallback(
    (nodeData: any) => {
      return (
        <div className="tree-item-container">
          <span>{nodeData.title}</span>
          {selectTreeKeys[0] === nodeData.key && (
            <Dropdown trigger={['hover']} menu={{ items, onClick: handleSelectMenu }}>
              <MoreOutlined className="moreIcon" onClick={e => e.stopPropagation()} />
            </Dropdown>
          )}
        </div>
      );
    },
    [selectTreeKeys, treeList]
  );

  // 顶层-新增node-新增表
  const handleAddPaySlipItem = useCallback(() => {
    // 新增&未保存新增的节点 -> 提醒 不给新增
    if (newNodeRef.current) {
      return notification.warning({
        message: '提醒',
        description: '请先保存新增的项目哦～',
      });
    }
    const newNodeData = handleGenerateNode();
    const newTreeList = [...treeList, newNodeData];
    newNodeRef.current = newNodeData.key;
    setSelectTreeKeys([newNodeData.key]);
    setInitData({ ...newNodeData });
    setTreeList(newTreeList);
    setTreeNodeType(TREE_NODE_KEY.TABLE_TYPE);
    // 重置
    form.resetFields();
  }, [treeList, newNodeRef.current]);

  // 展开/收齐
  const handleExpand = useCallback(newExpandedKeys => {
    setExpandedKeys(newExpandedKeys);
  }, []);

  // 更新/创建项目子集
  const handleUpdateTableItem = params => {
    setSaveLoading(true);
    const currentNodeParentId = parentNodeIdRef.current;
    fetchApi({
      ...salarySlip.salarySlipTplTableItemUpdate,
      data: {
        ...params,
        templateId,
      },
      onSuccess: res => {
        newNodeRef.current = null;
        showSucNotification('操作成功！');
        onFetchAllTreeList(!!params.isAdd, currentNodeParentId);
      },
    }).finally(() => setSaveLoading(false));
  };

  // 更新/创建项目表
  const handleUpdateTable = params => {
    setSaveLoading(true);
    fetchApi({
      ...salarySlip.salarySlipTplTableUpdate,
      data: {
        ...params,
        templateId,
      },
      onSuccess: res => {
        newNodeRef.current = null;
        showSucNotification('操作成功！');
        onFetchAllTreeList(!!params.isAdd);
      },
    }).finally(() => setSaveLoading(false));
  };

  // 保存配置
  const handleSave = () => {
    form.validateFields().then(formValues => {
      const params = {
        ...initData,
        ...formValues,
      };
      params.isAdd && delete params.id;
      // 更新项目
      if (params.parentId) {
        return handleUpdateTableItem(params);
      }
      // 更新项目表
      handleUpdateTable(params);
    });
  };

  // 判断是否是同层级
  const handleIsSameLevel = useCallback((prePos, nextPos) => {
    const preLevel = prePos.length;
    const nextLevel = nextPos.length;
    return preLevel === nextLevel;
  }, []);

  /** 判断是否是同一父节点 */
  const handleIsSameParent = useCallback((preKeys, nextKeys) => {
    const prePos = preKeys.slice();
    const nextPos = nextKeys.slice();
    prePos.pop();
    nextPos.pop();
    return prePos.join('') === nextPos.join('');
  }, []);

  const loop = useCallback(
    (data: DataNode[], key: React.Key, callback: (node: DataNode, i: number, data: DataNode[]) => void) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children!, key, callback);
        }
      }
    },
    []
  );

  const onFetchSortMoveUpdate = useCallback(sorts => {
    fetchApi({
      ...salarySlip.salarySlipTplMoveSort,
      data: sorts,
    });
  }, []);

  // 拖动item
  const handleDropTree = info => {
    const { dropToGap, node, dragNode } = info;
    const prePos = dragNode.pos.split('-');
    const nextPos = node.pos.split('-');
    const isSameLevel = handleIsSameLevel(prePos, nextPos);
    const isSameParent = handleIsSameParent(prePos, nextPos);
    const isBetweenNode = isSameLevel && isSameParent && dropToGap;
    // 拖到第一个位置时 路径没有最后一个没有，所以增加一个占位
    // undefined 单纯占位
    const isTopNode = !dropToGap && handleIsSameParent(prePos, [...nextPos, undefined]);
    const canDrop = isBetweenNode || isTopNode;
    if (!canDrop) return;
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const newTreeList = [...treeList];
    const updateIdSorts = [];

    let dragObj: DataNode;
    loop(newTreeList, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    if (!dropToGap) {
      loop(newTreeList, dropKey, (item: any) => {
        item.children = item.children || [];
        const startIndex = item.children[0]?.sort || 1;
        // where to insert 示例添加到头部，可以是随意位置
        item.children.unshift(dragObj);
        item.children = (item.children || []).map((childrenItem: any, index) => {
          const sort = startIndex + index;
          childrenItem.sort = sort;
          updateIdSorts.push({
            id: childrenItem.id,
            sort,
          });
          return childrenItem;
        });
      });
    } else {
      let updateArr;
      let updateIndex;
      const dropPosition = info.dropPosition - Number(prePos[prePos.length - 1]);
      if (dropPosition === 0) return;
      loop(newTreeList, dropKey, (item, index, arr) => {
        updateArr = arr;
        updateIndex = index;
      });
      const startIndex = updateIndex[0]?.sort || 1;
      updateArr.splice(updateIndex! + 1, 0, dragObj!);
      updateArr.map((updateItem, index) => {
        const sort = startIndex + index;
        updateItem.sort = sort;
        updateIdSorts.push({
          id: updateItem.id,
          sort,
        });
        return updateItem;
      });
    }

    onFetchSortMoveUpdate(updateIdSorts);
    setTreeList(newTreeList);
  };

  return (
    <div className="paySlipDisplay">
      <Row className="pay-slip-display-row">
        <Col span={6} className="left-pay-slip-col">
          <div className="left-title">
            <span>工资条项目</span>
            <PlusOutlined className="paySlipAddIcon" onClick={handleAddPaySlipItem} />
          </div>
          <div className="left-pay-slip-tree">
            <Tree
              draggable
              treeData={treeList}
              onDrop={handleDropTree}
              onExpand={handleExpand}
              titleRender={titleRender}
              onSelect={handleSelectTree}
              expandedKeys={expandedKeys}
              selectedKeys={selectTreeKeys}
            />
          </div>
        </Col>
        <Col span={18} className="right-pay-slip-col">
          <div className="right-title">
            <span>工资条项目配置</span>
            <Button type="primary" onClick={handleSave} loading={saveLoading}>
              保存配置
            </Button>
          </div>
          <RightPaySlipContent form={form} initData={initData} dictCodes={dictCodes} treeNodeType={treeNodeType} />
        </Col>
      </Row>
    </div>
  );
});

export default PaySlipDisplay;
