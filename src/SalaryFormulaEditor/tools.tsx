import CodeMirrorOrigin from 'codemirror';
import { EDataSourceType, COLOR_MAPPINGS } from '@constants/cnb';
import * as acorn from 'acorn';
import * as acornWalk from 'acorn-walk';
import { generate } from 'escodegen';
import * as estreeParent from 'estree-parent';
import { defaultObj, showErrNotification } from '@utils/tools';
import { request } from '@utils/http';
import cnbApis from '@apis/cnb';
import databaseApis from '@apis/database';

import { KEYWORD_LIT } from './data';

enum ESTREE_NODE_TYPE {
  Identifier = 'Identifier', // 标识符
  Literal = 'Literal', // 字面量
  Program = 'Program', // 程序
  ExpressionStatement = 'ExpressionStatement', // 表达式语句
  CallExpression = 'CallExpression', // 函数调用
  MemberExpression = 'MemberExpression', // 成员表达式
  VariableDeclaration = 'VariableDeclaration', // 变量声明
  VariableDeclarator = 'VariableDeclarator', // 变量声明
  AssignmentExpression = 'AssignmentExpression', // 赋值表达式
  BinaryExpression = 'BinaryExpression', // 二元表达式
  LogicalExpression = 'LogicalExpression', // 逻辑表达式
  UnaryExpression = 'UnaryExpression', // 一元表达式
  UpdateExpression = 'UpdateExpression', // 更新表达式
  ArrayExpression = 'ArrayExpression', // 数组表达式
  ObjectExpression = 'ObjectExpression', // 对象表达式
}

export const keywordsList = KEYWORD_LIT.map(item => item.code);

/** 插入标记  */
export interface IReplaceMark {
  title?: string;
  text: string;
  className?: string;
  code?: string; // 对应的数据源code，不一定有值
  id?: string; // 对应的数据源id
}

interface IRedayToHighlightQueue {
  replaceText: string;
  targetText: string;
  type: EDataSourceType;
  node: any;
  code: string;
  replaceOnly?: boolean; // 只替换不高亮
}

/**
 *编辑器关键词提示
 * @param editor 编辑器实例
 * @returns 高亮格式
 */
export const keywordHint = editor => {
  const cursor = editor.getCursor();
  const token = editor.getTokenAt(cursor);
  const word = token.string;
  if (!word) return;
  // 生成能匹配的提示项
  let matches = [];
  for (let i = 0; i < keywordsList.length; i++) {
    const item = keywordsList[i];
    if (item.indexOf(word) === 0) {
      matches.push(item);
    }
  }

  // 如果当前输入的字符不为空，并且长度为 1，则返回以当前输入字符开头的提示项
  if (word !== '' && word.length === 1) {
    matches = keywordsList.filter(item => {
      return item.indexOf(word) === 0;
    });
  }
  if (matches.length === 0) return;
  // 返回提示项
  return {
    list: [...matches, word],
    from: CodeMirrorOrigin.Pos(cursor.line, token.start),
    to: CodeMirrorOrigin.Pos(cursor.line, token.end),
  };
};

/** 设置光标 */
export const moveToPosition = (editor, line, ch) => {
  editor.focus(); // 设置光标前要设置焦点
  editor.setCursor(line, ch);
};

/** 插入高亮文本  */
export const insertTextWithHighlight = (editor, { title, text, className, code }: IReplaceMark) => {
  const cursor = editor.getCursor(); // 获取当前光标位置
  editor.replaceSelection(text); // 插入文本
  const end = editor.getCursor(); // 获取插入文本后的光标位置
  const hightlightEnd = {
    ...end,
    ch: end.ch - (text.length - title.length),
  };
  console.log('====插入文本后的光标位置====', end, hightlightEnd);
  const replaceSpan = `<span class="cm-field ${className}" data-code="${code}">${title}</span>`;
  const outDiv = document.createElement('div'); // 创建一个div
  outDiv.innerHTML = replaceSpan; // 将dom字符串赋给div
  editor.markText(cursor, hightlightEnd, {
    handleMouseEvents: true, // handleMouseEvents: 设置为true，这意味着这个标记（mark）会处理鼠标事件
    atomic: true, // atomic: 同样设置为true，这表示这个标记是不可分割的，即当用户进行文本操作时，该标记作为一个整体来处理。
    // 这是一个函数，它返回一个DOM元素，该元素会被用来替换标记的位置。replacedWith函数内部执行了以下操作：
    replacedWith: outDiv.firstChild,
    code,
    className,
    title,
  }); // 高亮插入的文本
  moveToPosition(editor, end.line, end.ch); // 将光标移动到插入文本之后
};

/** 中文转 code --- 单节点遍历转换
 * @param node 当前节点
 * @param ast AST树
 * @param editor 编辑器实例
 * @returns 转换后的代码值
 */
const nodeTransform = (node, ast, editor) => {
  // 检查节点名称是否匹配中文字符串
  const { name, loc } = node;
  /** 查询 */
  const parent = estreeParent(node, ast);
  console.log('parent节点====', parent);
  const form = {
    line: loc.start.line - 2,
    ch: loc.start.column,
  };
  const to = {
    line: loc.end.line - 2,
    ch: loc.end.column,
  };
  const markNode = editor.findMarks(form, to)?.find(k => k.title === name);
  if (!markNode) return;
  const { className, code } = markNode;
  /** 右边紧挨着的字符 */
  const rightFirstWord = editor.getRange(to, {
    ...to,
    ch: to.ch + 1,
  });
  /**
   * 数据集
   */
  if (className === COLOR_MAPPINGS[EDataSourceType.DS]) {
    /** 去数据集找目标 */
    if (rightFirstWord === '(') {
      node.name = `${code}`;
      return;
    }
    node.name = `DS("${code}")`;
  }
  /**
   * 计算函数
   */
  if (className === COLOR_MAPPINGS[EDataSourceType.CALC_FUNC]) {
    /** 如果右边不挨着 ( ,说明没参数，直接转换为 XZ('id'， "") 的形式 */
    if (rightFirstWord === '(') {
      node.name = `${code}`;
      return;
    }
    node.name = `${code}()`;
  }

  /** 薪酬项目
   * 如果直接点击薪酬项目，不是作为参数，则转换为 XZ('id', '') 的形式，后面的空串参数是必须的
   * 如果作为参数，则转换为一个 ID
   * 薪酬项目可以作为一个函数，接受薪资期间作为参数， 转化时就转为：XZ(薪资项目 ID, 发薪期间ID)
   */
  if (className === COLOR_MAPPINGS[EDataSourceType.XZ]) {
    /** 有可能会是几种形式
     * 薪资项目() =>  XZ("xxx")() =>  薪资项目 XZ("xxx", "")
     * 薪资项目(薪资期间) => XZ("xxx")("1111")  薪资项目 XZ("xxx", "1111")
     * 薪资项目 =>  XZ("XXXX") => XZ("xxx", "")
     */

    node.name = `XZ("${code}")`;
    /** 如果右边不挨着 ( ,说明没参数，直接转换为 XZ('id'， "") 的形式 */
    // if (rightFirstWord === '(') {
    //   node.name = `XZ("${code}")`;
    //   return;
    // }
    /** 如果父级是函数调用的,支取 ID */
    // if (parent?.type === ESTREE_NODE_TYPE.CallExpression) {
    //   node.name = `"${code}"`;
    //   return;
    // }
    /** 如果右边挨着 ( ,说明有参数, 本身就有括号了，转换时不需要继续用括号括的形式 */
    // node.name = `XZ("${code}", "")`;
    /** 如果是作为参数 */
  }
  if (className === COLOR_MAPPINGS[EDataSourceType.PERIOD]) {
    /** 不管如何，直接把薪资周期作为薪资项目的第二个参数 */
    /** 如果右边挨着 ( ,说明有参数, 本身就有括号了，转换时不需要继续用括号括的形式 */
    node.name = `"${code}"`;
    /** 如果是作为参数 */
  }

  if (className === COLOR_MAPPINGS[EDataSourceType.AZ]) {
    /** 不管如何，直接把薪资周期作为薪资项目的第二个参数 */
    /** 如果右边挨着 ( ,说明有参数, 本身就有括号了，转换时不需要继续用括号括的形式 */
    node.name = `AZ("${code}")`;
    /** 如果是作为参数 */
  }
  if (className === COLOR_MAPPINGS[EDataSourceType.GET_SALARY_SET_HID]) {
    node.name = `GET_SALARY_SET_HID("${code}")`;
    /** 如果是作为参数 */
  }
};

/**
 * 代码特殊转化
 * @param code
 * @returns
 */
const specialTransform = (code: string) => {
  /** 特殊转化 1
   /** 有可能会是几种形式
     * 薪资项目() =>  XZ("xxx")() =>  薪资项目 XZ("xxx", "")
     * 薪资项目(薪资期间) => XZ("xxx")("1111")  薪资项目 XZ("xxx", "1111")
     * 薪资项目 =>  XZ("XXXX") => XZ("xxx", "")
     * 
     * 薪资套如果作为薪资项目的参数，则最少为第三个参数，因为前面两个参数已经是固定了。下面为这种情况的转换例子：
     *  转换前：XZ("2841d60ac96a4602912f3dec24f1a12f")(GET_SALARY_SET_HID("24a30319d51b4dc889d2b05c3ad55b3c"))
     *  转换后：XZ("2841d60ac96a4602912f3dec24f1a12f", "", GET_SALARY_SET_HID("24a30319d51b4dc889d2b05c3ad55b3c"))
     * 
     */
  // const codeTransformByXZ = code.replaceAll(/XZ\("([^"]*)"\)(\("([^"]*)"\)|\(\)|)/g, (match, p1, p2, p3) => {
  //   console.log('====XZ匹配到的值====', match, p1, p2, p3);
  //   // 检查是否有薪资套参数
  //   const salarySetMatch = p2?.match(/\(GET_SALARY_SET_HID\("([^"]*)"\)/);
  //   if (salarySetMatch) {
  //     // 如果有薪资套参数，将其作为第三个参数
  //     return `XZ("${p1}", "", ${salarySetMatch[0]})`;
  //   }
  //   // 原有逻辑保持不变
  //   if (p1 && p2 && p3) {
  //     return `XZ("${p1}", "${p3}")`;
  //   }
  //   return `XZ("${p1}", "")`;
  // });
  const codeTransformByXZ = code.replaceAll(
    /XZ\("([^"]*?)"\)(?:\(\s*(?:"([^"]*?)")?(?:\s*,\s*)?((?:GET_SALARY_SET_HID\("[^"]*?"\)(?:\s*,\s*GET_SALARY_SET_HID\("[^"]*?"\))*)?)\s*\)?|\(\))?/g,
    (match, xzId, periodId, salarySets) => {
      // 提取所有的薪资套代码
      const salarySetMatches = match.match(/GET_SALARY_SET_HID\("[^"]*?"\)/g) || [];
      const salarySetParams = salarySetMatches.join(', ');
      // 如果有薪资期间和薪资套
      if (periodId && salarySetParams) {
        return `XZ("${xzId}", "${periodId}", ${salarySetParams})`;
      }

      // 如果只有薪资套
      if (salarySetParams) {
        return `XZ("${xzId}", "", ${salarySetParams})`;
      }

      // 如果只有薪资期间
      if (periodId) {
        return `XZ("${xzId}", "${periodId}")`;
      }

      // 默认情况
      return `XZ("${xzId}", "")`;
    }
  );
  return codeTransformByXZ;
};

const astTransform = (nodeList = [], options = {}) => {
  return nodeList.reduce((prev, next) => {
    return prev
      ? `${prev}
${generate(next, options)}`
      : `${generate(next, options)}`;
  }, '');
};

/**
 * 代码转换
 * 转换思路
 * 1. 获取所有的 mark
 * 2. 遍历 mark，根据 mark 的 className 进行不同的转换
 * 3. 转换后的值替换原来的值
 * @param editor 编辑器实例
 * @returns 转换后的值
 * @todo 优化代码
 */
export const transformCode = (editor, cb) => {
  const codeValues: string = editor.getValue();
  /** 如果要使用 return语句，外层一定要套一个函数，否则会异常 */
  const treeCode = `function main(){
${codeValues}
}`;
  /** AST树 */
  let ast;
  try {
    /**
     * locations: 设置为 true 之后会在 AST 的节点中携带多一个 loc 对象来表示当前的开始和结束的行数和列数
     */
    ast = acorn.parse(treeCode, { ecmaVersion: 2020, locations: true });
    if (!ast) {
      return showErrNotification('代码解析失败');
    }
    acornWalk.simple(ast, {
      Identifier(node) {
        nodeTransform(node, ast, editor);
      },
    });
    /** AST转换为源码 */
    const codeAfterTransform = astTransform(ast.body[0].body.body, {
      format: {
        quotes: 'double',
      },
    });
    console.log('====转换后的代码====', codeAfterTransform);
    /** 特殊转换 */

    const codeAfterSpecialTransform = specialTransform(codeAfterTransform);
    console.log(codeAfterTransform);
    console.log('最终使用代码', codeAfterSpecialTransform);
    cb({});
    return codeAfterSpecialTransform;
  } catch (error) {
    console.info('====代码解析失败====', error);
    cb(error);
  }
};

/** 节点高亮 */
const nodeReverseHighlight = (redayToHighlightQueue: IRedayToHighlightQueue[], editor) => {
  redayToHighlightQueue.forEach((k: IRedayToHighlightQueue) => {
    const { type, targetText, code, replaceOnly } = k;
    if (replaceOnly) return;
    const valuesBeforeUpddate: string = editor.getValue();
    const matchResults = Array.from(valuesBeforeUpddate?.matchAll(new RegExp(targetText, 'g')));
    matchResults.forEach(matchItems => {
      const from = editor.posFromIndex(matchItems.index);
      const to = editor.posFromIndex(matchItems.index + matchItems[0].length);
      const className = COLOR_MAPPINGS[type];
      const replaceSpan = `<span class="cm-field ${className}" data-code="${code}">${targetText}</span>`;
      const outDiv = document.createElement('div'); // 创建一个div
      outDiv.innerHTML = replaceSpan; // 将dom字符串赋给div
      editor.markText(from, to, {
        handleMouseEvents: true, // handleMouseEvents: 设置为true，这意味着这个标记（mark）会处理鼠标事件
        atomic: true, // atomic: 同样设置为true，这表示这个标记是不可分割的，即当用户进行文本操作时，该标记作为一个整体来处理。
        // 这是一个函数，它返回一个DOM元素，该元素会被用来替换标记的位置。replacedWith函数内部执行了以下操作：
        replacedWith: outDiv.firstChild,
        code,
        className,
        title: targetText,
      }); // 高亮插入的文本

      // editor.markText(from, to, { className: COLOR_MAPPINGS[type], code });
    });
  });
};

/** ds数据集 table字段缓存 */
const dsTableListCahce = {};
/**
 * code转中文 --- 节点遍历转换
 * @param value 代码值
 * @param editor 编辑器实例
 * @param codeMappings 代码映射
 * @returns 转换后的中文代码值
 * @extraTips 逆转代码时，其中数据集需要获取到对应字段的中文名称，所以需要请求接口，获取到数据时，需要 push进队列里去
 */
/** 代码逆向转换 */
export const codeRevereTrnasform = async ({ value, editor, codeMappings, setLoading, onUpdateDataSource }) => {
  const redayToHighlightQueue: IRedayToHighlightQueue[] = [];
  const asyncRecord: {
    [key: string]: {
      type: EDataSourceType;
      id: string;
      /** promise状态 */
      status: 'pending' | 'fulfilled' | 'rejected';
    };
  } = {};
  console.log('====逆向转换代码值====', value);
  const treeCode = `function main(){
    ${value}
    }`;
  /** AST代码树 */
  const ast = acorn.parse(treeCode, { ecmaVersion: 2020, locations: true });
  console.log('====AST代码树====', ast);
  setLoading(true);
  /** 异步任务 */
  acornWalk.simple(ast, {
    async CallExpression(node) {
      const { callee, arguments: funArguments } = node;
      // 函数调用遍历
      const { source, getNode, extraInfo } = codeMappings[callee.name] || codeMappings[EDataSourceType.CALC_FUNC];
      // console.log('====函数调用遍历====', node);
      /**
       * 1、只遍历ast Identifier 节点
       * 2、如果是 DS调用， 它的参数只能是 id#字段名称#字段类型 这种格式，不会有其他参数，可以依次拆分处理
       * 3、如果是 XZ调用， XZ函数第一个参数肯定是这个薪资项目的 id, 第二个参数是薪资期间的 id或者空串， 不会有第三个参数
       * 3、最后处理非 DS非 XZ的调用，这种调用名字就是函数名，参数也只能是 XZ 或者 DS,或者其他不需要转换的参数，
       */

      if (callee.name === EDataSourceType.DS) {
        // 三个元素 信息集id # 信息字段名称columnName # 信息字段类型dataType
        const dsValue = funArguments[0].value.split('#');
        /** 获取信息集信息 */
        const targetNode = getNode(source, dsValue[0]);
        if (!targetNode || !targetNode.tableName) {
          return showErrNotification(`信息集${targetNode.name}tableName不存在!`);
        }

        const uniqueKey = `${targetNode.id}_${Math.random().toString(16)}`;
        asyncRecord[uniqueKey] = {
          type: EDataSourceType.DS,
          id: `${targetNode.id}`,
          status: 'pending',
        };
        let tableFields = [];
        /** 缓存存在 */
        if (dsTableListCahce[targetNode.tableName]) {
          tableFields = dsTableListCahce[targetNode.tableName];
          console.log(`====缓存存在====tableName${targetNode.tableName}'`);
        } else {
          tableFields = await request({
            ...databaseApis.metaDataTable,
            params: {
              tableMetaId: targetNode.tableName,
              infosetId: targetNode.id,
            },
          }).catch(err => {
            asyncRecord[uniqueKey].status = 'rejected';
          });
          Array.isArray(tableFields) && (dsTableListCahce[targetNode.tableName] = tableFields);
        }
        const targetField = (dsTableListCahce[targetNode.tableName] || tableFields).find(k => k.metaId === dsValue[1]);
        if (!targetField || !targetNode) {
          return showErrNotification(`信息集${targetNode?.name}字段${dsValue[1]}不存在!`);
        }
        /** 替换缓存 */
        redayToHighlightQueue.push({
          replaceText: `${targetNode.name}_${targetField.metaName}("${funArguments[0].value}")`,
          targetText: `${targetNode.name}_${targetField.metaName}`,
          type: EDataSourceType.DS,
          node: targetNode,
          code: `${targetNode.id}#${targetField.metaId}`,
        });
        /** 改写名字 */
        callee.name = `${targetNode.name}_${targetField.metaName}`;
        /** 更新数据列表 */
        const columnList = tableFields.map(column => ({
          ...column,
          code: column.metaCode,
          name: column.metaName,
          // leaf: true,
          isLeaf: true,
          id: column.metaId,
          parentId: targetNode.id,
        }));
        onUpdateDataSource(columnList, targetNode.id);
        asyncRecord[uniqueKey].status = 'fulfilled';
        return;
      }

      if (callee.name === EDataSourceType.XZ) {
        const { periordOriginList, salarySetList } = defaultObj(extraInfo);
        const args = funArguments.map(arg => {
          if (arg.type === 'Literal') {
            return arg.value;
          }
          if (arg.type === 'CallExpression') {
            return arg.arguments[0].value;
          }
          return '';
        });
        const [xzId, periodId, ...salarySets] = args;
        const targetNode = getNode(source, xzId);
        if (!targetNode) return;
        callee.name = targetNode.name;
        // 删除第一个参数
        funArguments.shift();
        if (!periodId) {
          // 删除第二个参数
          funArguments.shift();
        }
        // 添加薪资项目的高亮
        if (xzId && !periodId && !salarySets.length) {
          redayToHighlightQueue.push({
            // replaceText: `${targetNode.name}("${xzId}", "")`,
            replaceText: `${targetNode.name}()`,
            targetText: targetNode.name,
            type: EDataSourceType.XZ,
            node: targetNode,
            code: targetNode.id,
          });
        } else {
          // 添加薪资项目高亮
          redayToHighlightQueue.push({
            replaceText: `${targetNode.name}`,
            targetText: targetNode.name,
            type: EDataSourceType.XZ,
            node: targetNode,
            code: targetNode.id,
          });
        }

        // 如果有薪资期间，添加薪资期间的高亮
        if (periodId) {
          const periodNode = periordOriginList.find(k => k.id === periodId);
          if (periodNode) {
            const calendarNode = periordOriginList.find(k => k.id === periodNode.parentId);
            redayToHighlightQueue.push({
              replaceText: `"${periodId}"`,
              targetText: `${calendarNode.name}_${periodNode.name}`,
              type: EDataSourceType.PERIOD,
              node: periodNode,
              code: periodNode.id,
            });
          }
        }

        // 如果有薪资套，添加薪资套的高亮
        if (salarySets.length) {
          salarySets.forEach(salarySetId => {
            if (salarySetId) {
              const salarySetNode = salarySetList.find(k => k.value === salarySetId);
              if (salarySetNode) {
                redayToHighlightQueue.push({
                  replaceText: `GET_SALARY_SET_HID("${salarySetId}")`,
                  targetText: salarySetNode.name,
                  type: EDataSourceType.GET_SALARY_SET_HID,
                  node: salarySetNode,
                  code: salarySetId,
                });
              }
            }
          });
        }

        return;
      }
      if (callee.name === EDataSourceType.AZ) {
        const arg1 = funArguments[0].value;
        const targetNode = getNode(source, arg1);
        if (!targetNode) return;
        callee.name = targetNode.name;
        /** 没有发薪期间参数 */
        redayToHighlightQueue.push({
          replaceText: `${targetNode.name}("${arg1}")`,
          targetText: targetNode.name,
          type: EDataSourceType.AZ,
          node: targetNode,
          code: targetNode.id,
        });
        return;
      }
      if (callee.name === EDataSourceType.GET_SALARY_SET_HID) {
        const arg1 = funArguments[0].value;
        const targetNode = getNode(source, arg1);
        if (!targetNode) return;
        callee.name = targetNode.name;
        // 信息套
        redayToHighlightQueue.push({
          replaceText: `${targetNode.name}("${arg1}")`,
          targetText: targetNode.name,
          type: EDataSourceType.GET_SALARY_SET_HID,
          node: targetNode,
          code: targetNode.value,
        });
        return;
      }

      const targetNode = getNode(source, callee.name);
      if (!targetNode) return;
      callee.name = targetNode.name;
      redayToHighlightQueue.push({
        replaceText: `${targetNode.name}()`,
        targetText: targetNode.name,
        type: EDataSourceType.CALC_FUNC,
        node: targetNode,
        code: targetNode.code,
      });
    },
  });
  const timer = window.setInterval(() => {
    if (Object.keys(asyncRecord).length === 0 || Object.values(asyncRecord).every(k => k.status !== 'pending')) {
      window.clearInterval(timer);
      /** 转换为源码 */
      let codeAfterTransform: string = astTransform(ast.body[0].body.body, {
        format: {
          quotes: 'double',
        },
      });
      /** 替换变量名称 */
      redayToHighlightQueue.forEach(k => {
        codeAfterTransform = codeAfterTransform.replace(k.replaceText, k.targetText);
      });
      console.log('=====codeAfterTransform---替换后', codeAfterTransform);
      editor.setValue(codeAfterTransform);
      /** 开始高亮 */
      nodeReverseHighlight(redayToHighlightQueue, editor);
      setLoading(false);
    }
  }, 1000);
};
