/** 信息集 */
export const informationSet = [
  {
    name: '人事引用信息集',
    displayName: '人事引用信息集',
    code: 'hr',
    type: 'SET', // 集合分类
    children: [
      {
        name: '税前薪资项目导入',
        displayName: '税前薪资项目导入',
        code: 'salary',
        type: 'NAME', // 分类名称
        children: [
          {
            name: '员工工号',
            displayName: '员工工号',
            code: 'employeeCode',
            func: 'EMPLOYEE_CODE',
            type: 'NAME', // 分类字段
          },
          {
            name: '记薪年份',
            displayName: '记薪年份',
            code: 'salaryYear',
            func: 'SALARY_YEAR',
            type: 'NAME', // 分类字段
          },
        ],
      },
    ],
  },
];

/** 一维 */
export const informationSetlist = [
  {
    name: '人事引用信息集',
    displayName: '人事引用信息集',
    code: 'hr',
    type: 'SET',
    parentId: 'root',
  },
  {
    name: '税前薪资项目导入',
    displayName: '税前薪资项目导入',
    code: 'salary',
    type: 'NAME',
    parentId: 'hr',
  },
  {
    name: '员工工号',
    displayName: '员工工号',
    code: 'employeeCode',
    func: 'EMPLOYEE_CODE',
    type: 'NAME',
    parentId: 'salary',
  },
  {
    name: '记薪年份',
    displayName: '记薪年份',
    code: 'salaryYear',
    func: 'SALARY_YEAR',
    type: 'NAME',
    parentId: 'salary',
  },
];

/** 一维 */
export const calcFunclist = [
  {
    name: '通用函数',
    displayName: '通用函数',
    code: 'common',
    parentId: 'root',
  },
  {
    name: '数学函数',
    displayName: '数学函数',
    code: 'math',
    parentId: 'common',
  },

  {
    name: '三级函数1',
    displayName: '三级函数2',
    code: 'func1-1',
    parentId: 'math',
    func: 'FUNC1',
  },

  {
    name: '薪酬函数',
    displayName: '薪酬函数',
    code: 'salaryFunc',
    parentId: 'root',
  },
  {
    name: '奖金函数',
    displayName: '奖金函数',
    code: 'awardFunc',
    parentId: 'salaryFunc',
  },
  {
    name: '三级函数2',
    displayName: '三级函数2',
    code: 'func1-2',
    parentId: 'awardFunc',
    func: 'FUNC2',
  },
];

export const salaryProjectList = [
  {
    name: '基本工资',
    displayName: '基本工资',
    code: 'basicSalary',
    parentId: 'root',
  },
  {
    name: '基本工资',
    displayName: '基本工资',
    code: 'basicSalaryProject',
    parentId: 'basicSalary',
    func: 'BASIC_SALARY',
  },

  {
    name: '岗位工资',
    displayName: '三级函数2',
    code: 'postSalary',
    parentId: 'basicSalary',
    func: 'POST_SALARY',
  },

  {
    name: '服务/奖励',
    displayName: '服务/奖励',
    code: 'serviceReward',
    parentId: 'root',
  },
  {
    name: '年终奖',
    displayName: '年终奖',
    code: 'yearEndBonus',
    parentId: 'serviceReward',
    func: 'YEAR_END_BONUS',
  },

  {
    name: '突出贡献',
    displayName: '突出贡献',
    code: 'outstandingContribution',
    parentId: 'serviceReward',
    func: 'OUTSTANDING_CONTRIBUTION',
  },
];

export const KEYWORD_LIT = [
  {
    name: 'IF',
    code: 'if',
  },
  {
    name: 'ELSE',
    code: 'else',
  },
  {
    name: 'ELSE IF',
    code: 'else if',
  },
  {
    name: 'AND',
    code: '&&',
  },
  {
    name: 'OR',
    code: '||',
  },
  // {
  //   name: 'LIKE',
  //   code: 'like',
  // },
  {
    name: 'RESULT',
    code: 'result',
  },
  {
    name: 'RETURN',
    code: 'return',
  },
];

export const CALC_SYMBOL = [
  {
    name: '+',
    code: '+',
  },
  {
    name: '-',
    code: '-',
  },
  {
    name: '*',
    code: '*',
  },
  {
    name: '/',
    code: '/',
  },
  {
    name: '>',
    code: '>',
  },
  {
    name: '<',
    code: '<',
  },
  {
    name: '>=',
    code: '>=',
  },
  {
    name: '<=',
    code: '<=',
  },
  {
    name: '!=',
    code: '!=',
  },
  {
    name: '=',
    code: '=',
  },
];

export const OTHER_SYMBOL = [
  {
    name: '()',
    code: '()',
  },
  {
    name: '""',
    code: '""',
  },
  {
    name: '//',
    code: '//',
  },
];
