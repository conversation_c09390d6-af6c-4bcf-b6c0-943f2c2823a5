// 定义一个接受高度参数的Mixin
.max-height-scroll(@height) {
  .datasource-tree-wrap {
    max-height: @height;
    overflow-y: scroll;
  }
}
.codemirrorWrap {
  // height: 500px;
  display: flex;
  justify-content: space-between;
  .leftWrap,
  .rightWrap {
    min-width: 200px;
    max-width: 250px;
    flex-shrink: 0;
  }
  .react-codemirror2 {
    .CodeMirror {
      height: 500px;
    }
  }
  .highlight_info_set {
    color: #ef3473 !important;
  }
  .highlight_calc_func {
    color: #e6ff00 !important;
  }
  .highlight_salary_project {
    color: #65c86c !important;
  }
  .highlight_salary_period {
    color: #ffa800 !important;
  }
  .highlight_tmg_item {
    color: #41ffd3 !important;
  }
  .highlight_salary_set {
    color: #ff0000 !important;
  }
  .height_info_set {
    .max-height-scroll(50vh);
  }

  .height_calc_func,
  .height_salary_project,
  .highlight_tmg_item,
  .height_psalary_period,
  .height_salary_set {
    .max-height-scroll(40vh);
  }
  // .height_info_set {
  //   .datasource-tree-wrap {
  //     max-height: 50vh;
  //     overflow-y: scroll;
  //   }
  // }
  // .height_calc_func {
  //   .datasource-tree-wrap {
  //     max-height: 40vh;
  //     overflow-y: scroll;
  //   }
  // }
  // .height_salary_project {
  //   .datasource-tree-wrap {
  //     max-height: 40vh;
  //     overflow-y: scroll;
  //   }
  // }
  // .height_psalary_period {
  //   .datasource-tree-wrap {
  //     max-height: 40vh;
  //     overflow-y: scroll;
  //   }
  // }
}
.topActionBtns {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
  .saveBtn {
    margin-left: 10px;
  }
}
