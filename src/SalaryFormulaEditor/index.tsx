import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef, useMemo } from 'react';
import { UnControlled as CodeMirror } from 'react-codemirror2';
import { EditorConfiguration } from 'codemirror';
import list2tree from '@cvte/list2tree';
import classnames from 'classnames';
import queryString from 'query-string';
import { Divider, Button, message, Spin, Alert, Modal } from 'antd'; // hint样式库

import useFetch from '@hooks/useFetch';
import cnbApis from '@apis/cnb';
import dataBaseApis from '@apis/database';
import { defaultObj, compineLoading, defaultArray, showSucNotification, showErrNotification } from '@utils/tools';

import { EDataSourceType, COLOR_MAPPINGS } from '@constants/cnb';
import { ERROR_TYPE_TRANSLATION, FORMAULA_EDITOR_MODE } from '@constants/common';
import { request } from '@utils/http';
import { KEYWORD_LIT, CALC_SYMBOL, OTHER_SYMBOL } from './data';
import DataSourceTree from './containers/DataSourceTree';
import ParamsDisPlay from './containers/ParamsDisPlay';
import Operation from './containers/Operation';

import { keywordHint, insertTextWithHighlight, transformCode, codeRevereTrnasform, IReplaceMark } from './tools';

import { IOriginalProps } from '../types/index.d';
import '../styles/atom.less';

import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/material.css';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/show-hint';
// import 'codemirror/mode/javascript/javascript';

import './style.less';

interface IAppProps extends IOriginalProps {
  onChange?: (value: string) => void;
  value?: string;
  defaultValue?: string; // 默认值
  disabled?: boolean; // 禁用编辑
  codeMirrorConfig?: EditorConfiguration;
  cnbSetItemId?: string; // 薪资项目 ID
  cnbSetId?: string; // 工资套 ID
}

interface Error {
  name?: string;
  message?: string;
}

const actionList = [
  {
    title: '关键字',
    list: KEYWORD_LIT,
  },
  {
    title: '计算符',
    list: CALC_SYMBOL,
  },
  {
    title: '其他功能符号',
    list: OTHER_SYMBOL,
  },
];

/** 数据转换 */
const transformTreeData = (data, newKeyOptions: Record<string, any> = {}) => {
  return list2tree({
    idKey: 'id',
    parentIdKey: 'parentId',
    newKey: {
      key: 'id',
      value: 'code',
      title: 'name',
      name: 'name',
      ...newKeyOptions,
    },
  })(data);
};

const CustomSampleRender = forwardRef(
  (
    {
      onChange,
      disabled,
      cnbSetId: cnbSetIdInProps,
      cnbSetItemId: cnbSetItemIdInProps,
      codeMirrorConfig = {},
      ...props
    }: IAppProps,
    ref
  ) => {
    const editorRef = React.useRef<any>(null);
    const [infoSetTreeData, setInfoSetTreeData] = React.useState<any[]>([]);
    const [infoSetOriginList, setInfoSetOriginList] = React.useState<any[]>([]);
    const [calcFuncOriginList, setCalcFuncOriginList] = React.useState<any[]>([]);
    const [calcFuncTreeData, setCalcFuncTreeData] = React.useState<any[]>([]);
    const [salaryProjectOriginList, setSalaryProjectOriginList] = React.useState<any[]>([]);
    const [salaryProjectTreeData, setSalaryProjectTreeData] = React.useState<any[]>([]);
    const [periordOriginList, setPeriordOriginList] = React.useState<any[]>([]);
    const [periordTreeData, setPeriordTreeData] = React.useState<any[]>([]);
    const [tmgItemOriginList, setTmgItemOriginList] = React.useState<any[]>([]);
    const [tmgItemTreeData, setTmgItemTreeData] = React.useState<any[]>([]);
    const [salarySetList, setSalarySetList] = React.useState<any[]>([]);
    const [isEditorInit, setEditorInit] = useState<boolean>(false);
    const [isFinishCodeReversal, setFinishCodeReversal] = useState<boolean>(false);
    const [curSelectedNode, setCurSelectedNode] = useState<Record<string, any>>();
    const timerRef = useRef<number | null>(null);
    const [validLoading, setValidLoading] = useState<boolean>(false);
    const [initLoading, setInitLoading] = useState<boolean>(false);
    const [codeErrInfo, setCodeErrInfo] = useState<Error>({});
    const [dataSourceKeys, setDataSourceKeys] = useState<string>('');
    // const [reFreshKey] = useState<boolean>(false);

    const dataSourceKeysRef = useRef<string[]>([]);
    /**
     * cnbSetItemId薪资项目 ID
     * cnbSetId 薪资套 ID
     */
    const {
      cnbSetItemId,
      cnbSetItemName,
      cnbSetId,
      scene = FORMAULA_EDITOR_MODE.CNB,
    } = {
      cnbSetId: cnbSetIdInProps,
      cnbSetItemId: cnbSetItemIdInProps,
      ...(queryString.parse(window.location.search) as {
        cnbSetItemId?: string;
        cnbSetItemName?: string;
        cnbSetId?: string;
        scene?: keyof typeof FORMAULA_EDITOR_MODE;
        string?: string;
      }),
    };

    const CACHE_KEY = `${cnbSetId}-${cnbSetItemId}-cache`;

    const handleUpdateDataSource = (data: Record<string, any>[], _parentId?: string) => {
      /** 已经加载过数据了不需要重复加载 */
      const parentId = _parentId || data?.[0]?.parentId;
      if (parentId && infoSetOriginList.find(k => k.parentId === parentId)) return;
      const list = [...infoSetOriginList, ...data];
      const dataList = transformTreeData(list);
      setInfoSetTreeData(dataList);
      setInfoSetOriginList(list);
    };

    /** 请求信息集 */
    const { Data: infosetClassify, Loading: infosetClassifyLoading } = useFetch({
      ...cnbApis.infosetClassifyList,
    });
    useEffect(() => {
      const { infosetClassifyList = [], infosetList = [] } = defaultObj(infosetClassify) as {
        infosetClassifyList: any[];
        infosetList: any[];
      };
      infosetList.forEach(item => {
        item.parentId = item.classifyId;
      });
      const allData = [...infosetClassifyList, ...infosetList];
      setInfoSetOriginList(allData);
      setInfoSetTreeData(transformTreeData(allData));
      dataSourceKeysRef.current = [...(dataSourceKeysRef.current || []), EDataSourceType.DS];
      setDataSourceKeys(Math.random().toString(6));
      // setDataSourceKeys([...dataSourceKeys, EDataSourceType.DS]);
    }, [infosetClassify]);

    /** 请求信计算函数 */
    const { Data: functionClassify, Loading: functionClassifyLoading } = useFetch({
      ...cnbApis.functionClassifyList,
    });

    useEffect(() => {
      const { functionClassifyList = [], functionList = [] } = defaultObj(functionClassify) as {
        functionClassifyList: any[];
        functionList: any[];
      };
      functionList.forEach(item => {
        item.parentId = item.classifyId;
      });
      const allData = [...functionClassifyList, ...functionList];
      setCalcFuncOriginList(allData);
      setCalcFuncTreeData(transformTreeData(allData));
      dataSourceKeysRef.current = [...(dataSourceKeysRef.current || []), EDataSourceType.CALC_FUNC];
      setDataSourceKeys(Math.random().toString(6));
    }, [functionClassify]);

    /** 请求薪资项目 */
    const onFetchCnbItem = () => {
      request({
        ...cnbApis.cnbItemClassifyList,
        onSuccess: res => {
          const { cnbCategoryList = [], cnbItemList = [] } = defaultObj(res) as {
            cnbCategoryList: any[];
            cnbItemList: any[];
          };
          cnbItemList.forEach(item => {
            item.parentId = item.categoryId;
          });
          const allData = [...cnbCategoryList, ...cnbItemList];
          setSalaryProjectOriginList(allData);
          setSalaryProjectTreeData(
            list2tree({
              idKey: 'id',
              parentIdKey: 'parentId',
              newKey: {
                key: 'id',
                value: 'id',
                code: 'id',
                title: 'name',
                name: 'name',
              },
            })(allData)
          );
          dataSourceKeysRef.current = [...(dataSourceKeysRef.current || []), EDataSourceType.XZ];
          // setDataSourceKeys([...dataSourceKeys, EDataSourceType.XZ]);
          setDataSourceKeys(Math.random().toString(6));
        },
      });
    };

    /** 请求算薪周期 */
    const onFetchCnbPeriod = async () => {
      /** 算薪周期列表 */
      const periodList = await request({
        ...cnbApis.periodList,
      });
      /** 算薪日历 */
      const calendarList = await request({
        ...cnbApis.calendarList,
      });

      const _periodList = periodList.map(k => ({
        ...k,
        parentId: k.objId,
      }));
      const allData = [...calendarList, ..._periodList];
      setPeriordOriginList(allData);
      setPeriordTreeData(
        transformTreeData(allData, {
          key: 'id',
          value: 'id',
          title: 'name',
          name: 'name',
        })
      );
      dataSourceKeysRef.current = [...(dataSourceKeysRef.current || []), EDataSourceType.PERIOD];
      setDataSourceKeys(Math.random().toString(6));
      // setDataSourceKeys([...dataSourceKeys, EDataSourceType.PERIOD]);
    };

    /** 请求计算项目 */

    const onFetchTmgItem = () => {
      request({
        ...dataBaseApis.tmgItem,
        onSuccess: data => {
          const treeData = (data || []).map(k => ({
            ...k,
            id: k.id,
            parentId: 'null',
            key: k.id,
            title: k.name,
            name: k.name,
            value: k.id,
          }));
          setTmgItemOriginList(treeData);
          setTmgItemTreeData(treeData);
          dataSourceKeysRef.current = [...(dataSourceKeysRef.current || []), EDataSourceType.AZ];
          setDataSourceKeys(Math.random().toString(6));
          // setDataSourceKeys([...dataSourceKeys, EDataSourceType.AZ]);
        },
      });
    };

    /** 请求工资套列表 */
    const onFetchSalarySet = () => {
      request({
        ...cnbApis.salarySetList,
        onSuccess: data => {
          const list = data.map(k => ({
            ...k,
            id: k.id,
            title: k.name,
            value: k.hid,
          }));
          setSalarySetList(list);
          dataSourceKeysRef.current = [...(dataSourceKeysRef.current || []), EDataSourceType.GET_SALARY_SET_HID];
          setDataSourceKeys(Math.random().toString(6));
        },
      });
    };

    useEffect(() => {
      console.log('~~~~场景～～～', scene);
      if (scene === FORMAULA_EDITOR_MODE.CNB) {
        onFetchCnbItem();
        onFetchCnbPeriod();
        onFetchSalarySet();
      }

      if (scene === FORMAULA_EDITOR_MODE.TMG) {
        onFetchTmgItem();
      }
    }, [scene]);

    const handleErrInfo = (err: Error) => {
      setCodeErrInfo(err);
    };

    const handleVlidate = () => {
      const codeValues = transformCode(editorRef.current, handleErrInfo);
      onChange && onChange(codeValues);
      setValidLoading(true);
      request({
        ...cnbApis.expresstionValidate,
        data: {
          expression: codeValues,
          itemId: cnbSetItemId,
          salarySetId: cnbSetId,
        },
        onSuccess: data => {
          console.log('data=======', data);
          showSucNotification('校验通过！');
          // setSalarySetDetail(data);
        },
        onError: err => {
          console.log('err=========', err);
        },
      }).finally(() => {
        setValidLoading(false);
      });
    };

    const handleTempSave = () => {
      if (!cnbSetId || !cnbSetItemId) {
        return showErrNotification('薪资项目信息不全！');
      }
      const editor = editorRef.current;
      const codeValues: string = editor.getValue();
      const allMarks = editor.getAllMarks();
      const hightligjtList = [];
      (allMarks || []).forEach(k => {
        const { from, to } = k.find();
        const { className, code, id, title } = k;
        hightligjtList.push({
          from,
          to,
          className,
          title,
          code,
          id,
        });
      });

      try {
        localStorage.setItem(
          CACHE_KEY,
          JSON.stringify({
            code: codeValues,
            hightligjtList,
          })
        );
        showSucNotification('缓存成功');
      } catch (error) {
        showErrNotification('缓存失败！请勿直接退出');
      }
      console.log('allMarks', allMarks, codeValues);
    };

    const restoreCache = () => {
      if (cnbSetId && cnbSetItemId) {
        const content = localStorage.getItem(CACHE_KEY);
        if (content) {
          try {
            const rule = JSON.parse(content);
            console.log('rulerule', rule);
            rule.code && editorRef.current.setValue(rule.code);
            rule.hightligjtList &&
              rule.hightligjtList.forEach(k => {
                const replaceSpan = `<span class="cm-field ${k.className}" data-code="${k.code}">${k.title}</span>`;
                const outDiv = document.createElement('div'); // 创建一个div
                outDiv.innerHTML = replaceSpan; // 将dom字符串赋给div
                editorRef.current.markText(k.from, k.to, {
                  handleMouseEvents: true, // handleMouseEvents: 设置为true，这意味着这个标记（mark）会处理鼠标事件
                  atomic: true, // atomic: 同样设置为true，这表示这个标记是不可分割的，即当用户进行文本操作时，该标记作为一个整体来处理。
                  // 这是一个函数，它返回一个DOM元素，该元素会被用来替换标记的位置。replacedWith函数内部执行了以下操作：
                  replacedWith: outDiv.firstChild,
                  code: k.code,
                  className: k.className,
                  title: k.title,
                }); // 高亮插入的文本
              });
          } catch (error) {}
        }
      }
    };

    /** 额外信息初始化 */
    const initExtraInfo = () => {
      props.configs?.context?.setFormData({
        C_ITEM_ID: cnbSetItemId,
        C_SALARY_SET_ID: cnbSetId,
      });
    };

    useEffect(() => {
      initExtraInfo();
      const content = localStorage.getItem(CACHE_KEY);
      if (content) {
        Modal.info({
          title: '检测到当前有暂存内容，请点击恢复暂存按钮获取暂存内容',
        });
      }
    }, [cnbSetItemId, cnbSetId]);

    /** 监听代码改变 */
    const onCodeChange = (editor, origin, value) => {
      if (!value) return;
      if (origin.origin === '+input') {
        editor.showHint({
          hint: keywordHint, // 智能提示函数
          completeSingle: true, // 完成单词后自动触发智能提示
        });
      }
      /** 执行 */
      const fn = () => {
        const codeValues = transformCode(editorRef.current, handleErrInfo);
        console.log('更新之后===', codeValues);
        onChange && onChange(codeValues);
        window.clearTimeout(timerRef.current);
        timerRef.current = null;
      };
      if (!timerRef.current) {
        timerRef.current = window.setTimeout(fn, 2000);
      }
    };

    /** 代码逆装 */

    const codeReversal = value => {
      if (!value) return;
      const editor = editorRef.current;
      const codeMappings = {
        [EDataSourceType.DS]: {
          regx: /DS\(["']([^"']*)["']\)/g,
          source: infoSetOriginList,
          hightlightClassName: COLOR_MAPPINGS[EDataSourceType.DS],
          getNode: (list, codeText) => {
            // const paramsArr = codeText.split('#'); // 三个元素 信息集id # 信息字段名称columnName # 信息字段类型dataType
            const node = list.find(k => k.id === codeText);
            return node;
          },
        },
        [EDataSourceType.CALC_FUNC]: {
          regx: /\b((?!(DS|XZ|if|else|then|and|or|like)\b)\w+)\(.*?\)/g,
          source: calcFuncOriginList,
          hightlightClassName: COLOR_MAPPINGS[EDataSourceType.CALC_FUNC],
          getNode: (list, codeText) => {
            const node = list.find(k => k.code === codeText);
            return node;
          },
        },
        [EDataSourceType.XZ]: {
          regx: /XZ\('(.*?)'\)/g,
          source: salaryProjectOriginList,
          extraInfo: {
            periordOriginList,
            salarySetList,
          },
          hightlightClassName: COLOR_MAPPINGS[EDataSourceType.XZ],
          getNode: (list, codeText) => {
            const node = list.find(k => k.id === codeText);
            return node;
          },
        },
        [EDataSourceType.AZ]: {
          regx: /AZ\('(.*?)'\)/g,
          source: tmgItemOriginList,
          extraInfo: {
            periordOriginList,
          },
          hightlightClassName: COLOR_MAPPINGS[EDataSourceType.AZ],
          getNode: (list, codeText) => {
            const node = list.find(k => k.id === codeText);
            return node;
          },
        },
        [EDataSourceType.GET_SALARY_SET_HID]: {
          regx: /GET_SALARY_SET_HID\('([^"]*?)'\)/g,
          source: salarySetList,
          hightlightClassName: COLOR_MAPPINGS[EDataSourceType.GET_SALARY_SET_HID],
          getNode: (list, codeText) => {
            const node = list.find(k => k.value === codeText);
            return node;
          },
        },
      };
      codeRevereTrnasform({
        value,
        editor,
        codeMappings,
        setLoading: setInitLoading,
        onUpdateDataSource: handleUpdateDataSource,
      });
      setFinishCodeReversal(true);
    };

    useEffect(() => {
      const initValue = props.value || props.defaultValue;
      const dataSourceValidateMaps = {
        [FORMAULA_EDITOR_MODE.CNB]: [
          EDataSourceType.DS,
          EDataSourceType.CALC_FUNC,
          EDataSourceType.XZ,
          EDataSourceType.PERIOD,
          EDataSourceType.GET_SALARY_SET_HID,
        ],
        [FORMAULA_EDITOR_MODE.TMG]: [EDataSourceType.DS, EDataSourceType.CALC_FUNC, EDataSourceType.AZ],
      };

      const _dataSourceKeys = Array.from(new Set(dataSourceKeysRef.current || []));
      console.log('dataSourceKeys', _dataSourceKeys, dataSourceKeys);
      const isCanInit =
        isEditorInit &&
        !isFinishCodeReversal &&
        dataSourceValidateMaps[scene].every(k => _dataSourceKeys.includes(k)) &&
        initValue;
      if (isCanInit) {
        setTimeout(() => {
          codeReversal(initValue);
        }, 1000);
      }
    }, [
      isEditorInit,
      scene,
      props.defaultValue,
      dataSourceKeys,
      periordOriginList,
      salarySetList,
      calcFuncOriginList,
      infoSetOriginList,
      salaryProjectOriginList,
      tmgItemOriginList,
    ]);

    /** 选择树节点 */
    const handleSelectNode =
      type =>
      (selectedKeys, { selectedNodes }) => {
        const targetNode = {
          ...selectedNodes[0],
          sourceType: type,
        };
        if (targetNode.children && targetNode.children.length > 0) return;
        /**
         * 暂时判定字段为合法的依据，叶子节点, dataType属性
         * 叶子节点取的parentId是父级信息集的是 key
         */
        if (type === EDataSourceType.DS && (!targetNode?.isLeaf || !targetNode.metaId)) return;
        const listMappings = {
          [EDataSourceType.DS]: {
            list: infoSetOriginList,
            getParams: parentNode => ({
              title: `${parentNode.name}_${targetNode.name}`,
              text: `${parentNode.name}_${targetNode.name}`,
              className: COLOR_MAPPINGS[type],
              code: `${parentNode.id}#${targetNode.id}`,
            }),
          },
          [EDataSourceType.CALC_FUNC]: {
            list: calcFuncOriginList,
            getParams: () => ({
              title: targetNode.name,
              text:
                targetNode.params?.length > 0
                  ? `${targetNode.name}(${targetNode.params.map(p => p.displayName).join(',')})`
                  : targetNode.name,
              className: COLOR_MAPPINGS[type],
              code: targetNode.code,
            }),
          },
          [EDataSourceType.XZ]: {
            list: salaryProjectOriginList,
            getParams: () => ({
              title: targetNode.name,
              text: targetNode.name,
              className: COLOR_MAPPINGS[type],
              code: targetNode.id,
            }),
          },
          [EDataSourceType.PERIOD]: {
            list: periordOriginList,
            getParams: parentNode => ({
              title: `${parentNode.name}_${targetNode.name}`,
              text: `${parentNode.name}_${targetNode.name}`,
              className: COLOR_MAPPINGS[type],
              code: targetNode.id,
            }),
          },
          [EDataSourceType.AZ]: {
            list: tmgItemOriginList,
            getParams: () => ({
              title: targetNode.name,
              text: targetNode.name,
              className: COLOR_MAPPINGS[type],
              code: targetNode.id,
            }),
          },
          [EDataSourceType.GET_SALARY_SET_HID]: {
            list: salarySetList,
            getParams: () => ({
              title: targetNode.name,
              text: targetNode.name,
              className: COLOR_MAPPINGS[type],
              code: targetNode.value,
            }),
          },
        };

        const configs = listMappings[type];
        if (!configs) return;
        const parentNode = defaultArray(configs.list).find(item => item.id === targetNode.parentId);
        if (!parentNode && type === EDataSourceType.DS) return message.error('未找到父节点');
        const params = configs.getParams(parentNode);
        setCurSelectedNode(targetNode);
        return insertTextWithHighlight(editorRef.current, params);
      };

    useImperativeHandle(
      ref,
      () => {
        return {
          setEditorValue(value: string) {
            editorRef.current.setValue(value);
          },
          reInit(value: string) {
            setFinishCodeReversal(false);
            value && codeReversal(value);
          },
          /**
           * 获取转换后的代码值
           */
          getCodeValues() {
            const codeValues = transformCode(editorRef.current, handleErrInfo);
            return codeValues;
          },
        };
      },
      [dataSourceKeys, props.defaultValue]
    );

    const visibleSourceKeys = useMemo(() => {
      const mappings = {
        [FORMAULA_EDITOR_MODE.CNB]: [
          EDataSourceType.DS,
          EDataSourceType.CALC_FUNC,
          EDataSourceType.XZ,
          EDataSourceType.PERIOD,
          EDataSourceType.GET_SALARY_SET_HID,
        ],
        [FORMAULA_EDITOR_MODE.TMG]: [
          EDataSourceType.DS,
          EDataSourceType.CALC_FUNC,
          EDataSourceType.AZ,
          EDataSourceType.GET_SALARY_SET_HID,
        ],
      };
      return mappings[scene];
    }, [scene]);
    const loading = compineLoading([
      infosetClassifyLoading,
      functionClassifyLoading,
      // cnbItemClassifyLoading,
      // periodListLoading,
      // calendarListLoading,
      validLoading,
      initLoading,
    ]);

    return (
      <Spin spinning={loading}>
        <div className={classnames('flex items-center', 'justify-between')}>
          <h2>项目公式信息{cnbSetItemName && `(${cnbSetItemName})`}</h2>
          {codeErrInfo?.message && (
            <Alert
              className="py-0 mb-10"
              message={`当前代码有语法错误，请检查代码后完善, 错误类型：${
                ERROR_TYPE_TRANSLATION[codeErrInfo.name]
              },  错误信息：${codeErrInfo.message}`}
              type="error"
            />
          )}
          <div>
            <Button onClick={handleVlidate} type="primary">
              校验
            </Button>
            <Button onClick={handleTempSave} type="primary" className="ml-10">
              暂存
            </Button>
            <Button onClick={restoreCache} type="primary" className="ml-10">
              恢复暂存
            </Button>
          </div>
        </div>
        <div className={classnames('codemirrorWrap', disabled ? 'c-disabled' : '')}>
          <div className="leftWrap mr-10">
            {visibleSourceKeys.includes(EDataSourceType.DS) && (
              <>
                <DataSourceTree
                  dataSource={infoSetTreeData}
                  originList={infoSetOriginList}
                  onUpdateDataSource={handleUpdateDataSource}
                  onSelectNode={handleSelectNode(EDataSourceType.DS)}
                  title="计算信息集"
                  type={EDataSourceType.DS}
                  className="height_info_set"
                />
                <Divider type="horizontal" />
              </>
            )}

            {visibleSourceKeys.includes(EDataSourceType.PERIOD) && (
              <>
                <DataSourceTree
                  dataSource={periordTreeData}
                  originList={periordOriginList}
                  onUpdateDataSource={handleUpdateDataSource}
                  onSelectNode={handleSelectNode(EDataSourceType.PERIOD)}
                  title="算薪周期"
                  type={EDataSourceType.PERIOD}
                  className="height_psalary_period"
                />
                <Divider type="horizontal" />
              </>
            )}
            {visibleSourceKeys.includes(EDataSourceType.GET_SALARY_SET_HID) && (
              <DataSourceTree
                dataSource={salarySetList}
                originList={salarySetList}
                onUpdateDataSource={handleUpdateDataSource}
                onSelectNode={handleSelectNode(EDataSourceType.GET_SALARY_SET_HID)}
                title="工资套"
                type={EDataSourceType.GET_SALARY_SET_HID}
                className="height_salary_set"
              />
            )}
          </div>
          <div className="flex-1">
            <CodeMirror
              options={{
                mode: 'javascript',
                // mode: 'cnb-editor',
                theme: 'material',
                lineNumbers: true,
                autofocus: true,
                tabSize: 2,
                lineWrapping: true,
                matchBrackets: true, // 启用括号匹配
                ...codeMirrorConfig,
              }}
              onChange={onCodeChange}
              editorDidMount={editor => {
                editorRef.current = editor;
                setEditorInit(true);
              }}
            />
            {/* 参数说明 */}
            <ParamsDisPlay data={curSelectedNode} />
            {/* 操作区 */}
            <Operation
              actionList={actionList}
              onHandleClick={(data: IReplaceMark) => insertTextWithHighlight(editorRef.current, data)}
            />
          </div>
          <div className="rightWrap ml-10">
            {visibleSourceKeys.includes(EDataSourceType.CALC_FUNC) && (
              <>
                <DataSourceTree
                  dataSource={calcFuncTreeData}
                  originList={calcFuncOriginList}
                  onUpdateDataSource={handleUpdateDataSource}
                  onSelectNode={handleSelectNode(EDataSourceType.CALC_FUNC)}
                  title="计算函数"
                  type={EDataSourceType.CALC_FUNC}
                  className="height_calc_func"
                />
                <Divider type="horizontal" />
              </>
            )}
            {visibleSourceKeys.includes(EDataSourceType.XZ) && (
              <DataSourceTree
                dataSource={salaryProjectTreeData}
                originList={salaryProjectOriginList}
                onUpdateDataSource={handleUpdateDataSource}
                onSelectNode={handleSelectNode(EDataSourceType.XZ)}
                title="薪酬项目"
                type={EDataSourceType.XZ}
                className="height_salary_project"
              />
            )}
            {visibleSourceKeys.includes(EDataSourceType.AZ) && (
              <DataSourceTree
                dataSource={tmgItemTreeData}
                originList={tmgItemOriginList}
                onUpdateDataSource={handleUpdateDataSource}
                onSelectNode={handleSelectNode(EDataSourceType.AZ)}
                title="考勤项目"
                type={EDataSourceType.AZ}
                className="highlight_tmg_item"
              />
            )}
          </div>
        </div>
      </Spin>
    );
  }
);

export default CustomSampleRender;
