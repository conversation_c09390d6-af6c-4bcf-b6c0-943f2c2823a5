import React from 'react';
import { Tag } from 'antd';
import './style.less';

export interface IAppProps {
  actionList: Record<string, any>[];
  onHandleClick: (data: Record<string, any>) => void;
}
const BaseInfo: React.FC<IAppProps> = ({ actionList, onHandleClick }) => {
  return (
    <div className="clickActionArea">
      {actionList.map(actionItem => (
        <div key={actionItem.title}>
          <h3>{actionItem.title}</h3>
          {actionItem.list.map(k => (
            <Tag
              key={k.code}
              onClick={() =>
                onHandleClick({
                  text: k.code,
                  title: k.code,
                })
              }
              className="operateBtn"
            >
              {k.name}
            </Tag>
          ))}
        </div>
      ))}
    </div>
  );
};

export default BaseInfo;
