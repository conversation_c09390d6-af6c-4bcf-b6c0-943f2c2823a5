@import 'src/styles/constants.less';
.make-dot(@color) {
  // 创建一个小圆点的样式
  display: inline-block;
  width: 10px; // 可根据需求调整大小
  height: 10px; // 同上
  border-radius: 50%; // 设置为圆形
  background-color: @color; // 使用传入的颜色值
}
.cnbEditorParmasDisplay {
  .dot_info_set {
    .make-dot(@infoSet);
  }
  .dot_calc_func {
    .make-dot(@calcFunc);
  }
  .dot_salary_project {
    .make-dot(@salaryProject);
  }
  .dot_salary_period {
    .make-dot(@salaryPeriod);
  }
  .dot_salary_set {
    .make-dot(@salarySet);
  }
}
