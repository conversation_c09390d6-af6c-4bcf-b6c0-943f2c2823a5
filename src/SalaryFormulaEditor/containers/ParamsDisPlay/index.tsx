import React from 'react';
import './style.less';
import { EDataSourceType } from '@constants/cnb';

/** 函数字段返回值映射 */
const FUNC_RETURN_TYPE_MAP = {
  LocalDateTime: '日期时间',
  Integer: '数字整型',
  String: '字符串',
  Boolean: '布尔类型',
  Long: '长整型',
  Double: '浮点类型',
};

/** 数据集字段返回集 */
const DS_DATA_TYPE_MAP = {
  boolean: '布尔类型',
  smallint: '数值',
  integer: '数值',
  bigint: '数值',
  real: '数值',
  'double precision': '数值',
  numeric: '数值',
  'character varying': '字符串',
  varchar: '字符串',
  character: '字符串',
  char: '字符串',
  text: '字符串',
  date: '日期时间',
  time: '日期时间',
  timestamp: '日期时间',
  'timestamp without time zone': '日期时间',
  'timestamp with time zone': '日期时间',
};

/** 薪资项目返回都是数字 */

/** 算薪期间 无返回 */

export interface IAppProps {
  data?: Record<string, any>;
}
const dotList = [
  {
    className: 'dot_info_set',
    text: '计算信息集字段',
  },
  {
    className: 'dot_calc_func',
    text: '计算函数',
  },
  {
    className: 'dot_salary_project',
    text: '薪酬项目',
  },
  {
    className: 'dot_salary_period',
    text: '薪资期间',
  },
  {
    className: 'dot_salary_set',
    text: '薪资套',
  },
];
const ParamsDisPlay: React.FC<IAppProps> = ({ data }) => {
  const getReturnType = (node: Record<string, any>) => {
    const { sourceType } = node;
    /** 函数返回类型 */
    if (sourceType === EDataSourceType.CALC_FUNC) {
      return FUNC_RETURN_TYPE_MAP[node.returnType] || node.returnType;
    }
    /** 数据集返回类型 */
    if (sourceType === EDataSourceType.DS) {
      return DS_DATA_TYPE_MAP[node.dataType] || node.dataType;
    }
    /** 薪资项目返回类型 */
    if (sourceType === EDataSourceType.XZ) {
      return '数值';
    }
    return '-';
  };
  return (
    <div className="mt-6 cnbEditorParmasDisplay">
      <div className="flex">
        {data && (
          <>
            <div>
              <h3 className="text-12 text-bold">当前字段说明</h3>
              <div>
                <span>名称:{data?.name}</span>
                {data?.type && <span className="ml-20">类型:{data?.type}</span>}
                <span className="ml-20">返回值类型:{getReturnType(data)}</span>
                {data?.desc && <span className="ml-20">备注:{data?.desc}</span>}
              </div>
            </div>
            {data?.params?.length > 0 && (
              <div className="ml-20">
                <h3 className="text-12 text-bold">参数说明:</h3>
                {(data?.params || []).map(k => (
                  <div key={k.id}>
                    <span>参数名:{k.displayName}</span>
                    <span className="ml-20">参数类型:{FUNC_RETURN_TYPE_MAP[k.type]}</span>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
        <div className="ml-20">
          <h3 className="text-12 text-bold">颜色标记说明</h3>
          <div>
            {dotList.map(k => (
              <span className="mr-10">
                <span className={`${k.className} mr-6`}> </span>
                <span>{k.text}</span>
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParamsDisPlay;
