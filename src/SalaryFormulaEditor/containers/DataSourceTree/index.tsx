import React, { useEffect } from 'react';
import { Tree, Input } from 'antd';
// import { DownOutlined } from '@ant-design/icons';
import { request } from '@utils/http';
import cnbApi from '@apis/cnb';
import databaseApis from '@apis/database';

import { EDataSourceType } from '@constants/cnb';

const { TreeNode } = Tree;

export interface IAppProps {
  title: string;
  type: EDataSourceType;
  className?: string;
  dataSource: Record<string, any>[];
  originList: Record<string, any>[];
  onUpdateDataSource?: (data: Record<string, any>[]) => void;
  onSelectNode?: (selectedKeys, e: { selected: boolean; selectedNodes; node }) => void;
}
const BaseInfo: React.FC<IAppProps> = ({
  title,
  dataSource,
  originList,
  type,
  className,
  onUpdateDataSource,
  onSelectNode,
}) => {
  const [inputValue, setInputValue] = React.useState('');
  const [treeSearchValue, setTreeSearchValue] = React.useState('');
  const [treeExpandedKeys, setTreeExpandedKeys] = React.useState([]);
  const [treeAutoExpandParent, setTreeAutoExpandParent] = React.useState(false);
  /** 树节点渲染 */
  const nodeKeysMapping = {
    [EDataSourceType.DS]: data => (data.isLeaf && data.parentId ? `${data.parentId}-${data.id}` : data.id),
    [EDataSourceType.CALC_FUNC]: 'id',
    [EDataSourceType.XZ]: 'id',
    [EDataSourceType.PERIOD]: 'id',
    [EDataSourceType.AZ]: 'id',
  };

  useEffect(() => {
    const handler = setTimeout(() => {
      setTreeSearchValue(inputValue);
    }, 300);
    return () => {
      clearTimeout(handler);
    };
  }, [inputValue]);

  // 树搜索关键字高亮
  const handleTreeHighlight = name => {
    const index = name.indexOf(treeSearchValue);
    const beforeStr = name.substr(0, index);
    const afterStr = name.substr(index + treeSearchValue.length);
    const renderTile =
      index !== -1 ? (
        <span>
          {beforeStr}
          <span style={{ color: '#f50' }}>{treeSearchValue}</span>
          {afterStr}
        </span>
      ) : (
        <span>{name}</span>
      );
    return renderTile;
  };
  const genKey = (data, type) => {
    const isFunc = Object.prototype.toString.call(nodeKeysMapping[type]) === '[object Function]';
    return isFunc ? nodeKeysMapping[type](data) : data[nodeKeysMapping[type]];
  };

  const renderTreeNodes = data =>
    data.map(item => {
      if (item.children && item.children.length) {
        return (
          <TreeNode
            {...item}
            title={handleTreeHighlight(item.name)}
            titleText={item.name}
            key={genKey(item, type)}
            // key={item[nodeKeysMapping[type]]}
            type={type}
          >
            {renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          {...item}
          title={handleTreeHighlight(item.name)}
          key={genKey(item, type)}
          // key={item[nodeKeysMapping[type]]}
          isLeaf={type !== EDataSourceType.DS ? true : item.isLeaf}
        />
      );
    });
  // 动态加载信息集函数
  const onLoadData = treeNode => {
    const { tableName, isLeaf } = treeNode;
    return new Promise<void>(resolve => {
      /** 只有信息集展开才走以下逻辑 */
      if (!tableName || isLeaf) {
        return resolve();
      }
      return request({
        ...databaseApis.metaDataTable,
        params: {
          tableMetaId: tableName,
          infosetId: treeNode.id,
        },
        onSuccess: columnData => {
          const columnList = columnData.map(column => ({
            ...column,
            code: column.metaCode,
            name: column.metaName,
            // leaf: true,
            isLeaf: true,
            id: column.metaId,
            parentId: treeNode.id,
          }));
          // treeNode.props.dataRef.children = columnList;
          onUpdateDataSource(columnList);
          resolve();
        },
        onError: err => {
          console.log('err', err);
          resolve();
        },
      });
    });
  };

  // 获取父节点 key
  const handleGetParentKey = (key, tree) => {
    let parentKey;
    // @ts-ignore
    for (const node of tree) {
      if (node.children) {
        if (node.children.some((item: { key: any }) => item.key === key)) {
          parentKey = node.key;
        } else if (handleGetParentKey(key, node.children)) {
          parentKey = handleGetParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };

  // 确认搜索内容
  const handleSearch = value => {
    if (!value) return;
    const _treeExpandedKeys = originList
      .map(item => {
        // @ts-ignore
        if (item.name.indexOf(value) !== -1) return handleGetParentKey(item.id, dataSource);
        return null;
      })
      .filter(item => item);
    setTreeExpandedKeys(_treeExpandedKeys);
    setTreeAutoExpandParent(true);
  };

  useEffect(() => {
    handleSearch(treeSearchValue);
  }, [treeSearchValue]);

  const handleTreeExpand = keys => {
    setTreeExpandedKeys(keys);
    setTreeAutoExpandParent(false);
  };

  return (
    <div className={className}>
      <h3>{title}</h3>
      <div className="datasource-search">
        <Input
          style={{ marginBottom: 8 }}
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          onPressEnter={(e: React.KeyboardEvent<HTMLInputElement>) => handleSearch(e.currentTarget.value)}
          placeholder="请输入内容"
          // suffix={<SearchOutlined />}
        />
      </div>
      <div className="datasource-tree-wrap">
        <Tree
          onSelect={onSelectNode}
          // defaultExpandParent
          autoExpandParent={treeAutoExpandParent}
          expandedKeys={treeExpandedKeys}
          onExpand={handleTreeExpand}
          showLine
          onLoad={() => console.log('加载完毕嗷嗷')}
          loadData={onLoadData}
        >
          {renderTreeNodes(dataSource || [])}
        </Tree>
      </div>
    </div>
  );
};

export default BaseInfo;
