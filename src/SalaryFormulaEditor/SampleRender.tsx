import React from 'react';
import { UnControlled as CodeMirror } from 'react-codemirror2';
import list2tree from '@cvte/list2tree';
import { Divider, Tag } from 'antd'; // hint样式库

import { EDataSourceType } from '@constants/cnb';
import { informationSetlist, calcFunclist, salaryProjectList, KEYWORD_LIT, CALC_SYMBOL, OTHER_SYMBOL } from './data';
import DataSourceTree from './containers/DataSourceTree';

import './style.less';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/material.css';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/show-hint';
import 'codemirror/mode/javascript/javascript';
// require('codemirror/mode/clike/clike.js'); // c/c++ 高亮库
// require('codemirror/mode/python/python.js'); // c/c++ 高亮库
// require('codemirror/mode/javascript/javascript.js'); // js高亮库
// require('codemirror/addon/hint/show-hint.js'); // hint库
// require('codemirror/addon/hint/show-hint.css');

interface ISampleRenderProps {}

const actionList = [
  {
    title: '关键字',
    list: KEYWORD_LIT,
  },
  {
    title: '计算符',
    list: CALC_SYMBOL,
  },
  {
    title: '其他功能符号',
    list: OTHER_SYMBOL,
  },
];

const CustomSampleRender = (props: ISampleRenderProps) => {
  console.log('props', props);
  const editorRef = React.useRef<any>(null);
  const [infoSetTreeData, setInfoSetTreeData] = React.useState<any[]>([]);
  const [calcFuncTreeData, setCalcFuncTreeData] = React.useState<any[]>([]);
  const [salaryProjectTreeData, setSalaryProjectTreeData] = React.useState<any[]>([]);

  /** 选择树节点 */
  const handleSelectNode = type => () => {
    console.log('type', type);
  };
  /** 数据转换 */
  const transformTreeData = data => {
    return list2tree({
      idKey: 'code',
      parentIdKey: 'parentId',
      newKey: {
        key: 'code',
        value: 'code',
        title: 'name',
        name: 'name',
      },
    })(data);
  };

  React.useEffect(() => {
    setInfoSetTreeData(transformTreeData(informationSetlist));
    setCalcFuncTreeData(transformTreeData(calcFunclist));
    setSalaryProjectTreeData(transformTreeData(salaryProjectList));
  }, []);

  return (
    <div>
      <div className="codemirrorWrap">
        <div className="leftWrap">
          <DataSourceTree
            dataSource={infoSetTreeData}
            onSelectNode={handleSelectNode(EDataSourceType.DS)}
            title="计算信息集"
            type={EDataSourceType.DS}
          />
        </div>
        <div className="flex-1">
          <CodeMirror
            options={{
              mode: 'javascript',
              theme: 'material',
              lineNumbers: true,
              autofocus: true,
              tabSize: 2,
            }}
            editorDidMount={editor => {
              editorRef.current = editor;
            }}
          />
          <div className="clickActionArea">
            {actionList.map(actionItem => (
              <div key={actionItem.title}>
                <h3>{actionItem.title}</h3>
                {actionItem.list.map(k => (
                  <Tag key={k.code} className="operateBtn">
                    {k.name}
                  </Tag>
                ))}
              </div>
            ))}
          </div>
        </div>
        <div className="rightWrap">
          <DataSourceTree
            dataSource={calcFuncTreeData}
            onSelectNode={handleSelectNode(EDataSourceType.CALC_FUNC)}
            title="计算函数"
            type={EDataSourceType.CALC_FUNC}
          />
          <Divider type="horizontal" />
          <DataSourceTree
            dataSource={salaryProjectTreeData}
            onSelectNode={handleSelectNode(EDataSourceType.XZ)}
            title="薪酬项目"
            type={EDataSourceType.XZ}
          />
        </div>
      </div>
    </div>
  );
};

export default CustomSampleRender;
