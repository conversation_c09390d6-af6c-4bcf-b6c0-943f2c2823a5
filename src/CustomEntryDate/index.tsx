import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { DatePicker, Spin } from 'antd';
import type { Moment } from 'moment';
import classnames from 'classnames';
import moment from 'moment';

import tmgApis from '@apis/tmg';
import { toDateFormat } from '@utils/tools';
import { request as fetchApi } from '@utils/http';

import { DAY_TYPES } from '@constants/absence';

import './style.less';
import axios from 'axios';

// 表单类型
const CONTAINER_TYPE_MAP = {
  TABLE: 'table',
  FORM: 'form',
};

// ag-grid 模式类型
const WULI_MODE_TYPE_MAP = {
  VIEW: 'view',
  EDIT: 'edit',
};

interface IAppProps {
  value: any;
  onChange: (val, valStr) => void;
  configs: {
    code: string;
    wuliMode: 'view' | 'edit';
    containerType: 'form' | 'table';

    context: {
      getFormData: () => any;
      setBaseConfig: (values) => void;
      refreshConfigs: (val: boolean) => void;
    };
    config: {
      baseConfig: {
        formCode: string;
        dictConfig: {
          empType: string;
          empTypeCode: string;
        };
      };
    };
  };
  utils: {
    cache: {
      getValue: (key: string) => any;
      setValue: (key: string, val) => void;
    };
  };
  record?: Record<string, any>;
}
const CustomEntryDate: React.FC<IAppProps> = props => {
  const { value, configs, utils, record, onChange } = props;
  const code = configs?.code;
  const wuliMode = configs?.wuliMode;
  // 当前是form还是table表单
  const containerType = configs?.containerType;
  const baseConfig: any = configs?.config?.baseConfig || {};

  const { dictConfig, formCode } = baseConfig;
  const { empType, empTypeCode } = dictConfig || {};
  const formData = configs.context.getFormData() || {};

  const scheduleIdRef = useRef(null);
  const cancelTokenSourceRef = useRef(null);
  const [open, setOpen] = useState<boolean>(false);
  const [scheduleList, setScheduleList] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);

  const scheduleListMap = useMemo(() => {
    return (scheduleList || []).reduce((pre, cur) => {
      pre[cur.date] = cur;
      return pre;
    }, {});
  }, [scheduleList]);

  const onFetchGetScheduleId = useCallback(
    empType => {
      fetchApi({
        ...tmgApis.getScheduleId,
        data: {
          empType,
        },
        onSuccess: id => {
          scheduleIdRef.current = id;
          utils.cache?.setValue(`dhr_entry_date_${formCode}_${code}`, id);
        },
      });
    },
    [formCode, code]
  );

  /** 获取入职日历 */
  const onFetchEntryDateList = useCallback(previewDate => {
    if (!previewDate || !scheduleIdRef.current) return;
    if (cancelTokenSourceRef.current) {
      cancelTokenSourceRef.current.cancel('取消上次请求');
      cancelTokenSourceRef.current = null;
    }
    cancelTokenSourceRef.current = axios.CancelToken.source();
    setLoading(true);
    fetchApi({
      ...tmgApis.scheduleDetailInfo,
      params: {
        scheduleId: scheduleIdRef.current,
        previewDate,
      },
      cancelToken: cancelTokenSourceRef.current.token,
      onSuccess: res => {
        cancelTokenSourceRef.current = null;
        /** 缓存入职列表 - 方便联动使用 */
        const key = record
          ? `dhr_entry_date_list_${formCode}_${code}_${record?._index}`
          : `dhr_entry_date_list_${formCode}_${code}`;
        utils?.cache?.setValue(key, res || []);
        setScheduleList(res || []);
      },
    }).finally(() => setLoading(false));
  }, []);

  // 打开/关闭
  const handleOpenChange = useCallback(
    newOpen => {
      if (newOpen && scheduleList.length === 0) {
        // 如果没有value 则默认当月的
        const previewDate = value || moment().valueOf();
        onFetchEntryDateList(previewDate);
      }
      setOpen(newOpen);
    },
    [scheduleList.length, value]
  );

  useEffect(() => {
    const id = utils?.cache?.getValue(`dhr_entry_date_${formCode}_${code}`);
    if (id) {
      scheduleIdRef.current = id;
    } else {
      const empTypeVal = empType ? empType : formData[empTypeCode];
      empTypeVal && onFetchGetScheduleId(empType);
    }
  }, [empType, formCode, code, empTypeCode]);

  const handleChange = useCallback((val: Moment, b) => {
    const updateVal = val.clone().startOf('day').valueOf();
    onChange(updateVal, b);
  }, []);

  useEffect(() => {
    const isOpen = containerType && containerType === CONTAINER_TYPE_MAP.TABLE && wuliMode === WULI_MODE_TYPE_MAP.EDIT;
    if (isOpen) {
      const id = utils?.cache?.getValue(`dhr_entry_date_${formCode}_${code}`);
      scheduleIdRef.current = id;
      handleOpenChange(true);
    }
  }, [wuliMode, containerType]);

  useEffect(() => {
    value && onFetchEntryDateList(value);
  }, []);

  const getTargetScheule = useCallback(
    (curDate: Moment) => {
      const timedate = curDate.clone().startOf('day').valueOf();
      return scheduleListMap[timedate] || {};
    },
    [scheduleListMap]
  );

  const dateRender = useCallback(
    (current: Moment) => {
      const targetSchdule = getTargetScheule(current);
      return (
        <div className="ant-picker-cell-inner dhr-entry-date-cell-style">
          {/* <span className="dhr-entry-date-cell-extra">{targetSchdule.dateAttrName || '休'}</span> */}
          <span>{current.clone().date()}</span>
          {targetSchdule.dateAttrName && (
            <span
              className={classnames('dhr-entry-date-date-attr', {
                'dhr-entry-date-attr-primary': targetSchdule?.dateAttr === DAY_TYPES.ENTRY_DAY,
                'dhr-entry-date-attr-red': targetSchdule?.dateAttr !== DAY_TYPES.ENTRY_DAY,
              })}
            >
              {targetSchdule.dateAttrName}
            </span>
          )}
        </div>
      );
    },
    [scheduleListMap]
  );

  // 切换日期面板
  const handlePanelChange = current => {
    onFetchEntryDateList(current.valueOf());
  };

  const initValue: Moment | undefined = useMemo(() => {
    return value && moment(Number(value));
  }, [value]);

  return wuliMode === WULI_MODE_TYPE_MAP.VIEW ? (
    <p className="value-p">{value && toDateFormat(Number(value))}</p>
  ) : (
    <Spin spinning={loading}>
      <DatePicker
        open={open}
        inputReadOnly
        value={initValue}
        showToday={false}
        onChange={handleChange}
        dateRender={dateRender}
        onOpenChange={handleOpenChange}
        className="dhr-custom-entry-date"
        onPanelChange={handlePanelChange}
        popupClassName={classnames('dhr-custom-entry-date-popup', loading ? 'dhr-custom-entry-date-loading' : '')}
      />
    </Spin>
  );
};

export default CustomEntryDate;
