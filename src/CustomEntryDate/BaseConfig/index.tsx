import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

import commonApis from '@apis/common';
import { request } from '@utils/http';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  let dictOptions = [];
  // 更新类型
  const handleUpdateFieldType = (key, type, otherHideKeys: string[] = [], otherConfig: any = {}) => {
    formRef?.current?.diffFormItem?.(
      [
        {
          key,
          type,
          ...otherConfig,
        },
      ],
      [...otherHideKeys]
    );
  };

  /** 获取表单列表 */
  const onFetchFormTableList = () => {
    if (dictOptions.length > 0) {
      return handleUpdateFieldType('dictConfigEmpType', 'select', [], {
        configs: {
          options: dictOptions,
        },
      });
    }
    const data = {
      appId: '96dd1d629bb24193bb8630c9dcb61aad',
      formClassId: '95985d5e087d4cd58dc2e2b6556bd56a',
      onlyMain: true,
      keyType: 'CAMEL',
      pageSize: 100,
    };
    request({
      ...commonApis.formTableList,
      data,
      headers: {
        'x-app-id': data.appId,
      },
      onSuccess: res => {
        const list = (res?.content || []).map(({ mainData }) => ({
          label: mainData?.cName,
          key: mainData?.id,
          value: mainData?.id,
        }));
        dictOptions = list;
        handleUpdateFieldType('dictConfigEmpType', 'select', [], {
          configs: {
            options: list,
          },
        });
      },
    });
  };
  onFetchFormTableList();
  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { empType, empTypeCode } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );

    formRef?.current?.setFormItem?.(
      'dictConfigEmpType',
      curFormData?.dictConfigEmpType || defFormData?.dictConfigEmpType || empType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigEmpTypeCode',
      curFormData?.dictConfigEmpTypeCode || defFormData?.dictConfigEmpTypeCode || empTypeCode
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigEmpTypeCode',
      label: '关联表单员工类型CODE',
      configs: {
        onBlur: () => {
          const dictConfigEmpTypeCode = formRef?.current?.getFormItem?.('dictConfigEmpTypeCode');
          context?.onConfirm?.('dictConfigEmpTypeCode', dictConfigEmpTypeCode);
          setDictConfig(formRef, 'empTypeCode', dictConfigEmpTypeCode, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigEmpType',
      label: '关联固定员工类型',
      configs: {
        allowClear: true,
        showSearch: true,
        optionFilterProp: 'label',
        placeholder: '请选择员工类型',
        options: [],
        onSelect: val => {
          context?.onConfirm?.('dictConfigEmpType', val);
          setDictConfig(formRef, 'empType', val, {
            context,
          });
        },
      },
    },
    // {
    //   type: 'input',
    //   key: 'dictConfigUnEntryTip',
    //   label: '非入职日期提醒文案',
    //   configs: {
    //     onBlur: () => {
    //       const dictConfigUnEntryTip = formRef?.current?.getFormItem?.('dictConfigUnEntryTip');
    //       context?.onConfirm?.('dictConfigUnEntryTip', dictConfigUnEntryTip);
    //       setDictConfig(formRef, 'unEntryTip', dictConfigUnEntryTip, {
    //         context,
    //       });
    //     },
    //   },
    // },
  ];
};

export default customCptBaseConfig;
