import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { controlType, label } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );

    formRef?.current?.setFormItem?.(
      'dictConfigControlType',
      curFormData?.dictConfigControlType || defFormData?.dictConfigControlType || controlType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigLabel',
      curFormData?.dictConfigLabel || defFormData?.dictConfigLabel || label
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'select',
      key: 'dictConfigControlType',
      label: '控件展示类型',
      configs: {
        placeholder: '默认展示复选框样式',
        options: [
          {
            key: 'radio',
            label: '单选框',
            value: 'radio',
          },
          {
            key: 'checkbox',
            label: '复选框',
            value: 'checkbox',
          },
        ],
        onSelect: val => {
          context?.onConfirm?.('dictConfigControlType', val);
          setDictConfig(formRef, 'controlType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigLabel',
      label: '提示内容',
      configs: {
        allowClear: true,
        placeholder: '请输入',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigLabel');
          context?.onConfirm?.('dictConfigLabel', value);
          setDictConfig(formRef, 'label', value, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
