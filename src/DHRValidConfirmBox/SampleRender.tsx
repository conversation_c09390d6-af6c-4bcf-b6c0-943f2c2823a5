import { Checkbox, Radio } from 'antd';
import React from 'react';

export interface IAppProps {
  configs: {
    config: {
      baseConfig: {
        dictConfig: {
          label: string;
          controlType: string;
        };
      };
    };
  };
}

const BaseInfo: React.FC<IAppProps> = props => {
  const dictConfig: any = props.configs?.config?.baseConfig?.dictConfig || {};
  const label = dictConfig?.label || '';
  const controlType = dictConfig?.controlType || 'checkbox';
  return (
    <div>
      {controlType === 'checkbox' ? <Checkbox className="customTimePicker">{label}</Checkbox> : <Radio>{label}</Radio>}
    </div>
  );
};

export default BaseInfo;
