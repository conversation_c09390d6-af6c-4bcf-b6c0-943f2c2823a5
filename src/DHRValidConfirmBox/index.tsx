import { Checkbox, Radio } from 'antd';
import React, { useCallback, useMemo } from 'react';

import { WULI_MODE } from '@constants/common';

export interface IAppProps {
  value: string;
  onChange: (value) => void;
  configs: {
    wuliMode: 'edit' | 'view';
    config: {
      baseConfig: {
        dictConfig: {
          label: string;
          controlType: string;
        };
      };
    };
  };
}

const DHRValidConfirmBox: React.FC<IAppProps> = props => {
  const { configs, value, onChange } = props;
  const wuliMode = configs.wuliMode;
  const isView = wuliMode === WULI_MODE.VIEW;
  const dictConfig: any = configs?.config?.baseConfig?.dictConfig || {};
  const label = dictConfig?.label || '';
  const controlType = dictConfig?.controlType || 'checkbox';

  const isChecked = useMemo(() => {
    return value === '1' ? true : false;
  }, [value]);

  const handleChange = useCallback(({ target: { checked } }) => {
    onChange(checked ? '1' : '0');
  }, []);

  const showText = useMemo(() => {
    return value === '1' ? '是' : '否';
  }, [value]);
  return (
    <div className="dhr-valid-confirm-box-wrap">
      {isView ? (
        showText
      ) : controlType === 'checkbox' ? (
        <Checkbox disabled={isView} checked={isChecked} onChange={handleChange}>
          {label}
        </Checkbox>
      ) : (
        <Radio disabled={isView} checked={isChecked} onChange={handleChange}>
          {label}
        </Radio>
      )}
    </div>
  );
};

export default DHRValidConfirmBox;
