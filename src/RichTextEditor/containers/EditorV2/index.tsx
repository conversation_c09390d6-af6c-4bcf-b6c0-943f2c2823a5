import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import QuillBetterTable from 'quill-better-table';
import ImageUploader from 'quill2-image-uploader';
import classnames from 'classnames';
import Quill from 'quill';

import { defaultArray } from '@utils/tools';

import 'quill/dist/quill.snow.css';
import 'quill-better-table/dist/quill-better-table.css';
import './style.less';

Quill.register(
  {
    'modules/better-table': QuillBetterTable,
    'modules/imageUploader': ImageUploader,
  },
  true
);
/**
 * @description table颜色配置
 * @todo 背景颜色支持配置自定义
 */
const TABLE_COLORS_OPTIONS = ['green', 'red', 'yellow', 'blue', 'white'];

/**
 * @description 富文本编辑器工具栏配置
 * @todo 可根据需求自定义配置
 */
const toolbarOptions = [
  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 多级标题
  [{ font: [] }], // 字体类型
  ['bold', 'italic', 'underline', 'strike'], // 加粗/倾斜/下划线/中划线
  ['blockquote', 'code-block'], // 块/代码
  [{ list: 'ordered' }, { list: 'bullet' }], // 序列
  [{ indent: '-1' }, { indent: '+1' }], // 缩进
  [{ direction: 'rtl' }], // 居左 居右
  [{ color: [] }, { background: [] }], // 文字颜色/文字背景色
  [{ align: [] }], // 布局
  ['link', 'image'],
  ['clean'], // 清除格式
  ['table'],
];

const operationMenuItems = {
  insertColumnRight: {
    text: '向右插入列',
  },
  insertColumnLeft: {
    text: '向左插入列',
  },
  insertRowUp: {
    text: '向上插入行',
  },
  insertRowDown: {
    text: '向下插入行',
  },
  mergeCells: {
    text: '合并单元格',
  },
  unmergeCells: {
    text: '取消合并单元格',
  },
  deleteColumn: {
    text: '删除列',
  },
  deleteRow: {
    text: '删除行',
  },
  deleteTable: {
    text: '删除表格',
  },
};

export interface IAppProps {
  ref?: any;
  value?: string;
  className?: string;
  onChange?: (value: string, instance) => void;
  onFocus?: () => void;
}

const QuillEditor: React.FC<IAppProps> = forwardRef(({ className, value, onChange, onFocus, ...restProps }, ref) => {
  const quillRef = useRef(null);
  /** dom ref */
  const _editorDomRef = useRef(null);
  const [isInit, setIsInit] = useState(false);

  const { dictConfig = {} } = restProps.configs.config?.baseConfig || {};

  useImperativeHandle(ref, () => ({
    getQuill: () => quillRef.current,
  }));

  /**
   * @description 上传图片
   * @param file 选择上传的图片文件
   * @returns 图片附件对应的附件地址
   */
  const uploadImg = file => {
    const { upload, download } = restProps.configs.context.apiConfigMap;
    console.log('uploadConfig', upload);
    const params = new FormData();
    params.append('files', file);
    params.append('categoryId', `/dhr/common/${upload.params.categoryId}`);
    params.append('catalogId', upload.params.catalogId);
    return new Promise(resolve => {
      restProps.utils
        .fetch({
          headers: {
            'Content-type': 'multipart/form-data',
          },
          url: upload?.url,
          method: 'post',
          data: params,
        })
        .then(res => {
          const fileIds = defaultArray(res.data.result.fileIds);
          resolve(fileIds.map(k => `${download.url}/${k}`));
        })
        .catch(err => {
          console.log('err', err);
          resolve('');
        });
    });
  };

  /**
   * @description 表格配置
   */
  const quillTableOptions = useMemo(() => {
    const colorOptions = {
      color: {
        colors: TABLE_COLORS_OPTIONS,
        text: '单元格背景颜色：',
      },
    };
    return {
      'better-table': {
        operationMenu: {
          items: operationMenuItems,
          ...colorOptions,
        },
      },
      keyboard: {
        bindings: QuillBetterTable.keyboardBindings,
      },
    };
  }, []);

  /**
   * @description 插入表格
   */
  const handleInsertTable = useCallback(() => {
    const currentQuill = quillRef.current;
    const tableModule = currentQuill?.getModule('better-table');
    tableModule.insertTable(3, 3);
  }, []);

  const quillOptions = useMemo(() => {
    const handlers = { table: handleInsertTable };
    const toolbar = {
      container: toolbarOptions,
      handlers,
    };
    return {
      modules: {
        toolbar,
        history: {
          delay: 2000,
          maxStack: 500,
          userOnly: true,
        },
        imageUploader: {
          upload: uploadImg,
        },
        table: false,
        ...quillTableOptions,
      },
      placeholder: '请输入内容...',
      theme: 'snow',
    };
  }, [quillTableOptions]);

  /**
   * @description 初始化编辑器内容
   */
  const initValue = () => {
    console.log('value=====', value);
    if (value && quillRef.current && !isInit) {
      const editor = quillRef.current;
      editor.clipboard.dangerouslyPasteHTML(value);
      setIsInit(true);
      const length = editor?.getLength();
      editor?.setSelection(length, 0);
    }
  };
  // 初始化编辑器
  const onInitEditor = () => {
    if (!quillRef.current) {
      let timeout = null;
      timeout = setTimeout(() => {
        try {
          const editor = new Quill(_editorDomRef.current, quillOptions);
          quillRef.current = editor;
          quillRef.current.on('text-change', () => {
            const content = quillRef.current?.root?.innerHTML;
            console.log('====编辑内容===', content);
            onChange?.(content, quillRef.current);
          });
          !isInit && initValue();
          clearTimeout(timeout);
          timeout = null;
        } catch (error) {
          console.log('quill初始化失败~~', error);
        }
      }, 300);
    }
  };

  /**
   * @description 初始化编辑器
   */
  useEffect(() => {
    onInitEditor();
    return () => {
      quillRef.current?.off('text-change');
      quillRef.current?.destroy();
    };
  }, []);
  return (
    <div className={classnames('tzRichText', className)}>
      <div
        ref={_editorDomRef}
        className="quillEditorContent"
        onFocus={onFocus}
        style={{
          height: `${dictConfig.height || 400}px`,
        }}
      />
    </div>
  );
});

export default QuillEditor;
