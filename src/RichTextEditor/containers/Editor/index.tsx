import React, { useEffect, useMemo } from 'react';
import ReactQuill from 'react-quill-v2.0';
import Quill from 'quill';
import classNames from 'classnames';
import 'react-quill-v2.0/dist/quill.snow.css';
import QuillBetterTable from 'quill-better-table';
import ImageUploader from 'quill2-image-uploader';
import 'quill-better-table/dist/quill-better-table.css';
// import 'quill2-image-uploader/dist/quill.imageUploader.min.css';
import { defaultArray } from '@utils/tools';

const TABLE_COLORS_OPTIONS = ['green', 'red', 'yellow', 'blue', 'white'];

Quill.register({
  'modules/imageUploader': ImageUploader,
});
Quill.register(
  {
    'modules/better-table': QuillBetterTable,
  },
  true
);

export const editorValidator = (r, v, cb) => {
  if (v === '<p><br></p>') return cb('必填');
  cb();
};

interface IAppProps {
  value: string;
  onChange?: (value: string) => void;
  className?: string;
  placeholder?: string;
  readOnly?: boolean;
}

const WysiwygEditor: React.FC<IAppProps> = ({ value, onChange, className, placeholder, ...restProps }) => {
  const { dictConfig = {} } = restProps.configs.config?.baseConfig || {};
  /**
   * @description 上传图片
   * @param file 选择上传的图片文件
   * @returns 图片附件对应的附件地址
   */
  const uploadImg = file => {
    const { upload, download } = restProps.configs.context.apiConfigMap;
    console.log('uploadConfig', upload);
    const params = new FormData();
    params.append('files', file);
    params.append('categoryId', `/dhr/common/${upload.params.categoryId}`);
    params.append('catalogId', upload.params.catalogId);
    return new Promise(resolve => {
      restProps.utils
        .fetch({
          headers: {
            'Content-type': 'multipart/form-data',
          },
          url: upload?.url,
          method: 'post',
          data: params,
        })
        .then(res => {
          const fileIds = defaultArray(res.data.result.fileIds);
          resolve(fileIds.map(k => `${download.url}/${k}`));
        })
        .catch(err => {
          console.log('err', err);
          resolve('');
        });
    });
  };
  /**
   * @description 富文本编辑器实例
   */
  const quillRef = React.useRef();

  // 插入表格
  const handleInsertTable = () => {
    console.log('quillRef', quillRef, quillRef.current, quillRef.current?.getModule);
    // const tableModule = quillRef.current?.getModule('better-table');
    // tableModule.insertTable(3, 3);
  };
  // 配置table
  const quillTableOptions = useMemo(() => {
    const colorOptions = {
      color: {
        colors: TABLE_COLORS_OPTIONS,
        text: '单元格背景颜色：',
      },
    };
    return {
      'better-table': {
        operationMenu: {
          items: {
            insertColumnRight: {
              text: '向右插入列',
            },
            insertColumnLeft: {
              text: '向左插入列',
            },
            insertRowUp: {
              text: '向上插入行',
            },
            insertRowDown: {
              text: '向下插入行',
            },
            mergeCells: {
              text: '合并单元格',
            },
            unmergeCells: {
              text: '取消合并单元格',
            },
            deleteColumn: {
              text: '删除列',
            },
            deleteRow: {
              text: '删除行',
            },
            deleteTable: {
              text: '删除表格',
            },
          },
          ...colorOptions,
        },
      },
      keyboard: {
        bindings: QuillBetterTable.keyboardBindings,
      },
    };
  }, []);

  /**
   * @description 富文本编辑器modules配置
   */
  // const modules = useMemo(() => {
  //   return {
  //     toolbar: {
  //       container: [
  //         [{ header: [1, 2, 3, 4, 5, 6, false] }],
  //         [{ font: [] }],
  //         ['bold', 'italic', 'underline', 'strike'],
  //         ['blockquote', 'code-block'],
  //         [{ list: 'ordered' }, { list: 'bullet' }],
  //         [{ indent: '-1' }, { indent: '+1' }],
  //         [{ direction: 'rtl' }],
  //         ['link', 'image'],
  //         [{ color: [] }, { background: [] }],
  //         [{ align: [] }],
  //         ['clean'],
  //         ['table'],
  //       ],
  //       handlers: {
  //         table: handleInsertTable,
  //       },
  //     },
  //     imageUploader: {
  //       upload: uploadImg,
  //     },
  //     table: false,
  //     ...quillTableOptions,
  //   };
  // }, []);
  const modules = {
    toolbar: {
      container: [
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ font: [] }],
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        ['link', 'image'],
        [{ color: [] }, { background: [] }],
        [{ align: [] }],
        ['clean'],
        ['table'],
      ],
      handlers: {
        table: handleInsertTable,
      },
    },
    imageUploader: {
      upload: uploadImg,
    },
    table: false,
    ...quillTableOptions,
  };

  /**
   * @description 富文本编辑器内容变化
   * @param content 富文本编辑器内容
   */
  const handleContentChange = content => {
    onChange?.(content);
  };

  console.log(
    'quillRef.current?.getModule',
    quillRef.current?.getEditor().getModule,
    quillRef.current?.getEditor().getModule('better-table')
  );

  return (
    <div className={classNames('tzRichText', className)}>
      <ReactQuill
        // ref={quillRef}
        ref={el => {
          console.log('el', el);
          quillRef.current = el;
        }}
        value={value}
        modules={modules}
        placeholder={placeholder}
        onChange={handleContentChange}
      >
        <div
          style={{
            height: `${dictConfig.height || 400}px`,
          }}
        />
      </ReactQuill>
    </div>
  );
};

export default WysiwygEditor;
