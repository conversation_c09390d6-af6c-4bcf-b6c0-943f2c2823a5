import React, { useMemo } from 'react';
import ReactQuill from 'react-quill-v2.0';
import classNames from 'classnames';
import 'quill/dist/quill.snow.css';

export const editorValidator = (r, v, cb) => {
  if (v === '<p><br></p>') return cb('必填');
  cb();
};

interface IAppProps {
  value: string;
  onChange?: (value: string) => void;
  className?: string;
  placeholder?: string;
  readOnly?: boolean;
}

const WysiwygEditor: React.FC<IAppProps> = ({ value, className, placeholder, ...restProps }) => {
  const quillRef = React.useRef();
  const modules = useMemo(() => {
    return {
      toolbar: [
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ font: [] }],
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        ['link', 'image'],
        [{ color: [] }, { background: [] }],
        [{ align: [] }],
        ['clean'],
      ],
    };
  }, []);

  return (
    <div className={classNames('tzRichText', className)}>
      <ReactQuill ref={quillRef} value={value} modules={modules} placeholder={placeholder}>
        <div
          style={{
            height: '100px',
          }}
        />
      </ReactQuill>
    </div>
  );
};

export default WysiwygEditor;
