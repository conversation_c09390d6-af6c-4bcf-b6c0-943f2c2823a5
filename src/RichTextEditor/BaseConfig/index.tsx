import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { height } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigHeight',
      curFormData?.dictConfigHeight || defFormData?.dictConfigHeight || height
    );
    // formRef?.current?.setFormItem?.(
    //   'dictConfigClassName',
    //   curFormData?.dictConfigClassName || defFormData?.dictConfigClassName || className
    // );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'inputNumber',
      key: 'dictConfigHeight',
      label: '编辑器高度',
      configs: {
        onBlur: () => {
          const dictConfigHeight = formRef?.current?.getFormItem?.('dictConfigHeight');
          context?.onConfirm?.('dictConfigHeight', dictConfigHeight);
          setDictConfig(formRef, 'height', dictConfigHeight, {
            context,
          });
        },
      },
    },
    // {
    //   type: 'select',
    //   key: 'dictConfigIsAllowEditDish',
    //   label: '是否允许编辑菜品',
    //   configs: {
    //     options: WHETHER_OPTIONS,
    //     onSelect: () => {
    //       const dictConfigIsAllowEditDish = formRef?.current?.getFormItem?.('dictConfigIsAllowEditDish');
    //       context?.onConfirm?.('dictConfigIsAllowEditDish', dictConfigIsAllowEditDish);
    //       setDictConfig(formRef, 'isAllowEditDish', dictConfigIsAllowEditDish, {
    //         context,
    //       });
    //     },
    //   },
    // },
    // {
    //   type: 'input',
    //   key: 'dictConfigClassName',
    //   label: '样式类名',
    //   configs: {
    //     onBlur: () => {
    //       const dictConfigClassName = formRef?.current?.getFormItem?.('dictConfigClassName');
    //       context?.onConfirm?.('dictConfigClassName', dictConfigClassName);
    //       setDictConfig(formRef, 'className', dictConfigClassName, {
    //         context,
    //       });
    //     },
    //   },
    // },
  ];
};

export default customCptBaseConfig;
