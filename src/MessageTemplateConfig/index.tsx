import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Row, Col, Tree, Form, Input, message, Spin } from 'antd';

import sceneApis from '@apis/scene';
import useFetch from '@hooks/useFetch';
import { defaultObj } from '@utils/tools';

import QuillEditor from '@components/QuillEditor';

import { NOTIFICATION_CHANNEL_TYPE } from '@constants/common';

import './style.less';

const { Item: FormItem } = Form;

const defaultLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

export interface IAppProps {
  ref?: any;
  sceneId: string;
  channelType: string;
  templateContent?: string;
  templateSubject?: string;
}
const MessageTemplate: React.FC<IAppProps> = forwardRef((props, ref) => {
  const { sceneId, channelType, templateContent, templateSubject } = props;
  const [form] = Form.useForm();
  const quillRef = useRef(null);
  const titleRef = useRef(null);
  const currentInputRef = useRef(null);

  const [treeData, setTreeData] = useState<any[]>([]);

  useImperativeHandle(ref, () => ({
    validateFields: () =>
      new Promise(resolve => {
        form.validateFields().then(formValues => {
          resolve(formValues);
        });
      }),
  }));

  useEffect(() => {
    if (form && templateSubject) {
      form.setFieldsValue({
        title: templateSubject,
        content: templateContent,
      });
    }
  }, [form, templateSubject, templateContent]);

  const {
    Data: sceneData = {},
    Loading: treeLoading,
    runAction: onFetchTreeList,
  } = useFetch({
    ...sceneApis.sceneInputTree,
    isImmediate: false,
  });
  const { list: treeList = [] } = defaultObj(sceneData);

  /** 组装treeList */
  const handleAssembleTreeData = (list, cb) => {
    /** 特殊规则,  */
    const newDataSource = list.map(k => {
      const newItems = {
        ...k,
        code: k.code,
        key: k.id,
        title: k.name,
        source: k.type,
        disabled: k.column?.length > 0,
        children:
          k.column?.map(childColumn => ({
            parentCode: k.code,
            code: childColumn.columnCode,
            key: childColumn.columnId,
            title: childColumn.columnName,
          })) || [],
      };
      return newItems;
    });
    cb(newDataSource);
  };

  useEffect(() => {
    sceneId &&
      onFetchTreeList({
        params: {
          sceneId,
        },
      });
  }, [sceneId]);

  useEffect(() => {
    handleAssembleTreeData(treeList || [], (data: any) => {
      setTreeData(data);
    });
  }, [treeList?.length]);

  /** 富文本插入变量 */
  const handleInsertQuill = useCallback(key => {
    const quill = quillRef.current?.getQuill();
    const rang = quill.getSelection();
    if (!rang) {
      return message.warning('先把鼠标光标定到对应位置再插入字段哦～');
    }
    quill.insertText(rang.index, `\${${key}}`);
  }, []);

  const handleInsertVariate = useCallback(
    key => {
      const focusType = currentInputRef.current;
      if (!focusType) {
        return message.warning('先把鼠标光标定到对应位置再插入字段哦～');
      }
      if (focusType === 'title') {
        const titleEle = titleRef.current?.input || {};
        const cursorIndex = titleEle.selectionStart || 0;
        const title = titleEle.value || '';
        const insertText = `\${${key}}`;
        const newTitle = title.slice(0, cursorIndex) + insertText + title.slice(cursorIndex);
        form.setFieldValue('title', newTitle);
        return;
      }

      handleInsertQuill(key);
    },
    [titleRef.current]
  );

  const handleSelectTree = useCallback((selectedKeys, e) => {
    const code = e?.node?.code;
    const parentCode = e?.node?.parentCode;
    const selectCode = parentCode ? `${parentCode}.${code}` : code;
    code && handleInsertVariate(selectCode);
  }, []);

  return (
    <div className="dhr-message-template-wrap">
      <Row>
        <Col span={4}>
          <div className="dhr-message-template-tree-wrap">
            <Spin spinning={treeLoading}>
              <Tree onSelect={handleSelectTree} treeData={treeData} />
            </Spin>
          </div>
        </Col>
        <Col span={20}>
          <Form form={form} {...defaultLayout}>
            <FormItem name="title" label="通知主题">
              <Input
                ref={titleRef}
                placeholder="请输入主题"
                onFocus={() => {
                  currentInputRef.current = 'title';
                }}
              />
            </FormItem>
            {[NOTIFICATION_CHANNEL_TYPE.SMS, NOTIFICATION_CHANNEL_TYPE.WECHAT].includes(channelType) && (
              <FormItem name="content" label="文本模板">
                <QuillEditor
                  ref={quillRef}
                  showToolbarOptions={false}
                  className="dhr-message-template-content"
                  onFocus={() => {
                    currentInputRef.current = 'quill';
                  }}
                />
              </FormItem>
            )}
            {channelType === NOTIFICATION_CHANNEL_TYPE.EMAIL && (
              <FormItem name="content" label="富文本模板">
                <QuillEditor
                  ref={quillRef}
                  className="dhr-message-template-content"
                  onFocus={() => {
                    currentInputRef.current = 'quill';
                  }}
                />
              </FormItem>
            )}
          </Form>
        </Col>
      </Row>
    </div>
  );
});

export default MessageTemplate;
