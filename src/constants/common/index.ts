// 字典映射
export const DICT_CODE_MAP_ID = {
  /** 启禁用 */
  DHR_COMMON_SWITCH: 'e9b3c765-7947-4926-85b2-7b46ad12be87',
  /** 开关 */
  DHR_COMMON_SWITCH_CASE: '82d426ad-8283-455b-aae5-2240c6769b06',
  /**  且或 */
  DHR_CNB_SALARY_SLIP_ANDOR: '64b7958d-a5e0-4198-a838-0d7cff9e40ac',
  /** 是否 */
  DHR_COMMON_WHETHER: 'f63acd6d913e4afeb3c0c5bb62c994a6',
  /** 引用字段类型 */
  DHR_CNB_SALARY_SLIP_TABLE_RELATION_TYPE: 'ff5fa65e-d20b-498e-bec3-fc7139055b82',
  /** 通知关联条件类型 */
  DHR_CNB_SALARY_SLIP_NOTIFY_CONDITION_TYPE: 'f3a4902f-4089-42a5-b4b1-08c768d675a5',
  /** 通知渠道 */
  DHR_NOTIFICATION_CHANNEL: '5047f0c4-cc6c-4184-b03f-e22eb8b3da60',
  /** 入职时间类型 */
  DHR_CNB_SALARY_SLIP_ENTRANT_TIME_TYPE: '8507fb8e-3393-4556-8019-3a5af0381074',
  /** 薪资发放过程状态 */
  DHR_CNB_PROCESS_STATUS: '902ac000-5a9c-400d-aca8-9db2ca78db06',
  /** 薪资发放过程状态 */
  DHR_NOTIFICATION_STATUS: '431a5d9f-c88d-4e2c-83c1-901342e19b52',
  /** 数值精度 */
  DHR_CNB_NUM_PRECISION: 'b2aff328-1cab-4f77-8f47-dda53c5270b4',
  /** 进位规则 */
  DHR_CNB_CARRY_RULE: '8b2442dc-c34e-49de-9dfc-cad534c7164b',
  /** 有效状态 */
  DHR_COMMON_STATUS_TYPE: '2f6357c1ebf0439fb64af9775a2b83e4',
  /** 考勤-核算状态 */
  DHR_TMG_CALCULATE_CAL_STATUS: 'ba032e76-58a6-41a8-a36c-3c0cdc152166',
  /** 多胞胎情形 */
  DHR_TMG_LEAVE_MULTIPLE_BIRTH_SITUATION: '45bc7a47-8834-49a4-8ef5-f9660c2e84b4',
  /** 生育情形 */
  DHR_TMG_LEAVE_CHILDBIRTH_SITUATION: 'c94125f0-b52e-40da-8b25-847f9d4157a3',
  /** 流程业务状态 */
  DHR_TMG_LEAVE_FLOW_BIZ_STATUS: '9c947ebe-0ce1-49c0-86ec-02d166e25f97',
  /** 性别  */
  DHR_STA_GENDER_TYPE: 'f7924113-9906-45eb-a868-f5756935ba09',
  /** 政治面貌 */
  DHR_STA_POLITICAL_STATUS: '4146a39445da48c286db37c64a1323ca',
  /** 婚姻状况 */
  DHR_STA_MARITAL_STATUS: '764d8d1073ac488d8338d788ccbb06b6',
  /** 职级方案类型  */
  DHR_CAREERPATH_SCHEME_TYPE: '98660ce8-9c03-4f93-bb74-9bac6604e8ed',
  /** 角色类型  */
  DHR_TP_ROLE_TYPE: '5530e07e-2855-496e-a992-5a2447a5b27b',
  /** 能力项分类 */
  DHR_CAREERPATH_ITEM_CLASS: 'abd130eb-9e63-4850-97ae-7dc7e0187041',
  /** 审核线角色类型 */
  DHR_ORG_AUDIT_LINE_ROLE_TYPE: 'a2286a05-3bc6-4004-b18f-7fc02c4fc166',
  /** 计税属性 */
  DHR_CNB_TAX_ATTR: 'f2f73e09-201e-46f4-829e-d4144ae041c8',
};

export const REFERENCE_TYPE_CODE = {
  // 信息集
  INFO_SET: 'INFO_SET',
  // 薪资项目
  SALARY_PROJECT: 'SALARY_PROJECT',
};

export const NOTIFY_CONDITION_TYPE = {
  // 	任职状态
  EMP_STATUS: 'EMP_STATUS',
  // 	薪资组
  SALARY_GROUP: 'SALARY_GROUP',
  // 	薪资结构类型
  SALARY_STRUCTURE: 'SALARY_STRUCTURE',
  // 入职时间类型
  ENTRANT_TIME: 'ENTRANT_TIME',
  // 社保缴纳方式
  SI_PAY_TYPE: 'SI_PAY_TYPE',
};

// 通知渠道
export const NOTIFICATION_CHANNEL_TYPE = {
  /** 企微通知 */
  WECHAT: 'WECHAT',
  /** 邮件通知 */
  EMAIL: 'EMAIL',
  /** 短信 */
  SMS: 'SMS',
};

// 发布方式
export const PUBLISH_TYPE = {
  // 邮件
  onEmail: '0',
  // 自助端
  onMobileTerminal: '1',
  // 企微
  onQywx: '2',
};

export const ERROR_TYPE_TRANSLATION = {
  Error: '错误',
  EvalError: 'eval函数错误',
  InternalError: '内部错误',
  RangeError: '范围错误',
  ReferenceError: '引用错误',
  SyntaxError: '语法错误',
  TypeError: '类型错误',
  URIError: 'URI处理错误',
  AggregateError: '聚合错误', // ES2022引入的新错误类型，表示一组相关错误

  // DOM相关的错误类型（非ECMAScript标准，但在浏览器环境中常见）
  DOMException: 'DOM异常',

  // 其他自定义或框架特定的错误类型示例
  // MyCustomError: '自定义错误',
};

// 排班组件-添加数据类型
export const SHIFT_TABLE_ADD_DATA_TYPE = {
  DICT: 'dict', // 数据字典
  FORM_LIST: 'formList', // 表单列表
};

// 排班组件- 数据勾选类型
export const SHIFT_TABLE_SELECT_TYPE = {
  MULTIPLE: 'multiple', // 多选
  RADIO: 'radio', // 单选
};

// 储存时间格式
export const TIME_PICKER_FORMAT_TYPE = {
  MINUTE: 'minute', // 分钟
  SECOND: 'second', // 格式化到秒
  TIMESTAMP: 'timestamp', // 时间戳
  MILLISECOND: 'millisecond', // 格式化到毫秒
};

// 个人信息卡片底部信息展示方式
export const EMPLOYEE_INFO_BOTTOM_SHOW_TYPES = {
  // 键值对
  KEY_VALUE: 'keyValue',
  // 按值
  VALUE: 'value',
};

// 日期属性类型
export const DATE_ATTR_TYPE = {
  // 节假日
  HOLIDAYS: 'HOLIDAYS',
  // 工作日
  WORK_DAY: 'WORK_DAY',
  // 休息日
  REST_DAY: 'REST_DAY',
};

// 日期属性对应颜色
export const DATE_ATTR_TYPE_MAP_COLORS = {
  // 工作日
  [DATE_ATTR_TYPE.WORK_DAY]: 'default',
  // 节假日
  [DATE_ATTR_TYPE.HOLIDAYS]: 'processing',
  // 休息日
  [DATE_ATTR_TYPE.REST_DAY]: 'volcano',
};

// tz组件模式
export const WULI_MODE = {
  VIEW: 'view', // 仅查看
  EDIT: 'edit', // 编辑
  SEARCH: 'search', // 搜索
};

export const CONTAINER_TYPE = {
  TABLE: 'table', // 明细表
};

export const FORMAULA_EDITOR_MODE = {
  CNB: 'CNB', // 薪酬
  TMG: 'TMG', // 考勤
};
// 页面编码
export const CSB_VIEW_CODES = {
  PAYROLL_PUBLISH: '7lr0obbt_w8w18rfh_payroll_publish', // 工资条发放
  CNB_EMP_PAY: '7lr0obbt_w8w18rfh_payroll_publish_emp', // 发薪人员
  PAYROLL_NOTICE_PROGESS: '7lr0obbt_w8w18rfh_payroll_publish_progess', // 工资条发放通知进度
  /** 人才档案详情 */
  TALENT_DETAIL_ARCHIVES: 'b5cbqdyq_talent_manage_archive_detail',
  /** 人才标签的人才档案详情 */
  TALENT_DETAIL_TAG: 'b5cbqdyq_talent_manage_tags_detail',
};

/** 是否 */
export const WHETHER_OPTIONS = [
  {
    label: '是',
    key: '1',
    value: '1',
  },
  {
    label: '否',
    key: '0',
    value: '0',
  },
];

// 人才档案详情模块类型
export const TALENT_DETAIL_MODULE_TYPES = {
  /** 基础信息 */
  EMPLOYEE_BASE: 'EMPLOYEE_BASE',
  /** 人才标签 */
  TALENT_TAGS: 'TALENT_TAGS',
  /** 绩效 */
  PERFORMANCE: 'PERFORMANCE',
  /** 内部经历 */
  TURNOVER: 'TURNOVER',
  /** 外部经历 */
  SOCIAL_RESUME: 'SOCIAL_RESUME',
  /** 近期访谈 */
  RECENT_INTERVIEW: 'RECENT_INTERVIEW',
  /** 培训经历 */
  TRAIN: 'TRAIN',
  /** 关键事项 */
  KEY_ITEM: 'KEY_ITEM',
  /** 人才盘点 */
  CAPABILITY_REPORT: 'CAPABILITY_REPORT',
  /** 继任地图 */
  SUCCEED_MAP: 'SUCCEED_MAP',
};

// 文件上传类型
export const UPLOAD_LIST_TYPE = {
  TEXT: 'text',
  PICTURE: 'picture',
  //
  PICTURE_CARD: 'picture-card',
};

// 文件上传限制文件格式
export const UPLOAD_LIMIT_FILE_TYPE = {
  JPG: 'jpg',
  PNG: 'png',
  JPEG: 'jpeg',
  GIF: 'gif',
  PDF: 'pdf',
  XLS: 'xls',
  XLSX: 'xlsx',
  XLR: 'xlr',
  XLW: 'xlw',
  XLAM: 'xlam',
  XLT: 'xlt',
  XLTM: 'xltm',
  XLTX: 'xltx',
  XLSB: 'xlsb',
  XLSM: 'xlsm',
};

/** 附件解析类型 */
export const FILE_PARSE_TYPE = {
  /** 身份证 */
  ID_CTF: 'ID_CTF',
  /** 离职证明 */
  EXIT_CTF: 'EXIT_CTF',
  /** 学历 */
  EDU_CTF: 'EDU_CTF',
};

/** 附件解析类型 */
export const FILE_PARSE_TYPE_NAME = {
  /** 身份证 */
  ID_CTF: '身份证',
  /** 离职证明 */
  EXIT_CTF: '离职证明',
  /** 学历 */
  EDU_CTF: '学历',
};

/** 高德jscodeh转发 */
export const AMAP_SERVICE_HOST =
  '/portal_apis/common/proxy/lcpGw/tz_api/dhr_api/hcm/dhr-lcp-hcm/admin/v1/administration/area/_AMapService';

export const AMAP_AKEY = 'f10c0a1a7e488728992a8630f666ae75';

// 下载链接前缀
export const DOWNLOAD_URL_PREFIX = 'https://itapis.cvte.com/cfile/c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3/v2/download';

/** 员工类型 */
export const EMP_TYPES = {
  /** 正编/正式员工 */
  REGULAR_EMPLOYEE: '4c8f73ae6b5a4d64abfd0d24da859170',
  /** 专项实习生 */
  SPECIAL_INTERNSHIP: 'abb6c5b007604f1a865def0bc8b073d1',
  /** offer实习 */
  OFFER_INTERNSHIP: '49124e07f5d44358bc78066bc9a545aa',
  /** 体验实习 */
  EXPERIENCE_INTERNSHIP: '999f0645ac5648849aba848e4104be0f',
  /** 长期实习 */
  LONG_TERM_INTERNSHIP: '7161cc0f158549899e104eb7713d1182',
};

/** 用工类型 */
export const ENTRY_EMPLOYMENT_TYPE = {
  /** 外包人员 */
  OUTSIDE: 'OUTSIDE',
  /** 实习生 */
  INTERN: 'INTERN',
  /** 正式员工 */
  INSIDE: 'INSIDE',
};
