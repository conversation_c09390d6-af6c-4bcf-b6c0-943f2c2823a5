// 日期性质
export const DAY_TYPES = {
  /** 休息日 */
  REST_DAY: 'REST_DAY', // 休息日
  /** 工作日 */
  WORK_DAY: 'WORK_DAY',
  /** 节假日 */
  HOLIDAYS: 'HOLIDAYS',
  /** 入职日 */
  ENTRY_DAY: 'ENTRY_DAY',
  /** 非入职日 */
  UN_ENTRY_DAY: 'UN_ENTRY_DAY',
};

// 日期性质对应的中文

export const ABSENCE_CATEGORY = {
  PARENT_CHILD_61: '亲子假（六一儿童假）',
  FUNERAL: '丧假',
  ABORTION: '流产假',
  PATERNITY: '陪产假',
  MATERNITY: '产假',
  BIRTH: '产检假',
  MARRIAGE: '婚假',
  PARENT_CHILD: '亲子假',
  PSYCHOLOGY: '心理假',
  FEMALE_PHYSICAL: '女性生理假',
  SICK: '病假',
  UNPAID_CASUAL: '无薪事假',
  PAID_CASUAL: '带薪事假',
  ANNUAL: '年假',
};

/**
 * 合作方 00d9feb181144f65b1bf93c17362746a
 * 人力外包 a757b39e195448b188bb9c0d4f401ced
 * 项目外包 adf4f0cc4cec4ccf8a548ba7df3ee6ff
 * 派遣 2dd73d4b9030498ea4adfa9afcac92cb
 * 顾问 78d50372fa5f4e08b455959591fd6539
 */
export const IGNORE_EMP_TYPES = [
  '00d9feb181144f65b1bf93c17362746a',
  'a757b39e195448b188bb9c0d4f401ced',
  'adf4f0cc4cec4ccf8a548ba7df3ee6ff',
  '2dd73d4b9030498ea4adfa9afcac92cb',
  '78d50372fa5f4e08b455959591fd6539',
];
