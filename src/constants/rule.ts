import { AttributeType } from './tzAttrTypes';
/**
 * @description: 规则类型
 */
export const OPERATION_TYPES = {
  eq: '等于',
  neq: '不等于',
  begin: '开头是',
  nbegin: '开头不是',
  end: '结尾是',
  nend: '结尾不是',
  contains: '包含',
  ncontains: '不包含',
  in: '等于任意一个',
  nin: '不等于任意一个',
  exists: '包含任意一个',
  existsall: '同时包含',
  empty: '为空',
  nepty: '不为空',
  gt: '大于',
  gte: '大于等于',
  lte: '小于等于',
  lt: '小于',
  between: '选择范围',
  today: '今天',
  lastDay: '昨天',
  thisWeek: '本周',
  lastWeek: '上周',
  thisMonth: '本月',
  lastMonth: '上月',
  withinThreeMonth: '三月内',
  childInAny: '归属于',
  childNotInAny: '不归属于',
};

/** 规则类型 */
export const RULE_TYPES = {
  DICTIONARY: 'DICTIONARY',
  FUNCTION: 'FUNCTION',
  INFO_SET: 'INFO_SET',
  BOOLEAN: 'Boolean',
  NUMBER: 'NUMBER',
  VARCHAR: 'VARCHAR',
};
/**  返回类型 */
export const RETURAN_TYPES = {
  BOOLEAN: 'Boolean',
  NUMBER: 'Number',
  STRING: 'String',
};

/** 条件编辑类型 */
export enum EDIT_TYPES {
  CONDITION = 'CONDITION', // 条件
  RESULT = 'RESULT', // 结果
}

/** 操作符 */
export const OPERATE_TYPES = {
  AND: '并且',
  OR: '或者',
};

/** 操作符 */
export const OPERATE_TYPES_CODES = {
  AND: '&&',
  OR: '||',
};

export const TZ_TYPES_TO_RULE_TYPES = {
  [AttributeType.TEXT]: 'STRING',
  [AttributeType.TEXTAREA]: 'STRING',
  [AttributeType.RADIO]: 'STRING',
  [AttributeType.CHECKBOX]: 'STRING',
  [AttributeType.SELECT]: 'STRING',
  [AttributeType.MULTI_SELECT]: 'STRING',
  [AttributeType.SEARCH]: 'STRING',
  [AttributeType.TREE]: 'STRING',
  [AttributeType.DATE]: 'DATE',
  [AttributeType.DATE_RANGE]: 'DATE',
  [AttributeType.INT]: 'NUMBER',
  [AttributeType.ORG]: 'ORG',
  String: 'STRING',
  STRING: 'STRING',
};

// export const SCENE_VARIABLE_TYPES = {

// }
