export const attrType = [
  {
    label: '单行文本',
    value: 'TEXT',
    key: 'TEXT',
    compType: 'input',
  },
  {
    label: '多行文本',
    value: 'TEXTAREA',
    key: 'TEXTAREA',
    compProp: {
      type: 'textarea',
      // col: 24,
      // labelCol: 3,
      // wrapperCol: 19,
    },
  },
  {
    label: '数字',
    value: 'INT',
    key: 'INT',
    compType: 'inputNumber',
  },
  {
    label: '日期',
    value: 'DATE',
    key: 'DATE',
    compType: 'datePicker',
  },
  {
    label: '日期区间',
    value: 'DATE_RANGE',
    key: 'DATE_RANGE',
    compProp: {
      type: 'rangePicker',
      // col: 16,
      // labelCol: 5,
      // wrapperCol: 18,
    },
  },
  {
    label: '单选框',
    value: 'RADIO',
    key: 'RADIO',
    compType: 'select',
    compConfigs: {
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    label: '复选框',
    value: 'CHECKBOX',
    key: 'CHECKBOX',
    compType: 'select',
    compConfigs: {
      mode: 'multiple',
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    label: '下拉单选',
    value: 'SELECT',
    key: 'SELECT',
    compType: 'select',
    compConfigs: {
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    label: '下拉多选',
    value: 'MULTI_SELECT',
    key: 'MULTI_SELECT',
    compType: 'select',
    compConfigs: {
      mode: 'multiple',
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    label: '单选搜索',
    value: 'SEARCH',
    key: 'SEARCH',
  },
  {
    label: '多选搜索',
    value: 'MULTI_SEARCH',
    key: 'MULTI_SEARCH',
  },
  // {
  //   label: "树形单选搜索",
  //   value: "TREE_SEARCH",
  //   key: "TREE_SEARCH",
  //   compType: "treeSelect",
  // },
  // {
  //   label: "树形多选搜索",
  //   value: "MULTI_TREE_SEARCH",
  //   key: "MULTI_TREE_SEARCH",
  //   compType: "treeSelect",
  //   compConfigs: { treeCheckable: true },
  // },
  {
    label: '树形单选',
    value: 'TREE',
    key: 'TREE',
    compType: 'treeSelect',
    compConfigs: {
      showSearch: true,
      treeNodeFilterProp: 'label',
    },
  },
  {
    label: '树形多选',
    value: 'MULTI_TREE',
    key: 'MULTI_TREE',
    compType: 'treeSelect',
    compConfigs: {
      treeCheckable: true,
      showSearch: true,
      treeNodeFilterProp: 'label',
    },
  },
  { label: '图片上传', value: 'MULTI_PICTURES', key: 'MULTI_PICTURES' },
  { label: '附件上传', value: 'MULTI_FILES', key: 'MULTI_FILES' },
  {
    label: '文本标签',
    value: 'TEXT_LABEL',
    key: 'TEXT_LABEL',
    compType: 'select',
    compConfigs: { mode: 'tags', showSearch: true, optionFilterProp: 'label' },
  },
  {
    label: '多标签输入',
    value: 'MULTI_LABEL',
    key: 'MULTI_LABEL',
    compType: 'select',
    compConfigs: { mode: 'tags', showSearch: true, optionFilterProp: 'label' },
  },
  { label: '单标签输入', value: 'LABEL', key: 'LABEL', compType: 'tagLabel' },
  { label: '人员单选', value: 'PERSON', key: 'PERSON' },
  { label: '人员多选', value: 'MULTI_PERSON', key: 'MULTI_PERSON' },
  { label: '组织单选', value: 'ORGANIZTION', key: 'ORGANIZTION' },
  { label: '组织多选', value: 'MULTI_ORGANIZTION', key: 'MULTI_ORGANIZTION' },
  // { label: "定制组件", value: "CUSTOM_COMP", key: "CUSTOM_COMP" },
  // { label: "定制组件多值", value: "CUSTOM_COMP_MULTI", key: "CUSTOM_COMP_MULTI" },
  // 2022.07.25 新增内置组件类型 --yuanzihan
  { label: '流水号', value: 'SERIAL_NUMBER', key: 'SERIAL_NUMBER', compType: 'input', compConfigs: { disabled: true } },
];
export const AttributeType = {
  CHECKBOX: 'CHECKBOX',
  DATE: 'DATE',
  DATE_RANGE: 'DATE_RANGE',
  INT: 'INT',
  MULTI_FILES: 'MULTI_FILES',
  MULTI_LABEL: 'MULTI_LABEL',
  MULTI_ORGANIZTION: 'MULTI_ORGANIZTION',
  MULTI_PERSON: 'MULTI_PERSON',
  MULTI_PICTURES: 'MULTI_PICTURES',
  MULTI_SEARCH: 'MULTI_SEARCH',
  MULTI_SELECT: 'MULTI_SELECT',
  // MULTI_TREE_SEARCH: "MULTI_TREE_SEARCH",
  MULTI_TREE: 'MULTI_TREE',
  ORGANIZTION: 'ORGANIZTION',
  PERSON: 'PERSON',
  RADIO: 'RADIO',
  SEARCH: 'SEARCH',
  SELECT: 'SELECT',
  TEXT: 'TEXT',
  TEXTAREA: 'TEXTAREA',
  TEXT_LABEL: 'TEXT_LABEL',
  LABEL: 'LABEL',
  // TREE_SEARCH: "TREE_SEARCH",
  TREE: 'TREE',
  // 2022.07.25 新增内置组件类型 --yuanzihan
  SERIAL_NUMBER: 'SERIAL_NUMBER',
  // 富文本
  RICH_TEXT: 'RICH_TEXT',

  ORG: 'ORG',

  // 关联数据
  RELATE_DATA: 'RELATE_DATA',
};
