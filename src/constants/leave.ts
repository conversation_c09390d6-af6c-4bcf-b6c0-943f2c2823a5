// 工作交接相关模块编码
export const LEAVE_JOIN_MODULE = {
  EAM: 'EAM', // 资产详情
  OA: 'OA', // 待办流程详情
  FEE: 'FEE', // 费控未结单据
  JIRA: 'JIRA', // JIRA待办任务
  BABY: 'BABY', // 幼早教信息查询
  WORK_HANDOVER: 'WORK_HANDOVER', // 工作交接信息
  TUTOR_INFO: 'TUTOR_INFO', // 导师
  EXT_LEAD: 'EXT_LEAD', // 外包
};

// 离职类型
export const EXIT_TYPE = {
  SELF: 'SELF', // 自主离职
  SPECIAL: 'FAST', // 快速离职
  HR_SUBMIT: 'HR_SUBMIT', // 人事HR复审
};

// 签署文件类型
export const SIGN_FILE_TYPE = {
  CHECK_LIST: 'CHECK_LIST', // 工作交接单
  EXIT_APPLY: 'EXIT_APPLY', // 离职申请书
  DELAY_DORMITORY: 'DELAY_DORMITORY', // 	延迟退宿申请书
  DEDUCTED_SI_PF: 'DEDUCTED_SI_PF', // 社保公积金代扣承诺函
};

// 离职测算类型
export const LEAVE_CALCULATE_TYPE = {
  EXIT_SALARY: 'exitSalary', // 离职当月薪资信息
  EXIT_SECURITY: 'exitSecurity', // 社保公积金信息
  EXIT_COMPENSATE: 'exitCompensate', // 离职补偿信息
  EXIT_COMPETE: 'exitCompete', // 离职竞业限制费
  EXIT_ANNUAL_LEAVE: 'exitAnnualLeave', // 离职未休年假测算
};
