/** 公式编辑源 */
export enum EDataSourceType {
  DS = 'DS', // 信息集
  CALC_FUNC = 'CALC_FUNC', // 计算函数
  XZ = 'XZ', // 薪酬项目
  PERIOD = 'PERIOD', // 薪资期间
  CALC_ITEM = 'CALC_ITEM', // 计算项
  AZ = 'AZ', // 考勤项目
  GET_SALARY_SET_HID = 'GET_SALARY_SET_HID', // 工资套
}

/** 数据源颜色映射 */
export const COLOR_MAPPINGS = {
  [EDataSourceType.DS]: 'highlight_info_set',
  [EDataSourceType.CALC_FUNC]: 'highlight_calc_func',
  [EDataSourceType.XZ]: 'highlight_salary_project',
  [EDataSourceType.PERIOD]: 'highlight_salary_period',
  [EDataSourceType.CALC_ITEM]: 'highlight_calc_item',
  [EDataSourceType.AZ]: 'highlight_tmg_item',
  [EDataSourceType.GET_SALARY_SET_HID]: 'highlight_salary_set',
};

/** 薪资发放过程状态  */

export const DHR_CNB_PROCESS_STATUS_MAP = {
  GRANT: 'GRANT', // 已发放
  PUBLISH: 'PUBLISH', // 已发布
};

// channel类型
export const DHR_CHANNEL_TYPE = {
  // 邮件
  EMAIL: 'EMAIL',
  // 企微
  WECHAT: 'WECHAT',
};

/** 信息源标记的颜色 */
export const CNB_FORMULA_COLOR_TYPES = {
  [EDataSourceType.DS]: '#ef3473',
  [EDataSourceType.CALC_FUNC]: '#e6ff00',
  [EDataSourceType.XZ]: '#65c86c',
  [EDataSourceType.PERIOD]: '#ffa800',
  [EDataSourceType.AZ]: '#41ffd3',
  [EDataSourceType.GET_SALARY_SET_HID]: '#ff0000',
};
