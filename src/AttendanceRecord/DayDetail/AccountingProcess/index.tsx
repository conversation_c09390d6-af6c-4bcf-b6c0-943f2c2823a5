import React from 'react';
import EmployeeInfoCard from '@src/EmployeeInfoCard';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';
import { toDateFormat } from '@utils/tools';

export interface IAppProps {}
const BaseInfo: React.FC<IAppProps> = props => {
  const data = {
    starTime: Date.now(),
    endTime: Date.now(),
  };

  const columns = [
    {
      title: '阶段',
      dataIndex: 'stage',
      key: 'stage',
    },
    {
      title: '步骤名称',
      dataIndex: 'stepName',
      key: 'stepName',
    },
    {
      title: '考勤项目编码',
      dataIndex: 'attendanceItemCode',
      key: 'attendance',
    },
    {
      title: '考勤项目名称',
      dataIndex: 'attendanceItemName',
      key: 'attendance',
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'value',
    },
  ];
  return (
    <div>
      <EmployeeInfoCard employeeId="" fetchUrl="" configs={{}} />
      <div
        style={{
          margin: '20px',
        }}
      >
        {toDateFormat(data.starTime)} 至 {toDateFormat(data.endTime)}，应出勤时长(AT_1030),共{}个步骤
      </div>
      <div className="CnbPayProcessWrap">
        <WULIWholeTable pagination canSelect multiSelect rowKey="id" columns={columns} data={[]} />
      </div>
    </div>
  );
};

export default BaseInfo;
