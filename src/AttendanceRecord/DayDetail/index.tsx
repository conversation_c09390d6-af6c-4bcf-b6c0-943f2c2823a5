import React, { useCallback, useEffect, useMemo, useState } from 'react';
import moment from 'moment';
import { Spin } from 'antd';

import tmgApis from '@apis/tmg';
import commomApis from '@apis/common';
import useFetch from '@hooks/useFetch';
import useRowToCols from '@hooks/useRowToCols';
import { request as fetchApi } from '@utils/http';
import { IOriginalProps } from '../../types/index';
import excelTask from '@components/ExportLargeExcel';
import { exportMultiExcel, IExport } from '@utils/excel';
import { defaultObj, toDateFormat, toEndDate, toStartDate } from '@utils/tools';

import DisplayItemSettingModal from '@components/DisplayItemSettingModal';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

const filterFormKey = 'attendanceDayDetailFormKey';
export interface IAppProps extends IOriginalProps {
  channel: string;
  processId: string;
  systemHelper: {
    history: {
      push: any;
    };
  };
}

const DayDetail: React.FC<IAppProps> = props => {
  const { systemHelper } = props;

  const [dayTypeOptions, setDayTypeOptions] = useState<any[]>([]);
  const [allDynamicColums, setAllDynamicColums] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);
  const [employeeTypeOptions, setEmployeeTypeOptions] = useState<any[]>([]);
  const [dispalyItemSettingModalVisible, setDispalyItemSettingModalVisible] = React.useState(false);

  /** 请求过程 */
  const {
    Data: payroll,
    Loading: payrollListLoading,
    runAction: runActionFetchNotofyList,
  } = useFetch({
    ...tmgApis.attendanceDailyRecordList,
    params: {
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
      dateEnd: moment().endOf('D').valueOf(),
      dateBegin: moment().startOf('D').valueOf(),
    },
  });

  const { onSetDisplayItem, assignList, unAssignList } = useRowToCols({
    itemList: allDynamicColums,
    rowKey: 'id',
    rowNameKey: 'cName',
    defaultShowKeyValue: '1',
    sortKey: 'cDailyDisplayOrder',
    defaultShowKey: 'cDailyDefDisplay',
    assignListStorageNamespace: 'attendanceDailyDetailIds',
    unAssignListStorageNamespace: 'attendanceUnDailyDetailIds',
    onConfirm: () => {},
  });

  // 获取搜索参数
  const onGetSearchQuery = async () => {
    try {
      const formData = await WULIFormActions.get(filterFormKey)?.asyncValidate();
      console.log('WULIFormActions===', WULIFormActions, formData);
      const [startDate, endDate] = formData['date'] || [];
      const result = {
        ...formData,
        date: undefined,
        dateEnd: toEndDate(endDate),
        dateBegin: toStartDate(startDate),
        periodDate: toStartDate(formData['periodDate'], 'M'),
      };
      return result;
    } catch (error) {
      return false;
    }
  };

  /** 请求过程 */
  const onFetchNotifyList = async (data: Record<string, any> = {}) => {
    const params = await onGetSearchQuery();
    if (!params) {
      return;
    }
    runActionFetchNotofyList({
      params: {
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...params,
        ...data,
      },
    });
  };

  // 获取列的数据
  const onFetchAttendanceItem = useCallback(() => {
    const params = {
      appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
      formClassId: '90d1e95d7c3d4a3ab3cb92abc50a04dd',
      onlyMain: true,
      keyType: 'CAMEL',
      pageSize: 500,
      mainParamsGroups: [
        {
          paramsList: [
            {
              operator: 'in',
              attrApi: 'C_ITEM_TYPE',
              value: ['DETAIL', 'ALL'],
            },
            {
              operator: '=',
              attrApi: 'C_STATUS',
              value: '1',
            },
          ],
        },
      ],
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const allDynamicColums = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.id,
          title: mainData?.cName,
          dataIndex: mainData?.id,
        }));
        setAllDynamicColums(allDynamicColums);
      },
    });
  }, []);

  // 获取日期类型
  const onFetchDayTypes = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
      formClassId: '82a3a3de2e384493982678b28bb3b6fa',
      mainParamsGroups: [
        {
          paramsList: [
            {
              operator: '=',
              attrApi: 'C_STATUS',
              value: '1',
            },
          ],
        },
      ],
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.id,
          label: mainData?.cName,
          dataIndex: mainData?.id,
        }));
        setDayTypeOptions(options);
      },
    });
  }, []);

  // 获取员工类型
  const onFetchEmployeeType = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      appId: '96dd1d629bb24193bb8630c9dcb61aad',
      formClassId: '95985d5e087d4cd58dc2e2b6556bd56a',
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const employeeTypeOptions = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.id,
          label: mainData?.cName,
          dataIndex: mainData?.id,
        }));
        setEmployeeTypeOptions(employeeTypeOptions);
      },
    });
  }, []);

  useEffect(() => {
    onFetchDayTypes();
    onFetchEmployeeType();
    onFetchAttendanceItem();
  }, []);

  const handleJumpDetail = useCallback(record => {
    const metaConfig = {
      compParams: {
        LCP_VIEW_TABLE_FLTER: {
          data: {
            C_DATE: record.date,
            C_EMP_NAME: record.empName,
            C_EMP_TYPE: record.empType,
            C_PERIOD_DATE: record.periodDate,
          },
        },
      },
    };
    systemHelper.history.push(
      `/portal/4schgytd/4schgytd_a81z9tpu_lzbgdgdt/list?metaConfig=${JSON.stringify(
        metaConfig
      )}&timestamp=${new Date().getTime()}`
    );
  }, []);

  // 导出Columns
  const exportColumns: any[] = useMemo(() => {
    const assembleColumns = assignList.map(columnItem => ({
      ...columnItem,
      render: (text, record) => {
        const itemView = record?.itemView || {};
        const value = itemView?.[columnItem.dataIndex]?.value;
        const result = typeof value === 'number' ? value : value || '-';
        return result;
      },
      formatteFn: (text, record) => {
        const itemView = record?.itemView || {};
        const value = itemView?.[columnItem.dataIndex]?.value;
        const result = typeof value === 'number' ? value : value || '-';
        return result;
      },
    }));
    return [
      {
        title: '别名',
        dataIndex: 'empName',
        key: 'empName',
      },
      {
        title: '域账号',
        dataIndex: 'empAccount',
        key: 'empAccount',
      },
      {
        title: '员工类型',
        key: 'empTypeName',
        dataIndex: 'empTypeName',
      },
      {
        title: '工号',
        dataIndex: 'empCode',
        key: 'empCode',
      },
      {
        title: '考勤期间',
        key: 'periodDate',
        dataIndex: 'periodDate',
        render: text => toDateFormat(text, 'YYYY-MM'),
        formatteFn: text => toDateFormat(text, 'YYYY-MM'),
      },
      {
        title: '归属日期',
        dataIndex: 'date',
        key: 'date',
        render: text => toDateFormat(text),
        formatteFn: text => toDateFormat(text),
      },
      {
        title: '日期类型',
        dataIndex: 'dayTypeName',
        key: 'dayTypeName',
      },
      ...assembleColumns,
    ];
  }, [assignList]);

  const columns = useMemo(() => {
    const assembleColumns = assignList.map(columnItem => ({
      ...columnItem,
      render: record => {
        const itemView = record?.data?.itemView || {};
        const value = itemView?.[columnItem.dataIndex]?.value;
        const result = typeof value === 'number' ? value : value || '-';
        return result;
      },
    }));
    return [
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        pinned: 'left',
        render: record => {
          const data = record?.data || {};
          return <a onClick={() => handleJumpDetail(data)}>明细项目</a>;
        },
      },
      {
        pinned: 'left',
        title: '别名',
        dataIndex: 'empName',
        key: 'empName',
      },
      {
        pinned: 'left',
        title: '域账号',
        dataIndex: 'empAccount',
        key: 'empAccount',
      },
      {
        pinned: 'left',
        title: '员工类型',
        dataIndex: 'empTypeName',
        key: 'empTypeName',
      },
      {
        pinned: 'left',
        title: '工号',
        dataIndex: 'empCode',
        key: 'empCode',
      },
      {
        pinned: 'left',
        title: '考勤期间',
        dataIndex: 'periodDate',
        key: 'periodDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM') || '-';
        },
      },
      {
        pinned: 'left',
        title: '归属日期',
        dataIndex: 'date',
        key: 'date',
        render: record => {
          const value = record?.value;
          return toDateFormat(value) || '-';
        },
      },
      {
        pinned: 'left',
        title: '日期类型',
        dataIndex: 'dayTypeName',
        key: 'dayTypeName',
      },
      ...assembleColumns,
    ];
  }, [assignList]);

  const filterFormItems: any[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'empName',
        label: '别名',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
        col: 8,
      },
      {
        type: 'select',
        key: 'empType',
        label: '员工类型',
        configs: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          optionFilterProp: 'label',
          options: employeeTypeOptions,
        },
        col: 8,
      },
      {
        type: 'monthPicker',
        key: 'periodDate',
        label: '考勤期间',
        configs: {
          allowClear: true,
          placeholder: '请选择',
        },
        col: 8,
      },
      {
        type: 'rangePicker',
        key: 'date',
        label: '归属日期',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          placeholder: '请选择',
        },
        col: 8,
      },
      {
        type: 'select',
        key: 'dayTypeId',
        label: '日期类型',
        configs: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          optionFilterProp: 'label',
          options: dayTypeOptions,
        },
        col: 8,
      },
    ],
    [employeeTypeOptions, dayTypeOptions]
  );

  const { pagination = {} } = defaultObj(payroll);

  const payrollList = useMemo(() => {
    const { list } = defaultObj(payroll);
    const newList = (list || []).map(listItem => ({
      ...listItem,
      rowId: `${listItem.empId}${listItem.date}${listItem.empType}${listItem.periodDate}`,
    }));
    return newList;
  }, [payroll]);

  const handleExport = async () => {
    if (selectedRows.length > 0) {
      const exportArr: IExport[] = [
        {
          data: selectedRows,
          sheetName: 'Sheet1',
          columns: exportColumns,
        },
      ];
      return exportMultiExcel(exportArr, '考勤记录日明细.xlsx');
    }
    const searchParams = await onGetSearchQuery();
    console.log('searchParams===', searchParams);

    const params = {
      fetchParams: {
        ...tmgApis.attendanceDailyRecordList,
        params: {
          ...searchParams,
        },
      },
      xlsxName: '考勤记录日明细',
      configs: {
        columns: exportColumns,
        maxPageSize: 300,
      },
    };
    excelTask.add(params);
  };

  const actionBtnItems: any[] = useMemo(
    () => [
      {
        key: 'displaySettings',
        content: '项目设置显示',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: () => setDispalyItemSettingModalVisible(true),
        },
      },
      {
        key: 'export',
        content: '导出',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleExport,
        },
      },
    ],
    [selectedRows, exportColumns, payrollList.length]
  );

  const handleOk = useCallback((_assignList, _unAssignList) => {
    onSetDisplayItem(_assignList, _unAssignList);
    setDispalyItemSettingModalVisible(false);
  }, []);

  return (
    <Spin spinning={payrollListLoading}>
      <div className="CnbPayProcessWrap">
        <WULIWholeTable
          pagination
          canSelect
          multiSelect
          columns={columns}
          data={payrollList}
          rowKey="rowId"
          paginationConfig={{
            current: pagination.pageNum,
            pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (pageNum, pageSize) => onFetchNotifyList({ pageNum, pageSize }),
          }}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            initialData: {
              date: [moment().startOf('D').valueOf(), moment().endOf('D').valueOf()],
            },
            onSearch: () => onFetchNotifyList(),
            onClear: () => {
              WULIFormActions.get(filterFormKey)?.reset();
              onFetchNotifyList();
            },
            defaultLayout: {
              col: 6,
              labelCol: 6,
              wrapperCol: 18,
            },
          }}
          action={actionBtnItems}
          selectedRows={selectedRows}
          afterCancelSelect={newRows => setSelectedRows(newRows)}
          onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => setSelectedRows(_selectedRows)}
        />
      </div>
      <DisplayItemSettingModal
        rowKey="id"
        rowNameKey="cName"
        title="考勤项目显示设置"
        onConfirm={handleOk}
        assignList={assignList}
        unAssignList={unAssignList}
        open={dispalyItemSettingModalVisible}
        onCancel={() => setDispalyItemSettingModalVisible(false)}
      />
    </Spin>
  );
};
export default DayDetail;
