/** 额外填充map */
export const genExtraSetValueMap = (extraSetValue: string) => {
  return extraSetValue?.split(';')?.reduce((acc, cur) => {
    const [field, key] = cur.split('=');
    acc[field] = key;
    return acc;
  }, {});
};

// 额外填充信息
export const getAdditionnalInfo = (selectedOption: Record<string, any>, extraSetValueMap: Record<string, any>) => {
  return extraSetValueMap
    ? Object.keys(extraSetValueMap).reduce((pre, cur) => {
      // key 为目标表单字段，value 为来源字段
      const _value = selectedOption[extraSetValueMap[cur]];
      pre[cur] = _value;
      return pre;
    }, {})
    : {};
};
