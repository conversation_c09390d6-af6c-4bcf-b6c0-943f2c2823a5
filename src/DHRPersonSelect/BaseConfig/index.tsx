import React from 'react';
import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { WHETHER_OPTIONS } from '@constants/common';
import DictSelect from '@components/DictSelect';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const {
      extraSetValue,
      searchKey,
      optionKey,
      optionLabel,
      optionLabelProp,
      modelType,
      isNeedPermission,
      subModelTypes,
    } = dictConfigByProps || {};

    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();
    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigExtraSetValue',
      curFormData?.dictConfigExtraSetValue || defFormData?.dictConfigExtraSetValue || extraSetValue
    );
    formRef?.current?.setFormItem?.(
      'dictConfigSearchKey',
      curFormData?.dictConfigSearchKey || defFormData?.dictConfigSearchKey || searchKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigOptionKey',
      curFormData?.dictConfigOptionKey || defFormData?.dictConfigOptionKey || optionKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigOptionLabel',
      curFormData?.dictConfigOptionLabel || defFormData?.dictConfigOptionLabel || optionLabel
    );
    // DHR_COMMON_SELECT_EMP_TYPE
    formRef?.current?.setFormItem?.({
      dictConfigIsNeedPermission:
        curFormData?.dictConfigIsNeedPermission || defFormData?.dictConfigIsNeedPermission || isNeedPermission,
      dictConfigModelType: curFormData?.dictConfigModelType || defFormData?.dictConfigModelType || modelType,
      dictConfigSubModelTypes:
        curFormData?.dictConfigSubModelTypes || defFormData?.dictConfigSubModelTypes || subModelTypes,
      optionLabelProp:
        curFormData?.dictConfigOptionLabelProp || defFormData?.dictConfigOptionLabelProp || optionLabelProp,
    });
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigSearchKey',
      label: '搜索字段',
      configs: {
        placeholder: '用于动态搜索',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigSearchKey');
          context?.onConfirm?.('dictConfigSearchKey', value);
          setDictConfig(formRef, 'searchKey', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigExtraSetValue',
      label: '额外赋值【选择项赋值表单字段】',
      configs: {
        placeholder: 'formField1=valueKey1;formField2=valueKey2',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigExtraSetValue');
          context?.onConfirm?.('dictConfigExtraSetValue', value);
          setDictConfig(formRef, 'extraSetValue', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigOptionKey',
      label: '选项OptionKey',
      configs: {
        placeholder: '',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigOptionKey');
          context?.onConfirm?.('dictConfigOptionKey', value);
          setDictConfig(formRef, 'optionKey', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigOptionLabel',
      label: '选项OptionLabel',
      configs: {
        placeholder: '样例：data.key1-data.key2',
        onBlur: e => {
          const value = formRef?.current?.getFormItem?.('dictConfigOptionLabel');
          console.log('ddd===', e.target.value, value);
          context?.onConfirm?.('dictConfigOptionLabel', value);
          setDictConfig(formRef, 'optionLabel', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigOptionLabelProp',
      label: '选项筛选key',
      configs: {
        placeholder: '筛选的key',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigOptionLabelProp');
          context?.onConfirm?.('dictConfigOptionLabelProp', value);
          setDictConfig(formRef, 'optionLabelProp', value, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigIsNeedPermission',
      label: '开启权限控制',
      configs: {
        options: WHETHER_OPTIONS,
        onSelect: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigIsNeedPermission');
          context?.onConfirm?.('dictConfigIsNeedPermission', value);
          setDictConfig(formRef, 'isNeedPermission', value, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigVisualFill',
      label: '开启虚拟填充',
      configs: {
        options: WHETHER_OPTIONS,
        onSelect: value => {
          formRef.current?.setFormItem?.('dictConfigVisualFill', value);
        },
      },
    },
    {
      type: 'custom',
      key: 'dictConfigModelType',
      label: '权限过滤类型',
      render: ({ value }) => (
        <DictSelect
          onSelect={(value: string) => {
            context?.onConfirm?.('dictConfigModelType', value);
            setDictConfig(formRef, 'modelType', value, {
              context,
            });
          }}
          fetchDict={context?.dataEntity?.fetchDict}
          dictCode="DHR_COMMON_SELECT_EMP_TYPE"
          value={value}
        />
      ),
    },
    {
      type: 'input',
      key: 'dictConfigSubModelTypes',
      label: '子类型',
      configs: {
        placeholder: 'JSON字符串格式',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigSubModelTypes');
          context?.onConfirm?.('dictConfigSubModelTypes', value);
          setDictConfig(formRef, 'subModelTypes', value, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
