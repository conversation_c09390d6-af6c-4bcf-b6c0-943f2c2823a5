import { request } from '@utils/http';

// 翻译接口
const translateUrl = '/admin/v1/buzrole/manage_employee/batch';

const batchTranslate = async props => {
  const { value: values, baseConfigs: recordConfigs } = props.configs;
  console.log('翻译configs=====', props);
  /** 没值的情况 */
  if (Object.values(values)?.filter(k => k).length === 0) {
    return {
      options: {},
      optionsMap: {},
    };
  }

  const targetKeys = [];
  const requestQueue = [];
  for (const targetKey in recordConfigs) {
    const dictConfig = recordConfigs[targetKey]?.dictConfig || {};
    if (values[targetKey]) {
      const objectIds: string[] = Array.isArray(values[targetKey]) ? values[targetKey] : [values[targetKey]];
      targetKeys.push(targetKey);
      requestQueue.push(
        request({
          url: translateUrl,
          method: 'post',
          data: {
            objectIds,
            modelType: dictConfig?.modelType,
            pageSize: objectIds?.length + 20 || 1000,
          },
        })
      );
    }
  }

  const result = await Promise.all(requestQueue);
  if (!result) {
    return {
      // map: {},
      options: {},
      optionsMap: {},
    };
  }

  let options = {};
  let optionsMap: Record<string, any> = {};
  const targetKeysLen = targetKeys.length;
  for (let index = 0; index < targetKeysLen; index++) {
    const empRes = result[index];
    if (empRes?.list) {
      let mapppings = {};
      let newList = [];
      const listLen = (empRes?.list || []).length;
      const dictConfig = recordConfigs[targetKeys[index]]?.dictConfig || {};
      const _optionKey = dictConfig.optionKey || 'empId';
      for (let empIndex = 0; empIndex < listLen; empIndex++) {
        const detail = empRes?.list?.[empIndex] || {};
        const newListItem = {
          ...detail,
          key: detail[_optionKey],
          value: detail[_optionKey],
        };
        mapppings[detail[_optionKey]] = newListItem;
        newList.push(newListItem);
      }
      options[targetKeys[index]] = newList;
      optionsMap[targetKeys[index]] = mapppings;
      mapppings = null;
      newList = null;
    }
  }

  console.log('mapp====', options, optionsMap);
  return {
    options,
    optionsMap,
  };
};
export default batchTranslate;
