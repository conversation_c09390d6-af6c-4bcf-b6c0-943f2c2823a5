import React, { useMemo, useEffect, useRef, useCallback } from 'react';
import { Select, Spin, Empty } from 'antd';
import axios from 'axios';
import * as R from 'ramda';

import hcmApis from '@apis/hcm';
import { request } from '@utils/http';
import useDebounce from '@hooks/useDebounce';
import { customTemplate, showErrNotification } from '@utils/tools';
import { genExtraSetValueMap, getAdditionnalInfo } from './helper';

import { WULI_MODE, CONTAINER_TYPE } from '@constants/common';

export interface IAppProps {
  onChange: (hid: string) => void;
  value: string;
  data: any;
  configs: {
    wuliMode: string;
    containerType: string;
    config: {
      baseConfig: {
        formCode: string;
        isEnabled: string;
        dictConfig: Record<string, any>;
      };
    };
    context: {
      getFormData: () => any;
      setFormData: (params: any) => void;
      getBatchTranslateCompleteFlag: () => void;
    };
  };
  setCache: (params: any) => void;
  getCache: () => void;
}

const DEFAULT_OPTION_LABEL_PROP = 'empName';

const SelectTree: React.FC<IAppProps> = props => {
  const { configs, onChange, value, data, setCache, getCache, parentType } = props;

  const { wuliMode, containerType, config, code: formItemCode, kind: formKind } = configs;
  // 是否在表单内，要排除表单列表
  const isInform = formKind === 'FORM_BASE';
  const pageId = configs.context?.config?.id;
  console.log('props=========', props);
  const { baseConfig } = config;
  const { dictConfig = {}, formCode, isEnabled } = baseConfig || {};
  const {
    searchKey,
    optionKey,
    modelType,
    optionLabel,
    subModelTypes,
    extraSetValue,
    optionLabelProp,
    isNeedPermission,
    visualFill,
  } = dictConfig;
  // 是否翻译完成
  // const isBatchTranslateCompleteFlag: any = configs.context?.getBatchTranslateCompleteFlag?.();
  // 额外填充数据
  const bacthTranslateFillCacheKey = `${pageId}_${formCode}_bacth_translate_fill`;

  // 初始化标志
  const initStatusRef = useRef(false);
  const cancelTokenSourceRef = useRef(null);
  const [options, setOptions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);

  const isView = wuliMode === WULI_MODE.VIEW;
  const isTable = CONTAINER_TYPE.TABLE === containerType; // 是否为明细表内
  console.log('containerType', containerType);

  const _optionKey = optionKey || 'empId';

  const _subModelTypes = useMemo(() => {
    const formData = configs.context?.getFormData?.() || {};
    let tempData = {};
    try {
      if (subModelTypes) {
        tempData = JSON.parse(subModelTypes);
      }
    } catch (error) {
      console.info('subModelTypes格式错误:', subModelTypes);
    }
    /**
     * 样例
     * {
     *  queryDate: 'fomrData-CRT_TIME', // 取当前主表单数据
     *  aa: 'xxxx'
     * }
     */
    Object.keys(tempData).forEach(key => {
      if (tempData[key]?.indexOf('formData-') > -1) {
        // delete tempData[key];
        tempData[key] = formData[tempData[key]?.replace('formData-', '')];
      }
    });
    return tempData;
  }, [subModelTypes]);

  // 标签生成
  const genLabel = useCallback((template, optionData) => {
    return template ? customTemplate(template, optionData) : optionData.name;
  }, []);

  // 缓存的数据
  const dataMapping = useMemo(() => {
    const cache: any = getCache?.();
    return cache;
  }, [value]);

  /** 额外填充map */
  const extraSetValueMap = useMemo(() => {
    return genExtraSetValueMap(extraSetValue);
  }, [extraSetValue]);

  // 搜索
  const handleSearch = useDebounce((searchValue?: string) => {
    if (cancelTokenSourceRef.current) {
      cancelTokenSourceRef.current.cancel('取消上次请求');
      cancelTokenSourceRef.current = null;
    }
    cancelTokenSourceRef.current = axios.CancelToken.source();
    setLoading(true);
    request({
      ...hcmApis.empSearch,
      params: {
        [searchKey || 'objectName']: searchValue,
        pageSize: 20,
        pageNum: 1,
        isNeedPermission,
        modelType,
        ..._subModelTypes,
      },
      cancelToken: cancelTokenSourceRef.current.token,
    })
      .then(res => {
        cancelTokenSourceRef.current = null;
        const list = res.list || [];
        if (!Array.isArray(list)) {
          return showErrNotification('数据格式错误');
        }
        const _options = list.map(item => ({
          ...item,
          key: item[_optionKey],
          value: item[_optionKey],
          label: genLabel(optionLabel, item),
        }));
        console.log('_options', _options);
        setOptions(_options);
      })
      .finally(() => {
        setLoading(false);
      })
      .catch(err => {
        console.log('err', err);
      });
  }, 200);

  // 判断对象是否为空
  const handleIsNil = useCallback(obj => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        return false;
      }
    }
    return true;
  }, []);

  // 缓存是否有值
  const isCacheDataMap = useMemo(() => {
    return !handleIsNil(dataMapping || {});
  }, [dataMapping]);

  const dataOptionLen = useMemo(() => (data?.options || []).length, [data?.options]);
  const dataMapLen = useMemo(() => Object.keys(data?.optionsMap || {}).length, [data?.optionsMap]);

  // 初始化options
  const handleInitOptions = params => {
    const { value, dataMapLen, dataOptionLen, data, optName, optKey, dataMapping, isCacheDataMap } = params;
    // 初始化标志
    // initStatusRef.current = true;
    let updateDetail;
    initStatusRef.current = true;
    // 优先走映射
    if (isCacheDataMap) {
      updateDetail = dataMapping?.[value];
    } else if (dataMapLen > 0) {
      updateDetail = data?.optionsMap?.[value];
    } // 次走options
    else if (dataOptionLen > 0) {
      updateDetail = (data?.options || []).find(optionItem => optionItem[_optionKey] === value);
    }
    updateDetail &&
      setOptions([
        {
          ...updateDetail,
          key: updateDetail[optKey],
          value: updateDetail[optKey],
          label: genLabel(optName, updateDetail),
        },
      ]);
    // 非列表或者明细表情况下设置缓存
    !isTable && updateDetail && configs.configs.utils?.cache?.setValue?.(value, updateDetail);
  };

  // 设置并触发联动
  const handleSetAndEmitFormData = (additionnalInfo: Record<string, any>) => {
    configs.context?.setFormData(additionnalInfo);
    Object.keys(additionnalInfo).forEach(key => {
      configs.relation?.emit({ attrCode: key, formCode, actionCode: 'change' }, {});
    });
  };

  /**
   * 初始化搜索翻译
   * @param value 人员ID
   */
  const onFetchData = (value: string) => {
    // 如果已存在值 - 则无需再请求
    const isNotPass = options.some(optionItem => optionItem[_optionKey] === value);
    if (!isNotPass) {
      request({
        method: 'get',
        url: '/admin/v1/buzrole/manage_employee',
        baseURL: '/apis/common/proxy/lcpGw/tz_api/dhr_api/hcm/dhr-lcp-hcm',
        params: {
          objectId: value,
          modelType,
          ..._subModelTypes,
        },
      }).then(res => {
        const list = res.list || [];
        if (!Array.isArray(list)) {
          return showErrNotification('数据格式错误');
        }

        const _options = list.map(item => ({
          ...item,
          key: item[_optionKey],
          value: item[_optionKey],
          label: genLabel(optionLabel, item),
        }));
        setOptions(_options);
        const target = list.find(item => item[_optionKey] === value) || {};
        // 非列表或者明细表情况下设置缓存
        !isTable && configs.configs.utils?.cache?.setValue?.(value, target);
        const additionnalInfo = getAdditionnalInfo(target, extraSetValueMap);
        extraSetValueMap && handleSetAndEmitFormData(additionnalInfo);
      });
    }
  };

  // 初始化options
  useEffect(() => {
    // 初始化批量翻译赋值
    const isPass =
      !!value && (dataMapLen > 0 || dataOptionLen > 0 || isCacheDataMap) && !initStatusRef.current && !isView;

    isPass &&
      handleInitOptions({
        data,
        value,
        dataMapLen,
        dataMapping,
        dataOptionLen,
        isCacheDataMap,
        optName: optionLabel,
        optKey: _optionKey,
      });
  }, [data, value, isCacheDataMap, dataMapping, dataOptionLen, dataMapLen, isView]);

  // set进来的值的场景
  useEffect(() => {
    // 是否已执行完翻译
    /** 明细表会延迟 */
    const hasNoTranslateValue =
      isInform &&
      !data.record &&
      (R.isNil(data.options) || R.isEmpty(data.options)) &&
      (R.isNil(data.optionsMap) || R.isEmpty(data.optionsMap));

    if (value && hasNoTranslateValue) {
      onFetchData(value);
    }
  }, [value, data.options, isInform]);

  // 翻译
  const handletranslateText = useCallback(
    value => {
      if (value) {
        // 非初始化状态只需要取options的值
        let detail = options.find(optionItem => optionItem[_optionKey] === value);
        // 优先映射查是否存在 且是初始化的时候
        // 次先 options
        if (!detail && isCacheDataMap) {
          detail = dataMapping?.[value];
        }
        if (!detail && dataMapLen > 0) {
          detail = data?.optionsMap?.[value];
        }
        if (!detail && dataOptionLen > 0) {
          detail = (data?.options || []).find(optionItem => optionItem[_optionKey] === value);
        }
        return detail?.[optionLabelProp || DEFAULT_OPTION_LABEL_PROP] || value;
      }
      return '';
    },
    [isCacheDataMap, options, optionLabelProp, _optionKey, dataOptionLen, dataMapLen]
  );

  /**
   * 虚拟填充功能
   */

  const extraFill = () => {
    if (pageId && value && visualFill === '1' && data.options?.length > 0) {
      const timer = sessionStorage.getItem(bacthTranslateFillCacheKey);
      if (!timer) {
        const newTimer = setTimeout(() => {
          // 进入批量填充逻辑
          const configsMapping = configs.context.configsMapping || {};
          const extraInfo = genExtraSetValueMap(extraSetValue);

          const visualFieldFillMap = Object.keys(extraInfo)
            .filter(field => {
              const mappingKey = `${formCode}:${field}`;
              return configsMapping[mappingKey]?.config?.baseConfig?.isVirtual === '1';
            })
            .reduce((acc, cur) => {
              acc[cur] = extraInfo[cur];
              return acc;
            }, {});
          if (isTable) {
            //明细表模式填充
            const detailFormData = configs.context?.getFormData()?.[formCode];
            if (!R.isNil(visualFieldFillMap) && detailFormData?.length > 0) {
              const _newDetailFormData = detailFormData.map(curRow => {
                const empId = curRow[formItemCode];
                const curOption = data.options.find(curOption => curOption[_optionKey] === empId);
                const _addtionalInfo = getAdditionnalInfo(curOption, visualFieldFillMap);
                return Object.assign(curRow, _addtionalInfo);
              });
              configs.context?.setFormData({
                [formCode]: _newDetailFormData,
              });
            }
          } else {
            // 非明细表模式
            const curOption = data.options.find(curOption => curOption[_optionKey] === value);
            const _addtionalInfo = getAdditionnalInfo(curOption, visualFieldFillMap);
            if (!R.isEmpty(_addtionalInfo)) {
              configs.context?.setFormData(_addtionalInfo);
            }
          }
          sessionStorage.removeItem(bacthTranslateFillCacheKey);
        }, 1000);
        sessionStorage.setItem(bacthTranslateFillCacheKey, newTimer.toString());
      }
    }
  };
  useEffect(() => {
    extraFill();
  }, [pageId, isTable, value, visualFill, data.options]);

  const handleChange = value => {
    const target = options.find(item => item[_optionKey] === value) || {};
    const additionnalInfo = getAdditionnalInfo(target, extraSetValueMap);
    /**
     * 明细表模式
     * 1、更新缓存，用于展示翻译
     * 2、更新明细表数据
     */
    if (isTable) {
      value &&
        setCache({
          [value]: target,
        });
      if (configs.context?.setFormData) {
        const detailList = configs.context?.getFormData()?.[formCode];
        const curRow = detailList[data.rowIndex];
        const newRow = Object.assign(curRow, additionnalInfo);
        detailList[data.rowIndex] = newRow;
        configs.context?.setFormData({
          [formCode]: detailList,
        });
      }
    }
    /**
     * 非明细表模式
     */
    if (!isTable) {
      extraSetValueMap && handleSetAndEmitFormData(additionnalInfo);
      // 非明细表模式下设置缓存
      value && target && configs.configs.utils.cache.setValue(value, target);
    }
    !value && setOptions([]);
    onChange?.(value);
  };

  if (isView) {
    return <span>{handletranslateText(value)}</span>;
  }

  return (
    <Select
      showSearch
      allowClear
      value={value}
      options={options}
      loading={loading}
      onSearch={handleSearch}
      onChange={handleChange}
      filterOption={false} // 禁用过滤
      style={{ minWidth: '100%' }}
      disabled={isEnabled === '0'}
      className="dhrPersonSelect"
      dropdownMatchSelectWidth={false}
      optionLabelProp={optionLabelProp || DEFAULT_OPTION_LABEL_PROP}
      notFoundContent={loading ? <Spin size="small" /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
    />
  );
};

export default SelectTree;
