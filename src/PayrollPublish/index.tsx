import React, { useCallback, useEffect, useMemo, useState } from 'react';
import list2tree from '@cvte/list2tree';
import queryString from 'query-string';
import { Spin, Tag } from 'antd';

import cnbApis from '@apis/cnb';
import { request } from '@utils/http';
import useFetch from '@hooks/useFetch';
import useViews from '@hooks/useViews';
import useDictCode from '@hooks/useDictCode';
import salarySlipApis from '@apis/salarySlip';
import { compineLoading, defaultArray, defaultObj, showSucNotification } from '@utils/tools';

import PayrollActionModal, { ACTION_TYPES } from '@components/PayrollActionModal';
import WULIWholeTable, { WULIFormActions, DEFAULT_PAGE_SIZE } from '@components/WholeTable/AgGrid';

import { IOriginalProps } from '../types/index.d';
import { DICT_CODE_MAP_ID, CSB_VIEW_CODES } from '@constants/common';
import { DHR_CNB_PROCESS_STATUS_MAP, DHR_CHANNEL_TYPE } from '@constants/cnb';

const filterFormKey = 'payrollPublishFormKey';
export interface IAppProps extends IOriginalProps {
  systemHelper: any;
}

const NoticeProgess: React.FC<IAppProps> = ({ systemHelper }) => {
  const [periordTreeData, setPeriordTreeData] = React.useState<any[]>([]);
  const [commonLoading, setCommonLoading] = React.useState<boolean>(false);
  const [selectedRow, setSelectedRow] = React.useState<Record<string, any>>({});
  const [actionType, setActionType] = React.useState<ACTION_TYPES | undefined>(undefined);

  const { DHR_CNB_PROCESS_STATUS } = useDictCode([DICT_CODE_MAP_ID.DHR_CNB_PROCESS_STATUS]);
  const statusList = defaultArray(DHR_CNB_PROCESS_STATUS);

  /** 请求过程 */
  const {
    Data: payroll,
    Loading: payrollListLoading,
    runAction: runActionOfPayrollList,
  } = useFetch({
    ...salarySlipApis.salaryslipPublishList,
    params: {
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  // 角色权限
  const { Data: csbViews } = useViews(CSB_VIEW_CODES.PAYROLL_PUBLISH);

  /** 算薪周期列表 */

  const { Data: periodList = [], Loading: periodListLoading } = useFetch({
    ...cnbApis.periodList,
  }) as {
    Data: any[];
    Loading: boolean;
  };

  /** 算薪日历列表 */
  const { Data: calendarList = [], Loading: calendarListLoading } = useFetch({
    ...cnbApis.calendarList,
  }) as {
    Data: any[];
    Loading: boolean;
  };

  /** 数据转换 */
  const transformTreeData = data => {
    return list2tree({
      idKey: 'id',
      parentIdKey: 'parentId',
      newKey: {
        key: 'id',
        value: 'id',
        label: 'name',
        name: 'name',
      },
    })(data);
  };

  useEffect(() => {
    if (periodList.length === 0 || calendarList.length === 0) return;
    const _periodList = defaultArray(periodList).map(k => ({
      ...k,
      parentId: k.objId,
    }));
    const allData = [...calendarList, ..._periodList];
    setPeriordTreeData(transformTreeData(allData));
  }, [periodList, calendarList]);

  const statusMappings = React.useMemo(() => {
    return (DHR_CNB_PROCESS_STATUS || []).reduce((acc, cur) => {
      return {
        ...acc,
        [cur.itemValue]: cur.name,
      };
    }, {});
  }, [DHR_CNB_PROCESS_STATUS]);

  const { list: payrollList = [], pagination = {} } = defaultObj(payroll);
  const handleActionConfirm = (values: Record<string, any>, type: ACTION_TYPES) => {
    const action = {
      [ACTION_TYPES.PUBLISH]: {
        fetch: salarySlipApis.salaryslipPublish,
        data: {
          processId: selectedRow?.id,
          templateIdList: values.template,
        },
      },
    };
    console.log('values', values);
    setCommonLoading(true);
    return request({
      ...action[type].fetch,
      data: action[type].data,
      onSuccess: data => {
        setActionType(undefined);
        showSucNotification('已发放');
        handleJumpToPayroll({ id: selectedRow?.id, salarySetHid: selectedRow?.salarySetHid });
      },
      onError: err => {
        console.log('err', err);
      },
    }).finally(() => setCommonLoading(false));
  };

  const handleJumpToPayroll = record => {
    const jumpUrl = '/portal/7lr0obbt/7lr0obbt_w8w18rfh_payroll_publish/emp';
    systemHelper.history.push(
      `${jumpUrl}?${queryString.stringify({
        processId: record.id,
        salarySetHid: record.salarySetHid || '',
        uId: `${record.id}${record.salarySetHid || ''}`,
      })}`
    );
  };

  const handleJumpToNoticeProgress = (record: Record<string, any>, channel: string) => {
    const jumpUrl = '/portal/7lr0obbt/7lr0obbt_w8w18rfh_payroll_publish/progess';
    systemHelper.history.push(
      `${jumpUrl}?${queryString.stringify({
        channel,
        processId: record.id,
        uId: `${channel}${record.id}`,
      })}`
    );
  };

  console.log('periordTreeData', periordTreeData);
  const columns = [
    {
      title: '工资表名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '工资套',
      dataIndex: 'salarySetName',
      key: 'salarySetName',
    },
    {
      title: '薪资组',
      dataIndex: 'groupName',
      key: 'groupName',
    },
    {
      title: '发薪期间',
      dataIndex: 'periodName',
      key: 'periodName',
    },
    {
      title: '发薪人数',
      dataIndex: 'payEmpCount',
      key: 'payEmpCount',
      render: record => {
        const data = record.data || {};
        return <a onClick={() => handleJumpToPayroll(data)}>{data.payEmpCount}</a>;
      },
    },
    {
      title: '邮件消息进度',
      dataIndex: 'emailProgress',
      key: 'emailProgress',
      render: record => {
        const data = record.data || {};
        return (
          <span
            style={{ color: '#1890ff', cursor: 'pointer' }}
            onClick={() => handleJumpToNoticeProgress(data, DHR_CHANNEL_TYPE.EMAIL)}
          >
            {`${data.emailSendSuccessCount}/${data.emailSendCount}`}
          </span>
        );
      },
    },
    {
      title: '企业号消息进度',
      dataIndex: 'qyhProgress',
      key: 'qyhProgress',
      render: record => {
        const data = record.data || {};
        return (
          <span
            style={{ color: '#1890ff', cursor: 'pointer' }}
            onClick={() => handleJumpToNoticeProgress(data, DHR_CHANNEL_TYPE.WECHAT)}
          >
            {`${data.qywxSendSuccessCount} / ${data.qywxSendCount}`}
          </span>
        );
      },
    },
    {
      title: '工资条状态',
      dataIndex: 'status',
      key: 'status',
      render: record => {
        const data = record.data || {};
        return (
          <Tag color={data.status === DHR_CNB_PROCESS_STATUS_MAP.PUBLISH ? 'green' : ''}>
            {statusMappings[data.status]}
          </Tag>
        );
      },
    },
  ];

  const filterFormItems = [
    {
      type: 'cascader',
      key: 'periodId',
      label: '发薪期间',
      configs: {
        allowClear: true,
        placeholder: '请选择',
        options: periordTreeData,
      },
    },
    {
      type: 'select',
      key: 'salarySlipStatus',
      label: '工资条状态',
      configs: {
        allowClear: true,
        placeholder: '请选择',
        options: statusList.map(k => ({
          label: k.name,
          value: k.itemValue,
          key: k.itemValue,
        })),
      },
    },
    {
      type: 'input',
      key: 'processName',
      label: '工资表名称',
      configs: {
        allowClear: true,
        placeholder: '工资表名称',
      },
    },
  ];

  const actionBtnItems = useMemo(() => {
    const list = [
      {
        key: 'publish',
        type: 'button',
        content: '发布工资条',
        viewCode: 'publish',
        config: {
          type: 'primary',
          disabled: !selectedRow?.salarySetHid,
          onClick: () => setActionType(ACTION_TYPES.PUBLISH),
        },
      },
      {
        key: 'restrict',
        type: 'button',
        viewCode: 'restrict',
        content: '限制发放人员',
        config: {
          type: 'primary',
          onClick: () => systemHelper.history.push(`/portal/7lr0obbt/7lr0obbt_w8w18rfh_tw5g3uue/list`),
        },
      },
    ].filter(({ viewCode }) => csbViews.includes(viewCode));

    return list;
  }, [selectedRow, csbViews]);

  /** 请求过程 */
  const onFetchPayrollList = (data: Record<string, any> = {}) => {
    const { periodId: periodIds, ...otherFilterValues } = WULIFormActions.get(filterFormKey)?.getValue();
    const periodId = [...(periodIds || [])].pop();
    runActionOfPayrollList({
      params: {
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...otherFilterValues,
        periodId,
        ...data,
      },
    });
  };

  console.log('payrollList===', csbViews, payrollList);

  const loading = compineLoading([payrollListLoading, periodListLoading, calendarListLoading]);
  console.log('selectedRow', selectedRow);
  return (
    <Spin spinning={loading}>
      <div className="CnbPayProcessWrap">
        <WULIWholeTable
          pagination
          canSelect
          rowKey="id"
          selectType="radio"
          columns={columns}
          data={payrollList}
          action={actionBtnItems}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            actionNotSingleLine: true,
            onSearch: () => onFetchPayrollList(),
            defaultLayout: {
              col: 8,
              labelCol: 6,
              wrapperCol: 18,
            },
          }}
          paginationConfig={{
            size: 'small',
            ...pagination,
            current: pagination.pageNum || 1,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (page, pageSize) =>
              onFetchPayrollList({
                pageNum: page,
                pageSize,
              }),
          }}
          onSelect={(_selectedRow, isSelect, _selectedRowKeys, _selectedRows) => {
            setSelectedRow(_selectedRows?.[0] || {});
          }}
        />
      </div>
      <PayrollActionModal
        type={actionType}
        open={!!actionType}
        isMultiple={true}
        selectedRow={selectedRow}
        onConfirm={handleActionConfirm}
        confirmLoading={commonLoading}
        onCancel={() => setActionType(undefined)}
      />
    </Spin>
  );
};
export default NoticeProgess;
