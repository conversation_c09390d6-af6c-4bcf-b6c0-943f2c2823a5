import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useCallback, useEffect, useState } from 'react';
import { Modal } from 'antd';

import './style.less';

export interface ModalProps {
  title: string;
  content: string;
}

export interface ExtraProps {
  onAfterClose: () => void;
}

export interface IAppProps extends ModalProps, ExtraProps {
  open: boolean;
}
const TemplatePreview: React.FC<IAppProps> = ({ open, title, content, onAfterClose }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  return (
    <Modal
      centered
      open={visible}
      title={title}
      footer={null}
      width={1000}
      className="templatePreviewModal"
      afterClose={onAfterClose}
      onCancel={handleCancel}
    >
      <div className="templatePreview">
        <div dangerouslySetInnerHTML={{ __html: content }}></div>
      </div>
    </Modal>
  );
};

const withParams = (params: ModalProps) => {
  return {
    ...params,
    open: true,
  };
};

const previewModal = (params: any) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
  };
  reactRender(<TemplatePreview {...params} onAfterClose={onAfterClose} />, container);
};

const TemplatePreviewModal = TemplatePreview as any;

TemplatePreviewModal.preview = function previewFn(props: ModalProps) {
  return previewModal(withParams(props));
};

export default TemplatePreviewModal;
