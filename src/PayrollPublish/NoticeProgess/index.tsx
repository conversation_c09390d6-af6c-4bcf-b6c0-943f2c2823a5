import React, { useEffect, useMemo } from 'react';
import { message, Spin } from 'antd';

import useFetch from '@hooks/useFetch';
import useViews from '@hooks/useViews';
import useDictCode from '@hooks/useDictCode';
import salarySlipApis from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';
import { defaultObj, showSucNotification, toDateFormat } from '@utils/tools';

import TemplatePreviewModal from './containers/TemplatePreviewModal';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

import { DHR_CHANNEL_TYPE } from '@constants/cnb';
import { DICT_CODE_MAP_ID, CSB_VIEW_CODES } from '@constants/common';

import { IOriginalProps } from '../../types/index.d';

const filterFormKey = 'noticeProgessFormKey';

export interface IAppProps extends IOriginalProps {
  channel: string;
  processId: string;
}

const BaseInfo: React.FC<IAppProps> = ({ processId, channel }) => {
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);
  const [resendLoading, setResendLoading] = React.useState<boolean>(false);

  const { DHR_NOTIFICATION_STATUS = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_NOTIFICATION_STATUS]);

  // 角色权限
  const { Data: csbViews } = useViews(CSB_VIEW_CODES.PAYROLL_NOTICE_PROGESS);

  /** 请求过程 */
  const {
    Data: payroll,
    Loading: payrollListLoading,
    runAction: runActionFetchNotofyList,
  } = useFetch({
    ...salarySlipApis.salaryslipNotifyList,
    params: {
      channel,
      processId,
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  useEffect(() => {
    channel && onFetchNotifyList({ channel });
  }, [channel]);

  /** 请求过程 */
  const onFetchNotifyList = (data: Record<string, any> = {}) => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    runActionFetchNotofyList({
      params: {
        channel,
        processId,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...formData,
        ...data,
      },
    });
  };

  // 预览模板
  const handleReadTemplate = (channel, content) => {
    if (!content) {
      return message.warning('暂无模板预览');
    }
    const title = channel === DHR_CHANNEL_TYPE.EMAIL ? '邮件模板预览' : '企业号消息预览';
    TemplatePreviewModal.preview({
      title,
      content,
    });
  };

  const columns = useMemo(
    () => [
      {
        title: '接收人',
        dataIndex: 'receiverCode',
        key: 'receiverCode',
        render: ({ data }) => {
          const { receiverCode, content } = data;
          return <a onClick={() => handleReadTemplate(channel, content)}>{receiverCode}</a>;
        },
      },
      {
        title: '渠道',
        dataIndex: 'channelTypeName',
        key: 'channelTypeName',
      },
      {
        title: '状态',
        dataIndex: 'statusName',
        key: 'statusName',
      },
      {
        title: '类型',
        dataIndex: 'typeName',
        key: 'typeName',
      },
      {
        title: '重试次数',
        dataIndex: 'sendCount',
        key: 'sendCount',
      },
      {
        title: '计划发送时间',
        dataIndex: 'definedSendTime',
        key: 'definedSendTime',
        render: ({ data: { definedSendTime } }) => toDateFormat(definedSendTime),
      },
      {
        title: '发送时间',
        dataIndex: 'sendTime',
        key: 'sendTime',
        render: ({ data: { sendTime } }) => toDateFormat(sendTime),
      },
      {
        title: '创建时间',
        dataIndex: 'crtTime',
        key: 'crtTime',
        render: ({ data: { crtTime } }) => toDateFormat(crtTime),
      },
    ],
    [channel]
  );

  const filterFormItems: any[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'receiverCode',
        label: '接收人',
        configs: {
          allowClear: true,
          placeholder: '请输入',
        },
        col: 8,
      },
      {
        type: 'select',
        key: 'status',
        label: '状态',
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: (DHR_NOTIFICATION_STATUS || []).map(codeItem => ({
            ...codeItem,
            label: codeItem.name,
            value: codeItem.itemValue,
            key: codeItem.itemValue,
          })),
        },
        col: 8,
      },
    ],
    [DHR_NOTIFICATION_STATUS]
  );

  /** 重发 */
  const handleResend = () => {
    setResendLoading(true);
    fetchApi({
      ...salarySlipApis.salaryslipNotifyResend,
      data: {
        detailIdList: selectedRowKeys,
      },
      onSuccess: () => {
        showSucNotification('操作成功');
        onFetchNotifyList();
      },
    }).finally(() => setResendLoading(false));
  };

  const actionBtnItems: any[] = useMemo(() => {
    const list = [
      {
        key: 'publish',
        content: '重发',
        type: 'button',
        viewCode: 'retry',
        config: {
          type: 'primary',
          loading: resendLoading,
          onClick: handleResend,
          disabled: selectedRowKeys.length === 0,
        },
      },
    ].filter(({ viewCode }) => csbViews.includes(viewCode));
    return list;
  }, [resendLoading, selectedRowKeys, csbViews]);

  const { list: payrollList = [], pagination = {} } = defaultObj(payroll);
  return (
    <Spin spinning={payrollListLoading}>
      <div className="CnbPayProcessWrap">
        <WULIWholeTable
          pagination
          canSelect
          multiSelect
          rowKey="id"
          columns={columns}
          data={payrollList}
          paginationConfig={{
            current: pagination.pageNum,
            pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (pageNum, pageSize) => onFetchNotifyList({ pageNum, pageSize }),
          }}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            onSearch: () => onFetchNotifyList(),
            onClear: () => {
              WULIFormActions.get(filterFormKey)?.reset();
              onFetchNotifyList();
            },
            defaultLayout: {
              col: 6,
              labelCol: 6,
              wrapperCol: 18,
            },
          }}
          action={actionBtnItems}
          onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => {
            setSelectedRowKeys(selectKeys);
            setSelectedRows(_selectedRows);
          }}
          selectedRows={selectedRows}
          showSelectedDetail
          rowNameKey="receiverCode"
          afterCancelSelect={newRows => {
            setSelectedRows(newRows);
            setSelectedRowKeys(newRows.map(k => k.id));
          }}
        />
      </div>
    </Spin>
  );
};
export default BaseInfo;
