.dhr-entry-detail-container {
  height: initial;
  background: #fff;
  border-radius: 20px;
  .dhr-entry-detail-cover {
    position: relative;
    top: -36px;
    margin-bottom: -36px;
    .dhr-entry-back-wrap {
      position: absolute;
      bottom: 0;
      left: 20px;
      .dhr-entry-back-btn.ant-btn {
        width: 100px;
        color: #fff;
        padding: 0px;
        height: 40px;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
        line-height: 40px;
        border-radius: 8px;
        border-width: 0px;
        background-image: linear-gradient(128deg, #49f6f6 -90%, #0887fd 104%);
        &:active {
          border-color: #1da6fb;
        }
      }
      .dhr-entry-back-btn.ant-btn[type='button'] {
        & > span {
          line-height: 40px;
          vertical-align: initial;
          margin-top: 0px;
        }
      }
    }
  }
  .dhr-entry-detail-content {
    padding: 20px;
    min-height: 300px;
  }
}
