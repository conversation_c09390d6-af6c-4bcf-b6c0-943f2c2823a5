import React, { useCallback } from 'react';
import * as R from 'ramda';
import { Empty } from 'antd';

import { LCPDetailTemplate, LCPPageTemplate, LCPListTemplate, LCPResourceTemplate } from '@components/LcpTemplate';

import { ENTRY_DISPLAY_TYPE, ENTRY_EMPLOYMENT_TYPE } from '@constants/entry';

import 'antd/dist/antd.css';

const CoreRenderer = (props: any) => {
  // 根据data.type 判断渲染哪个组件
  const LCPRenderComponent = useCallback(params => {
    let config: Record<string, any> = {};
    const { data, empCategory, entryProcessId, ...otherParams } = params;
    const { itemParams = [], instanceId: pageId } = data;
    const inputParamsObj = (itemParams || []).reduce((acc, cur) => {
      acc[cur.paramCode] = cur.value;
      return acc;
    }, {});
    if (R.isEmpty(inputParamsObj)) {
      return <Empty description="当前活动未配置" />;
    }

    switch (data.displayType) {
      case ENTRY_DISPLAY_TYPE.TABLE:
        /** 根据不同员工类型 获取对应的metaConfig */
        const metaConfigKey = ENTRY_EMPLOYMENT_TYPE[empCategory]?.toLocaleLowerCase();
        config = R.pick(['appId', 'classId', 'tenantId'])(inputParamsObj);
        config.metaConfig = metaConfigKey && R.path([`${metaConfigKey}_metaConfig`])(inputParamsObj);
        /** 增加metaConfig配置的场景 */
        if (config.metaConfig) {
          try {
            config.metaConfig = JSON.parse(inputParamsObj.metaConfig);
          } catch (error) {
            console.log('metaConfig格式错误：', error);
          }
        }
        return <LCPDetailTemplate {...otherParams} {...config} pageId={pageId} />;
      case ENTRY_DISPLAY_TYPE.CUSTOM:
        config = R.pick(['resourceName', 'exposeName', 'resourceVersion'])(inputParamsObj);
        return (
          <LCPResourceTemplate
            {...otherParams}
            {...config}
            actData={data}
            pageId={pageId}
            extraData={inputParamsObj}
            entryProcessId={entryProcessId}
          />
        );
      // case ENTRY_DISPLAY_TYPE.POP:
      //   config = R.pick(['appId', 'classId', 'tenantId'])(inputParamsObj);
      //   return <LCPPageTemplate {...config} />;
      // case 'LIST':
      //   config = R.pick(['appId', 'classId', 'tenantId'])(inputParamsObj);
      //   return <LCPListTemplate {...config} />;
    }
  }, []);
  return <LCPRenderComponent {...props} />;
};

export default CoreRenderer;
