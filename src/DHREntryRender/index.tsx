import React, { useCallback, useEffect, useState } from 'react';
import { But<PERSON>, BackTop } from 'antd';

import coreHrApis from '@apis/coreHr';
import { request as fetchApi } from '@utils/http';

import CoreRenderer from './containers/CoreRenderer';

import { DOWNLOAD_URL_PREFIX } from '@constants/common';

import './style.less'; // 引入样式文件

export interface IAppProps {
  actItemId: string;
  empCategory: string;
  entryProcessId: string;
  onRefresh?: () => void;
  onBack?: () => void;
}
const DHREntryRender: React.FC<IAppProps> = props => {
  const { actItemId, onBack } = props;
  const [initValues, setInitValues] = useState<Record<string, any>>({});
  /** 获取活动详情 */
  const onFetchScheduleDetail = actItemId => {
    fetchApi({
      ...coreHrApis.dhrActItem,
      params: {
        actItemId,
      },
      onSuccess: res => {
        setInitValues(res || {});
      },
    });
  };

  useEffect(() => {
    actItemId && onFetchScheduleDetail(actItemId);
  }, [actItemId]);

  const handleRefresh = useCallback(() => {
    actItemId && onFetchScheduleDetail(actItemId);
    props?.onRefresh?.();
  }, [actItemId]);

  return (
    <div className="dhr-entry-detail-container">
      <div className="dhr-entry-detail-cover">
        <img src={`${DOWNLOAD_URL_PREFIX}/669e8033b9524a86a15ab42f77c78858`} width="100%" height="auto" />
        <div className="dhr-entry-back-wrap">
          <Button className="dhr-entry-back-btn" onClick={onBack}>
            返回
          </Button>
        </div>
      </div>
      <div className="dhr-entry-detail-content">
        <CoreRenderer {...props} onRefresh={handleRefresh} data={initValues} />
      </div>
      <BackTop />
    </div>
  );
};

export default DHREntryRender;
