import React, { useCallback, useEffect, useMemo, useState } from 'react';

import commonApis from '@apis/common';
import { request as fetchApi } from '@utils/http';

import { ENTRY_ACT_STATUS, ENTRY_ACT_STATUS_NAME } from '@constants/entry';

import './style.less';

export interface IAppProps {
  pageId: string;
  actData: Record<string, any>;
  extraData: Record<string, any>;
}
const DocumentSigning: React.FC<IAppProps> = props => {
  const { pageId, extraData, actData } = props;
  const status = actData?.status || '';

  const [initValues, setInitValues] = useState<Record<string, any>>({});

  const onFetchFormData = useCallback(values => {
    const params = {
      onlyMain: true,
      keyType: 'CAMEL',
      appId: 'efa37869ee1c4930b434a4c7b1548d46',
      formClassId: '51595b2f4b284803bc0808e003e85a71',
      ...values,
    };
    fetchApi({
      ...commonApis.formDetailInfo,
      params,
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const newInitValues = res?.mainData || {};
        setInitValues(newInitValues);
      },
    });
  }, []);

  useEffect(() => {
    pageId &&
      onFetchFormData({
        id: pageId,
      });
  }, [pageId]);

  const columns = useMemo(
    () => [
      {
        title: '入职合同',
        key: 'cContractRequestId',
      },
      {
        title: '个人资料文件',
        key: 'cContractFileRequestId',
      },
    ],
    []
  );

  console.log('props===22', props, initValues);

  return (
    <div className="dhr-entry-document-signing-wrap">
      <div className="dhr-entry-document-signing-title">
        <h3>{`文件签署活动${ENTRY_ACT_STATUS_NAME[status] || ''}`}</h3>
      </div>
      <div className="dhr-entry-document-signing-content">
        <div className="dhr-entry-document-signing-tip">
          <p>{extraData?.[status] || '哈哈哈哈好啊哈哈'}</p>
        </div>
        <div className="dhr-entry-document-signing-file-url-wrap">
          <p>签署链接：</p>
          {columns.map((columnItem, index) => (
            <div key={columnItem.key} style={{ marginBottom: 10 }}>
              <label>{`${index + 1}、${columnItem.title}：`}</label>
              <span>
                {initValues[columnItem.key] ? (
                  <a href={initValues[columnItem.key]} target="_blank" rel="noreferrer">
                    {initValues[columnItem.key]}
                  </a>
                ) : (
                  '暂无数据'
                )}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DocumentSigning;
