import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { EditOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React from 'react';

import RichTextModal from '../RichTextModal';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { textContent, title, richText } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigTitle',
      curFormData?.dictConfigTitle || defFormData?.dictConfigTitle || title
    );
    formRef?.current?.setFormItem?.(
      'dictConfigTextContent',
      curFormData?.dictConfigTextContent || defFormData?.dictConfigTextContent || textContent
    );
    formRef?.current?.setFormItem?.(
      'dictConfigRichText',
      curFormData?.dictConfigRichText || defFormData?.dictConfigRichText || richText
    );
  });

  const handleEditRichText = ({ value }: { value: any }) => {
    RichTextModal.edit({
      content: value,
      onOk: (content: string) => {
        context?.onConfirm?.('dictConfigRichText', content);
        setDictConfig(formRef, 'richText', content, {
          context,
        });
      },
    });
  };

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigTitle',
      label: '标题内容',
      configs: {
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigTitle');
          context?.onConfirm?.('dictConfigTitle', value);
          setDictConfig(formRef, 'title', value, {
            context,
          });
        },
      },
    },
    {
      type: 'textarea',
      key: 'dictConfigTextContent',
      label: '文本内容',
      configs: {
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigTextContent');
          context?.onConfirm?.('dictConfigTextContent', value);
          setDictConfig(formRef, 'textContent', value, {
            context,
          });
        },
      },
    },
    {
      type: 'custom',
      key: 'dictConfigRichText',
      label: '富文本内容',
      render: (params: any) => (
        <Button block icon={<EditOutlined />} onClick={() => handleEditRichText(params)}>
          编辑富文本
        </Button>
      ),
    },
  ];
};

export default customCptBaseConfig;
