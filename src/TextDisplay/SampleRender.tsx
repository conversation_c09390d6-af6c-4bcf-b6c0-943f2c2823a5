import React from 'react';
import { IOriginalProps } from '@src/types';

import './style.less';

type IAppProps = IOriginalProps;
const BaseInfo: React.FC<IAppProps> = props => {
  const { title, textContent, richText } = props.configs.config?.baseConfig?.dictConfig || {};
  const titleText = !textContent && !richText && !title ? '段落文字展示' : title;
  return (
    <div className="dhr-text-display-sample-container">
      {titleText && <h2>{titleText}</h2>}
      {textContent && (
        <p
          dangerouslySetInnerHTML={{
            __html: textContent?.replace(/\n/g, '<br>'),
          }}
        />
      )}
      {richText && (
        <div className="dhr-text-display-sample-rich-text-wrap" dangerouslySetInnerHTML={{ __html: richText }} />
      )}
    </div>
  );
};

export default BaseInfo;
