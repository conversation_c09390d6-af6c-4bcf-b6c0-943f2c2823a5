import React from 'react';
import { IOriginalProps } from '@src/types';
import { defaultObj } from '@utils/tools';

import './style.less';

type IAppProps = IOriginalProps;
const BaseInfo: React.FC<IAppProps> = props => {
  const { dictConfig = {} } = props.configs.config?.baseConfig || {};
  console.log('=====文本内容配置======', dictConfig);
  const { title, textContent, richText } = defaultObj(dictConfig);
  return (
    <div className="dhr-text-display-container">
      {title && <h2>{title}</h2>}
      {textContent && (
        <p
          dangerouslySetInnerHTML={{
            __html: textContent?.replace(/\n/g, '<br>'),
          }}
        />
      )}
      {richText && <div className="dhr-text-display-rich-text-wrap" dangerouslySetInnerHTML={{ __html: richText }} />}
    </div>
  );
};

export default BaseInfo;
