import { unmountComponentAtNode as reactUnmount, render as reactRender } from 'react-dom';
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Modal } from 'antd';

import QuillEditor from '@components/QuillEditor';

import './style.less';

export interface ModalConfig {
  content: string;
  onOk: (content: string) => void;
}

export interface IAppProps extends ModalConfig {
  open: boolean;
  onAfterClose: () => void;
}

const RichTextContent: React.FC<IAppProps> = props => {
  const { content, open, onOk, onAfterClose } = props;

  const quillRef = useRef<any>(null);
  const [visible, setVisible] = useState<boolean>(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  const handleOk = useCallback(() => {
    const quill = quillRef.current?.getQuill();
    const content = quill.root.innerHTML;
    onOk?.(content);
    handleCancel();
  }, []);

  return (
    <Modal
      centered
      width={660}
      okText="确定"
      open={visible}
      destroyOnClose
      onOk={handleOk}
      cancelText="取消"
      title="编辑富文本内容"
      maskClosable={false}
      onCancel={handleCancel}
      afterClose={onAfterClose}
    >
      <div className="dhr-text-display-rich-text-modal">
        <QuillEditor ref={quillRef} value={content} />
      </div>
    </Modal>
  );
};

RichTextContent.displayName = 'RichTextContent';

/**
 * 为配置添加默认属性，创建完整的Modal属性对象
 * @param config 基础配置信息
 * @returns 完整的Modal属性对象
 */
function withParams(config: ModalConfig): IAppProps {
  return {
    open: true,
    onAfterClose: () => {},
    ...config,
  };
}

/**
 * 在DOM中创建并渲染一个临时Modal组件
 * @param props Modal配置信息
 */
function editFn(props: ModalConfig): void {
  // 使用文档片段避免实际DOM操作
  const container = document.createDocumentFragment();

  // 关闭后直接清理DOM引用，避免内存泄漏
  const handleAfterClose = (): void => {
    // 直接卸载组件
    reactUnmount(container);
  };

  // 创建完整的属性集
  const fullProps: IAppProps = {
    ...withParams(props),
    onAfterClose: handleAfterClose,
  };

  // 渲染组件到DOM
  reactRender(<RichTextContent {...fullProps} />, container);
}

// 扩展组件类型，添加静态方法
interface RichTextContentType extends React.FC<IAppProps> {
  edit: (props: ModalConfig) => void;
}

const modal = RichTextContent as RichTextContentType;

modal.edit = function (props: ModalConfig): void {
  editFn(props);
};

export default modal;
