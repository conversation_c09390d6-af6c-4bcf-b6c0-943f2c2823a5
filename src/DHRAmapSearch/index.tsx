import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ToolBarControl } from '@uiw/react-amap-tool-bar-control';
import { ScaleControl } from '@uiw/react-amap-scale-control';
import { APILoader } from '@uiw/react-amap-api-loader';
import { Marker } from '@uiw/react-amap-marker';
import { Select, Spin, Tooltip } from 'antd';
import { Map } from '@uiw/react-amap-map';

import commonApis from '@apis/common';
import useDebounce from '@hooks/useDebounce';
import { request as fetchApi } from '@utils/http';

import { WULI_MODE, AMAP_SERVICE_HOST, AMAP_AKEY } from '@constants/common';

import './style.less';

// 表单类型
const CONTAINER_TYPE_MAP = {
  TABLE: 'table',
  FORM: 'form',
};

// ag-grid 模式类型
const WULI_MODE_TYPE_MAP = {
  VIEW: 'view',
  EDIT: 'edit',
};

interface IAppProps {
  value: any;
  onChange: (val) => void;
  configs: {
    wuliMode: 'view' | 'edit';
    containerType: 'form' | 'table';
    config: {
      baseConfig: {
        dictConfig: {
          isShowAmap: string;
        };
      };
    };
  };
}
const DHRAmapSearch: React.FC<IAppProps> = props => {
  const { value, configs, onChange } = props;
  const wuliMode = configs?.wuliMode;
  // 当前是form还是table表单
  const containerType = configs?.containerType;
  const dictConfig: any = configs?.config?.baseConfig?.dictConfig || {};
  const isShowAmap = dictConfig.isShowAmap === '1';
  const isView = wuliMode === WULI_MODE.VIEW;

  const optionsRef = useRef<any[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [options, setOptions] = useState<any[]>([]);
  const amapValueRef = useRef<string | undefined>(undefined);

  useEffect(() => {
    if (isShowAmap) {
      // 后端转发
      window._AMapSecurityConfig = {
        serviceHost: AMAP_SERVICE_HOST,
      };
    }
  }, [isShowAmap]);

  const handleChange = useCallback(value => {
    amapValueRef.current = value;
    onChange(value);
  }, []);

  /** 根据经纬度获取地址信息 */
  const onFetchLnglatAddress = useCallback(location => {
    setLoading(true);
    fetchApi({
      ...commonApis.getLnglatAddress,
      params: {
        location,
      },
      onSuccess: res => {
        const newOptions = [{ ...(res || {}), location, key: location, value: location, label: res.formattedAddress }];
        optionsRef.current = newOptions;
        setOptions(newOptions);
      },
    }).finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    /** 外部set的方式 */
    if (value !== amapValueRef.current) {
      const isOptionItem = optionsRef.current.some(({ key }) => key === value);
      if (!isOptionItem) {
        onFetchLnglatAddress(value);
      }
    }
  }, [value, amapValueRef.current]);

  /** 点击地图的点 */
  const handleClickMap = useCallback(e => {
    const {
      lnglat: { lng, lat },
    } = e;
    const newPosition = [lng, lat];
    const location = newPosition.join(',');
    handleChange(location);
    onFetchLnglatAddress(location);
  }, []);

  // const handleDragEndMap = e => {
  //   const { lng, lat } = e.target.getCenter() || {};
  //   setPosition([lng, lat]);

  // };

  const handleSearch = useDebounce(
    value => {
      setLoading(true);
      fetchApi({
        ...commonApis.addressSearch,
        params: {
          address: value,
        },
        onSuccess: list => {
          const _options = (list || []).map(listItem => ({
            ...listItem,
            key: listItem.location,
            value: listItem.location,
            label: listItem.formatAddress,
          }));
          optionsRef.current = _options;
          setOptions(_options);
        },
        onError: error => {
          console.log('地址搜索请求失败：', error);
          optionsRef.current = [];
          setOptions([]);
        },
      }).finally(() => setLoading(false));
    },
    500,
    []
  );

  const position = useMemo(() => {
    if (value) {
      const content = value.split(',');
      return content;
    }
    return;
  }, [value]);

  const viewText = useMemo(() => {
    if (isView) {
      const detail = (optionsRef.current || []).find(({ key }) => key === value);
      return detail?.label || '-';
    }
  }, [isView, value]);

  return (
    <div className="dhr-amap-search-wrap">
      {isView ? (
        <Tooltip placement="topLeft" title={viewText || ''}>
          <span className="text-ellipsis">{viewText || ''}</span>
        </Tooltip>
      ) : (
        <>
          <div className="dhr-amap-select-wrap">
            <Select
              allowClear
              showSearch
              value={value}
              showArrow={false}
              options={options}
              filterOption={false}
              placeholder="请搜索地址"
              onChange={handleChange}
              onSearch={handleSearch}
              notFoundContent={loading ? <Spin size="small" /> : null}
            />
          </div>
          {isShowAmap && (
            <div className="dhr-amap-map-wrap">
              <APILoader version="2.0" akey={AMAP_AKEY} plugins={['AMap.Geocoder']}>
                <div style={{ width: '100%', height: '300px' }}>
                  <Map zoom={17} center={position} onClick={handleClickMap}>
                    <Marker position={position} />
                    <ScaleControl offset={[16, 30]} position="LB" />
                    <ToolBarControl offset={[16, 10]} position="RB" />
                  </Map>
                </div>
              </APILoader>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default DHRAmapSearch;
