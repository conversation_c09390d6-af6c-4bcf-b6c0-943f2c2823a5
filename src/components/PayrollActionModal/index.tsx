import React, { useEffect, useMemo, useState } from 'react';
import { Form, Modal, Select, Input } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import { request } from '@utils/http';
import salarySlipApis from '@apis/salarySlip';

import {} from '@constants/common';
const { TextArea } = Input;

export enum ACTION_TYPES {
  TEST = 'TEST',
  // RECALL = 'RECALL',
  PUBLISH = 'PUBLISH',
  // STATUS_UPDATE = 'STATUS_UPDATE',
}

const titlleMappings = {
  [ACTION_TYPES.TEST]: '测试工资条发放',
  [ACTION_TYPES.PUBLISH]: '发放工资条',
  // [ACTION_TYPES.RECALL]: '撤回工资条',
  // [ACTION_TYPES.STATUS_UPDATE]: '变更工资条状态',
};
export interface IAppProps extends ModalProps {
  type: ACTION_TYPES;
  isMultiple?: boolean;
  selectedRow: Record<string, any>;
  onConfirm: (values: Record<string, any>, type: ACTION_TYPES) => void;
}
const BaseForm: React.FC<IAppProps> = ({ selectedRow, type, isMultiple, ...restProps }) => {
  const [form] = Form.useForm();

  const [salarySlipTplList, setSalarySlipTplList] = useState<Record<string, any>[]>([]);
  const handleConfirm = () => {
    form.validateFields().then(values => {
      // const { emp } = values;
      restProps.onConfirm(values, type);
    });
  };

  const _salarySlipTplList = useMemo(() => {
    return salarySlipTplList.map(k => ({
      ...k,
      label: k.name,
      value: k.id,
    }));
  }, [salarySlipTplList]);

  const onFetchSalarySlipTplList = () => {
    request({
      ...salarySlipApis.salarySlipTpl,
      params: {
        salarySetId: selectedRow?.salarySetHid,
      },
      onSuccess: data => {
        // if ((data || []).length === 0) {
        //   Modal.info({
        //     title: '提醒',
        //     content: '当前工资条发放未配置工资条模版哦',
        //   });
        // }
        data && setSalarySlipTplList(data);
      },
      onError: err => {},
    });
  };

  useEffect(() => {
    restProps.open && onFetchSalarySlipTplList();
    return () => {
      form.resetFields();
    };
  }, [selectedRow, restProps.open]);

  const isShowTemplate = useMemo(() => [ACTION_TYPES.PUBLISH, ACTION_TYPES.TEST].includes(type), [type]);
  return (
    <Modal {...restProps} width={600} title={titlleMappings[type]} destroyOnClose onOk={() => handleConfirm()}>
      <Form
        form={form}
        name="remark"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        initialValues={{}}
        autoComplete="off"
        className="ml-10"
      >
        {isShowTemplate && (
          <Form.Item
            label="工资条发放模板"
            name="template"
            rules={[{ required: true, message: '请选择工资条发放模板' }]}
            labelCol={{ span: 6 }}
          >
            <Select
              showSearch
              placeholder="请选择"
              mode="multiple"
              options={_salarySlipTplList}
              optionFilterProp="children"
              mode={isMultiple ? 'multiple' : undefined}
              filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
            />
          </Form.Item>
        )}
        {/* {type === ACTION_TYPES.RECALL && (
          <>
            <p>撤回后，将关闭所有员工移动端和PC端的数据查询。如确认撒回，请填写撤回原因后，继续操作。</p>
            <Form.Item
              label="撤回原因"
              name="recall_reason"
              rules={[{ required: true, message: '请选择工资条发放模板' }]}
            >
              <TextArea />
            </Form.Item>
          </>
        )}
        {type === ACTION_TYPES.STATUS_UPDATE && (
          <Form.Item label="工资条状态" name="status" rules={[{ required: true, message: '请选择工资条发放模板' }]}>
            <Select
              showSearch
              options={[]}
              optionFilterProp="children"
              filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
            />
          </Form.Item>
        )} */}
      </Form>
    </Modal>
  );
};

export default BaseForm;
