.dhr-free-card {
  margin-bottom: 12px;
  .ant-card-head {
    cursor: pointer;
    min-height: 30px;
    padding: 0 12px;
    background: #edf5fa;
    font-size: calc(var(--dynamic-primary-font-size, 12px) + 2px);
    color: rgba(0, 0, 0, 0.7);
    .ant-card-head-wrapper {
      .ant-card-head-title,
      .ant-card-extra {
        padding: 0;
      }

      .ant-card-head-title {
        .dhr-collapse-card-title {
          display: flex;
          & > div {
            padding: 8px 0;
            font-size: var(--dynamic-primary-font-size, 12px);
          }
          .dhr-handle-collapse {
            flex: 1;
            padding-left: 10px;
          }
        }
      }

      .ant-card-extra {
        .dhr-free-card-extra {
          .dhr-free-card-icon {
            padding: 8px 0;
            user-select: none;
            // transform: rotate(0);
            color: rgba(0, 0, 0, 0.5);
            transition: transform 0.24s;
          }
          .dhr-card-icon-rotate {
            transform: rotate(90deg);
          }
        }
      }
    }
  }
  .ant-card-body {
    padding: 0;
    overflow: hidden;
    .dhr-free-card-content-container {
      .dhr-free-card-content {
        padding: 12px;
      }
    }
    .dhr-motion-collapse {
      overflow: hidden;
      transition: height 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }
  }
}
