import React, { useCallback, useEffect, useRef, useState } from 'react';
import { RightOutlined } from '@ant-design/icons';
import classnames from 'classnames';
import { Card } from 'antd';

import './style.less';

let timeout = null;

export interface IAppProps {
  title: string;
  subTitle?: string;
  className?: string;
  children: React.ReactNode;
}

const FreeCard: React.FC<IAppProps> = props => {
  const contentRef = useRef(null);
  const heightRef = useRef(null);
  const [isActive, setIsActive] = useState(true);
  const [contentStyle, setContentStyle] = useState<any>({});

  const { title, subTitle, children, className } = props;

  const onStarTimeout = useCallback(
    (updateValues, timeNum: number) =>
      new Promise(resolve => {
        handleClearTimeout();
        timeout = setTimeout(() => {
          setContentStyle({
            ...updateValues,
          });
          resolve(true);
        }, timeNum);
      }),
    [timeout]
  );

  const handleClearTimeout = useCallback(() => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  }, [timeout]);

  const handleUpdateVisible = useCallback(async newVisible => {
    setIsActive(newVisible);
    if (newVisible) {
      await onStarTimeout({ height: heightRef.current }, 0);
      await onStarTimeout({}, 300);
      handleClearTimeout();
    } else {
      onUpdateContentHeight();
      setContentStyle({
        height: heightRef.current,
      });
      await onStarTimeout({ height: 0 }, 0);
      handleClearTimeout();
    }
  }, []);

  const TitleComponent = useCallback(
    ({ title, subTitle }) => (
      <div className="dhr-collapse-card-title" onClick={() => handleUpdateVisible(!isActive)}>
        <div>{title}</div>
        <div className="dhr-handle-collapse">{subTitle || ''}</div>
      </div>
    ),
    [isActive]
  );

  const ExtraComponent = useCallback(
    () => (
      <div className="dhr-free-card-extra" onClick={() => handleUpdateVisible(!isActive)}>
        <RightOutlined className={classnames('dhr-free-card-icon', { 'dhr-card-icon-rotate': isActive })} />
      </div>
    ),
    [isActive]
  );

  // 获取内容高度
  const onUpdateContentHeight = useCallback(() => {
    if (contentRef.current) {
      const height = contentRef.current.offsetHeight;
      const newHeight = `${height}px`;
      heightRef.current = newHeight;
    }
  }, []);

  useEffect(() => {
    return () => {
      handleClearTimeout();
    };
  }, []);

  return (
    <Card
      extra={<ExtraComponent />}
      className={classnames('dhr-free-card', className || '')}
      title={<TitleComponent title={title} subTitle={subTitle} />}
    >
      <div ref={contentRef} style={contentStyle} className="dhr-free-card-content-container dhr-motion-collapse">
        <div className="dhr-free-card-content">{children}</div>
      </div>
    </Card>
  );
};

export default FreeCard;
