import React, { useState, useRef } from 'react';
import filesize from 'filesize';
import classnames from 'classnames';
import { request as fetchApi } from '@utils/http';
import {
  CheckCircleTwoTone,
  DeleteFilled,
  FileExcelFilled,
  FileImageFilled,
  FilePptFilled,
  FileTextFilled,
  FileUnknownTwoTone,
  FileWordFilled,
  FileZipFilled,
  ReloadOutlined,
} from '@ant-design/icons';
import { Upload, Row, Tooltip, Col, Table, Alert, Button, Modal } from 'antd';
import { ellipsisTag } from '@utils/tools';
import useAttachment from '@hooks/useAttachment';
import { IAppProps } from './index.d';
import './style.less';

const MAPPINF = [
  { label: '全部', key: 'all' },
  { label: '已上传', key: 'done' },
  { label: '上传中', key: 'uploading' },
  { label: '等待上传', key: 'waiting' },
  { label: '上传失败', key: 'error' },
];

const NameMapping = {
  all: '全部',
  done: '已上传',
  uploading: '上传中',
  error: '上传失败',
  validate: '校验失败',
  waiting: '等待上传',
};

const defaultMapping = [
  { reg: /\.(jpg|jpeg|png|gif)$/i, icon: <FileImageFilled style={{ fontSize: '1.5em', color: '#ff7643' }} /> },
  { reg: /\.(txt)$/i, icon: <FileTextFilled style={{ fontSize: '1.5em', color: '#4c98ff' }} /> },
  { reg: /\.(pdf)$/i, icon: <FilePptFilled style={{ fontSize: '1.5em', color: '#ff595a' }} /> },
  { reg: /\.(excel|exce|xlsx|xls)$/i, icon: <FileExcelFilled style={{ fontSize: '1.5em', color: '#64c422' }} /> },
  { reg: /\.(doc|docx)$/i, icon: <FileWordFilled style={{ fontSize: '1.5em', color: '#4c98ff' }} /> },
  { reg: /\.(ppt|pptx)$/i, icon: <FilePptFilled style={{ fontSize: '1.5em', color: '#ff9743' }} /> },
  { reg: /\.(zip|rar|7z)$/i, icon: <FileZipFilled style={{ fontSize: '1.5em', color: '#55c7f7' }} /> },
];

const BaseBatchUpload: React.FC<IAppProps> = ({
  className,
  max = 6,
  iconMapping = [],
  onValidateBeforeUpload,
  onUploadFile,
  onUploadComplete,
  children,
  ...restConfig
}) => {
  const [fileList, setFileList] = useState<any[]>([]);
  const [startUpload, setStartUpload] = useState(false);
  const [validating, setValidating] = useState(false);
  const [filterType, setFilterType] = useState('all');
  const { uploadConfig } = useAttachment();

  const ini = useRef<any>('');

  const handleRemove = file => {
    const nextFileList = fileList.filter(item => item.uid !== file.uid);
    if (filterType === 'validate' && nextFileList.every(item => item.status !== 'validate')) {
      setFilterType('all');
    }
    setFileList(nextFileList);
  };

  const onBeforeUpload = (file, fileList) => {
    if (ini.current !== fileList) {
      setFileList(prevFileList => [...prevFileList, ...fileList]);
    }
    return Promise.reject();
  };

  const onUpdateFileStatus = (file, status) => {
    const newFileList = [...fileList].map(item => {
      if (item.uid !== file.uid) return item;
      return Object.assign(item, status);
    });
    setFileList(newFileList);
    // 判断是否已经全部上传成功
    if (newFileList.every(item => item.status === 'done')) {
      onUploadComplete(newFileList);
    }
  };

  const handleValidateFile = async fileList => {
    if (!onValidateBeforeUpload) return true;
    try {
      const { isSuccess, msg }: any = await onValidateBeforeUpload(fileList);
      setValidating(false);
      if (isSuccess === false) {
        if (!msg) return false;
        setFileList(prevFileList =>
          prevFileList.map(item => {
            return Object.assign(item, { errMsg: msg[item.uid], status: msg[item.uid] ? 'validate' : undefined });
          })
        );
        return false;
      }
      return true;
    } catch (e) {
      setValidating(false);
      return false;
    }
  };

  const handleUploadFile = async file => {
    onUpdateFileStatus(file, { status: 'uploading', percent: 0 });
    try {
      const { action, uploadParams = {} } = uploadConfig;
      const data = new FormData();
      data.append('files', file);
      data.append('categoryId', uploadParams.data.categoryId);
      const uploadRes = await fetchApi({
        url: action,
        baseURL: '/',
        method: 'POST',
        data,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      const fileId = uploadRes.result.fileIds[0];
      if (!fileId) {
        return onUpdateFileStatus(file, { status: 'error', error: uploadRes, errMsg: '上传失败' });
      }
      if (!onUploadFile) {
        return onUpdateFileStatus(file, { status: 'done', response: uploadRes, fileId });
      }
      const result = await onUploadFile(file, fileId);
      if (result) {
        onUpdateFileStatus(file, { status: 'error', error: result, errMsg: result });
      } else {
        onUpdateFileStatus(file, { status: 'done', response: result, fileId });
      }
    } catch (e) {
      onUpdateFileStatus(file, { status: 'error', error: e, errMsg: '网络错误' });
    }
    return file;
  };

  /**
   * 开始上传
   */
  const handleUpload = async () => {
    setValidating(true);
    const validate = await handleValidateFile(fileList);
    if (!validate) return;
    setStartUpload(true);
    setValidating(false);
    let index = 0;
    const func = async () => {
      if (!fileList[index]) return;
      await handleUploadFile(fileList[index++]);
      func();
    };
    for (let i = 0; i < max && fileList[i]; i++) {
      func();
    }
  };

  const handleRemoveValidateError = () => {
    Modal.confirm({
      title: '删除所有校验错误的文件',
      content: '请逐一确认校验失败的原因',
      okText: '确认删除',
      cancelText: '取消删除',
      onOk: () => {
        setFileList(prevFileList => prevFileList.filter(item => item.status !== 'validate'));
        setFilterType('all');
      },
    });
  };

  const handleReTry = fileList => {
    fileList.forEach(file => file.status === 'error' && handleUploadFile(file));
  };

  const handleFilter = e => {
    setFilterType(e.target.id);
  };

  const onMapFileIcon = file => {
    const { name } = file;
    const mapping = [...iconMapping, ...defaultMapping];
    const target = mapping.find(item => item.reg.test(name));
    return target ? target.icon : <FileUnknownTwoTone style={{ fontSize: '1.5em' }} />;
  };

  const count = { all: 0, done: 0, uploading: 0, error: 0, validate: 0, waiting: 0 };
  fileList.forEach(file => {
    count.all += 1;
    if (file.status === 'done') count.done += 1;
    if (file.status === 'error') count.error += 1;
    if (file.status === 'uploading') count.uploading += 1;
    if (file.status === 'validate') count.validate += 1;
    if (!file.status) count.waiting += 1;
  });

  const columns = [
    { title: '', dataIndex: 'type', render: (text, record) => onMapFileIcon(record) },
    { title: '名称', dataIndex: 'name', render: text => ellipsisTag(text, 20) },
    { title: '大小', dataIndex: 'size', render: text => text && filesize(text, { bit: true }) },
    {
      title: '',
      dataIndex: 'percent',
      render: (text, record) => {
        if (record.status === 'uploading') {
          return `${text.toFixed(2)}%`;
        }
        if (record.fileId) return <CheckCircleTwoTone twoToneColor="#52c41a" />;
        if (record.status === 'error' || record.status === 'validate') {
          return <span className="color-red">{record.errMsg}</span>;
        }
      },
    },
    {
      title: '',
      dataIndex: 'action',
      width: 40,
      render: (text, record) => {
        if (!startUpload) return <DeleteFilled onClick={() => handleRemove(record)} style={{ color: 'red' }} />;
        if (record.status === 'error') {
          return <ReloadOutlined onClick={() => handleReTry([record])} />;
        }
      },
    },
  ];
  return (
    <div className={classnames('base-batch-upload', className)}>
      {!startUpload && (
        <Upload.Dragger
          {...uploadConfig}
          {...restConfig}
          fileList={fileList}
          showUploadList={false}
          beforeUpload={onBeforeUpload}
        >
          {children}
        </Upload.Dragger>
      )}
      {count.all === count.done && count.all !== 0 && (
        <div className="upload-complete-alert">
          <Alert type="success" showIcon message={<div>已全部上传完成</div>} />
        </div>
      )}
      {startUpload && (
        <div className="base-main-content">
          <div className="upload-status">
            {MAPPINF.map(({ key, label }, index) => {
              if (key === 'error') {
                return (
                  <span className="error" key={index}>
                    上传失败:
                    <a id="error" onClick={handleFilter}>
                      {count.error}
                    </a>
                    {count.error > 0 && (
                      <Tooltip title="重试">
                        <ReloadOutlined onClick={() => handleReTry(fileList)} />
                      </Tooltip>
                    )}
                  </span>
                );
              }
              return (
                <span className="total" key={index}>
                  {label}:
                  <a id={key} onClick={handleFilter}>
                    {count[key]}
                  </a>
                </span>
              );
            })}
          </div>
        </div>
      )}
      {!startUpload && count.validate > 0 && (
        <div className="base-main-content">
          <div className="upload-status">
            <span className="error">
              存在校验错误的文件
              <a id="validate" onClick={handleFilter}>
                {count.validate}
              </a>
            </span>
            <DeleteFilled onClick={handleRemoveValidateError} style={{ color: 'red' }} />
          </div>
        </div>
      )}
      <div className="base-batch-list">
        <Row justify="space-between" align="middle">
          <Col>
            {NameMapping[filterType]}
            <a id={filterType} onClick={handleFilter}>
              {count[filterType]}
            </a>
            个
          </Col>
          <Col>
            <Button
              size="small"
              disabled={startUpload || !fileList[0]}
              onClick={handleUpload}
              loading={validating}
              type="primary"
            >
              开始上传
            </Button>
          </Col>
        </Row>
        <Table
          className="list-table"
          size="small"
          columns={columns}
          bordered={false}
          rowKey="uid"
          dataSource={fileList.filter(
            item => filterType === 'all' || (filterType === 'waiting' && !item.status) || item.status === filterType
          )}
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
          }}
        />
      </div>
    </div>
  );
};

export default BaseBatchUpload;
