import { UploadProps, RcFile } from 'antd/lib/upload/interface.d';
export interface IAppProps extends UploadProps {
  className?: string;
  data?: any; // { categoryId: 'careers_id' }, // 上传的参数
  max?: number; // 同时上传的最大值 默认是6， 因为google是6
  // reg 用于匹配 file.name, 如果符合，就返回icon作为图标，
  iconMapping?: { reg: RegExp; icon: any }[];
  // 如果返回 !result 则不继续上传。返回
  /**
   * 点击开始后先执行判断，{isSuccess: boolean; msg: {[uid: string]:string}}
   * @param fileList {any} 所有的附件
   * @returns {isSuccess: boolean; msg: {[uid: string]: string}} 结果 或失败时 uid对应文件的错误信息
   */
  onValidateBeforeUpload?: (fileList: RcFile[]) => { isSuccess: boolean; msg: { [uid: string]: string } };
  /**
   * 单个文件上传成功执行，
   * @param file {any} 上传的附件
   * @param fileId {string} 上传到目标地址后，返回的id
   * @returns {string} 错误信息。如果存在，则设置该附件的最终状态为上传失败，并注入返回的错误信息
   */
  onUploadFile?: (file, fileId) => string | void;
  /**
   * 当所有的附件上传成功后执行
   * @param fileList {}
   */
  onUploadComplete?: (fileList: RcFile[]) => void;
}
