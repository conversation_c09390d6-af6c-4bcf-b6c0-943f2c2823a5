import React, { useCallback, useEffect, useState, useMemo, ReactNode } from 'react';
import { Row, Col, Upload, Modal, Button, Form } from 'antd';
import type { UploadProps } from 'antd';
import classnames from 'classnames';

import './style.less';

const { Dragger } = Upload;

export interface UploadModalParams {
  /** 下载附件url前缀 */
  downUrlPrefix?: string;
  /** 外部传来的form */
  form?: any;
  /** 嵌套内容 - 可选 */
  children?: any;
  /** 弹窗标题 - 可选，默认标题："上传" */
  title?: string;
  /** 上传地址 */
  action: string;
  /** upload参数 - 可选*/
  uploadParams?: any;
  /** 确认按钮 loading - 可选*/
  confirmLoading?: boolean;
  /** 类名 - 可选 */
  className?: string;
  /** 是否允许上传多份 - 可选，默认是 */
  isMultiple?: boolean;
  /** 下载模板url - 可选 */
  templateLink?: string;
  /** 是否展示下载模板 - 可选，默认不展示 */
  isShowTemplate?: boolean;
  /** 弹窗关闭后 - 可选 */
  onAfterClose?: () => void;
  /** 下载模板方法 - 可选  */
  defineTemplate?: () => void;
  /** 附件切换 - 可选 */
  onChangeFile?: (file) => void;
  /** 上传附件前置 - 可选 */
  onBeforeUpload?: (file) => void;
  /**
   *  点击确认按钮
   * @parrams values - formValues 数据
   * @params closeFn - 关闭弹窗回调
   * @params updateLoadingFn - 更新确认按钮的loading
   */
  onOk?: (values: any, closeFn: () => void, updateLoadingFn: (newLoading: boolean) => void) => void;
}

export interface IAppProps extends UploadModalParams {
  // open:boolean;
}
const UploadModal: React.FC<IAppProps> = props => {
  const {
    onOk,
    action,
    children,
    className,
    downUrlPrefix,
    title = '上传',
    onAfterClose,
    templateLink,
    onChangeFile,
    form: propForm,
    defineTemplate,
    onBeforeUpload,
    uploadParams = {},
    isMultiple = true,
    confirmLoading = false,
    isShowTemplate = false,
  } = props;

  const [form] = Form.useForm(propForm);
  const [loading, setLoading] = useState<boolean>(false);
  const [visible, setvisible] = useState<boolean>(false);

  const files = Form.useWatch('files', form);

  const isNotUpload = (files || []).length === 0;
  const isNotPass = (files || []).some(({ status }) => status !== 'done');

  /**
   * 更新loading
   *  - 兼容upload方法 - 所以内部确认按钮的loading
   * */
  const handleUpdateLoading = useCallback(newLoading => {
    setLoading(newLoading);
  }, []);

  useEffect(() => {
    handleUpdateLoading(confirmLoading);
  }, [confirmLoading]);

  /** 下载模板 */
  const handleDownTemp = useCallback(() => {
    if (templateLink) {
      return window.open(templateLink);
    }
    defineTemplate?.();
  }, [templateLink, defineTemplate]);

  const handleChange = useCallback(
    info => {
      info.fileList.map(file => {
        if (file.response) {
          if (file.response.status !== '0' || file.response?.httpStatus !== 200) file.status = 'error';
          const fileId = file.response?.data?.result?.fileIds?.[0];
          file.fileId = fileId;
          if (downUrlPrefix) {
            file.url = `${downUrlPrefix}/${fileId}`;
          }
        }
        return file;
      });
      onChangeFile?.(info);
    },
    [downUrlPrefix]
  );

  const uploadProps: UploadProps = useMemo(
    () => ({
      name: 'files',
      action: action,
      multiple: isMultiple,
      onChange: handleChange,
      beforeUpload: onBeforeUpload,
      showUploadList: {
        showPreviewIcon: false,
        showDownloadIcon: false,
      },
      // progress: {
      //   strokeColor: {
      //     '0%': '#108ee9',
      //     '100%': '#108ee9',
      //   },
      //   strokeWidth: 3,
      //   format: percent => percent && `${parseFloat(percent.toFixed(2))}%`,
      // },
      ...uploadParams,
    }),
    [uploadParams, downUrlPrefix]
  );

  useEffect(() => {
    setvisible(true);
  }, []);

  const handleCancel = useCallback(() => {
    setvisible(false);
  }, []);

  const normFile = useCallback((e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  }, []);

  const handleOk = () => {
    form.validateFields().then(formValues => {
      onOk?.(formValues, handleCancel, handleUpdateLoading);
    });
  };

  return (
    <Modal
      width={650}
      title={title}
      open={visible}
      maskClosable={false}
      onCancel={handleCancel}
      afterClose={() => onAfterClose?.()}
      footer={
        <>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleOk} disabled={isNotUpload || isNotPass} loading={loading}>
            确定
          </Button>
        </>
      }
    >
      <Form form={form}>
        <div className={classnames('dhr-upload-modal-container', className)}>
          {isShowTemplate && (
            <Row className="dhr-upload-file-template" onClick={handleDownTemp}>
              <Col>
                <span>下载模板</span>
              </Col>
            </Row>
          )}
          <Row>
            <Col span={24}>
              <div>
                <Form.Item
                  name="files"
                  valuePropName="fileList"
                  getValueFromEvent={normFile}
                  rules={[{ required: true, message: '请上传附件' }]}
                  noStyle
                >
                  <Dragger {...uploadProps}>
                    <div className="dhr-file-import-upload-container">
                      <span>拖动文件到此或</span>
                      <span className="dhr-file-browse-upload-text">浏览</span>
                    </div>
                  </Dragger>
                </Form.Item>
              </div>
            </Col>
          </Row>
          {children}
        </div>
      </Form>
    </Modal>
  );
};

export default UploadModal;
