import { Button } from 'antd';
import React from 'react';

export interface IAppProps {
  step: number;
  /**
   * 取消
   */
  onCancel: () => void;
  /**
   * 切换step
   */
  onChangeStep: (step: number) => void;
  /**
   * 确认并导入
   * */
  onImportExcel: () => void;
  /**
   * 下一步Fn
   *
   */
  onNextStep: () => void;
  /**
   * 导入 - loading
   */
  importLoading: boolean;
  /**
   * 下一步 - loading
   */
  nextLoading: boolean;
  /**
   * 下一步 - disabled
   */
  nextDisabled: boolean;
  /**
   * 导入 - disabled
   */
  // importDisabled: boolean;
  /**是否需要校验 */
  isValidate: boolean;
}
const FooterSteps: React.FC<IAppProps> = ({
  step = 0,
  nextLoading,
  nextDisabled,
  importLoading,
  // importDisabled,
  isValidate,
  onCancel,
  onNextStep,
  onChangeStep,
  onImportExcel,
}) => {
  return (
    <div>
      {step === 0 && (
        <>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" disabled={nextDisabled} loading={nextLoading} onClick={onNextStep}>
            {isValidate ? '下一步' : '确认'}
          </Button>
        </>
      )}
      {step === 1 && (
        <>
          <Button onClick={() => onChangeStep(step - 1)}>返回</Button>
          <Button type="primary" loading={importLoading} onClick={onImportExcel}>
            确定并立即导入
          </Button>
        </>
      )}
      {step === 2 && (
        <>
          <Button onClick={() => onChangeStep(step - 2)}>返回</Button>
        </>
      )}
    </div>
  );
};

export default FooterSteps;
