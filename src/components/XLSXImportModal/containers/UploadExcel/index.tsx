import React, { useCallback, useMemo } from 'react';
import { Row, Col, Upload, Form } from 'antd';
import type { UploadProps } from 'antd';
import classnames from 'classnames';

import { ModalCommonProps } from '../../index';

import './style.less';

const { Dragger } = Upload;
const { Item: FormItem } = Form;

export interface IAppProps extends ModalCommonProps {
  className?: string;
  /** Upload组件参数 */
  uploadConfig?: UploadProps;
  /** 附件下载地址 */
  downloadUrlPrefix?: string;
  /** 改变附件 */
  onChangeFile: (file) => void;
  /** 上传前 */
  onBeforeUpload: (file) => any;
}

const UploadExcel: React.FC<IAppProps> = ({
  className,
  templateLink,
  defineTemplate,
  uploadConfig = {},
  isMultiple = true,
  downloadUrlPrefix,
  onBeforeUpload,
  onChangeFile,
}) => {
  /** 下载模板 */
  const handleDownTemp = useCallback(() => {
    if (templateLink) {
      return window.open(templateLink);
    }
    defineTemplate && defineTemplate();
  }, [templateLink, defineTemplate]);

  const uploadProps: UploadProps = useMemo(
    () => ({
      name: 'files',
      multiple: isMultiple,
      onChange: onChangeFile,
      beforeUpload: onBeforeUpload,
      showUploadList: {
        showPreviewIcon: false,
        showDownloadIcon: false,
      },
      ...uploadConfig,
      // progress: {
      //   strokeColor: {
      //     '0%': '#108ee9',
      //     '100%': '#108ee9',
      //   },
      //   strokeWidth: 3,
      //   format: percent => percent && `${parseFloat(percent.toFixed(2))}%`,
      // },
    }),
    [uploadConfig]
  );

  const normFile = useCallback((e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  }, []);

  return (
    <div className={classnames('uploadExcelContainer', className)}>
      <Row className="downXlsxTemp" onClick={handleDownTemp}>
        <Col>
          <span>下载模板</span>
        </Col>
      </Row>
      <Row>
        <Col span={4}>
          <div className="xlsxImportUploadTitle">附件</div>
        </Col>
        <Col span={18}>
          <div>
            <FormItem
              noStyle
              name="files"
              valuePropName="fileList"
              getValueFromEvent={normFile}
              rules={[{ required: true, message: '请上传附件' }]}
            >
              <Dragger {...uploadProps}>
                <div className="xlsxImportUploadContainer">
                  <span>拖动文件到此或</span>
                  <span className="browseUploadText">浏览</span>
                </div>
              </Dragger>
            </FormItem>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default UploadExcel;
