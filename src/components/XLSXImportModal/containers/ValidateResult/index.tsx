import classnames from 'classnames';
import { Row, Col } from 'antd';
import React, { useCallback, useEffect, useRef } from 'react';

import './style.less';

export interface IAppProps {
  total?: string;
  success?: string;
  error?: string;
  errMsg?: string[];
  className?: string;
}
const ValidateResult: React.FC<IAppProps> = ({ className, total, success, error, errMsg = [] }) => {
  const resourceRef = useRef(null);

  const handleCleanupResources = useCallback(() => {
    const DOMURL = window.URL || window['webkitURL'] || window['mozURL'] || window['msURL'];
    if (resourceRef.current) {
      DOMURL.revokeObjectURL(resourceRef.current);
      resourceRef.current = null;
    }
  }, []);

  const handleDownloadErrorInfo = useCallback(() => {
    /** 清除URL引用 */
    handleCleanupResources();
    const text = (errMsg || []).join('\n');
    const DOMURL = window.URL || window['webkitURL'] || window['mozURL'] || window['msURL'];
    const blob = new window.Blob([text], { type: 'text/plain' });
    const url = DOMURL.createObjectURL(blob);
    const aLink = document.createElement('a');
    aLink.download = '错误报告.txt';
    aLink.href = url;
    aLink.click();
  }, [errMsg]);

  useEffect(() => {
    return handleCleanupResources;
  }, []);
  return (
    <div className={classnames('xlsxValidateResultContainer', className)}>
      <Row>
        <Col span={24} className="xlsx-import-result-item">
          <span>导入记录总数:</span>
          <span>【{total}】</span>
        </Col>
        <Col span={24} className="xlsx-import-result-item">
          <span> 通过记录总数:</span>
          <span>【{success}】</span>
        </Col>
        <Col span={24} className="xlsx-import-result-item">
          <span>错误记录总数:</span>
          <span>【{error}】</span>
        </Col>
      </Row>
      {errMsg.length > 0 && (
        <Row>
          <Col span={24} className="xlsx-import-error-tip-item">
            {errMsg.slice(0, 10).map((text, index) => (
              <div key={index}>{text}</div>
            ))}
          </Col>
          <Col span={24} className="xlsx-import-error-tip-download" onClick={handleDownloadErrorInfo}>
            <a>下载详细错误报告</a>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default ValidateResult;
