.xlsxValidateResultContainer {
  .xlsx-import-result-item,
  .xlsx-import-error-tip-download {
    text-align: center;
  }

  .xlsx-import-error-tip-download {
    margin-top: 10px;
    & > a {
      position: relative;
      &:hover {
        &::before {
          content: '';
          left: 4px;
          right: 2px;
          height: 1.5px;
          bottom: -2px;
          position: absolute;
          background-color: var(--antd-dynamic-primary-shadow-color);
        }
      }
    }
  }

  .xlsx-import-error-tip-item {
    color: #999;
    & > div {
      margin-top: 6px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
