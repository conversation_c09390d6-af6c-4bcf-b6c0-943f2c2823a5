import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';
import { Modal, Form } from 'antd';
import ExcelJS from 'exceljs';
import { exportMultiExcel, IExport } from '@utils/excel';
import type RequestConfig from '@utils/http';
import useAttachment from '@hooks/useAttachment';
import { request as fetchApi } from '@utils/http';
import { showErrNotification, showSucNotification, showWarnNotification, toStartDate } from '@utils/tools';

import FooterSteps from './containers/FooterSteps';
import UploadExcel from './containers/UploadExcel';
import ValidateResult from './containers/ValidateResult';

import entity from './entity';

import './style.less';

export const excelRegexp = /\.(xlr|xlw|xlam|xml|xls|xlt|xltm|xltx|xlsb|xlsm|xlsx)$/i;

const formLayout = {
  wrapperCol: { span: 18 },
  labelCol: { span: 4 },
};

/**使用例子 */
// XLSXImportModal.import({
//   sheetConfigs: [
//     {
//       names: ['code', 'name', 'deptFullName', 'group', 'payGroup', 'relation', 'status', 'item', 'money'],
//       nameFormats: {
//         money: {
//           type: 'number',
//         },
//       },
//     },
//   ],
//   importConfig: {
//     fetchConfig: {
//       method: 'POST',
//       url: '/apis/fff',
//       data: {},
//     },
//   },
// });

// export interface UploadConfig {
//   url: string;
//   method: string;
//   params: {
//     categoryId: string;
//     catalogId: string;
//   };
// }

// 默认提交数据的路径
export const defaultFileKeyFullPath = 'data';

export interface FetchApiConfig {
  /** 请求的内容 */
  fetchConfig: RequestConfig;
  /**
   * 文件key的具体路径
   * 默认值: data.file
   * 默认放在data里面,文件key默认为file
   */
  fileKeyFullPath?: string;
  /**
   * 数据额外处理函数
   * files 上传的数据list
   * 默认提交:转换格式好的数据
   * formData 格式化好的数据
   * 如果单纯想用file的数据，那么fileKeyType为file即可。
   */
  dataHandler?: (formValues, formData) => Promise<any>;
  /**
   *  默认用的format的数据
   */
  fileKeyType?: 'file' | 'format';
  /** 导入成功 */
  onOk?: () => void;
}

export interface ValidateData {
  total: string;
  error: string;
  success: string;
  errMsg: string[];
}

export interface FormatFn {
  /** 是否校验通过 */
  isPass: boolean;
  /** 格式化转换的数据 */
  data: any;
  /** 提示语 */
  tip?: string;
}

export interface SheetConfigs {
  /**
   * sheet的名称 - 没传 默认Sheet[1、2、 3....]
   */
  sheetName?: string;
  /**
   * 多个sheet蒲时对应的key name
   * sheet集合上传对应的fileKey - 没传 默认 使用sheetName
   */
  sheetKey?: string;
  /**
   * 字段名称
   */
  names: string[];
  /**
   * 需要转换的类型 -> 报错则抛错
   * 可校验的类型：
   * 【number】【date】 【(text, rowItem, rowIndex, colIndex):FormatFn => {}】
   * 例如：{ a:'date'}
   * */
  nameFormats?: {
    [key: string]: {
      type: 'number' | 'date' | 'fn';
      formatFn?: (text: any, rowItem: any, rowIndex: number, colIndex: number) => FormatFn;
    };
  };
}

export interface ModalCommonProps {
  /** 模板链接 */
  templateLink?: string;
  /** 点击下载模板 */
  defineTemplate?: () => void;
  /**
   * 是否支持上传多份
   * 默认 true
   * */
  isMultiple?: boolean;
  /**
   * 上传前的校验
   *  通过校验直接返回false -> false代表不上传
   */
  onBeforeUpload?: (file: any) => boolean;
  /**
   * 校验sheet的key字段
   */
  sheetConfigs?: SheetConfigs[];
  /**
   * 校验接口配置
   */
  validateConfig?: FetchApiConfig;
  /**
   * 提交的接口配置
   */
  importConfig: FetchApiConfig;

  /** 附件是否需要上传 - 默认 上传 */
  isUpload?: boolean;

  /**
   * 导入成功回调
   * values 数据
   * onCancel 关闭弹窗
   */
  onOk: (values?: any, onCancel?: () => void) => void;
  /** 支持从外部传进来 */
  propForm?: any;
  /** 插入内容 */
  content?: ReactNode;
}

export interface IAppProps extends ModalCommonProps {
  visible: boolean;
  /** 销毁组件 */
  onDestroy: () => void;
}

const XLSXImportModal: React.FC<IAppProps> = props => {
  const {
    onOk,
    content,
    visible,
    propForm,
    onDestroy,
    importConfig,
    onBeforeUpload,
    validateConfig,
    isUpload = true,
    sheetConfigs = [{ names: [], sheetName: 'Sheet1', sheetKey: 'importDTOs' }],
  } = props;

  const { apiCongiMap, runAsync, onUpdateApiCongiMap } = useAttachment({ manual: true });

  const [form] = Form.useForm(propForm);

  // 格式好的数据
  const formatDataRef = useRef(null);
  const [step, setStep] = useState<number>(0);
  const [open, setOpen] = useState<boolean>(false);
  const [importLoading, setImportLoading] = useState<boolean>(false);
  const [nextStepLoading, setNextStepLoading] = useState<boolean>(false);
  const [validateData, setValidateData] = useState<Partial<ValidateData>>({});

  const files = Form.useWatch('files', form);

  const isUploadNotPass = useMemo(() => {
    const lenEmpty = (files || []).length === 0;
    if (lenEmpty) {
      return true;
    }
    const isNotPass = (files || []).some(({ status }) => status !== 'done' && isUpload);
    if (isNotPass) {
      return true;
    }
    return false;
  }, [files]);

  /** 缓存apiConfigMap - 避免每次打开弹窗都需要调附件服务接口 */
  useEffect(() => {
    const apiConfigMap = entity.apiConfigMap;
    if (apiConfigMap) {
      onUpdateApiCongiMap(apiConfigMap);
    } else {
      runAsync()
        .then((newApiConfigMap: any) => {
          entity.setApiConfigMap({ ...(newApiConfigMap || {}) });
        })
        .catch(error => {
          console.log('附件地址获取失败：', error);
          showWarnNotification('附件上传地址获取失败，请刷新后重试');
        });
    }
  }, []);

  useEffect(() => {
    setOpen(visible);
  }, [visible]);

  const handleCancel = useCallback(() => {
    setOpen(false);
  }, []);

  /** upload组件参数 */
  const uploadProps = useMemo(() => {
    const { upload } = apiCongiMap;
    if (upload) {
      const downloadUrlPrefix = apiCongiMap.download?.url || '';
      return {
        uploadConfig: {
          action: upload.url,
          data: {
            ...(upload.params || {}),
            // 文件储存路径
            categoryId: `/dhr/common/${upload?.params?.categoryId || ''}`,
          },
        },
        downloadUrlPrefix,
      };
    }
    return {};
  }, [apiCongiMap]);

  // 附件 - 上传前置校验
  const handleBeforeUpload = useCallback(
    file => {
      if (onBeforeUpload) {
        return onBeforeUpload(file);
      }
      if (!excelRegexp.test(file.name)) {
        showErrNotification('请上传 excel 文件');
        file.status = 'error';
        return false;
      }
      return isUpload;
    },
    [onBeforeUpload, isUpload]
  );

  // 附件内容 - 上传切换
  const handleChangeFile = useCallback(
    info => {
      info.fileList.map(file => {
        if (file.response) {
          if (file.response.status !== '0' && !file.response?.data?.result?.fileIds?.[0]) file.status = 'error';
          const fileId = file.response?.data?.result?.fileIds?.[0];
          file.fileId = fileId;
          if (uploadProps.downloadUrlPrefix) {
            file.url = `${uploadProps.downloadUrlPrefix}/${fileId}`;
          }
        }
        return file;
      });
    },
    [isUpload, uploadProps]
  );

  const readFile = useCallback(
    file =>
      new Promise((resolve, reject) => {
        const fr = new FileReader();
        fr.onload = () => {
          resolve(fr.result);
        };
        fr.onerror = reject;
        fr.readAsArrayBuffer(file.originFileObj);
      }),
    []
  );

  const onFormatDigits = useCallback((val: string | number) => {
    const formatValStr = String(val).padStart(2, '0');
    return formatValStr;
  }, []);

  // 提交数据
  const handleImportResult = useCallback(
    async (list?: any[], formValues?: any) => {
      let result = formatDataRef.current;
      setImportLoading(true);
      if (importConfig) {
        try {
          const { fetchConfig, fileKeyFullPath, dataHandler, fileKeyType } = importConfig || {};
          const fullPath = fileKeyFullPath || defaultFileKeyFullPath;
          // 不存在格式化好的数据 说明 则是没有校验接口的
          if (!result) {
            result = dataHandler
              ? await dataHandler(list || [], formValues || {})
              : fileKeyType === 'file'
                ? formValues?.files
                : list;
          }
          const fetchParams = {
            params: {},
            data: {},
            ...fetchConfig,
          };
          fetchParams[fullPath] = result;
          fetchApi({
            ...fetchParams,
            onSuccess: res => {
              // return
              showSucNotification('操作成功！');
              handleCancel();
              onOk?.(res);
            },
          }).finally(() => {
            setImportLoading(false);
          });
        } catch (error) {
          console.error('导入提交报错啦：', error);
          setImportLoading(false);
        }
      } else {
        /**   不上传-本地导入 */
        if (!result) {
          result = list;
        }
        onOk(
          {
            list: result,
            ...formValues,
          },
          () => {
            setImportLoading(false);
            handleCancel();
          }
        );
      }
    },
    [importConfig]
  );

  // 前端-校验导入数据
  const handleValidData = useCallback(
    async (list: any[], formValues: any, errMsg: string[], success: number) => {
      try {
        const files = formValues.files;
        const error = errMsg.length;
        const total = error + success;
        const newValidateData: ValidateData = {
          errMsg,
          total: onFormatDigits(total),
          error: onFormatDigits(error),
          success: onFormatDigits(success),
        };

        /**
         * 显示错误信息
         */
        if (error > 0) {
          setNextStepLoading(false);
          setValidateData(newValidateData);
          // const newStep = error > 0 ? 2 : 1;
          return setStep(2);
        }

        /** 缓存格式化好的数据 */
        // /** 存在校验的接口 */
        if (validateConfig) {
          const { fetchConfig, fileKeyFullPath, dataHandler, fileKeyType } = validateConfig || {};
          const fullPath = fileKeyFullPath || defaultFileKeyFullPath;
          const result = dataHandler ? await dataHandler(list, formValues) : fileKeyType === 'file' ? files : list;
          const fetchParams = {
            data: {},
            ...fetchConfig,
          };
          /** 缓存格式化好的数据 */
          formatDataRef.current = result;
          fetchParams[fullPath] = result;
          fetchApi({
            ...fetchParams,
            onSuccess: list => {
              const errorNum = (list || []).reduce((pre, cur) => pre + cur.errorNum, 0);
              const totalNum = (list || []).reduce((pre, cur) => pre + cur.totalNum, 0);
              const successNum = (list || []).reduce((pre, cur) => pre + cur.successNum, 0);
              const errMsg = (list || []).reduce(
                (prev, obj) => prev.concat(obj.errMsg.map(text => `${obj.fileName}: ${text}`)),
                []
              );
              const newValidateValues: ValidateData = {
                errMsg,
                total: onFormatDigits(totalNum),
                error: onFormatDigits(errorNum),
                success: onFormatDigits(successNum),
              };
              setValidateData(newValidateValues);
              // 跳转到第二步
              setStep(errorNum > 0 ? 2 : 1);
            },
          }).finally(() => {
            setNextStepLoading(false);
          });
          return;
        }
        /**
         * 不存在校验的接口
         * 直接提交
         */
        setNextStepLoading(false);
        // 提交
        handleImportResult(list, formValues);
      } catch (error) {
        console.error('校验导入报错啦：', error);
        setNextStepLoading(false);
      }
    },
    [validateConfig, importConfig]
  );

  /** 下一步 */
  const handleNextStep = async () => {
    form.validateFields().then(formValues => {
      setNextStepLoading(true);
      const xlsxList = [];
      const errTipList = [];
      // 成功条数
      let succTotal = 0;
      const oldFiles = formValues.files;
      const wb = new ExcelJS.Workbook();
      const filesLen = oldFiles.length;
      try {
        // 没传 默认取Sheet1
        const sheetConfigsSlice = (sheetConfigs || []).slice();
        Promise.all(
          oldFiles.map(async file => {
            const buffer: any = await readFile(file);
            const uploadWb = await wb.xlsx.load(buffer);

            const processSheet = async (config, configIndex) => {
              const { names, nameFormats = {} } = config;
              const isHasFormats = Object.values(nameFormats).length > 0;
              const sheetName = config.sheetName || `Sheet${configIndex + 1}`;
              const sheetKey = config.sheetKey || sheetName;
              const wsItem = uploadWb.getWorksheet(sheetName);
              if (wsItem) {
                const formatRows = [];
                // 包含headers
                const allRows = wsItem
                  .getSheetValues()
                  .slice(1)
                  .map((r: any[]) => r.slice(1));
                /** 格式化数据 */
                if (names?.length > 0) {
                  const rows = allRows.slice(1);
                  for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
                    const rowItem = rows[rowIndex];
                    const formatValues = {};
                    for (let colIndex = 0; colIndex < names.length; colIndex++) {
                      const name = names[colIndex];
                      let rowItemVal = rowItem[colIndex];
                      const formatDetail = nameFormats[name];
                      if (isHasFormats && formatDetail) {
                        const { type, formatFn } = formatDetail;
                        const isDate = type === 'date';
                        const isFormatFn = type === 'fn';
                        const isNumber = type === 'number';
                        // 数字类型转换
                        if (isNumber) {
                          rowItemVal = parseInt(rowItemVal);
                          // 类型转换错误
                          if (isNaN(rowItemVal)) {
                            errTipList.push(
                              `${file.name}:${sheetName}:第${rowIndex + 2}行第${colIndex}列的【${rowItem[colIndex]
                              }】类型错误，请检查；`
                            );
                            continue;
                          }
                        } else if (isDate) {
                          rowItemVal = toStartDate(rowItemVal);
                          // 类型转换错误
                          if (isNaN(rowItemVal)) {
                            errTipList.push(
                              `${file.name}:${sheetName}:第${rowIndex + 2}行第${colIndex}列的【${rowItem[colIndex]
                              }】类型错误，请检查；`
                            );
                            continue;
                          }
                        } else if (isFormatFn) {
                          /**
                           * 函数 - 自定义校验
                           * 返回对象：FormatFn
                           *
                           */
                          const formatResult = formatFn(rowItemVal, rowItem, rowIndex, colIndex) || {};
                          if (!formatResult.isPass) {
                            const errTip =
                              formatResult.tip ||
                              `${file.name}:${sheetName}:第${rowIndex + 2}行第${colIndex}列的【${rowItem[colIndex]
                              }】类型错误，请检查；`;
                            errTipList.push(errTip);
                            continue;
                          }
                          rowItemVal = formatResult.data;
                        }
                      }

                      formatValues[name] = rowItemVal;
                    }
                    succTotal += 1;
                    formatRows.push(formatValues);
                  }
                }
                /**
                 * @params formatRows 格式化rows
                 * @params sheetKey sheetName
                 * @params rows 原始数据
                 */
                return { sheetKey, formatRows, rows: allRows };
              }
              return;
            };

            const results = await Promise.all(sheetConfigsSlice.map(processSheet));

            const dataMap = results.reduce((acc, result) => {
              if (result) {
                const { sheetKey, formatRows, rows } = result;
                acc[sheetKey] = formatRows;
                acc[`${sheetKey}_rows`] = rows;
              }
              return acc;
            }, {});

            const result = {
              objFileSaveDTO: {
                uid: file.uid,
                description: '',
                fileName: file.name,
                fileId: file.fileId,
                csbFileId: file.fileId,
                fileSize: file.size,
                fileType: file.type,
              },
              ...dataMap,
            };

            xlsxList.push(result);
            // 全部处理完毕 则调用下一步
            xlsxList.length === filesLen && handleValidData(xlsxList, formValues, errTipList, succTotal);
          })
        );
      } catch (error) {
        setNextStepLoading(false);
        console.log('文件解析错误：', error);
        showErrNotification('文件解析错误~');
      }
    });
  };
  return (
    <Modal
      title="导入"
      width={700}
      open={open}
      onCancel={handleCancel}
      afterClose={() => onDestroy()}
      footer={
        <FooterSteps
          step={step}
          onCancel={handleCancel}
          onNextStep={handleNextStep}
          isValidate={!!validateConfig}
          nextLoading={nextStepLoading}
          importLoading={importLoading}
          nextDisabled={isUploadNotPass}
          onChangeStep={val => setStep(val)}
          onImportExcel={() => handleImportResult()}
        />
      }
    >
      <Form form={form} {...formLayout}>
        <div className="xLSXImportModal">
          <UploadExcel
            {...uploadProps}
            {...props}
            onChangeFile={handleChangeFile}
            onBeforeUpload={handleBeforeUpload}
            className={classnames('step-upload-xlsx', { 'step-tabpane-hidden': step !== 0 })}
          />
          <div className={classnames({ 'step-tabpane-hidden': step !== 0 })}>{content}</div>
          <ValidateResult
            {...validateData}
            className={classnames('step-upload-xlsx', { 'step-tabpane-hidden': step === 0 })}
          />
        </div>
      </Form>
    </Modal>
  );
};

function withParams(params: ModalCommonProps): IAppProps {
  return {
    visible: true,
    onDestroy: () => { },
    ...params,
  };
}

function confirm(config: IAppProps) {
  const containers = document.createDocumentFragment();
  const destroy = () => {
    reactUnmount(containers);
  };
  reactRender(<XLSXImportModal {...config} onDestroy={destroy} />, containers);
}

export type ModalStaticFunctions = Record<string, any>;

type ModalType = typeof XLSXImportModal & ModalStaticFunctions;

const ConfirmModal = XLSXImportModal as ModalType;

ConfirmModal.import = function confirmFn<T extends ModalCommonProps>(props: T) {
  return confirm(withParams(props));
};

/**
 * @description 批量下载附件
 * @param values 附件ID List
 * @param action 请求地址
 * @param zipName 打包名称
 */
ConfirmModal.batchDownload = (values: string[], action: string, zipName?: string) => {
  // 使用form下载附件
  const params = {
    // 参数
    fileIds: (values && values.join(',')) || '',
    zipName: zipName || '人员附件',
  };
  const form = window.document.createElement('form');
  window.document.body.appendChild(form);
  for (const obj in params) {
    if (params.hasOwnProperty(obj)) {
      const input = window.document.createElement('input');
      input.type = 'hidden';
      input.name = obj;
      input.value = params[obj];
      form.appendChild(input);
    }
  }
  form.target = '_blank';
  form.method = 'POST'; // 请求方式
  form.action = action;
  form.submit();
  form.remove();
};

ConfirmModal.exportMultiExcel = (exportArr: IExport[], xlsxName: string) => {
  return exportMultiExcel(exportArr, xlsxName);
};

export default ConfirmModal;
