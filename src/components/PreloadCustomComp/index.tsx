import React, { useEffect, useState, forwardRef } from 'react';
import { treeDataToListByBFS } from '@cvte/kylin-tools';
import { http } from '@cvte/cir-core-net';

// import { CUSTOM_FIELD } from '../../config/fieldMapCondition';

// impor, { ICustomComp } from 'src/index.d';
// 自定义组件格式
type ICustomComp = {
  mainData: {
    // 资源名称
    sourceName: string;
    // 暴露的模块名
    exposesKey: string;
    // 组件名称
    label: string;
    // 其他属性
    [propsName: string]: any;
  };

  bizDataMap: {
    COMPONENT: {
      // 资源名称
      sourceName: string;
      // 暴露的模块名
      exposesKey: string;
      // 输出类型
      outputType: string;
      // 终端类型
      clientType: string;
      // 其他属性
      [propsName: string]: any;
    }[];
  };
}[];

const CUSTOM_FIELD = 'CUSTOM_FIELD';
const DATA_API_PREFIX = '/apis/common/proxy/lcpData';
const API_PREFIX = '/apis/common/proxy/lcpApp';
const appId = document.getElementById('pluginAppId')?.value;

/**
 * @description: 获取资源组件的配置
 * @param {string} resourceCode
 * @return {*}
 */
const fetchResourceCompConfigApi = async data => {
  // 固定的表单分类
  const classApi = 'LCP_PAGE_COMPONENTS';
  const classAppId = '1269620726624013b98cf4baadaa759f';

  return await http.fetch({
    url: `${DATA_API_PREFIX}/v1/data/read/${classApi}/form/table/main`,
    method: 'post',
    data,
    headers: {
      'x-app-id': classAppId,
    },
  });
};

const formatCommonOptions = (
  fieldMap: { key: string; label: string },
  data: Record<string, any>[],
  dataFormat?: (item: Record<string, any>) => Record<string, any>
) => {
  return (data || [])?.map(item => {
    let tmpItem = {
      ...(item || {}),
      key: item?.[fieldMap?.key],
      value: item?.[fieldMap?.key],
      label: item?.[fieldMap?.label],
    } as Record<string, any>;

    tmpItem = dataFormat ? dataFormat(tmpItem) : tmpItem;
    return tmpItem;
  });
};

/**
 * @description: 静态数据字典
 * @return {*}
 */
const fetchDictList = async (data, header) => {
  return await http.fetch({
    url: `${API_PREFIX}/dict/batch_dict_list`,
    method: 'post',
    data,
    showErrMsg: false,
    headers: {
      'x-app-id': header?.appId || appId,
    },
  });
};

// 预加载组件
const preloadCustomComp = WrapperComp => {
  return forwardRef((props, ref) => {
    const { targetFieldList: targetFieldTreeList } = props;
    const [customCompList, setCustomCompList] = useState<ICustomComp>([]);
    const [dictList, setDictList] = useState([]);

    /**
     * @description: 获取操作符
     * @return {*}
     */
    const fetchOperateList = async () => {
      const res = await fetchDictList(['LCP_FORM_DESGIN_SEARCH_OPT'], null);

      if (res?.success) {
        const tmpOptions = formatCommonOptions(
          { key: 'code', label: 'name' },
          res?.data?.content?.['LCP_FORM_DESGIN_SEARCH_OPT'] || []
        );
        setDictList(tmpOptions);
      }
    };

    /**
     * @description: 获取自定义组件配置
     * @return {*}
     */
    const fetchResourceCompConfig = async () => {
      const targetCustomFieldList = treeDataToListByBFS([...(targetFieldTreeList || [])])?.filter(p =>
        [CUSTOM_FIELD]?.includes(p?.type)
      );
      console.log('targetCustomFieldList', targetCustomFieldList);
      const paramsList = (targetCustomFieldList || [])?.map(item => ({
        attrApi: 'CODE',
        value: item?.attrType,
        andOr: 'or',
      }));

      console.log('paramsList', paramsList);

      const res = await fetchResourceCompConfigApi({
        mainParamsGroups: [{ paramsList }],
        keyType: 'CAMEL',
        page: 1,
        pageSize: 10000,
      });

      if (res?.success) {
        setCustomCompList(res?.data?.content || []);
      }
    };

    /**
     * @description: 初始化请求
     * @return {*}
     */
    const fetchInit = async () => {
      await fetchOperateList();
      fetchResourceCompConfig();
    };

    /**
     * @description: 初始化自定义组件配置
     * @return {*}
     */
    useEffect(() => {
      if (targetFieldTreeList?.length > 0) {
        fetchInit();
      }
    }, [targetFieldTreeList]);

    return <WrapperComp {...(props || {})} customCompList={customCompList} conditionList={dictList} ref={ref} />;
  });
};

export { preloadCustomComp };
export default preloadCustomComp;
