import { LeftOutlined, RightOutlined, DoubleRightOutlined, DoubleLeftOutlined } from '@ant-design/icons';
import { Col, Modal, Row, Input, Checkbox, Table, Button } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { ModalProps } from 'antd/lib/modal';
import classnames from 'classnames';

import useDebounce from '@hooks/useDebounce';
import { sortUp, sortDown, ENUM_SORT_TYPES } from '@utils/tools/sort';

import './style.less';
export interface IAppProps extends ModalProps {
  // 行key
  rowKey?: string;
  rowNameKey?: string;
  unAssignList: Record<string, any>[]; // 未分配的薪资项
  assignList: Record<string, any>[]; // 已分配的薪资项
  onConfirm: (assignList: Record<string, any>[], unAssignList: Record<string, any>[]) => void;
}
const DisplayItemSettingModal: React.FC<IAppProps> = ({
  title,
  rowKey = 'itemId',
  rowNameKey = 'displayName',
  unAssignList = [],
  assignList = [],
  onConfirm,
  ...restProps
}) => {
  const [leftFilterKey, setLeftFilterKey] = useState('');
  const [rightFilterKey, setRightFilterKey] = useState('');
  const [assignSelected, setAssignSelected] = useState<string[]>([]);
  const [unAssignSelected, setUnAssignSelected] = useState<string[]>([]);
  const [_assignList, _setAssignList] = useState<Record<string, any>[]>([]);
  const [_unAssignList, _setUnAssignList] = useState<Record<string, any>[]>([]);

  const assignListJson = JSON.stringify(assignList);
  const unAssignListJson = JSON.stringify(unAssignList);

  useEffect(() => {
    const indexList = [..._assignList]?.map((k, index) => ({
      ...k,
      displayNo: index + 1,
    }));
    _setAssignList(indexList);
  }, [_assignList.length]);

  useEffect(() => {
    _setAssignList(assignList);
    _setUnAssignList(unAssignList);
    return () => {
      _setAssignList([]);
      _setUnAssignList([]);
      setAssignSelected([]);
      setUnAssignSelected([]);
    };
  }, [assignListJson, unAssignListJson]);

  const assignListColumns = useMemo(
    () => [
      {
        title: '字段名称',
        dataIndex: rowNameKey,
        key: rowNameKey,
        filteredValue: rightFilterKey ? [rightFilterKey] : null,
        onFilter: (val, record) => record[rowNameKey].includes(val),
      },
      {
        title: '排序顺序',
        dataIndex: 'displayNo',
        key: 'displayNo',
      },
    ],
    [rightFilterKey]
  );

  /** 字段移入以及移出 */
  const selectToAssign = (type: 'ADD' | 'CANCEL' | 'ADD_ALL' | 'CANCEL_ALL') => {
    // /** 移入 */
    const typeMapFn = {
      ADD: () => {
        const newAssignListTemp = [..._assignList].concat(
          _unAssignList.filter(k => unAssignSelected.includes(k[rowKey]))
        );
        const newUnAssignListTemp = [..._unAssignList].filter(k => !unAssignSelected.includes(k[rowKey]));
        _setAssignList(newAssignListTemp);
        _setUnAssignList(newUnAssignListTemp);
        setAssignSelected([]);
        setUnAssignSelected([]);
      },
      CANCEL: () => {
        const newAssignListTemp = [..._assignList].filter(k => !assignSelected.includes(k[rowKey]));
        const newUnAssignListTemp = [..._unAssignList].concat(
          _assignList.filter(k => assignSelected.includes(k[rowKey]))
        );
        _setAssignList(newAssignListTemp);
        _setUnAssignList(newUnAssignListTemp);
        setAssignSelected([]);
        setUnAssignSelected([]);
      },
      ADD_ALL: () => {
        const newAssignListTemp = [..._assignList, ..._unAssignList];
        const newUnAssignListTemp = [];
        _setAssignList(newAssignListTemp);
        _setUnAssignList(newUnAssignListTemp);
      },
      CANCEL_ALL: () => {
        const newAssignListTemp = [];
        const newUnAssignListTemp = [..._assignList, ..._unAssignList];
        _setAssignList(newAssignListTemp);
        _setUnAssignList(newUnAssignListTemp);
      },
    };
    typeMapFn[type] && typeMapFn[type]();
  };

  const onConfirmFields = () => {
    onConfirm(_assignList, _unAssignList);
  };

  const handleSort = (type: ENUM_SORT_TYPES) => {
    const action = type === ENUM_SORT_TYPES.UP ? sortUp : sortDown;
    const _assignListTemp: Record<string, any>[] = action({
      list: _assignList,
      selectedRowKeys: assignSelected,
      rowKey,
      sortKey: 'displayNo',
    });
    _assignListTemp && _setAssignList(_assignListTemp);
  };

  const handleFilterLeft = useDebounce(({ target: { value } }) => {
    setLeftFilterKey(value);
  }, 300);

  const handleFilterRight = useDebounce(({ target: { value } }) => {
    setRightFilterKey(value);
  }, 300);

  return (
    <Modal {...restProps} width={900} title={title || '项目显示设置'} destroyOnClose onOk={onConfirmFields}>
      <Row className="dhr-display-item-setting-modal">
        <Col span={6}>
          <Row className="dhr-display-item-left-container">
            <Col span={24}>
              <span className="dhr-display-item-left-title">选择需要显示的薪资项目</span>
            </Col>
            <Col span={24}>
              <Input placeholder="请选择需要显示的项目" onChange={handleFilterLeft} />
              <div className="dhr-display-item-left-content-container">
                <Checkbox.Group
                  style={{ width: '100%' }}
                  value={unAssignSelected}
                  onChange={(value: string[]) => setUnAssignSelected(value)}
                >
                  {_unAssignList
                    .filter(listItem => (listItem[rowNameKey] || '').includes(leftFilterKey))
                    .map(k => (
                      <div className="dhr-display-item-left-content-item" key={k[rowKey]}>
                        <Checkbox key={k[rowKey]} value={k[rowKey]}>
                          {k[rowNameKey]}
                        </Checkbox>
                      </div>
                    ))}
                </Checkbox.Group>
              </div>
            </Col>
          </Row>
        </Col>
        <Col span={2}>
          <div className="dhr-display-item-action-container">
            <RightOutlined
              className={classnames(
                'dhr-display-item-action-right',
                _unAssignList.length && unAssignSelected.length
                  ? 'dhr-display-item-action-pointer'
                  : 'dhr-display-item-action-disabled'
              )}
              style={{ fontSize: '18px' }}
              onClick={() => selectToAssign('ADD')}
            />
            <DoubleRightOutlined
              style={{ fontSize: '16px' }}
              onClick={() => selectToAssign('ADD_ALL')}
              className={classnames(
                'dhr-display-item-action-double-right',
                _unAssignList.length > 0 ? 'dhr-display-item-action-pointer' : 'dhr-display-item-action-disabled'
              )}
            />
            <DoubleLeftOutlined
              style={{ fontSize: '16px' }}
              onClick={() => selectToAssign('CANCEL_ALL')}
              className={classnames(
                'dhr-display-item-action-double-left',
                _assignList.length > 0 ? 'dhr-display-item-action-pointer' : 'dhr-display-item-action-disabled'
              )}
            />
            <LeftOutlined
              className={classnames(
                'dhr-display-item-action-left',
                _assignList.length && assignSelected.length
                  ? 'dhr-display-item-action-pointer'
                  : 'dhr-display-item-action-disabled'
              )}
              style={{ fontSize: '18px' }}
              onClick={() => selectToAssign('CANCEL')}
            />
          </div>
        </Col>
        <Col span={16} className="dhr-display-item-right-container">
          <Row>
            <Col span={10}>
              <span className="dhr-display-item-right-title">设置固定项目排序</span>
            </Col>
          </Row>
          <Row justify="space-between" className="dhr-display-item-right-content-container">
            <Col span={10}>
              <Input placeholder="请选择项目" onChange={handleFilterRight} />
            </Col>
            <Col>
              <Button type="primary" size="small" onClick={() => handleSort(ENUM_SORT_TYPES.UP)}>
                上移
              </Button>
              <Button type="primary" size="small" className="ml-10" onClick={() => handleSort(ENUM_SORT_TYPES.DOWN)}>
                下移
              </Button>
            </Col>
          </Row>
          <Table
            size="small"
            rowKey={rowKey}
            pagination={false}
            scroll={{ y: 300 }}
            dataSource={_assignList}
            columns={assignListColumns}
            rowSelection={{
              onChange: (selectedRowKeys: string[]) => setAssignSelected(selectedRowKeys),
            }}
          />
        </Col>
      </Row>
    </Modal>
  );
};

export default DisplayItemSettingModal;
