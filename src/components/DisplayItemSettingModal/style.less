.dhr-display-item-setting-modal {
  .dhr-display-item-left-container {
    .dhr-display-item-left-title {
      line-height: 26px;
    }
    .dhr-display-item-left-content-container {
      margin-top: 10px;
      height: 354px;
      overflow-y: scroll;
      .dhr-display-item-left-content-item {
        margin-bottom: 20px;
      }
    }
  }

  .dhr-display-item-action-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .dhr-display-item-action-right,
    .dhr-display-item-action-left,
    .dhr-display-item-action-double-right,
    .dhr-display-item-action-double-left {
      margin-bottom: 20px;
      display: block;
      &.dhr-display-item-action-pointer {
        cursor: pointer;
        &:hover {
          color: var(--antd-dynamic-primary-hover-color);
        }
      }

      &.dhr-display-item-action-disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }
    }
  }

  .dhr-display-item-right-container {
    .dhr-display-item-right-title {
      line-height: 26px;
    }

    .dhr-display-item-right-content-container {
      margin-bottom: 10px;
    }
  }
}
