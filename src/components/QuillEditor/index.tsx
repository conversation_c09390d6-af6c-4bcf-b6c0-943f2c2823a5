import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import QuillBetterTable from 'quill-better-table';
import classnames from 'classnames';
import Quill from 'quill';

import 'quill/dist/quill.snow.css';
import 'quill-better-table/dist/quill-better-table.css';

import './style.less';

Quill.register(
  {
    'modules/better-table': QuillBetterTable,
  },
  true
);

export const tableStyleStr =
  'table.quill-better-table{table-layout: fixed;border-collapse: collapse;}table.quill-better-table td{border: 1px solid #000;padding: 2px 5px;}';

export const qlIndentStyle =
  '.ql-indent-1:not(.ql-direction-rtl) {padding-left: 3em;} .ql-indent-2:not(.ql-direction-rtl) {padding-left: 6em;}  .ql-indent-5:not(.ql-direction-rtl) {padding-left: 15em;} .ql-indent-6:not(.ql-direction-rtl) {padding-left: 18em;}  .ql-indent-7:not(.ql-direction-rtl) {padding-left: 21em;} .ql-indent-8:not(.ql-direction-rtl) {padding-left: 24em;}';
/*
  1.将table的基本样式塞进去 防止发到邮箱那边失去了样式
  2.如果最后的标签是table标签 在最后面加个br标签 解决重新打开富文本编辑器表格最后不可编辑问题
*/
export const onAssembleTplContent = (templateContent: string) => {
  if (!templateContent) return templateContent;
  let styles = '';
  const length = '</style>'.length;
  const index = templateContent.lastIndexOf('</style>');
  const pureStr = index === -1 ? templateContent : templateContent.slice(index + length); // 清楚前面的style标签
  // 没有插入的改类 - 则无需插入style样式
  if (templateContent.includes('quill-better-table')) {
    styles = styles.concat(tableStyleStr);
  }
  /** 后续优化样式问题 */
  if (templateContent.includes('ql-indent')) {
    styles = styles.concat(qlIndentStyle);
  }

  if (styles.length > 0) {
    return `<style>${styles}</style>${pureStr}`;
  }
  return pureStr;
  // const pureContent = content.trim();
  // if (pureContent.endsWith('</table></div>')) {
  //   return `${content}<p><br></p><p><br></p>`;
  // }
  // if (pureContent.endsWith('</table></div><p><br></p>')) {
  //   return `${content}<p><br></p>`;
  // }
};

export interface IAppProps {
  ref?: any;
  value?: string;
  className?: string;
  placeholder?: string;
  /** 是否插入table */
  showTable?: boolean;
  /** table颜色 */
  tableColorOptions?: string[];
  /** 是否允许使用table颜色 */
  showTableColorOptions?: boolean;
  /** 是否展示上面的操作栏 */
  showToolbarOptions?: boolean;
  /** 定制单元格右键菜单 参照quill-better-table的参数 */
  operationMenu?: Record<string, any>;
  onChange?: (value: string, instance) => void;
  onFocus?: () => void;
}

const QuillEditor: React.FC<IAppProps> = forwardRef(
  (
    {
      className,
      placeholder,
      value,
      onChange,
      onFocus,
      operationMenu,
      showTable = true,
      showToolbarOptions = true,
      showTableColorOptions = true,
      tableColorOptions = ['#f6f6f6', 'green', 'red', 'yellow', 'blue', 'white'],
    },
    ref
  ) => {
    const initDataRef = useRef(null);
    const quillRef = useRef(null);
    const editorRef = useRef(null);
    const [quill, setQuill] = useState<Quill>();
    useImperativeHandle(ref, () => ({
      getQuill: () => quillRef.current,
    }));

    // 配置table
    const quillTableOptions = useMemo(() => {
      const colorOptions = !showTableColorOptions
        ? {}
        : {
            color: {
              colors: tableColorOptions,
              text: '单元格背景颜色：',
            },
          };
      return {
        'better-table': {
          operationMenu: !operationMenu
            ? {
                items: {
                  insertColumnRight: {
                    text: '向右插入列',
                  },
                  insertColumnLeft: {
                    text: '向左插入列',
                  },
                  insertRowUp: {
                    text: '向上插入行',
                  },
                  insertRowDown: {
                    text: '向下插入行',
                  },
                  mergeCells: {
                    text: '合并单元格',
                  },
                  unmergeCells: {
                    text: '取消合并单元格',
                  },
                  deleteColumn: {
                    text: '删除列',
                  },
                  deleteRow: {
                    text: '删除行',
                  },
                  deleteTable: {
                    text: '删除表格',
                  },
                },
                ...colorOptions,
              }
            : operationMenu,
          menuContainer: document.body, // 将菜单挂载到body上
        },
        keyboard: {
          bindings: QuillBetterTable.keyboardBindings,
        },
      };
    }, [showTableColorOptions, tableColorOptions, operationMenu]);

    // 插入表格
    const handleInsertTable = useCallback(() => {
      const currentQuill = quillRef.current;
      const tableModule = currentQuill?.getModule('better-table');
      tableModule.insertTable(3, 3);
    }, []);

    const quillOptions = useMemo(() => {
      const toolbarOptions = [
        [{ header: [1, 2, 3, 4, 5, 6, false] }], // 多级标题
        [{ font: [] }], // 字体类型
        ['bold', 'italic', 'underline', 'strike'], // 加粗/倾斜/下划线/中划线
        ['blockquote', 'code-block'], // 块/代码
        [{ list: 'ordered' }, { list: 'bullet' }], // 序列
        [{ indent: '-1' }, { indent: '+1' }], // 缩进
        [{ direction: 'rtl' }], // 居左 居右
        [{ color: [] }, { background: [] }], // 文字颜色/文字背景色
        [{ align: [] }], // 布局
        ['link'], // 链接
        ['clean'], // 清除格式
      ];
      showTable && toolbarOptions.push(['table']);
      const handlers = showTable ? { table: handleInsertTable } : {};
      const toolbar = showToolbarOptions
        ? {
            container: toolbarOptions,
            handlers,
          }
        : false;
      return {
        modules: {
          toolbar,
          history: {
            delay: 2000,
            maxStack: 500,
            userOnly: true,
          },
          table: false,
          ...quillTableOptions,
        },
        placeholder: placeholder || '请输入内容...',
        theme: 'snow',
      };
    }, [placeholder, showTable, quillTableOptions, showToolbarOptions]);

    // 初始化编辑器
    const onInitEditor = () => {
      if (!quillRef.current) {
        let timeout = null;
        timeout = setTimeout(() => {
          try {
            const editor = new Quill(editorRef.current, quillOptions);
            setQuill(editor);
            quillRef.current = editor;
            // 初始化initData
            const initData = initDataRef.current;
            if (initData) {
              editor.clipboard.dangerouslyPasteHTML(initData);
              // 将光标移动到最后一个位置
              const length = editor.getLength();
              editor.setSelection(length, 0);
            }
            clearTimeout(timeout);
            timeout = null;
          } catch (error) {
            console.log('quill初始化失败~~', error);
          }
        }, 300);
      }
    };

    useEffect(() => {
      onInitEditor();
      return () => {
        quillRef.current?.off('text-change');
        quillRef.current?.destroy();
      };
    }, []);

    useEffect(() => {
      if (quill) {
        quillRef.current.on('text-change', () => {
          const content = onAssembleTplContent(quillRef.current?.root?.innerHTML);
          onChange?.(content, quillRef.current);
        });
      }

      return () => {
        quillRef.current?.off('text-change');
      };
    }, [quill, onChange]);

    useEffect(() => {
      if (value && !quill) {
        initDataRef.current = value;
      }
    }, [value]);

    return (
      <div className={classnames('quillEditor', className)}>
        <div ref={editorRef} className="quillEditorContent" onFocus={onFocus} />
      </div>
    );
  }
);

export default QuillEditor;
