.quillEditor {
  min-height: 200px;
  max-height: 260px;
  @media (min-width: 1200px) and (max-width: 1599px) {
    .quillEditorContent {
      min-height: 300px;
      max-height: 320px;
      overflow: scroll;
    }
  }

  @media (min-width: 1600px) {
    .quillEditorContent {
      min-height: 300px;
      max-height: 510px;
      overflow: scroll;
    }
  }
}

// 如果遇到层级问题 - 应该在对应使用的地方添加
// .qlbt-operation-menu {
//   z-index: 1050 ;
// }
