import classnames from 'classnames';
import * as React from 'react';

import './style.less';

interface IAppProps {
  title?: string;
  titleClassName?: string;
  subTitleClassName?: string;
  children: React.ReactNode;
  subTitle?: string | React.ReactNode;
}

const STYLE_PREFIX = 'baseBlocks';
const IApp: React.FC<IAppProps> = props => {
  const { title, subTitle, children, titleClassName, subTitleClassName } = props;
  return (
    <div className={STYLE_PREFIX}>
      {title && (
        <div className={`${STYLE_PREFIX}Header`}>
          <div className={classnames(`${STYLE_PREFIX}Title`, titleClassName)}>{title}</div>
          <div className={classnames(`${STYLE_PREFIX}SubTitle`, subTitleClassName)}>{subTitle}</div>
        </div>
      )}
      <div className={`${STYLE_PREFIX}Content`}>{children}</div>
    </div>
  );
};

IApp.defaultProps = {
  title: '',
};

export default IApp;
