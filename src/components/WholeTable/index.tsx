import React from 'react';
import { WULIWholeTable } from '@cvte/wuli-antd';
import { IProps as TableIProps } from '@cvte/wuli-antd/src/WULIWholeTable';
import './style.less';

export const DEFAULT_PAGE_SIZE = 50;

export interface IAppProps extends TableIProps {}
const BaseInfo: React.FC<IAppProps> = ({ table, ...restProps }) => {
  // const { filter, table, actions, className, style, title } = props;
  return (
    <div className="dhrWholeTable">
      <WULIWholeTable
        {...restProps}
        table={{
          ...table,
          pageSize: table?.pageSize || DEFAULT_PAGE_SIZE,
        }}
      />
    </div>
  );
};

export default BaseInfo;
