.dhrWholeTable {
  .wuli-ag-whole-table,
  .wuli-whole-table {
    margin: 10px !important;
    .wuli-filter {
      background-color: transparent;
      border: none;
      padding: 0;
      .filters {
        display: flex;
        border-bottom: 1px solid #eee;
        padding-bottom: 6px;
        .ant-row {
          flex: 1;
        }
        .actions {
          .ant-btn-primary {
            background-color: var(--antd-dynamic-primary-color);
          }
        }
      }
    }
    .ag-header {
      height: 60px !important;
      .ag-header-row {
        height: 60px !important;
      }
    }
    .ag-grid-custom-header {
      order: 300 !important;
      max-height: 200px;
      overflow-y: scroll;
    }
  }
}

.wholeTableAgGrid {
  .wuli-ag-whole-table {
    .wuli-filter {
      border: none;
      background-color: transparent;
      border-bottom: 1px solid #e8e8e8;
      padding: var(--dynamic-primary-spacing-normal, 20px);
      .filters {
        .custom-item-wrap {
          .ant-legacy-form-item {
            margin-bottom: 0;
          }
        }
      }
      .ant-legacy-form-item {
        margin-bottom: 0px;
      }
    }
    .ag-grid-whole-table-action {
      margin-top: var(--dynamic-primary-spacing-normal, 20px);
      margin-bottom: 10px;
    }
    .ag-theme-alpine {
      &.wuli-ag-grid {
        .ag-grid-custom-row {
          .ag-cell {
            line-height: 32px;
          }
        }
      }
    }
    .ag-grid-custom-header {
      order: 300 !important;
      max-height: 200px;
      overflow-y: scroll;
      margin-bottom: 10px;
    }
  }
}
