import { SearchOutlined, FileSearchOutlined } from '@ant-design/icons';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { IHeaderParams } from 'ag-grid-community';
import { Popover } from 'antd';

import { useEvent } from '@cvte/kylin-hooks';

import DropDownContent from '../DropDownContent';

import './style.less';

export interface IAppProps extends IHeaderParams {
  /**
   * 过滤的数据
   *
   * @type {Record<string, any>}
   * @memberof IProps
   */
  filters: Record<string, any>;
  /**
   * 发生改变
   *
   * @memberof IProps
   */
  onChange: (values: any) => void;
}
const HeaderFilter: React.FC<IAppProps> = props => {
  const { displayName, column, filters, onChange } = props;

  const filtersRef = useRef(null);
  const [open, setOpen] = useState<boolean>(false);

  const col = column?.getColDef() as any;
  const { field = '', filterKey = '', filterType, filterConfig } = col || {};

  const headerFilterKey = filterKey || field;
  const value = filters?.[headerFilterKey];

  const menu = useMemo(() => {
    if ([undefined, null, ''].includes(value)) {
      return <SearchOutlined className="dhr-header-filter-more" />;
    }
    return <FileSearchOutlined className="dhr-header-filter-more dhr-header-filter-bars" />;
  }, [filters, value]);

  const handleChangePopover = useEvent((newOpen: boolean) => {
    if (newOpen || !filtersRef.current?.[headerFilterKey]) {
      return;
    }
    onChange({ ...filtersRef.current });
    filtersRef.current = {};
  });

  // 重置
  const handleReset = useEvent(() => {
    onChange({ [headerFilterKey]: undefined });
    filtersRef.current = {};
  });

  // 搜索
  const handleSearch = useEvent(() => {
    handleOpenChange(false);
    handleChangePopover(false);
  });

  const handleChange = useEvent(values => {
    filtersRef.current = { ...values };
  });

  const handleOpenChange = useCallback(newOpen => {
    setOpen(newOpen);
  }, []);

  return (
    <div className="dhr-header-filter-block">
      <span className="dhr-header-filter-display">{displayName}</span>
      {filterType && (
        <Popover
          open={open}
          trigger="click"
          placement="bottom"
          onOpenChange={handleOpenChange}
          content={
            <DropDownContent
              {...props}
              filters={filters}
              onReset={handleReset}
              onSearch={handleSearch}
              field={headerFilterKey}
              onChange={handleChange}
              filterType={filterType}
              filterConfig={filterConfig}
            />
          }
          overlayClassName="header-filter-dropdown"
        >
          <span className="dhr-header-filter-left-icon-block">{menu}</span>
        </Popover>
      )}
    </div>
  );
};

export default HeaderFilter;
