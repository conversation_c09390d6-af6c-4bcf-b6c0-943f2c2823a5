import React, { memo, useCallback, useEffect, useState } from 'react';
import { Button, Select, Input } from 'antd';
import { useEvent } from '@cvte/kylin-hooks';

import classnames from 'classnames';

import './style.less';

export interface IAppProps {
  field: string;
  filters: any;
  options?: any[];
  onReset(): void;
  onSearch(): void;
  filterConfig?: any;
  filterType: string;
  onChange: (values) => void;
}
const DropDownContent: React.FC<IAppProps> = props => {
  const { onSearch, onReset, filterType, options = [], filterConfig = {}, onChange, field, filters } = props;
  const initVal = filters?.[field];
  const [searchVal, setSearchVal] = useState(undefined);

  useEffect(() => {
    handleChange(initVal);
  }, [initVal]);

  const handleReset = useCallback(() => {
    setSearchVal(undefined);
    onReset();
  }, []);

  const handleChange = useEvent(value => {
    setSearchVal(value);
    onChange({ [field]: value });
  });

  return (
    <div className="dhr-filter-header-content-block">
      {filterType === 'input' && (
        <Input
          autoFocus
          placeholder="请输入"
          {...filterConfig}
          value={searchVal}
          onChange={({ target: { value } }) => handleChange(value)}
          onPressEnter={({ target: { value } }) => handleChange(value)}
        />
      )}
      {filterType === 'select' && (
        <Select
          autoFocus
          showSearch
          optionFilterProp="label"
          style={{ width: '100%' }}
          placeholder="请选择"
          options={options}
          {...filterConfig}
          value={searchVal}
          onChange={values => handleChange(values)}
        />
      )}
      <div className="dhr-filter-header-content-action">
        <Button type="primary" size="small" className="dhr-filter-action-search" onClick={onSearch}>
          搜索
        </Button>
        <Button type="primary" size="small" onClick={handleReset}>
          重置
        </Button>
      </div>
    </div>
  );
};

export default memo(DropDownContent);
