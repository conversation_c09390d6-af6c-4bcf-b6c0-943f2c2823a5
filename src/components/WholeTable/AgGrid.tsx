import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
// import { WULIWholeTable } from '@cvte/wuli-antd';
// import { IProps as TableIProps } from '@cvte/wuli-antd/src/WULIWholeTable';
import { AntdWholeTable } from '@cvte/wuli-ag-grid';
import { IWholeTable } from '@cvte/wuli-ag-grid/dist/wholeTable';
import { IntlWrapper } from '@cvte/cir-lcp-sdk';
import '@cvte/wuli-ag-grid/dist/index.css';
import { RowDragMoveEvent } from '@ag-grid-community/core';

import './style.less';
import '../../styles/atom.less';
import { Alert, Tag } from 'antd';

import HeaderFilter from './containers/HeaderFilter';
export { WULIFormActions } from '@cvte/wuli-ag-grid/src/wholeTable';

export const DEFAULT_PAGE_SIZE = 50;

export interface IAppProps extends IWholeTable {
  className?: string;
  height?: number;
  selectedRows?: Record<string, any>[];
  showSelectedDetail?: boolean;
  rowNameKey?: string; // 行展示名称的key， 如 name、empName
  afterCancelSelect?: (newRows) => void;
  // 列头 搜索
  onHeaderChange?: (values) => void;
  isHideHeaderSelect?: boolean;
  onDrapMove?: (formIndex: number, toIndex: number, formData: any, toData: any) => void;
}
const BaseInfo: React.FC<IAppProps> = (
  {
    className,
    height,
    selectedRows = [],
    afterCancelSelect,
    rowNameKey,
    showSelectedDetail,
    onHeaderChange,
    isHideHeaderSelect,
    agConfig,
    data,
    onDrapMove,
    ...restProps
  },
  ref
) => {
  const gridRef = useRef(null);
  const headerFiltersRef = useRef({});
  const immutableStoreRef = useRef([]);

  const [rowData, setRowData] = useState<any[]>([]);

  /** 优先使用外部的 ref，如果没有就有内部的 ref */
  const innerRef = useRef(null);
  /** data为空则清除全部 */
  const handleClose = (data?: string) => {
    console.log('data=======', data);
    const _selectedRows = data ? selectedRows.filter(k => k[restProps.rowKey] !== data) : [];
    afterCancelSelect?.(_selectedRows);
    (ref || innerRef).current?.setSelected(
      !data
        ? selectedRows.map(k => ({
          rowKey: k[restProps.rowKey],
          isSelected: false,
        }))
        : [
          {
            rowKey: data,
            isSelected: false,
          },
        ]
    );
  };
  const alertMessage = (
    <div className="flex items-center">
      <div
        style={{
          minWidth: '110px',
        }}
      >
        {selectedRows.length > 0 ? <div>已选择{selectedRows.length}条数据</div> : ''}
      </div>
      <div className="ml-20">
        {showSelectedDetail &&
          rowNameKey &&
          selectedRows.map(k => (
            <Tag closable onClose={() => handleClose(k[restProps.rowKey])} key={k[restProps.rowKey]}>
              {k[rowNameKey]}
            </Tag>
          ))}
        <Tag color="red" onClick={() => handleClose()} className="pointer">
          全部清除
        </Tag>
      </div>
    </div>
  );
  const customHeader =
    selectedRows?.length > 0 ? (
      <Alert
        type="info"
        message={alertMessage}
        showIcon
        style={{
          marginBottom: '10px',
        }}
      />
    ) : (
      ''
    );

  const handleHeaderSearch = useCallback(values => {
    const newHeaderFilters = {
      ...headerFiltersRef.current,
      ...values,
    };
    headerFiltersRef.current = newHeaderFilters;
    onHeaderChange?.(newHeaderFilters);
  }, []);

  useEffect(() => {
    if (Array.isArray(data)) {
      immutableStoreRef.current = data;
      setRowData(data);
    }
  }, [data]);

  const moveInArray = useCallback((arr: any[], fromIndex: number, toIndex: number) => {
    const element = arr[fromIndex];
    arr.splice(fromIndex, 1);
    arr.splice(toIndex, 0, element);
  }, []);

  const handleRowDragMove = useCallback((event: RowDragMoveEvent) => {
    const movingNode = event.node;
    const overNode = event.overNode;
    const rowNeedsToMove = movingNode !== overNode;
    if (rowNeedsToMove) {
      const immutableStore = immutableStoreRef.current;
      const fromIndex = movingNode.rowIndex;
      const toIndex = overNode!.rowIndex;
      const newStore = immutableStore.slice();
      moveInArray(newStore, fromIndex, toIndex);
      onDrapMove?.(fromIndex, toIndex, movingNode.data, overNode!.data);
      immutableStoreRef.current = newStore;
      setRowData(newStore);
      gridRef.current!.api.clearFocusedCell();
    }
  }, []);

  const newAgConfig = useMemo(
    () => ({
      ref: gridRef,
      suppressMoveWhenRowDragging: true,
      // 表格创建完成
      // onGridReady: handleGridReady,
      // 整行拖动，不用点拖动图标
      rowDragEntireRow: true,
      onRowDragMove: handleRowDragMove,
      suppressKeyboardEvent: () => true,
      defaultColDef: {
        // 可拖动列的位置
        lockPosition: true,
        headerComponentParams: {
          onChange: handleHeaderSearch,
          filters: headerFiltersRef.current,
        },
      },
      components: {
        agColumnHeader: HeaderFilter,
      },
      gridOptions: {
        // 多行拖拽
        // rowDragMultiRow: true,
        // rowSelection:{ mode: "multiRow", headerCheckbox: false },
        // 拖拽时，禁用默认行为
        suppressMoveWhenRowDragging: true,
      },
      ...agConfig,
    }),
    [headerFiltersRef.current, agConfig, data]
  );

  return (
    <div
      style={{
        height: height ? `${height}px` : 'calc(100vh - 200px)',
      }}
      className={`wholeTableAgGrid ${className}`}
    >
      <AntdWholeTable
        agConfig={newAgConfig}
        customHeader={!isHideHeaderSelect && customHeader}
        data={rowData}
        {...restProps}
        ref={ref || innerRef}
      />
    </div>
  );
};

export default IntlWrapper(BaseInfo, {
  forwardRef: true,
});
