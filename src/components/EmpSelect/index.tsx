import { SelectProps } from 'antd/lib/select';
import React, { useRef, useState } from 'react';
import { Select, Spin } from 'antd';
import axios from 'axios';

import hcmApis from '@apis/hcm';
import useDebounce from '@hooks/useDebounce';
import { request as fetchApi } from '@utils/http';
import { showErrNotification, customTemplate } from '@utils/tools';

export interface IAppProps extends SelectProps {
  /** 防抖 - 默认300毫秒 */
  delay?: number;
  /** 搜索额外参数 */
  searchExtraParams?: Record<string, any>;
  /** options - key */
  optKey?: string;
  /** options - label */
  optLabel?: string;
  /** 外部传进来移步请求 */
  asyncAction?: (keyword) => Promise<{ key: string; label: any; [propsName: string]: any }[]>;
  /** options - 自定义label函数 */
  customLabel?: (optionItem: any, index: number) => any;
}

const EmpSelect: React.FC<IAppProps> = props => {
  const {
    delay = 300,
    customLabel,
    asyncAction,
    optKey = 'empId',
    optLabel = 'data.empName(data.empDeptName)',
    searchExtraParams = {},
    ...resetProps
  } = props;

  const cancelFetchRef = useRef(null);
  const [options, setOptions] = useState<any[]>([]);
  const [keyword, setKeyword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const handleSearch = useDebounce(
    searchValue => {
      setLoading(true);
      setKeyword(searchValue);
      if (asyncAction) {
        asyncAction(searchValue)
          .then(result => {
            if (keyword !== searchValue) {
              return;
            }
            setOptions(result || []);
          })
          .finally(() => setLoading(false));
        return;
      }
      if (cancelFetchRef.current) {
        cancelFetchRef.current.cancel('取消上次请求');
        cancelFetchRef.current = null;
      }
      cancelFetchRef.current = axios.CancelToken.source();
      fetchApi({
        ...hcmApis.empSearch,
        params: {
          objectName: searchValue,
          pageNum: 1,
          pageSize: 50,
          ...searchExtraParams,
        },
        cancelToken: cancelFetchRef.current.token,
        onSuccess: res => {
          cancelFetchRef.current = null;
          const list = res.list || [];
          if (!Array.isArray(list)) {
            return showErrNotification('数据格式错误');
          }
          const newOptions = list.map((optionItem, index) => ({
            ...optionItem,
            key: optionItem[optKey],
            value: optionItem[optKey],
            label: customLabel ? customLabel(optionItem, index) : customTemplate(optLabel, optionItem),
          }));

          setOptions(newOptions);
        },
      }).finally(() => setLoading(false));
    },
    [delay],
    [searchExtraParams, optKey, optLabel, customLabel, asyncAction]
  );

  return (
    <Select
      labelInValue
      showSearch
      allowClear
      loading={loading}
      options={options}
      placeholder="请输入搜索"
      notFoundContent={
        (loading && <Spin spinning={true}></Spin>) ||
        (keyword && <span>当前输入没有匹配项</span>) || <span>请输入关键字</span>
      }
      {...resetProps}
      filterOption={false}
      onSearch={handleSearch}
    />
  );
};

export default EmpSelect;
