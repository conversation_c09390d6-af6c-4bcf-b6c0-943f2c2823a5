.exportExcelTaskProgressContainer {
  position: absolute;
  top: 48px;
  right: 20px;
  z-index: 1000;
  width: 400px;
  border-radius: 4px;
  background-color: #ffffff;
  cursor: move;
  transition: width 0.3s ease, padding 0.3s ease, height 0.35s ease, max-height 0.5s ease, overflow 0.3s ease;
  box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
  .exportExcelTaskProgress {
    padding: 34px 12px 20px 24px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    position: relative;
    max-height: 300px;
    transition: all 0.3s ease;
    .taskMenu {
      height: 0;
      overflow: hidden;
      .taskMenuIcon {
        font-size: 22px;
      }
    }
    .export-down-all-close-icon-container {
      position: absolute;
      top: 8px;
      left: 10px;
      right: 10px;
      text-align: left;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .excelTaskTitle {
        padding-right: 4px;
        color: var(--antd-dynamic-primary-color);
      }
    }
    .down-list-container {
      overflow-y: scroll;
      max-height: 240px;
      .excelTask {
        padding-bottom: 10px;
        .intro {
          display: flex;
          line-height: 26px;
          align-items: center;
          padding-right: 10px;
          .title {
            flex: 1;
            white-space: now;
            word-wrap: break-word;
            word-break: break-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .status {
            width: 80px;
            margin-left: 10px;
            text-align: right;
          }
          .export-down-close-icon-container {
            width: 24px;
            margin-left: 4px;
            text-align: center;
          }
        }
      }
    }
    .close-icon {
      font-size: 16px;
      cursor: pointer;
      &:hover {
        color: var(--antd-dynamic-primary-color);
      }
    }
    .shrink-icon {
      font-size: 16px;
      cursor: pointer;
      margin-right: 10px;
      &:hover {
        color: var(--antd-dynamic-primary-color);
      }
    }
  }
}

.shrinkExportExcelTaskProgressContainer {
  width: 140px;
  .exportExcelTaskProgress {
    overflow: hidden;
    padding-bottom: 6px;
    .down-list-container {
      max-height: 0;
      overflow: hidden;
    }
    .taskMenu {
      height: 20px;
      overflow: initial;
    }
  }
}
