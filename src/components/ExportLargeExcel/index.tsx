import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import { CloseOutlined, FullscreenExitOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Progress, Tooltip, Badge } from 'antd';
import classnames from 'classnames';

import Draggable from 'react-draggable';

import ExportWork, { ExportWorkParams, ExportWorkConfigs } from './exportWork';

import './style.less';

export interface IAppProps {
  onClose: () => void;
  excelConfigs: ExportWorkConfigs;
}
const ExportLargeExcel: React.FC<IAppProps> = ({ excelConfigs, onClose }) => {
  const countRef = useRef(0);
  const taskListRef = useRef([]);
  const taskIdWaitsRef = useRef([]);
  const destroyWorkerRef = useRef({});
  const taskIdPendingsRef = useRef([]);
  const removeUrlDomStringRef = useRef([]);

  const [taskList, setTaskList] = useState([]);
  const [count, setCount] = useState<number>(0);
  const [taskIdWaits, setTaskIdWaits] = useState([]);
  const [taskIdPendings, setStaskIdPendings] = useState([]);
  const [isShrink, setIsShrink] = useState<boolean>(false);

  useEffect(() => {
    return () => {
      /** 清除URL的引用 */
      try {
        if (removeUrlDomStringRef.current?.length > 0) {
          (removeUrlDomStringRef.current || []).forEach(url => URL.revokeObjectURL(url));
        }
        removeUrlDomStringRef.current = null;
      } catch (error) {}
    };
  }, []);

  // 更新count
  const handleUpdateBadgeCount = useCallback(
    (type: 'add' | 'dec') => {
      const oldCurrent = countRef.current;
      const newCurrent = type === 'add' ? oldCurrent + 1 : oldCurrent - 1;
      const result = newCurrent < 0 ? 0 : newCurrent;
      countRef.current = result;
      setCount(result);
    },
    [count]
  );

  const handleUpdateTaskList = params => {
    const { taskId, ...otherParams } = params;
    const currentTaskList = taskListRef.current || [];
    const newTaskList = [...currentTaskList];
    const index = newTaskList.findIndex(taskItem => taskItem.taskId === taskId);
    const isPass = index > -1;
    if (isPass) {
      // 已存在下载链接 不再更新
      const { downUrl } = newTaskList[index];
      if (!downUrl) {
        newTaskList[index] = {
          ...newTaskList[index],
          ...otherParams,
        };
        setTaskList(newTaskList);
        taskListRef.current = newTaskList;
      }
    }
  };
  // 进行下一个任务
  const handleNextStartTask = taskId => {
    // 下载中的任务id
    // 删掉下载完成的任务id
    const newStaskIdPendings = [...taskIdPendingsRef.current];
    const index = newStaskIdPendings.indexOf(taskId);
    index !== -1 && newStaskIdPendings.splice(index, 1);

    // 任务等待队列
    const currentTaskWaits = taskIdWaitsRef.current || [];
    if (currentTaskWaits.length > 0) {
      // 任务等待队列取出一个
      // 加入下载中的任务队列中
      const newTaskIdWaits = [...currentTaskWaits];
      const nextTaskId = newTaskIdWaits.shift();
      setTaskIdWaits(newTaskIdWaits);
      taskIdWaitsRef.current = newTaskIdWaits;
      const detail = (taskListRef.current || []).find(({ taskId }) => taskId === nextTaskId);
      if (detail) {
        newStaskIdPendings.push(nextTaskId);
        // 开始执行任务
        handleStartTask(detail);
      }
    }
    setStaskIdPendings(newStaskIdPendings);
    taskIdPendingsRef.current = newStaskIdPendings;
  };

  // 下载失败
  const handleFailure = params => {
    // 删除需要销毁的任务id
    const newDestroyWorkerMap = { ...destroyWorkerRef.current };
    newDestroyWorkerMap[params.taskId] && delete newDestroyWorkerMap[params.taskId];
    destroyWorkerRef.current = newDestroyWorkerMap;
    handleNextStartTask(params.taskId);
    handleUpdateTaskList(params);
  };

  // 下载完成
  const handleOk = params => {
    console.log('进度完成！！！');
    // 删除需要销毁的任务id
    const newDestroyWorkerMap = { ...destroyWorkerRef.current };
    newDestroyWorkerMap[params.taskId] && delete newDestroyWorkerMap[params.taskId];
    destroyWorkerRef.current = newDestroyWorkerMap;
    handleNextStartTask(params.taskId);
    handleUpdateTaskList(params);
    handleUpdateBadgeCount('add');
  };

  // 开始执行
  const handleStartTask = params => {
    const ef = new ExportWork({
      ...params,
      onOk: handleOk,
      onFailure: handleFailure,
      onProgress: handleUpdateTaskList,
    });
    ef.start();
    destroyWorkerRef.current = {
      ...destroyWorkerRef.current,
      [params.taskId]: ef,
    };
  };

  // 初始化 -> 任务队列
  const handleInitTaskQueue = taskId => {
    if (taskIdWaits.length === 0 && taskIdPendings.length < 3) {
      const newTaskIdPendings = [...taskIdPendingsRef.current, taskId];
      setStaskIdPendings(newTaskIdPendings);
      taskIdPendingsRef.current = newTaskIdPendings;
      handleStartTask(excelConfigs);
    } else {
      const newTaskWaits = [...taskIdWaitsRef.current, taskId];
      setTaskIdWaits(newTaskWaits);
      taskIdWaitsRef.current = newTaskWaits;
    }
  };

  const handleUpdateTaskQueue = () => {
    const { taskId } = excelConfigs;
    const newTaskList = [...taskList, { ...excelConfigs, progress: 0 }];
    setTaskList(newTaskList);
    taskListRef.current = newTaskList;
    handleInitTaskQueue(taskId);
  };

  useEffect(() => {
    excelConfigs && handleUpdateTaskQueue();
  }, [excelConfigs]);

  // 关闭下载弹窗
  const handleClose = useCallback(list => {
    list.length === 0 && onClose();
  }, []);

  /** 点击下载 */
  const handleDowmUrl = (taskId, removeUrl) => {
    /** 避免内存泄漏 - 收集需要删除URL的引用 */
    removeUrlDomStringRef.current.push(removeUrl);
    const newTaskList = [...(taskListRef.current || [])];
    const delIndex = newTaskList.findIndex(taskItem => taskItem.taskId === taskId);
    delIndex > -1 && newTaskList.splice(delIndex, 1);
    setTaskList(newTaskList);
    taskListRef.current = newTaskList;
    handleClose(newTaskList);
    handleUpdateBadgeCount('dec');
  };

  // 关闭当前任务
  const handleCloseStask = currentTaskId => {
    const currentEf = destroyWorkerRef.current[currentTaskId];
    currentEf && currentEf.destroy();
    const newTaskList = [...taskListRef.current];
    const delIndex = newTaskList.findIndex(({ taskId }) => currentTaskId === taskId);
    if (delIndex > -1) {
      const [taskItem] = newTaskList.splice(delIndex, 1);
      // 下载完成的需删除
      taskItem.downUrl && handleUpdateBadgeCount('dec');
    }
    // const newTaskList = [...taskList].filter(({ taskId }) => currentTaskId !== taskId);
    setTaskList(newTaskList);
    taskListRef.current = newTaskList;
    handleNextStartTask(currentTaskId);
    handleClose(newTaskList);
  };

  /** 关闭全部下载中心 */
  const handleCloseAll = () => {
    const destroyWorkerMap = destroyWorkerRef.current;
    Object.values(destroyWorkerMap).forEach((ef: any) => ef.destroy());
    onClose();
  };

  /** 重试 */
  const handleRetryDown = params => {
    const { progress, isFailure, downUrl, ...excelConfigs } = params;
    const currentTaskList = taskListRef.current || [];
    const newTaskList = [...currentTaskList];
    const index = currentTaskList.findIndex(taskItem => taskItem.taskId === params.taskId);
    if (index > -1) {
      newTaskList[index] = { ...excelConfigs, progress: 0 };
      setTaskList(newTaskList);
      taskListRef.current = newTaskList;
    }
    handleInitTaskQueue(params.taskId);
  };

  return (
    <Draggable bounds="body" cancel=".ant-tooltip a, .not-draggable">
      <div
        className={classnames('exportExcelTaskProgressContainer', { shrinkExportExcelTaskProgressContainer: isShrink })}
      >
        <div className="exportExcelTaskProgress">
          <div className="export-down-all-close-icon-container">
            <Badge count={count}>
              <div className="excelTaskTitle">下载中心</div>
            </Badge>
            <div>
              <Tooltip title="展开/收起" placement="left">
                <FullscreenExitOutlined className="shrink-icon not-draggable" onClick={() => setIsShrink(!isShrink)} />
              </Tooltip>
              <Tooltip title="关闭所有" placement="left">
                <CloseOutlined className="close-icon not-draggable" onClick={handleCloseAll} />
              </Tooltip>
            </div>
          </div>
          <div className="down-list-container">
            {taskList.map(taskItem => {
              const { taskId, progress, xlsxName, downUrl, isFailure } = taskItem;
              const title = `${xlsxName}#${taskId}`;
              return (
                <div className="excelTask" key={taskId}>
                  <div className="intro">
                    <Tooltip title={title} placement="left">
                      <span className="title">{title}</span>
                    </Tooltip>
                    <span className="status">
                      {!isFailure && downUrl && (
                        <a href={downUrl} download={`${title}.xlsx`} onClick={() => handleDowmUrl(taskId, downUrl)}>
                          点击下载
                        </a>
                      )}
                      {isFailure && <a onClick={() => handleRetryDown(taskItem)}>重试</a>}
                      {!isFailure && !downUrl && progress === 100 && <span>链接生成中</span>}
                      {!isFailure && progress > 0 && progress < 100 && <span>正在下载</span>}
                      {!isFailure && !downUrl && progress === 0 && <span>等待下载</span>}
                    </span>
                    <span
                      className="export-down-close-icon-container not-draggable"
                      onClick={() => handleCloseStask(taskId)}
                    >
                      <Tooltip title="关闭当前下载任务">
                        <CloseOutlined className="close-icon" />
                      </Tooltip>
                    </span>
                  </div>
                  {!downUrl && !isFailure && <Progress percent={progress} status="active" style={{ width: '92%' }} />}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </Draggable>
  );
};

class ExportExcelTask {
  public container;
  public excelConfigs = [];

  constructor() {
    this.creatElement();
  }

  creatElement() {
    const element = document.createElement('div'); // 创建一个新的 div 元素
    document.body.appendChild(element); // 将该 div 元素添加到 body 中
    this.container = element;
    return element;
  }

  withRenderModal(newExcelConfigs) {
    let container = this.container;
    if (!this.container) {
      container = this.creatElement();
    }
    // 卸载组件
    const handleClose = () => {
      reactUnmount(container);
      // document.body.removeChild(container);
      // this.container = null;
    };
    reactRender(<ExportLargeExcel onClose={handleClose} excelConfigs={newExcelConfigs} />, this.container);
  }

  add(configs: ExportWorkParams) {
    const newTaskId = new Date().getTime();
    const newTask = {
      taskId: newTaskId,
      ...configs,
    };
    this.withRenderModal(newTask);
  }
}

const excelTask = new ExportExcelTask();

export default excelTask;
