import { request } from '@utils/http';

interface ColumItemType {
  title: string;
  key: string;
  /** 宽度 - px */
  width?: number;
  dataIndex?: string;
  /**
   *  结果-> 格式化
   * text
   * record item值
   * 当前行的index
   */
  formatteFn?: (text, record, index) => any;
  /** 单元格列的配置 */
  alignment?: {
    /* 垂直 对齐方式 */
    vertical?: 'top' | 'middle' | 'bottom' | 'distributed' | 'justify';
    /* 水平 对齐方式 */
    horizontal?: 'left' | 'center' | 'right' | 'fill' | 'justify' | 'centerContinuous' | 'distributed';
    /** 自动换行 */
    wrapText?: boolean;
    /** 单元格缩进 */
    indent?: number;
    /** 自适应 */
    shrinkToFit?: boolean;
  };
}
export interface ExportWorkParams {
  /** 请求的参数 */
  fetchParams: {
    params?: any;
    data?: any;
    method: string;
    url: string;
  };
  /** 导出表名 */
  xlsxName: string;
  configs: {
    /** 返回的数据路径**/
    dataSourcePath?: string;
    /** 分页的路径 **/
    paginationPath?: string;
    /** columns */
    columns: ColumItemType[];
    /** 一次最大请求页 */
    maxPageSize?: number;
    /** 对导出list额外处理的函数 */
    extraHandle?: any;
  };
}

export interface ExportWorkConfigs extends ExportWorkParams {
  /** 任务id */
  taskId: string;
  /** 完成回调 */
  onOk?: (url) => void;
  /** 下载失败回调 */
  onFailure?: (taskId: string) => void;
  /** 进度回调 */
  onProgress?: (params: { taskId: string; progress: number }) => void;
}

class ExportWork {
  // 最大请求页码
  public maxPageNum = 1;
  // 停止的标识
  public stopNum = 0;
  // 导出队列
  public exportQueue = [];

  public fetchParams;

  public worker;
  // 任务id
  public taskId;
  // 进度
  public progress = 0;
  //常量 - 主子线程各一半 用来计算进度
  public HALF = 50;
  // 进度回调
  public onProgress;
  // 下载完成 回调
  public onOk;
  // 下载失败 回调
  public onFailure;

  // 最大重试次数 -> 达到 -> 则终止
  public maxRetryCount = 10;
  // 是否终止 - 异常报错太多则直接终止请求
  public isTerminate = false;

  public keyMapfomatteFn = {};

  public configs = {
    columns: [],
    // 一次最大请求页
    maxPageSize: 2000,
    // xlsxName: '表',
    /** 对导出list额外处理的函数 */
    extraHandle: undefined,
    /** 默认list的路径 */
    dataSourcePath: 'list',
    /** 默认分页路径 */
    paginationPath: 'pagination',
  };

  //基本类型校验组
  public vailBaseTypes = [100, 'test', '2024-02-29', 1709177081081];

  public MAX_SYNC_FETCH_COUN = 3;

  public UrlDomString = null;

  /** 创建work */
  createWorker(fn) {
    // const fnStr = fn.toString();
    // const newFnStr = fnStr.substring(fnStr.indexOf('{') + 1, fnStr.lastIndexOf('}'));
    // const blob = new Blob([newFnStr]);
    const fnStr = `${fn()}`;
    const blob = new Blob([fnStr], { type: 'application/javascript' });
    const url = window.URL.createObjectURL(blob);
    this.UrlDomString = url;
    const worker = new Worker(url);
    const columns = JSON.stringify([...this.configs.columns], function (key, value) {
      if (typeof value === 'function') {
        return value.toString(); // 将函数转换为字符串
      }
      return value;
    });

    worker.postMessage({
      type: 'init',
      data: {
        columns,
      },
    });
    this.worker = worker;
    this.monitorWorker();
  }

  // key 映射结果内容转换
  fomatteResultMap(columns) {
    const isArray = Array.isArray(columns);
    if (!isArray) return;
    const newKeyMapfomatteFn = {};
    columns.slice().forEach(({ key, dataIndex, formatteFn }) => {
      formatteFn && (newKeyMapfomatteFn[key || dataIndex] = formatteFn);
    });
    this.keyMapfomatteFn = newKeyMapfomatteFn;
  }

  constructor(params: ExportWorkConfigs) {
    const { taskId, fetchParams, configs, onOk, onFailure, onProgress } = params;
    const { columns, ...ontherConfigs } = configs;
    this.configs = {
      ...this.configs,
      ...ontherConfigs,
      columns,
    };
    this.onOk = onOk;
    this.taskId = taskId;
    this.onFailure = onFailure;
    this.fomatteResultMap(columns);
    this.fetchParams = fetchParams;
    this.createWorker(this.workerRegister);
    this.onProgress = this.debounce(onProgress, 500);
  }

  workerRegister() {
    return `
    self.importScripts('https://s0.seewo.com/cloud-static/common/exceljs/exceljs.min.js');
    let wb;
    let ws;
    let pages = 0;
    let HALF = 25;
    let exportColumns;
    const mergeArr = [];
    const allAoa = [];
    const tableList = [];

    // 初始化Excel
    function handleInitExport({ columns }) {
      const newColumns = JSON.parse(columns, function (key, value) {
        if (typeof value === 'string') {
          const prefix = value.substring(0, 8);
          if (prefix === 'function') {
            return new Function('return ' + value)();
          }
        }
        return value;
      });
      exportColumns = newColumns;
      wb = new ExcelJS.Workbook();
      ws = wb.addWorksheet('Sheet1');
      const wsColumns = [];
      const configList = [];
      for (const index in newColumns) {
        const { title, key, dataIndex, width,alignment } = newColumns[index];
        // 配置
        alignment && configList.push({
          ...newColumns[index],
          index,
          colIndex:index+1
        })
        wsColumns.push({
          header: title,
          key: key || dataIndex,
          width: width ? Math.max(Math.ceil(width / 6), 30) : 30,
        });
      }
      ws.columns = wsColumns;
      if(configList.length > 0){
        for (const index in configList) {
          const {colIndex,key, alignment} = configList[index];
          ws.getColumn(key).alignment = alignment;
        }
      }
    }

    /** 开始处理数据 */
    function handleStart(buffer, props) {
      pages = props.pages;
      const resultString = new TextDecoder().decode(buffer);
      const list = JSON.parse(resultString);
      handleAssembleRow(list);
    }

    // 更新进度
    function handleUpdateProgress(value) {
      self.postMessage({ type: 'update:progress', data: { value } });
    }

    /** 处理行数据 */
    function handleAssembleRow(list) {
      const indexs = [];
      const exportList = [];
      const listLen = list.length;
      for (let listIndex = 0; listIndex < listLen; listIndex++) {
        const listItem = list[listIndex];
        const index = listItem.index - 1;
        indexs[listIndex] = index;
        exportList.push(listItem.list || []);
        tableList[index] = listItem.list || [];
      }
      let startIndex = 0;
      const exportColumnsLen = exportColumns.length;
      const len = exportList.length;
      const oneProgress = (HALF / pages / len).toFixed(6);
      while (exportList.length > 0) {
        const aoa = [];
        const assemblelist = exportList.shift();
        const assemblelistLen = assemblelist.length;
        for (let listIndex = 0; listIndex < assemblelistLen; listIndex++) {
          let row = {};
          const dataItem = assemblelist[listIndex];
          for (let colIndex = 0; colIndex < exportColumnsLen; colIndex++) {
            const columItem = exportColumns[colIndex] || {};
            const key = columItem.key || columItem.dataIndex;
            const text = dataItem[key];
            row[key] = text;
          }
          aoa.push(row);
        }
        handleUpdateProgress(oneProgress);
        allAoa[indexs[startIndex]] = aoa;
        startIndex++;
      }
    }

    /** 转化**/
    function xlsxWriteBuff() {
      wb.xlsx.writeBuffer().then(uint8ArrayData => {
        const buffer = uint8ArrayData.buffer;
        self.postMessage({ buffer, type: 'success' }, [buffer]);
        self.close();
      });
    }

    /** 批量处理合并单元格 */
    function mergeNextBatch(arr, totalProgress) {
      const step = 500;
      let currentIndex = 0;
      const promiseStasks = [];
      const arrLen = arr.length;
      const oneProgress = ((totalProgress / arrLen) * step).toFixed(6);
      let asyncParallelFn = function (startIndex, endIndex, arr) {
        return new Promise(function (resolve) {
          let timeout = setTimeout(function () {
            for (let index = startIndex; index < endIndex; index++) {
              const mergeItem = arr[index];
              ws.mergeCells(mergeItem.startRow, mergeItem.startCol, mergeItem.endRow, mergeItem.endCol);
            }
            handleUpdateProgress(oneProgress);
            clearTimeout(timeout);
            timeout = null;
            resolve(true);
          }, 0);
        });
      };
      while (currentIndex < arrLen) {
        const endIndex = Math.min(currentIndex + step, arrLen);
        promiseStasks.push(asyncParallelFn(currentIndex, endIndex, arr));
        currentIndex = endIndex;
      }
      Promise.all(promiseStasks).then(function () {
        console.log('合并完成');
        console.log('计算时间~~结束', new Date().getTime());
        xlsxWriteBuff();
      });
    }

    /**  处理全部数据 */
    function handleAssembleExportData() {
      let result = [];
      let newTableList = [];
      const allAoaLen = allAoa.length;
      for (let index = 0; index < allAoaLen; index++) {
        Array.prototype.push.apply(result, allAoa[index]);
        Array.prototype.push.apply(newTableList, tableList[index]);
      }
      const resultLen = result.length;
      const exportColumnsLen = exportColumns.length;
      // 每页增加进度值
      // 如果需要合并的话 是需要/2的 不合并的话 直接*2即可
      const oneProgress = Math.ceil(HALF / pages / 2);
      // 分到处理每个的进度值
      const childProgress = (oneProgress / resultLen).toFixed(6);
      // ws.addRows(result);
      // 是否需要合并
      let isMerge = false;
      for (let dataIndex = 0; dataIndex < resultLen; dataIndex++) {
        const dataItem = newTableList[dataIndex];
        for (let colIndex = 0; colIndex < exportColumnsLen; colIndex++) {
          const columItem = exportColumns[colIndex];
          // 合并
          // 源数据存在rowSpan/colSpan 则合并
          // rowSpan/colSpan 按照antd合并格式
          if (columItem.onCell) {
            !isMerge && (isMerge = true);
            const { rowSpan, colSpan } = columItem.onCell(dataItem, dataIndex);
            if (rowSpan || colSpan) {
              const startRow = dataIndex + 2;
              const startCol = colIndex + 1;
              const endRow = startRow + (rowSpan > 0 ? rowSpan - 1 : 0);
              const endCol = startCol + (colSpan > 0 ? colSpan - 1 : 0);
              // 开始行，开始列，结束行，结束列
              mergeArr.push({ startRow, startCol, endRow, endCol });
            }
          }
        }
        const newOneProgress = isMerge ? childProgress : childProgress * 2;
        handleUpdateProgress(newOneProgress);
      }
      ws.addRows(result);
      // 合并单元格
      if (mergeArr.length > 0) {
        console.log('计算时间~~开始', new Date().getTime());
        mergeNextBatch(mergeArr, oneProgress);
        return;
      }
      xlsxWriteBuff();
    }

    self.onmessage = function (e) {
      const type = e.data.type;
      const data = e.data.data;
      switch (type) {
        case 'init':
          handleInitExport(data);
          break;
        case 'start':
          handleStart(e.data.buffer, data);
          break;
        case 'fetch:end':
          handleAssembleExportData();
        default:
          break;
      }
    };
    `;
  }

  /** 加入队列 */
  joinQueue(joinData) {
    this.exportQueue.push({ ...joinData });
  }

  /** 出队列 */
  deQueue(index) {
    const exportArr = [...this.exportQueue];
    this.worker && this.startWork(exportArr.slice(index));
  }

  /** 执行 */
  startWork(arr) {
    const jsonStr = JSON.stringify(arr);
    const buffer = new TextEncoder().encode(jsonStr).buffer;
    this.worker.postMessage({ buffer, type: 'start', data: { pages: this.maxPageNum } }, [buffer]);
  }

  /** 防抖 */
  debounce(func, delay) {
    let timer;
    return function (...args) {
      clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  }

  /** 更新进度 */
  updateProgress(val) {
    /** 销毁/终止不再更新进度 */
    if (!this.worker || this.isTerminate) {
      return;
    }
    const currentProgress = this.progress;
    const newProgress = Number((Number(currentProgress) + Number(val)).toFixed(6));
    this.progress = newProgress > 100 ? 100 : newProgress;
    this.onProgress?.({
      taskId: this.taskId,
      progress: Number(newProgress.toFixed(2)),
    });
  }

  retryList: number[] = [];

  makeRequest(pageNum) {
    const HALF = this.HALF;
    const { maxPageSize: pageSize } = this.configs;
    const { params, data, ...otherFetchParams } = this.fetchParams;
    return new Promise(resolve => {
      request({
        ...otherFetchParams,
        params: params && {
          ...params,
          pageNum,
          pageSize,
        },
        data: data && {
          ...data,
          pageNum,
          pageSize,
        },
        onSuccess: res => {
          const list = res[this.configs.dataSourcePath] || [];
          const pagination = res[this.configs.paginationPath] || {};
          const pages = pagination.pages || 1;
          if (this.stopNum < 1) {
            this.maxPageNum = pages;
            this.stopNum = pages;
          }
          // 更新当前进度
          const oneProgress = (HALF / pages).toFixed(6);
          this.updateProgress(oneProgress);
          resolve({
            sort: pageNum,
            list,
          });
        },
        onError: err => {
          console.error(`请求失败原因:`, err);
          console.error(`当前页面： ${pageNum}. 重新请求中...`);
          const totalRetryCount = this.retryList.reduce((pre, cur) => pre + cur, 0);
          // 最大试错次数大于20 则终止
          if (totalRetryCount >= this.maxRetryCount) {
            this.isTerminate = true;
            return;
          }
          const index = pageNum - 1;
          const newRetryCount = (this.retryList[index] || 0) + 1;
          this.retryList.splice(index, 1, newRetryCount);
          this.makeRequest(pageNum).then(resolve); // 重新请求
        },
      });
    });
  }

  // 格式化列表
  fomatteResult(list) {
    const startTime = new Date().getTime();
    if (!list) {
      return [];
    }
    let result = [...list];
    const extraHandleFn = this.configs.extraHandle;
    if (extraHandleFn) {
      result = extraHandleFn(result);
    }
    const keys = Object.keys(this.keyMapfomatteFn);
    const isFomatte = keys.length > 0;
    if (isFomatte) {
      try {
        for (let i = result.length - 1; i >= 0; i--) {
          for (let y = keys.length - 1; y >= 0; y--) {
            const record = result[i];
            const field = keys[y];
            const text = record[field];
            const formatteFn = this.keyMapfomatteFn[field];
            const val = formatteFn(text, record, i);
            /** 输出类型如果是【object Object】 则不格式化 */
            const isTypeObj = Object.prototype.toString.apply(val) === '[object Object]';
            !isTypeObj && (result[i][field] = formatteFn(text, record, i));
          }
        }
      } catch (error) {
        console.error('column的formatteFn出错啦：', error);
      }
    }
    const endTime = new Date().getTime();
    console.log('格式化计算时间：', (endTime - startTime) / 1000, '秒');

    return result;
  }

  /**
   * 开始请求
   * 为什么fetch不搬到子线程？
   * 因为子线程需要同源，那么就需要完整的请求接口，
   * 天舟云进入的方式有多种，不同进入的方式 请求接口的前缀uri都不同
   * 不是固定的，所以只能搬到主线程来。
   * 子线程可以映射uri，但不灵活。不适合扩展。
   *
   */
  onStartFetch() {
    let currentIndex = 1;
    const fetchPromises = []; // 存储所有请求的Promise

    const executeRequests = async () => {
      /** 销毁｜终止 则不再继续执行 */
      if (!this.worker || this.isTerminate) {
        return;
      }
      while (currentIndex <= this.maxPageNum && fetchPromises.length < this.MAX_SYNC_FETCH_COUN) {
        fetchPromises.push(this.makeRequest(currentIndex));
        currentIndex += 1;
      }

      await Promise.all(fetchPromises)
        .then(responses => {
          responses.forEach(res => {
            this.joinQueue({
              index: res.sort,
              list: this.fomatteResult(res.list),
            });
          });
        })
        .catch(error => {
          console.error('Error during requests:', error);
        });

      if (currentIndex <= this.maxPageNum) {
        fetchPromises.length = 0; // 清空已完成的请求
        executeRequests(); // 继续执行剩余请求
      }
    };

    executeRequests();
  }

  /** 请求结束 */
  fetchEndWorker() {
    this.worker.postMessage({
      type: 'fetch:end',
    });
  }

  /** 监听队列 */
  monitorQueue() {
    let queueCount = 0;
    let interval = setInterval(() => {
      if (!this.worker) {
        clearInterval(interval);
        interval = null;
      }
      /** 是否终止请求 */
      if (this.isTerminate) {
        this.destroy();
        clearInterval(interval);
        interval = null;
        this.onFailure?.({
          taskId: this.taskId,
          isFailure: true,
        });
      }

      if (this.stopNum > 0) {
        // 当前队列长度变化了 则取出变化的部分做导出处理
        const len = this.exportQueue.length;
        if (queueCount < this.stopNum && queueCount < len) {
          this.deQueue(queueCount);
          const changeLength = len - queueCount;
          queueCount = queueCount + changeLength;
        }
        if (queueCount >= this.stopNum) {
          this.fetchEndWorker();
          clearInterval(interval);
          interval = null;
        }
      }
    }, 1000);
  }

  // 生成下载地址
  genDownLoadLink(buffer) {
    this.worker = null;
    /** 避免内存泄漏 - 删除URL的引用 */
    this.UrlDomString && URL.revokeObjectURL(this.UrlDomString);
    this.UrlDomString = null;

    // 将数据流转换为Blob对象
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const downUrl = URL.createObjectURL(blob);
    this.onOk?.({
      downUrl,
      taskId: this.taskId,
      progress: 100,
    });

    // 创建一个下载链接，并模拟用户单击下载
    // const link = document.createElement('a');
    // link.href = window.URL.createObjectURL(blob);
    // link.download = this.configs.xlsxName;
    // link.click();
  }

  /** 监听线程 */
  monitorWorker() {
    this.worker.onmessage = e => {
      const type = e.data.type;
      const data = e.data.data;
      switch (type) {
        case 'update:progress':
          const { value = 0 } = data;
          this.updateProgress(value);
          break;
        case 'success':
          this.genDownLoadLink(e.data.buffer);
          break;

        default:
          break;
      }
    };
  }

  /**开始执行请求 */
  start() {
    this.onStartFetch();
    this.monitorQueue();
  }
  /** 销毁worker */
  destroy() {
    if (this.worker) {
      this.worker.terminate();
      /** 避免内存泄漏 - 删除URL的引用 */
      this.UrlDomString && URL.revokeObjectURL(this.UrlDomString);
      this.UrlDomString = null;
      this.worker = null;
    }
  }
}

export default ExportWork;
