import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useCallback, useEffect, useState } from 'react';
import { Modal, ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';

import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';

import { ModalProps } from 'antd/lib/modal';

import './style.less';

const formLayout = {
  col: 24,
  labelCol: 4,
  wrapperCol: 18,
};

export interface IAppProps extends ModalProps {
  // open: boolean;
  formKey: string;
  onAfterClose?: () => void;
  onInit?: (cb: any, updateLoading: any) => void;
  formItems: IFormItem[];
}
const RenderFormModal: React.FC<IAppProps> = props => {
  const {
    open,
    title,
    formKey,
    formItems,
    onAfterClose,
    getContainer = () => document.querySelector('.lcp-portal-frame-content-tabs-item'),
    ...otherProps
  } = props;

  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleClose = () => {
    setVisible(false);
  };

  const handleUpdateLoading = useCallback((val: boolean) => {
    setLoading(val);
  }, []);

  useEffect(() => {
    if (visible && props.onInit) {
      props.onInit(handleClose, handleUpdateLoading);
    }
  });

  const handleOk = () => {
    WULIFormActions.get(formKey).validate((success, data) => {
      if (success === 'success') {
        props.onOk && props.onOk(data);
      }
    });
  };

  return (
    <ConfigProvider locale={zhCN}>
      <Modal
        {...otherProps}
        title={title}
        open={visible}
        onOk={handleOk}
        onCancel={handleClose}
        confirmLoading={loading}
        afterClose={onAfterClose}
        getContainer={getContainer}
        wrapClassName="dhr-render-form-modal"
      >
        <WULIForm formKey={formKey} formItems={formItems} defaultLayout={formLayout} />
      </Modal>
    </ConfigProvider>
  );
};

const withParams = (params: IAppProps): IAppProps => {
  return {
    ...params,
    open: true,
  };
};

// Store modal instances for destroy functionality
interface ModalInstance {
  container: DocumentFragment;
  close: () => void;
}

const modalInstances = new Map<string, ModalInstance>();

const addModal = (params: IAppProps) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
    params.onAfterClose && params.onAfterClose();
    // Remove from instances when closed
    if (params.formKey) {
      modalInstances.delete(params.formKey);
    }
  };
  reactRender(<RenderFormModal {...params} onAfterClose={onAfterClose} />, container);

  // Store the instance for later destruction
  if (params.formKey) {
    modalInstances.set(params.formKey, {
      container,
      close: () => {
        reactUnmount(container);
        modalInstances.delete(params.formKey);
      },
    });
  }

  return params.formKey;
};

// Define the type for the modal with static methods
interface ModalType extends React.FC<IAppProps> {
  add: (props: IAppProps) => string;
  destroy: (formKey: string) => boolean;
}

const modal = RenderFormModal as ModalType;

modal.add = function addFn(props: IAppProps) {
  return addModal(withParams(props));
};

modal.destroy = function destroyFn(formKey: string) {
  const instance = modalInstances.get(formKey);
  if (instance) {
    instance.close();
    return true;
  }
  return false;
};

export default modal;
