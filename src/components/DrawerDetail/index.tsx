import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import classnames from 'classnames';
import { Drawer } from 'antd';

import './style.less';

export interface ModalCommonProps {
  title: string;
  className?: string;
  width?: string | number;
  content: string | ReactNode;
  onInit?: (destroy: () => void) => void; // 初始化回调
}

export interface IAppProps extends ModalCommonProps {
  visible: boolean;
  /** 销毁组件 */
  onDestroy: () => void;
}

const DrawerDetail: React.FC<IAppProps> = props => {
  const { visible, onDestroy, content, title, width = 500, className, ...restProps } = props;

  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    setOpen(visible);
  }, [visible]);

  const handleCancel = useCallback(() => {
    setOpen(false);
  }, []);

  return (
    <Drawer
      {...restProps}
      title={title}
      width={width}
      open={open}
      onClose={handleCancel}
      afterOpenChange={open => !open && onDestroy()}
    >
      <div className={classnames('dhr-drawer-container', className || '')}>{content}</div>
    </Drawer>
  );
};

function withParams(params: ModalCommonProps): IAppProps {
  return {
    visible: true,
    onDestroy: () => { },
    ...params,
  };
}

function confirm(config: IAppProps) {
  // 利用createDocumentFragment创建的实例很难在外部通过api关闭，因为不能通过id找到dom节点
  const containers = document.createDocumentFragment();
  // 创建一个容器
  // const container = document.createElement('div');
  // container.id = 'drawer-detail-container'; // 给容器一个 id 方便寻找
  document.body.appendChild(containers);
  const destroy = () => {
    reactUnmount(containers);
  };
  // 回传销毁方法，方便主动关闭
  config.onInit && config.onInit(destroy);
  reactRender(<DrawerDetail {...config} onDestroy={destroy} />, containers);
}

export type ModalStaticFunctions = Record<string, any>;

type ModalType = typeof DrawerDetail & ModalStaticFunctions;

const DrawerDetailModal = DrawerDetail as ModalType;

DrawerDetailModal.detail = function confirmFn<T extends ModalCommonProps>(props: T) {
  return confirm(withParams(props));
};
// DrawerDetailModal.close = function closeFn() {
//   reactUnmount(document.getElementById('drawer-detail-container'));
// };

export default DrawerDetailModal;
