import React, { useEffect } from 'react';
import { Select } from 'antd';

export interface IAppProps {
  dictCode: string;
  fetchDict?: (code: string) => Promise<any>;
  onSelect?: (value: string) => void;
  value?: string;
}
const DictSelect: React.FC<IAppProps> = props => {
  const { dictCode, fetchDict } = props;
  const [dataList, setDataList] = React.useState<any[]>([]);
  const handleGetDict = code => {
    fetchDict(code).then(res => {
      Array.isArray(res) && setDataList(res);
    });
  };

  useEffect(() => {
    handleGetDict(dictCode);
  }, [dictCode]);
  return dataList?.length > 0 ? (
    <Select dropdownMatchSelectWidth={false} onChange={value => props?.onSelect(value)} defaultValue={props?.value}>
      {(dataList || []).map((item: any) => {
        return (
          <Select.Option key={item.itemValue} value={item.itemValue}>
            {item.name}
          </Select.Option>
        );
      })}
    </Select>
  ) : (
    <div />
  );
};

export default DictSelect;
