import React, {
  useRef,
  useMemo,
  useEffect,
  forwardRef,
  useCallback,
  MutableRefObject,
  useImperativeHandle,
} from 'react';
import { Tree, Input, Spin } from 'antd';
import * as R from 'ramda';

import cnbApis from '@apis/cnb';
import useFetch from '@hooks/useFetch';
import list2tree from '@cvte/list2tree';
import useDebounce from '@hooks/useDebounce';

import TitleRender from './containers/TitleRender';

import './style.less';

// const { TreeNode } = Tree;

export interface IAppProps {
  fetchParams?: any;
  extraContent?: React.ReactNode;
  isImmediateFetch?: boolean; // 是否立即请求数据
  onDimDataUpdate?: (data: any) => void;
  onSelectNode?: (selectedKeys, e: { selected: boolean; selectedNodes; node }, selectRows: any[]) => void;
}

// const dimData = JSON.parse(localStorage.getItem('dhr-tree-data'));

const SelectTree = (props: IAppProps, ref: MutableRefObject<any>) => {
  const { fetchParams = {}, onSelectNode, onDimDataUpdate, extraContent, isImmediateFetch } = props;

  const searchRef = useRef('');
  const [treeData, setTreeData] = React.useState([]);
  const [inputValue, setInputValue] = React.useState('');
  // const [treeSearchValue, setTreeSearchValue] = React.useState('');
  const [treeExpandedKeys, setTreeExpandedKeys] = React.useState([]);
  const [treeAutoExpandParent, setTreeAutoExpandParent] = React.useState(false);

  const {
    Data: _dimData,
    Loading: dimLoading,
    runAction,
  } = useFetch({
    ...cnbApis.dimList,
    params: {
      ...fetchParams,
      excludeCategories: 'COMPANY', // 过滤法人公司
    },
    isImmediate: isImmediateFetch,
  });
  const dimData = useMemo(() => _dimData || [], [_dimData]);
  /** 数据转换 */
  const transformTreeData = data => {
    return list2tree({
      idKey: 'hid',
      parentIdKey: 'parentHid',
      newKey: {
        key: 'hid',
        value: 'hid',
        title: 'name',
        name: 'name',
      },
    })(data);
  };

  // 对外暴露方法
  useImperativeHandle(ref, () => ({
    fetchTreeData: params => {
      setInputValue('');
      runAction({ params });
    },
  }));

  // 获取父节点 key
  const handleGetParentKey = (key, tree) => {
    let parentKey;
    // @ts-ignore
    for (const node of tree) {
      if (node.children) {
        if (node.children.some((item: { key: any }) => item.key === key)) {
          parentKey = node.key;
        } else if (handleGetParentKey(key, node.children)) {
          parentKey = handleGetParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };

  // 确认搜索内容
  const handleSearch = value => {
    if (!value) return;
    const _treeExpandedKeys = dimData
      .map(item => {
        // @ts-ignore
        if (item.name.indexOf(value) !== -1) return handleGetParentKey(item.hid, treeData);
        return null;
      })
      .filter(item => item);
    setTreeExpandedKeys(_treeExpandedKeys);
    setTreeAutoExpandParent(true);
  };

  // 树搜索关键字高亮
  // const handleTreeHighlight = name => {
  //   const treeSearchValue = searchRef.current;
  //   const index = name.indexOf(treeSearchValue);
  //   const beforeStr = name.substr(0, index);
  //   const afterStr = name.substr(index + treeSearchValue.length);
  //   const title =
  //     index !== -1 ? (
  //       <span>
  //         {beforeStr}
  //         <span style={{ color: '#f50' }}>{treeSearchValue}</span>
  //         {afterStr}
  //       </span>
  //     ) : (
  //       <span>{name}</span>
  //     );
  //   return title;
  // };

  useEffect(() => {
    const _treeData = transformTreeData(dimData);
    setTreeData(_treeData);
    onDimDataUpdate && onDimDataUpdate(dimData);
  }, [dimData]);

  useEffect(() => {
    const getOrgHid = R.pluck('hid');
    const treeExpandedKeys = getOrgHid(treeData);
    setTreeExpandedKeys(treeExpandedKeys);
  }, [treeData]);

  // useEffect(() => {
  //   const handler = setTimeout(() => {
  //     setTreeSearchValue(inputValue);
  //   }, 300);
  //   return () => {
  //     clearTimeout(handler);
  //   };
  // }, [inputValue]);

  // useEffect(() => {
  //   handleSearch(treeSearchValue);
  // }, [treeSearchValue]);

  const handleTreeExpand = keys => {
    setTreeExpandedKeys(keys);
    setTreeAutoExpandParent(false);
  };

  /** 树节点渲染 */
  // const renderTreeNodes = data =>
  //   data.map(item => {
  //     if (item.children && item.children.length) {
  //       return (
  //         <TreeNode title={handleTreeHighlight(item.name)} key={item.hid} icon="folder">
  //           {renderTreeNodes(item.children)}
  //         </TreeNode>
  //       );
  //     }
  //     return <TreeNode title={item.name} key={item.hid} {...item} />;
  //   });

  const handleSelectNode = useCallback(
    (selectedKeys, e) => {
      const selectRows = (dimData || []).filter(({ hid }) => (selectedKeys || []).includes(hid));
      onSelectNode?.(selectedKeys, e, selectRows);
    },
    [dimData]
  );

  // 防抖
  const handleChangeSearchNode = useDebounce(
    e => {
      const { value } = e.target;
      searchRef.current = value;
      setInputValue(value);
      handleSearch(value);
    },
    300,
    []
  );

  const titleRender = useCallback(nodeData => <TitleRender treeSearchValue={searchRef.current} {...nodeData} />, []);
  return (
    <Spin spinning={dimLoading}>
      <div className="condition-fields">
        {extraContent && <div className="condition-expand">{extraContent}</div>}
        <Input
          // value={inputValue}
          placeholder="请输入内容"
          style={{ marginBottom: 8 }}
          onChange={handleChangeSearchNode}
          onPressEnter={(e: React.KeyboardEvent<HTMLInputElement>) => handleSearch(e.currentTarget.value)}
        />
      </div>
      <div>
        {treeData.length > 0 && (
          <Tree
            showLine
            treeData={treeData}
            titleRender={titleRender}
            onSelect={handleSelectNode}
            onExpand={handleTreeExpand}
            className="treeSelectComp"
            expandedKeys={treeExpandedKeys}
            autoExpandParent={treeAutoExpandParent}
          />
        )}
      </div>
    </Spin>
  );
};

export default forwardRef(SelectTree);
