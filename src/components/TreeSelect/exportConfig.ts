import { CustomComponentInjectConfig } from '@cvte/cir-lcp-sdk/src/lib/customComponent'; // @cvte/cir-lcp-sdk版本须>=1.1.0
import CustomSampleRender from './SampleRender'; // 用于设计画布展示的渲染组件
import BaseConfigForm from './BaseConfig';

const customCptInjectConfig = new CustomComponentInjectConfig({
  // 组件类型编码，内容须用大写字母+下划线表示，用于做唯一标识
  code: 'TREE_SELECT_COMP',
  // 组件类型名称，用于在设计器中显示
  name: '树形选择',
});

customCptInjectConfig.setRenderConfig({
  // 组件在设计器画布区域中展示的渲染组件
  sampleRender: CustomSampleRender,
});

// customCptInjectConfig.setConfig({
//   type: 'CUSTOM_BUTTON',
//   outputType: 'string',
// });

// 设置组件的在设计器端展示的基础配置项
customCptInjectConfig.setBaseConfig(BaseConfigForm);

export default customCptInjectConfig;
