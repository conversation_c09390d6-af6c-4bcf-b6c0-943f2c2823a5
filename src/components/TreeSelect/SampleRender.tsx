import React, { useEffect } from 'react';
import { Tree, Input } from 'antd';
import list2tree from '@cvte/list2tree';

// import { dimData } from '../data';

const { TreeNode } = Tree;

export interface IAppProps {
  onSelectNode: (selectedKeys, e: { selected: boolean; selectedNodes; node }) => void;
}
const SelectTree: React.FC<IAppProps> = ({ onSelectNode }) => {
  const [treeData, setTreeData] = React.useState([]);
  const [treeSearchValue, setTreeSearchValue] = React.useState('');
  const [treeExpandedKeys, setTreeExpandedKeys] = React.useState([]);
  /** 数据转换 */
  const transformTreeData = data => {
    return list2tree({
      idKey: 'orgId',
      parentIdKey: 'parentId',
      newKey: {
        key: 'orgId',
        value: 'orgId',
        title: 'orgName',
        name: 'orgName',
      },
    })(data);
  };

  // 获取父节点 key
  const handleGetParentKey = (key, tree) => {
    let parentKey;
    // @ts-ignore
    for (const node of tree) {
      if (node.children) {
        if (node.children.some((item: { key: any }) => item.key === key)) {
          parentKey = node.key;
        } else if (handleGetParentKey(key, node.children)) {
          parentKey = handleGetParentKey(key, node.children);
        }
      }
    }
    return parentKey;
  };

  // 确认搜索内容
  const handleSearch = value => {
    if (!value) return;
    const _treeExpandedKeys = treeData
      .map(item => {
        if (item.orgName.indexOf(value) !== -1) return handleGetParentKey(item.orgHid, treeData);
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
    setTreeExpandedKeys(_treeExpandedKeys);
  };

  useEffect(() => {
    console.log('treeSearchValue', treeSearchValue);
    handleSearch(treeSearchValue);
  }, [treeSearchValue]);
  // 树搜索关键字高亮
  const handleTreeHighlight = name => {
    const index = name.indexOf(treeSearchValue);
    const beforeStr = name.substr(0, index);
    const afterStr = name.substr(index + treeSearchValue.length);
    const title =
      index !== -1 ? (
        <span>
          {beforeStr}
          <span style={{ color: '#f50' }}>{treeSearchValue}</span>
          {afterStr}
        </span>
      ) : (
        <span>{name}</span>
      );
    return title;
  };

  useEffect(() => {
    const dimData = JSON.parse(localStorage.getItem('dhr-tree-data'));
    console.log('dimData', dimData);
    const _treeData = transformTreeData(dimData);
    console.log('transformTreeData(dimData)', _treeData);
    setTreeData(_treeData);
  }, []);

  /** 树节点渲染 */
  const renderTreeNodes = data =>
    data.map(item => {
      if (item.children && item.children.length) {
        return (
          <TreeNode title={handleTreeHighlight(item.name)} key={item.code} icon="folder">
            {renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode title={item.name} key={item.code} {...item} />;
    });

  return (
    <div>
      <div className="condition-fields">
        <Input
          style={{ marginBottom: 8 }}
          value={treeSearchValue}
          onChange={e => setTreeSearchValue(e.target.value)}
          onPressEnter={(e: React.KeyboardEvent<HTMLInputElement>) => handleSearch(e.currentTarget.value)}
          placeholder="请输入内容"
        // suffix={<SearchOutlined />}
        />
      </div>
      <div>
        {treeData.length > 0 && (
          <Tree onSelect={onSelectNode} autoExpandParent expandedKeys={treeExpandedKeys}>
            {renderTreeNodes(treeData)}
          </Tree>
        )}
      </div>
    </div>
  );
};

export default SelectTree;
