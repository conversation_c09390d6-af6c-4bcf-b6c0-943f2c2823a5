import React from 'react';

import '../../style.less';

export interface IAppProps {
  name: string;
  hid: string;
  treeSearchValue: string;
}

const TitleRender: React.FC<IAppProps> = ({ name, hid, treeSearchValue }) => {
  // 如果搜索值为空，直接返回原始名称
  if (!treeSearchValue) {
    return <span key={hid}>{name}</span>;
  }

  // 搜索值不为空时，查找并高亮
  const index = name.indexOf(treeSearchValue);

  // 未找到匹配项，直接返回原始名称
  if (index === -1) {
    return <span key={hid}>{name}</span>;
  }

  // 找到匹配项，分割并高亮显示
  return (
    <span key={hid}>
      {name.substring(0, index)}
      <span style={{ color: '#f50' }}>{treeSearchValue}</span>
      {name.substring(index + treeSearchValue.length)}
    </span>
  );
};

export default TitleRender;
