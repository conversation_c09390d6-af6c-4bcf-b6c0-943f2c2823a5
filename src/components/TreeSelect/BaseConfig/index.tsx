import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (formRef: IInjectFormItemFnParamsContext['formRef'], key: string, value?: any) => {
  const dictConfig = formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig');
  let dictConfigObj = JSON.parse(dictConfig || '{}');
  if (!dictConfigObj) {
    dictConfigObj = {};
  }
  dictConfigObj[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', JSON.stringify(dictConfigObj));
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  // const { formRef } = context || {};

  // setTimeout(() => {
  //   const defFormData = formRef?.current?.getInitFormData?.();
  //   console.log('defFormData', defFormData);
  //   const curFormData = formRef?.current?.getFormItem?.();
  //   console.log('curFormData', curFormData);
  //   formRef?.current?.setFormItem?.(
  //     'dictConfigTransferRule',
  //     curFormData?.dictConfigTransferRule || defFormData?.dictConfigTransferRule
  //   );
  // });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'textarea',
      key: 'dictConfigTransferRule',
      label: '树数据转换规则',
      configs: {
        // onBlur: () => {
        //   const dictConfigTransferRule = formRef?.current?.getFormItem?.('dictConfigTransferRule');
        //   console.log('dictConfigTransferRule=========', dictConfigTransferRule);
        //   context?.onConfirm?.('dictConfigTransferRule', dictConfigTransferRule);
        //   setDictConfig(formRef, 'dictConfigTransferRule', dictConfigTransferRule);
        // },
      },
    },
  ];
};

export default customCptBaseConfig;
