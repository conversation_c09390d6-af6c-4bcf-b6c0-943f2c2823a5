import React from 'react';
import { Select } from 'antd';
import commonApis from '@apis/common';
import useFetch from '@hooks/useFetch';

export interface IAppProps {
  onSelect?: (value: string, option: string) => void;
  value?: any;
}
const RoleList: React.FC<IAppProps> = props => {
  const { Data: dictRes = {} } = useFetch({
    ...commonApis.dictList,
    data: ['DHR_EXIT_SIGN_DOC_TYPE'],
    headers: {
      'x-app-id': '1269620726624013b98cf4baadaa759f',
    },
  }) as {
    Data: any[];
  };
  const { DHR_EXIT_SIGN_DOC_TYPE = [] } = dictRes?.content || {};
  return (
    <Select
      dropdownMatchSelectWidth={false}
      onChange={(value, option) => {
        props?.onSelect(value, option?.children);

        // 获取当前选中的item
      }}
      defaultValue={props?.value}
    >
      {(DHR_EXIT_SIGN_DOC_TYPE || []).map((item: any) => {
        return (
          <Select.Option key={item.itemValue} value={item.itemValue}>
            {item.name}
          </Select.Option>
        );
      })}
    </Select>
  );
};

export default RoleList;
