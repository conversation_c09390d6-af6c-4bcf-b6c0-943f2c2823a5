import React, { forwardRef, ReactNode, useCallback, useEffect, useImperativeHandle, useRef } from 'react';
import { message, Modal } from 'antd';
import classnames from 'classnames';

import taskApis from '@apis/task';
import { request as fetchApi } from '@utils/http';
import { renderException } from '@hooks/useTask';

import { ENUM_TASK_STATUS } from '@constants/task';

import './style.less';

export interface IAppProps {
  ref?: any;
  processId: string;
  fetchParams?: any;
  className?: string;
  /** 可以正常往下继续执行 */
  onOk?: () => void;
  children?: ReactNode;
}
const TaskSlot: React.FC<IAppProps> = forwardRef(({ className, processId, children, fetchParams = {}, onOk }, ref) => {
  // 终止任务状态
  const taskEndStatusRef = useRef(false);
  const containerRef = useRef(null);
  const taskErrModalRef = useRef(null);
  const taskPendingModalRef = useRef<{
    modal: any;
    status: ENUM_TASK_STATUS;
  }>(null);

  useImperativeHandle(ref, () => ({
    onReplayTask: (processId?: string) => handleReplayTask(processId),
  }));

  useEffect(() => {
    return () => {
      /** 销毁所有任务 */
      taskEndStatusRef.current = true;
      handleDestroyTaskModal(['error', 'pending']);
    };
  }, []);

  /** 销毁任务弹窗modal */
  const handleDestroyTaskModal = useCallback((destroyArr = []) => {
    // 进行中弹窗 如果有 - 销毁
    if (taskPendingModalRef?.current && destroyArr.includes('pending')) {
      taskPendingModalRef.current?.modal?.destroy();
      taskPendingModalRef.current = null;
    }
    // 异常弹窗 如果有 - 销毁
    if (taskErrModalRef.current && destroyArr.includes('error')) {
      taskErrModalRef.current.destroy();
      taskErrModalRef.current = null;
    }
  }, []);

  /** 已经初始化过 */
  /** 执行中或者准备中 */
  const handleFreeDoingStatus = useCallback((data, bizId) => {
    let timeout = setTimeout(() => {
      onStartTask(bizId);
      clearTimeout(timeout);
      timeout = null;
    }, 3000);
    /**
     * 任务弹窗不存在 或者 任务弹窗存在但是状态不是空闲状态
     */
    if (
      !taskPendingModalRef.current ||
      (taskPendingModalRef.current?.status === ENUM_TASK_STATUS.FREE && data.status === ENUM_TASK_STATUS.D0ING)
    ) {
      handleDestroyTaskModal(['error', 'pending']);
      const taskModalInfo = Modal.info({
        title: '任务提示',
        className: 'dhr-task-modal-dialog', // 添加自定义的class名
        getContainer: () => containerRef.current,
        content: data.status === ENUM_TASK_STATUS.D0ING ? '任务正在执行中...' : '任务初始化中',
        closable: false,
        maskClosable: false,
        closeIcon: false,
        okText: '请稍后...',
        centered: true,
        okButtonProps: {
          disabled: true,
        },
      });
      taskPendingModalRef.current = {
        modal: taskModalInfo,
        status: data.status,
      };
    }
  }, []);

  /** 处理异常 */
  const handleException = useCallback((data, bizId) => {
    handleDestroyTaskModal(['pending']);
    const taskErrModalInfo = Modal.info({
      title: '任务执行异常提示',
      content: renderException(data),
      okText: '重新执行',
      centered: true,
      className: 'dhr-task-modal-dialog', // 添加自定义的class名
      getContainer: () => containerRef.current,
      onOk: () => {
        fetchApi({
          ...taskApis.reExecuteTask,
          params: {
            taskId: data.id,
          },
          onSuccess: () => {
            message.success('重新执行成功');
            handleDestroyTaskModal(['error']);
            onStartTask(bizId);
          },
          onError: err => {
            console.log('err', err);
          },
        });
      },
      closable: false,
      maskClosable: false,
      closeIcon: false,
    });
    taskErrModalRef.current = taskErrModalInfo;
  }, []);

  // 开始执行任务
  const onStartTask = useCallback(bizId => {
    fetchApi({
      ...taskApis.getTaskByBizId,
      params: {
        bizId,
        ...fetchParams,
      },
      onSuccess: data => {
        if (!taskEndStatusRef.current) {
          if (data.status === ENUM_TASK_STATUS.DONE) {
            handleDestroyTaskModal(['error', 'pending']);
            return onOk?.();
          }
          /** 已经初始化过 */
          /** 执行中或者准备中 */
          [ENUM_TASK_STATUS.D0ING, ENUM_TASK_STATUS.FREE].includes(data.status) &&
            handleFreeDoingStatus(data || {}, bizId);
          /** 异常 */
          data.status === ENUM_TASK_STATUS.EXCEPTION && !taskErrModalRef.current && handleException(data || {}, bizId);
        }
      },
    });
  }, []);

  // 重新执行任务
  const handleReplayTask = useCallback(
    newProcessId => {
      const bizId = newProcessId || processId;
      onStartTask(bizId);
    },
    [processId]
  );

  useEffect(() => {
    processId && onStartTask(processId);
  }, [processId]);
  return (
    <div className={classnames('dhr-task-slot-container', className)} ref={containerRef}>
      {children}
    </div>
  );
});

export default TaskSlot;
