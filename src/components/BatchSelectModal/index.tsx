import React, { useMemo, useImperative<PERSON>andle, useEffect } from 'react';
import { ModalProps } from 'antd/lib/modal';
import { Spin, Tabs } from 'antd';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';
import { IntlWrapper } from '@cvte/cir-lcp-sdk';
import { request } from '@utils/http';

import { defaultObj } from '@utils/tools';
import { AxiosRequestConfig } from 'axios';

const filterFormKey = 'batchSelectModalFormKey';
export interface IAppProps extends ModalProps {
  requestParams: AxiosRequestConfig;
  // onConfirm: (empIds: string[]) => void;
  title: string;
  filterFormItems: any[];
  columns: any[];
  rowKey: string;
  rowNameKey: string;
  heitht: number;
  extraSearchParams?: Record<string, any>;
  defaultPageSize?: number;
  responseHandler?: (data: any) => {
    list: any[];
    pagination: Record<string, any>;
  };
  isTzList?: boolean; // 是否是天舟云列表
}

/** 操作符映射 */
const operatorMappings = {
  input: 'like',
  select: '=',
  eq: '=',
  neq: '!=',
  in: 'in',
  like: 'like',
  gt: '>',
  lt: '<',
  gte: '>=',
  lte: '<=',
};
const BatchSelectModal: React.FC<IAppProps> = (
  {
    requestParams = {},
    title,
    filterFormItems = [],
    columns = [],
    rowKey,
    rowNameKey,
    heitht,
    extraSearchParams = {},
    defaultPageSize,
    responseHandler,
    isTzList = false,
  },
  ref
) => {
  const [listRes, setListRes] = React.useState<any>({});
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);
  const [commonLoading, setCommonLoading] = React.useState<boolean>(false);
  const _defaultPageSize = defaultPageSize || DEFAULT_PAGE_SIZE;

  useImperativeHandle(ref, () => ({
    getSelectedRows: () => selectedRows,
  }));

  // 获取请求参数 -- 默认处理天舟云返回格式数据
  const getApiParams = (values = {}) => {
    const mergeValues = {
      ...values,
      ...extraSearchParams,
    };
    /** 筛选有值项目 */
    const filters = filterFormItems
      .filter(k => mergeValues[k.key])
      ?.map(searchItem => {
        return {
          paramsList: [
            {
              attrApi: searchItem.key,
              operator: operatorMappings[searchItem.operator] || operatorMappings[searchItem.type] || 'like',
              value: mergeValues[searchItem.key],
            },
          ],
        };
      });
    return filters.length === 0
      ? []
      : {
          onlyMain: true,
          mainParamsGroups: filters,
          sortBy: 'CRT_TIME desc',
          page: values?.page || 1,
          pageSize: values?.pageSize || _defaultPageSize,
        };
  };

  // 获取请求参数 -- 自定义处理
  const getApiParamsCustom = (values = {}) => {
    const mergeValues = {
      ...values,
      ...extraSearchParams,
    };
    return mergeValues;
  };

  const onFetchList = async (params: Record<string, any> = {}) => {
    setCommonLoading(true);
    const queryParams: Record<string, any> = {};
    if (requestParams.method === 'get') {
      queryParams.params = {
        ...(requestParams.params || {}),
        ...(isTzList ? getApiParams(params) : getApiParamsCustom(params)),
      };
    } else {
      queryParams.data = {
        ...(requestParams.data || {}),
        ...(isTzList ? getApiParams(params) : getApiParamsCustom(params)),
      };
    }
    const _listRes = await request({
      ...requestParams,
      ...queryParams,
    }).finally(() => setCommonLoading(false));
    setListRes(_listRes);
  };

  useEffect(() => {
    onFetchList();
  }, [requestParams]);

  // 处理响应数据, 优先使用 responseHandler 处理，否则使用默认处理（默认处理天舟云返回格式数据）
  const { list: dataList = [], pagination = {} } = responseHandler
    ? responseHandler(listRes)
    : {
        list: listRes.content?.map(k => k.mainData),
        pagination: listRes.pagination,
      };
  return (
    <Spin spinning={commonLoading}>
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={title || '列表'} key="1">
          <WULIWholeTable
            columns={columns}
            data={dataList}
            height={(heitht || 800) - 100}
            canSelect
            multiSelect
            onSelect={(selectedRow, isSelect, _selectedRowKeys, _selectedRows) => {
              setSelectedRowKeys(_selectedRowKeys);
              setSelectedRows(_selectedRows);
            }}
            paginationConfig={{
              current: pagination.page || pagination.pageNum,
              pageSize: pagination.pageSize || _defaultPageSize,
              pageSizeOptions: [10, 20, 50, 100, 200, 500],
              size: 'small',
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              onChange: (page, pageSize) => {
                const searchVal = WULIFormActions.get(filterFormKey).getValue();
                onFetchList({
                  page,
                  pageNum: page,
                  pageSize,
                  ...searchVal,
                });
              },
            }}
            pagination
            rowKey={rowKey || 'ID'}
            filter={{
              formKey: filterFormKey,
              filters: filterFormItems,
              actionNotSingleLine: true,
              // advanced: true,
              onSearch: data => {
                onFetchList({
                  ...data,
                });
              },
            }}
            selectRowKeys={selectedRowKeys}
            selectedRows={selectedRows}
            showSelectedDetail
            rowNameKey={rowNameKey}
            afterCancelSelect={newRows => {
              setSelectedRows(newRows);
              setSelectedRowKeys(newRows.map(k => k.empId));
            }}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="已选" key="2">
          <WULIWholeTable columns={columns} data={selectedRows} rowKey={rowKey || 'ID'} />
        </Tabs.TabPane>
      </Tabs>
    </Spin>
  );
};

export default IntlWrapper(BatchSelectModal, { forwardRef: true });
