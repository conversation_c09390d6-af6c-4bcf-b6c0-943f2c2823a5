import Loader, { ReactRemoteLoaderComponent, ILoaderResourceEnv } from '@cvte/resource-center-sdk';

const Resource = ReactRemoteLoaderComponent(
  new Loader({
    appName: document.getElementById?.('lcpAppRcRootApp')?.getAttribute?.('value') || 'lcp-2-8-renli-app',
    name: 'tz-render',
    env: `${document.getElementById('ENV')?.getAttribute('value') as ILoaderResourceEnv}`,
  }),
  'LCPResourceTemplate',
  {
    useShared: true,
    mode: 'page',
  }
);
export default Resource;
