import Loader, { ReactRemoteLoaderComponent, ILoaderResourceEnv } from '@cvte/resource-center-sdk';

const CSBList = ReactRemoteLoaderComponent(
  new Loader({
    appName: document.getElementById?.('lcpAppRcRootApp')?.getAttribute?.('value') || 'lcp-2-3-app',
    name: 'tz-render',
    env: `${document.getElementById('ENV')?.getAttribute('value') as ILoaderResourceEnv}`,
  }),
  'LCPListTemplate',
  {
    useShared: true,
    mode: 'page',
  }
);

export default CSBList;
