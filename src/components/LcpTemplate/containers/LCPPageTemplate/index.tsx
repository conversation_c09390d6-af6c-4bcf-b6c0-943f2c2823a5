import Loader, { ILoaderResourceEnv, ReactRemoteLoaderComponent } from '@cvte/resource-center-sdk';

const List = ReactRemoteLoaderComponent(
  new Loader({
    appName: document.getElementById?.('lcpAppRcRootApp')?.getAttribute?.('value') || 'lcp-2-3-app',
    name: 'tz-render',
    env: `${document.getElementById('ENV')?.getAttribute('value') as ILoaderResourceEnv}`,
  }),
  'LCPPageTemplate',
  {
    useShared: true,
    mode: 'page',
  }
);
export default List;
