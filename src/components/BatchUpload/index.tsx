import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { request as fetchApi } from '@utils/http';
import { UploadProps } from 'antd/lib/upload/interface.d';
import attachmentApis from '@apis/attachment';

import BaseBatchUpload from '../BaseBatchUpload';

export interface IAppProps extends UploadProps {
  extraTips: string;
  [key: string]: any;
}

const App = (props: IAppProps, ref) => {
  const [uploadStatus, setUploadStatus] = useState<'uploading' | 'done' | 'waiting'>('waiting');
  const { extraTips, ...restConfig } = props;

  const handleValidateBeforeUpload = (fileList: Record<string, any>[]) => {
    return new Promise(resolve => {
      const names = [];
      const uidNameMapping = {};
      fileList.forEach(file => {
        const { name, uid } = file;
        names.push(name);
        uidNameMapping[name] = (uidNameMapping[name] || []).concat(uid);
      });
      fetchApi({
        ...attachmentApis.empFileCheck,
        data: names,
      })
        .then(res => {
          setUploadStatus(!!res ? 'waiting' : 'uploading');
          const msg = Object.keys(res || {}).reduce((pre, name) => {
            const uids = uidNameMapping[name];
            uids.forEach(uid => {
              pre[uid] = res[name];
            });
            return pre;
          }, {});
          resolve({
            isSuccess: !res,
            msg: msg,
          });
        })
        .catch(() => {
          resolve({ isSuccess: false, msg: false });
        });
    });
  };

  const handleAfterUploadFile = (file, fileId) => {
    return new Promise(resolve => {
      fetchApi({
        ...attachmentApis.empFileUpload,
        data: {
          fileStore: fileId, //服务器文件id
          fileName: file.name, //文件名称
          fileSize: file.size, //文件大小
          fileType: file.type, //文件类型
        },
      })
        .then(() => {
          resolve();
        })
        .catch(() => {
          resolve('记录文件错误');
        });
    });
  };

  const handleUploadComplete = () => {
    setUploadStatus('done');
  };
  useImperativeHandle(ref, () => ({
    getUploadStatus: () => uploadStatus,
  }));

  // const handleCancel = () => {
  //   if (uploadStatus === 'uploading') {
  //     Modal.warning({
  //       title: '不可关闭',
  //       content: '文件上传中，关闭后无法查看上传进度',
  //     });
  //     return;
  //   }
  // };

  return (
    <BaseBatchUpload
      {...{
        data: { categoryId: 'careers_id' },
        onValidateBeforeUpload: handleValidateBeforeUpload,
        onUploadFile: handleAfterUploadFile,
        onUploadComplete: handleUploadComplete,
        ...restConfig,
      }}
      directory={true}
    >
      <div className="upload-btn">
        <div>
          请选择文件夹<span className="color-blue">浏览</span>
          <p>如需上传单独文件，请直接拖拽文件到此</p>
          {extraTips || ''}
        </div>
      </div>
    </BaseBatchUpload>
  );
};

export default forwardRef<IAppProps>(App);
