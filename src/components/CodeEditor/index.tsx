/*
 * @Author: 柯剑烽 k<PERSON><PERSON><PERSON>@cvte.com
 * @Date: 2024-04-11 10:07:47
 * @LastEditors: 柯剑烽 k<PERSON><PERSON><PERSON>@cvte.com
 * @LastEditTime: 2024-04-11 11:27:48
 */
import React from 'react';
import { UnControlled as CodeMirror } from 'react-codemirror2';
import { EditorConfiguration } from 'codemirror';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/material.css';
import 'codemirror/theme/mdn-like.css';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/show-hint';
import 'codemirror/mode/javascript/javascript';

export interface IAppProps {
  className?: string;
  value?: string;
  codeMirrorConfig?: EditorConfiguration;
  onChange?: (value: string) => void;
  onDidMount?: (editor: any) => void;
  disabled?: boolean;
}

/** 设置光标 */
export const moveToPosition = (editor, line, ch) => {
  editor.focus(); // 设置光标前要设置焦点
  editor.setCursor(line, ch);
};
const BaseInfo: React.FC<IAppProps> = ({ className, codeMirrorConfig = {}, onChange, onDidMount }) => {
  return (
    <CodeMirror
      options={{
        mode: 'javascript',
        theme: 'material',
        lineNumbers: true,
        autofocus: true,
        tabSize: 2,
        lineWrapping: true,
        matchBrackets: true, // 启用括号匹配
        ...codeMirrorConfig,
      }}
      onChange={onChange}
      editorDidMount={editor => {
        onDidMount && onDidMount(editor);
      }}
      className={className}
    />
  );
};

export default BaseInfo;
