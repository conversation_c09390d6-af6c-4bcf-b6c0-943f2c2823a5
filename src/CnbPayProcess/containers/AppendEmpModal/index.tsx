import React, { useMemo } from 'react';
import { Form, Modal, Select } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import { request } from '@utils/http';
import payProcessApis from '@apis/payProcess';
import useDebounce from '@hooks/useDebounce';

export interface IAppProps extends ModalProps {
  processId: string;
  onConfirm: (empId: string) => void;
}
const BaseForm: React.FC<IAppProps> = ({ processId, ...restProps }) => {
  const [form] = Form.useForm();
  const [empList, setEmpList] = React.useState<Record<string, any>[]>([]);
  const [empLoading, setEmpLoading] = React.useState<boolean>(false);
  /** 查找人员 */
  const onSearchEmp = useDebounce(data => {
    if (!data) return;
    setEmpLoading(true);
    return request({
      ...payProcessApis.getAddEmp,
      params: {
        processId,
        empName: data,
      },
      onSuccess: res => setEmpList(res.list || []),
      onError: err => {
        console.log('err', err);
      },
    }).finally(() => setEmpLoading(false));
  }, 300);

  const _empList = useMemo(() => {
    return empList.map(k => ({
      ...k,
      label: k.empName,
      value: k.empId,
    }));
  }, [empList]);

  const handleConfirm = () => {
    form.validateFields().then(values => {
      const { emp } = values;
      restProps.onConfirm(emp);
    });
  };

  return (
    <Modal {...restProps} width={600} title="追加人员" destroyOnClose onOk={() => handleConfirm()}>
      <Form
        form={form}
        name="remark"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        initialValues={{}}
        autoComplete="off"
        className="ml-10"
      >
        <Form.Item
          label="请选择需要追加的人员"
          name="emp"
          rules={[{ required: true, message: '请选择需要追加的人员' }]}
        >
          <Select
            onSearch={onSearchEmp}
            showSearch
            options={_empList}
            optionFilterProp="children"
            loading={empLoading}
            filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
            mode="multiple"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default BaseForm;
