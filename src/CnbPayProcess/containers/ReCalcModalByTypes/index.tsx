import React, { useEffect, useState } from 'react';
import { Form, Modal, Select } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import { WHETHER_OPTIONS, DICT_CODE_MAP_ID } from '@constants/common';

import useDictCode from '@hooks/useDictCode';


export interface IReCalcModalByTypesProps extends ModalProps {
  onConfirm: (values: any) => void;
}

const ReCalcModalByTypes: React.FC<IReCalcModalByTypesProps> = ({ onConfirm, ...restProps }) => {
  const [form] = Form.useForm();
  const [recalcAll, setRecalcAll] = useState<string | undefined>(undefined);
  const { DHR_CNB_TAX_ATTR } = useDictCode([DICT_CODE_MAP_ID.DHR_CNB_TAX_ATTR]);
  const handleConfirm = () => {
    form.validateFields().then(values => {
      onConfirm({
        calTaxAttribute: values.calTaxAttribute ? values.calTaxAttribute.join(',') : undefined,
      });
    });
  };
  useEffect(() => {
    // 重置
    if (!restProps.open) {
      form.resetFields();
      setRecalcAll(undefined);
    }
  }, [restProps.open]);
  const taxSalaryOptions = DHR_CNB_TAX_ATTR?.map(item => ({ label: item.name, value: item.itemValue })) || [];
  const isShowTaxSalaryType = recalcAll === '0'

  return (
    <Modal {...restProps} width={400} title="重新计算项目选择" destroyOnClose onOk={handleConfirm}>
      <Form
        form={form}
        name="isRecalcAll"
        layout='vertical'
        initialValues={{ recalcAll: '1', taxSalaryType: [] }}
        autoComplete="off"
      >
        <Form.Item
          label="是否重新计算全部薪资项目"
          name="recalcAll"
          rules={[{ required: true, message: '请选择是否重新计算全部薪资项目' }]}
        >
          <Select options={WHETHER_OPTIONS} onChange={value => {
            setRecalcAll(value)
            if (value === '1') {
              form.setFieldsValue({ calTaxAttribute: [] })
            }
          }} />
        </Form.Item>
        {isShowTaxSalaryType &&  (
          <Form.Item
            label="请选择需要重新计算的计税薪资项目类别"
            name="calTaxAttribute"
            rules={[{ required: true, message: '请选择至少一个类别' }]}
          >
            <Select options={taxSalaryOptions} mode="multiple" />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default ReCalcModalByTypes;
