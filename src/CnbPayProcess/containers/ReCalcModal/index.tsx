import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Form, Modal, Select } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import SalaryFormulaEditor from '@src/SalaryFormulaEditor';
import { request } from '@utils/http';
import cnbApis from '@apis/cnb';

export interface IAppProps extends ModalProps {
  processId: string;
  onConfirm: (empId: string) => void;
  initialValue?: Record<string, any>;
  salarySetId: string; // 工资套 ID
  selectedRows: Record<string, any>[];
}

enum ENUM_FOMRULA_TYPES {
  DEFAULT = 'DEFAULT',
  MANUAL = 'MANUAL',
}
const formulaTypesOptions = [
  {
    label: '默认公式',
    value: ENUM_FOMRULA_TYPES.DEFAULT,
  },
  {
    label: '手动设置公式',
    value: ENUM_FOMRULA_TYPES.MANUAL,
  },
];

enum ENUM_EMP_RANGE_TYPES {
  SELECTED = 'selected', // 指当前进入调整状态的所有人员
  ALL = 'all',
}

const BaseForm: React.FC<IAppProps> = ({ salarySetId, initialValue, selectedRows, ...restProps }) => {
  const [formulaContent, setFormulaContent] = React.useState<string>('');
  const [defaultContent, setDefaultContent] = React.useState<string>('');
  // const [editorDisabled, setEditorDisabled] = useState<boolean>(true);
  const [formulaType, setFormulaType] = useState<keyof typeof ENUM_FOMRULA_TYPES>(ENUM_FOMRULA_TYPES.DEFAULT);

  const empRangeOptions = [
    {
      label: '选中人员',
      value: ENUM_EMP_RANGE_TYPES.SELECTED,
      disabled: selectedRows?.length === 0,
    },
    {
      label: '全部人员',
      value: ENUM_EMP_RANGE_TYPES.ALL,
    },
  ];

  console.log('initialValue', initialValue);
  const editorRef = useRef<any>(null);
  const [form] = Form.useForm();
  const handleConfirm = () => {
    const itemFormula = editorRef.current?.getCodeValues();
    form.validateFields().then(values => {
      restProps.onConfirm({
        ...initialValue,
        ...values,
        itemFormula: values.type !== ENUM_FOMRULA_TYPES.DEFAULT ? itemFormula : undefined, // 默认公式不传
      });
    });
  };

  const onFormaulaTypeChange = (type: ENUM_FOMRULA_TYPES) => {
    setFormulaType(type);
    if (type === ENUM_FOMRULA_TYPES.MANUAL) {
      editorRef.current?.setEditorValue(`result = 0;`);
    } else {
      defaultContent ? editorRef.current?.reInit(defaultContent) : editorRef.current?.setEditorValue('');
    }
  };

  const getDefaultFormala = () => {
    request({
      ...cnbApis.getDefaultFormala,
      params: {
        salarySetId,
        itemId: initialValue.itemId,
      },
      onSuccess: data => {
        setFormulaContent(data.content);
        setDefaultContent(data.content);
      },
      onError: err => {
        console.log('err', err);
      },
    });
  };

  useEffect(() => {
    salarySetId && initialValue.itemId && getDefaultFormala();
    return () => {
      setFormulaContent('');
      setDefaultContent('');
      form.resetFields(['item', 'type', 'range', 'formula']);
    };
  }, [salarySetId, initialValue]);

  const editorDisabled = useMemo(() => {
    return formulaType === ENUM_FOMRULA_TYPES.DEFAULT;
  }, [formulaType]);
  return (
    <Modal {...restProps} width={1200} title="重新计算" destroyOnClose onOk={() => handleConfirm()}>
      <Form
        form={form}
        name="recalc"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        initialValues={{}}
        autoComplete="off"
        className="ml-10"
      >
        <Form.Item label="计算薪资项目" name="item">
          <span>{initialValue.title}</span>
        </Form.Item>
        <Form.Item label="选择设置公式类型" name="type" initialValue={ENUM_FOMRULA_TYPES.DEFAULT}>
          <Select options={formulaTypesOptions} onChange={onFormaulaTypeChange} />
        </Form.Item>
        <Form.Item
          label="选择计算人员范围"
          name="range"
          initialValue={selectedRows.length > 0 ? ENUM_EMP_RANGE_TYPES.SELECTED : ENUM_EMP_RANGE_TYPES.ALL}
        >
          <Select options={empRangeOptions} />
        </Form.Item>
        {/* <Form.Item label="选择计算人员范围" name="range" initialValue={ENUM_EMP_RANGE_TYPES.SELECTED}>
          <p>当前为默认公式不允许编辑，若要编辑请选择公式类型为手动设置公式</p>
        </Form.Item> */}
        {/* <Row>
          <Col offset={4}>
            <span>当前为默认公式不允许编辑，若要编辑请选择公式类型为手动设置公式</span>
          </Col>
        </Row> */}
        <Form.Item label="公式内容" name="formula">
          {editorDisabled && (
            <span style={{ display: 'block', color: 'red', marginTop: '6px' }}>
              当前为默认公式不允许编辑，若要编辑请选择公式类型为手动设置公式
            </span>
          )}
          <SalaryFormulaEditor
            ref={editorRef}
            value={formulaContent}
            defaultValue={formulaContent}
            onChange={setFormulaContent}
            configs={{}}
            key="formula"
            disabled={editorDisabled}
            codeMirrorConfig={{
              lineNumbers: false,
            }}
            cnbSetItemId={initialValue.itemId}
            cnbSetId={salarySetId}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default BaseForm;
