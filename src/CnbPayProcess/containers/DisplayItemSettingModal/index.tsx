import React, { useEffect, useMemo, useState } from 'react';
import { Col, Modal, Row, Input, Checkbox, Table, Button } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import { LeftOutlined, RightOutlined, DoubleRightOutlined, DoubleLeftOutlined } from '@ant-design/icons';
import { sortUp, sortDown, ENUM_SORT_TYPES } from '@utils/tools/sort';
import useDebounce from '@hooks/useDebounce';
import classnames from 'classnames';

export interface IAppProps extends ModalProps {
  processId: string;
  unAssignList: Record<string, any>[]; // 未分配的薪资项
  assignList: Record<string, any>[]; // 已分配的薪资项
  onConfirm: (assignList: Record<string, any>, unAssignList: Record<string, any>) => void;
}
const BaseForm: React.FC<IAppProps> = ({ unAssignList = [], assignList = [], onConfirm, ...restProps }) => {
  const [leftFilterKey, setLeftFilterKey] = useState('');
  const [rightFilterKey, setRightFilterKey] = useState('');
  const [unAssignSelected, setUnAssignSelected] = React.useState<React.Key[]>([]);
  const [assignSelected, setAssignSelected] = React.useState<React.Key[]>([]);
  const [_assignList, _setAssignList] = React.useState<Record<string, any>[]>([]);
  const [_unAssignList, _setUnAssignList] = React.useState<Record<string, any>[]>([]);

  useEffect(() => {
    const indexList = _assignList?.map((k, index) => ({
      ...k,
      displayNo: index + 1,
    }));
    _setAssignList(indexList);
  }, [_assignList.length]);

  useEffect(() => {
    _setUnAssignList(unAssignList);
  }, [unAssignList.length]);
  useEffect(() => {
    _setAssignList(assignList);
  }, [assignList.length]);

  useEffect(() => {
    if (!restProps.open) {
      setLeftFilterKey('');
      setRightFilterKey('');
      setUnAssignSelected([]);
      setAssignSelected([]);
    }
  }, [restProps.open]);
  const assignListColumns = useMemo(
    () => [
      {
        title: '字段名称',
        dataIndex: 'displayName',
        key: 'displayName',
        filteredValue: rightFilterKey ? [rightFilterKey] : null,
        onFilter: (val, record) => record.displayName.includes(val),
      },
      {
        title: '排序顺序',
        dataIndex: 'displayNo',
        key: 'displayNo',
      },
    ],
    [rightFilterKey]
  );

  /** 字段移入以及移出 */
  const selectToAssign = (type: 'ADD' | 'CANCEL' | 'ADD_ALL' | 'CANCEL_ALL') => {
    // /** 移入 */
    const typeMapFn = {
      ADD: () => {
        const newAssignListTemp = [..._assignList].concat(
          _unAssignList.filter(k => unAssignSelected.includes(k.itemId))
        );
        const newUnAssignListTemp = [..._unAssignList].filter(k => !unAssignSelected.includes(k.itemId));
        _setAssignList(newAssignListTemp);
        _setUnAssignList(newUnAssignListTemp);
        setAssignSelected([]);
        setUnAssignSelected([]);
      },
      CANCEL: () => {
        const newAssignListTemp = [..._assignList].filter(k => !assignSelected.includes(k.itemId));
        const newUnAssignListTemp = [..._unAssignList].concat(
          _assignList.filter(k => assignSelected.includes(k.itemId))
        );
        _setAssignList(newAssignListTemp);
        _setUnAssignList(newUnAssignListTemp);
        setAssignSelected([]);
        setUnAssignSelected([]);
      },
      ADD_ALL: () => {
        const newAssignListTemp = [..._assignList, ..._unAssignList];
        const newUnAssignListTemp = [];
        _setAssignList(newAssignListTemp);
        _setUnAssignList(newUnAssignListTemp);
      },
      CANCEL_ALL: () => {
        const newAssignListTemp = [];
        const newUnAssignListTemp = [..._assignList, ..._unAssignList];
        _setAssignList(newAssignListTemp);
        _setUnAssignList(newUnAssignListTemp);
      },
    };

    typeMapFn[type] && typeMapFn[type]();
  };

  const onConfirmFields = () => {
    onConfirm(_assignList, _unAssignList);
  };

  const handleSort = (type: ENUM_SORT_TYPES) => {
    const action = type === ENUM_SORT_TYPES.UP ? sortUp : sortDown;
    const _assignListTemp: Record<string, any>[] = action({
      list: _assignList,
      selectedRowKeys: assignSelected,
      rowKey: 'itemId',
      sortKey: 'displayNo',
    });
    _assignListTemp && _setAssignList(_assignListTemp);
  };

  const handleFilterLeft = useDebounce(({ target: { value } }) => {
    setLeftFilterKey(value);
  }, 300);

  const handleFilterRight = useDebounce(({ target: { value } }) => {
    setRightFilterKey(value);
  }, 300);


  return (
    <Modal {...restProps} width={900} title="算薪过程薪资项目显示设置" destroyOnClose onOk={onConfirmFields}>
      <Row>
        <Col span={6}>
          <Row>
            <Col span={24}>
              <span className="lh-26">选择需要显示的薪资项目</span>
            </Col>
            <Col span={24}>
              <Input placeholder="请选择需要显示的薪资项目" onChange={handleFilterLeft} />
              <div className="mt-10" style={{ height: '354px', overflowY: 'scroll' }}>
                <Checkbox.Group
                  style={{ width: '100%' }}
                  value={unAssignSelected}
                  onChange={value => {
                    setUnAssignSelected(value);
                  }}
                >
                  {_unAssignList
                    .filter(({ displayName }) => leftFilterKey ? displayName.includes(leftFilterKey) : true)
                    .map(k => (
                      <div className="mb-20">
                        <Checkbox value={k.itemId}>{k.displayName}</Checkbox>
                      </div>
                    ))}
                </Checkbox.Group>
              </div>
            </Col>
          </Row>
        </Col>
        <Col span={2}>
          <div className="flex flex-direction-column justify-center h-full">
            <RightOutlined
              className={classnames(
                'block mb-20',
                _unAssignList.length && unAssignSelected.length ? 'cursor-pointer' : 'c-disabled'
              )}
              style={{ fontSize: '18px' }}
              onClick={() => selectToAssign('ADD')}
            />
            <DoubleRightOutlined
              className={classnames('block mb-20', _unAssignList.length > 0 ? 'cursor-pointer' : 'c-disabled')}
              style={{ fontSize: '16px' }}
              onClick={() => selectToAssign('ADD_ALL')}
            />
            <DoubleLeftOutlined
              style={{ fontSize: '16px' }}
              onClick={() => selectToAssign('CANCEL_ALL')}
              className={classnames('block mb-20', _assignList.length > 0 ? 'cursor-pointer' : 'c-disabled')}
            />
            <LeftOutlined
              className={classnames(
                'block mb-20',
                _assignList.length && assignSelected.length ? 'cursor-pointer' : 'c-disabled'
              )}
              style={{ fontSize: '18px' }}
              onClick={() => selectToAssign('CANCEL')}
            />
          </div>
        </Col>
        <Col span={16}>
          <Row>
            <Col span={10}>
              <span className="lh-26">设置固定薪资项目排序</span>
            </Col>
          </Row>
          <Row justify="space-between" className="mb-10">
            <Col span={10}>
              <Input placeholder="请选择薪资项目" onChange={handleFilterRight} />
            </Col>
            <Col>
              <Button type="primary" size="small" onClick={() => handleSort(ENUM_SORT_TYPES.UP)}>
                上移
              </Button>
              <Button type="primary" size="small" className="ml-10" onClick={() => handleSort(ENUM_SORT_TYPES.DOWN)}>
                下移
              </Button>
            </Col>
          </Row>
          <Table
            columns={assignListColumns}
            dataSource={_assignList}
            rowKey="itemId"
            pagination={false}
            scroll={{ y: 300 }}
            selectedRowKeys={assignSelected}
            rowSelection={{
              type: 'checkbox',
              onChange: selectedRowKeys => {
                setAssignSelected(selectedRowKeys);
              },
            }}
          />
        </Col>
      </Row>
    </Modal>
  );
};

export default BaseForm;
