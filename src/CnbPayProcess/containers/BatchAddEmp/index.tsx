import React from 'react';
import payProcessApis from '@apis/payProcess';
import { ModalProps } from 'antd/lib/modal';
import { Modal, Spin, Tabs } from 'antd';
import dayjs from 'dayjs';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';
import { IntlWrapper } from '@cvte/cir-lcp-sdk';
import useFetch from '@hooks/useFetch';
import { defaultObj } from '@utils/tools';

const filterFormKey = 'batchAddEmpFormKey';
export interface IAppProps extends ModalProps {
  processId: string;
  onConfirm: (empIds: string[]) => void;
}
const BatchAddEmp: React.FC<IAppProps> = ({ processId, onConfirm, ...restProps }) => {
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);
  const filterFormItems = [
    {
      type: 'input',
      key: 'empName',
      label: '姓名',
      configs: {
        placeholder: '请输入姓名',
      },
      col: 8,
      wrapperCol: 14,
    },
    {
      type: 'input',
      key: 'deptName',
      label: '部门',
      configs: {
        placeholder: '请输入部门',
      },
      col: 8,
      wrapperCol: 14,
    },
  ];
  /** 请求过程 */
  const {
    Data: empsData,
    Loading: empListLoading,
    runAction: onFetchEmpList,
  } = useFetch({
    ...payProcessApis.getAddEmp,
    params: {
      processId,
    },
  });

  const { list: empList = [], pagination = {} } = defaultObj(empsData) as {
    list: Record<string, any>[];
    pagination: Record<string, any>;
  };

  const columns = [
    {
      title: '别名',
      dataIndex: 'empName',
      key: 'empName',
    },
    {
      title: '工号',
      dataIndex: 'empCode',
      key: 'empCode',
    },
    {
      title: '部门',
      dataIndex: 'deptName',
      key: 'deptName',
    },
    {
      title: '用工关系类型',
      dataIndex: 'empTypeName',
      key: 'empTypeName',
    },
    {
      title: '任职状态',
      dataIndex: 'empStatusName',
      key: 'empStatusName',
    },
    {
      title: '发薪单位',
      dataIndex: 'payUnitName',
      key: 'payUnitName',
    },
    {
      title: '发薪开始时间',
      dataIndex: 'beginDate',
      key: 'beginDate',
      render: record => dayjs(record.value).format('YYYY-MM-DD'),
      width: 140,
    },
    {
      title: '发薪结束时间',
      dataIndex: 'endDate',
      key: 'endDate',
      render: record => dayjs(record.value).format('YYYY-MM-DD'),
      width: 140,
    },
  ];
  return (
    <Modal
      {...restProps}
      width={1000}
      destroyOnClose
      onOk={() => onConfirm(selectedRowKeys)}
      afterClose={() => {
        setSelectedRowKeys([]);
        setSelectedRows([]);
      }}
    >
      <Spin spinning={empListLoading}>
        <Tabs defaultActiveKey="1">
          <Tabs.TabPane tab="人员列表" key="1">
            <WULIWholeTable
              columns={columns}
              data={empList}
              height={700}
              canSelect
              multiSelect
              onSelect={(selectedRow, isSelect, _selectedRowKeys, _selectedRows) => {
                setSelectedRowKeys(_selectedRowKeys);
                setSelectedRows(_selectedRows);
              }}
              paginationConfig={{
                current: pagination.pageNum,
                pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
                size: 'small',
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                onChange: (page, pageSize) => {
                  const searchVal = WULIFormActions.get(filterFormKey).getValue();
                  onFetchEmpList({
                    params: {
                      pageNum: page,
                      pageSize,
                      processId,
                      ...searchVal,
                    },
                  });
                },
              }}
              pagination
              rowKey="empId"
              filter={{
                formKey: filterFormKey,
                filters: filterFormItems,
                actionNotSingleLine: true,
                // advanced: true,
                onSearch: data => {
                  onFetchEmpList({
                    params: {
                      processId,
                      ...data,
                    },
                  });
                },
              }}
              selectRowKeys={selectedRowKeys}
              selectedRows={selectedRows}
              showSelectedDetail
              rowNameKey="empName"
              afterCancelSelect={newRows => {
                setSelectedRows(newRows);
                setSelectedRowKeys(newRows.map(k => k.empId));
              }}
            />
          </Tabs.TabPane>
          <Tabs.TabPane tab="已选择人员" key="2">
            <WULIWholeTable columns={columns} data={selectedRows} rowKey="empId" />
          </Tabs.TabPane>
        </Tabs>
      </Spin>
    </Modal>
  );
};

export default IntlWrapper(BatchAddEmp, { forwardRef: true });
