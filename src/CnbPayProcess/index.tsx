import React, { forwardRef, useImperativeHandle } from 'react';
import { LCPDetailTemplate } from '@components/LcpTemplate';
import Detail from './Detail';
import { IOriginalProps } from '../types';

export interface IAppProps extends IOriginalProps {
  processId: string;
}
const CnbPayProcess: React.FC<IAppProps> = forwardRef((props, ref) => {
  console.log('CnbPayProcessref======', props, ref);
  const { processId } = props;
  // useImperativeHandle(ref, () => ({}));
  return processId ? <Detail {...props} /> : <LCPDetailTemplate {...props} ref={ref} />;
});

export default CnbPayProcess;
