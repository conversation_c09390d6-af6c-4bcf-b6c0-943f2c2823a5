import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';
import { Button, Modal, Spin, Tag } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import useFetch from '@hooks/useFetch';
import { renderException } from '@hooks/useTask';
import cnbApis from '@apis/cnb';
import { request } from '@utils/http';
import payProcessApis from '@apis/payProcess';
import tasksApis from '@apis/task';
import { ENUM_TASK_STATUS } from '@constants/task';
import {
  defaultArray,
  defaultObj,
  showErrNotification,
  showSucNotification,
  compineLoading,
  toDateFormat,
} from '@utils/tools';
import { StorageCRUD } from '@utils/tools/storage';
import { exportMultiExcel } from '@utils/excel';
import excelTask from '@components/ExportLargeExcel';
import DisplayItemSettingModal from './containers/DisplayItemSettingModal';
import ReCalcModal from './containers/ReCalcModal';
import BatchAddEmp from './containers/BatchAddEmp';
import ReCalcModalByTypes from './containers/ReCalcModalByTypes';

import { IOriginalProps } from '../types';

import CustomNumberEditor from './containers/CustomNumberEditor';

import '../styles/atom.less';
import './style.less';

const filterFormKey = 'cnbPayProcessFilterKey';

export interface IAppProps extends IOriginalProps {
  processId: string;
}

// const processId = 'efb75198bcd74053b17c04f0c7ae55dd';

/** 页面参数
 * empId 员工 ID
 * tyep 详情类型 LATEST 最新详情 GROUP_HISTORY 历史详情 STUCTURE_HISTORY 薪资结构历史详情
 */

// const CUR_PAGE_TYPE = ENUM_PAGE_TYPES.LATEST;

/**
 *
 * retroPay  0常规薪资过程  1补发薪资过程
 *
 *
 */

const BaseInfo: React.FC<IAppProps> = ({ processId }) => {
  const containerRef = useRef(null);
  const taskErrModalRef = useRef(null);
  const taskPendingModalRef = useRef<{
    modal: any;
    status: ENUM_TASK_STATUS;
  }>();
  // const [empModalVisible, setEmpModalVisible] = React.useState(false);
  const [dispalyItemSettingModalVisible, setDispalyItemSettingModalVisible] = React.useState(false);
  const [reCalcModalVisible, setReCalcModalVisible] = React.useState(false);
  const [bacthAddEmpModalVisible, setBacthAddEmpModalVisible] = React.useState(false);
  const [salarySetDetail, setSalarySetDetail] = React.useState<Record<string, any>>({});
  const [assignList, setAssignList] = React.useState<Record<string, any>[]>([]);
  const [unAssignList, setUnAssignList] = React.useState<Record<string, any>[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]); // 非调整模式下的勾选
  const [selectedRows, setSelectedRows] = React.useState<Record<string, any>[]>([]); // 非调整模式下的勾选
  const [dataSourceInAdjust, setDataSourceInAdjust] = React.useState<Record<string, any>[]>([]); // 非调整模式下的勾选
  const [dataSourceInAdjustRender, setDataSourceInAdjustRender] = React.useState<Record<string, any>[]>([]);
  const [isInAdjust, setAdjust] = React.useState<Boolean>(false);
  const [isAdjustAll, setAdjustAll] = React.useState<Boolean>(false); // 标志调整全部
  const [curReCalcCol, setCurReCalcCol] = React.useState<Record<string, any>>({}); // 当前重新计算的行
  const [validLoading, setValidLoading] = React.useState(false);
  const [commonLoading, setCommonLoading] = React.useState<boolean>(false);
  const [recCalBytypesModalVisible, setRecCalBytypesModalVisible] = React.useState(false);
  const editAgGridRef = useRef(null);

  const assignListStorageNamespace = `${processId}-itemsDisplay`;
  const unAssignListStorageNamespace = `${processId}-itemsUnDisplay`;

  const handleRestSelected = () => {
    editAgGridRef.current?.setSelected(
      selectedRowKeys.map(k => ({
        rowKey: k,
        isSelected: false,
      }))
    );
    setSelectedRows([]);
    setSelectedRowKeys([]);
  };

  const {
    Data: payEmpProcess,
    Loading: payEmpProcessLoading,
    runAction: runActionOfPayEmpProcess,
  } = useFetch({
    ...payProcessApis.payEmpProcess,
    isImmediate: false,
  });

  /** 请求过程 */
  const onFetchPayEmpProcess = (data: Record<string, any> = {}) => {
    const searchParams = WULIFormActions.get(filterFormKey)?.getValue();
    runActionOfPayEmpProcess({
      params: {
        processId,
        pageSize: DEFAULT_PAGE_SIZE,
        ...searchParams,
        ...data,
      },
    });
  };

  /** 发放过程详情 */
  const {
    Data: payEmpProcessDetail = {},
    Loading: payEmpProcessDetailLoading,
    runAction: onFetchPayProcessDetail,
  } = useFetch({
    ...payProcessApis.payProcessDetail,
    isImmediate: false,
  });

  useEffect(() => {
    if (processId) {
      onFetchPayEmpProcess();
      handleGetTaskStatus();
      onFetchPayProcessDetail({
        params: {
          processId,
        },
      });
    }
  }, [processId]);

  const { list: payEmpProcessList = [], pagination: payEmpProcessPagination = {} } = defaultObj(payEmpProcess);

  /** 处理行专列数据 */
  const handleRowsData = (list: Record<string, any>[]) => {
    return list.map(k => {
      const { payRecordMaps } = k;
      const appendInfo = payRecordMaps
        ? ([...assignList, ...unAssignList]).reduce((prev, cur) => {
            cur.dataIndex in payRecordMaps &&
              (prev[cur.dataIndex] = payRecordMaps[cur.dataIndex][isInAdjust ? 'tempValue' : 'value']);
            return prev;
          }, {})
        : {};
      return {
        ...k,
        ...appendInfo,
      };
    });
  };

  const dataSource = useMemo(() => {
    return handleRowsData(payEmpProcessList);
  }, [payEmpProcessList, assignList, isInAdjust]);

  const dataSourceInAdjustFinally = useMemo(() => {
    return handleRowsData(dataSourceInAdjustRender);
  }, [dataSourceInAdjustRender]);

  /** 是否补发 */
  const IS_RETRO_PAY = payEmpProcessDetail?.retroPay === '1';
  /** 工资套详情 */
  const onFetchSalarySetDetail = (salarySetId: string) => {
    request({
      ...payProcessApis.salarySetDetail,
      params: {
        id: salarySetId,
      },
      onSuccess: data => {
        setSalarySetDetail(data);
      },
      onError: err => {
        console.log('err', err);
      },
    });
  };

  useEffect(() => {
    payEmpProcessDetail.salarySetId && onFetchSalarySetDetail(payEmpProcessDetail.salarySetId);
  }, [payEmpProcessDetail.salarySetId]);

  /** 显示/隐藏工资套的项目 */
  useEffect(() => {
    /**  查找是否有本发薪过程的缓存 */
    const assignListInStorage = StorageCRUD.retrieve(assignListStorageNamespace);
    const unAssignListInStorage = StorageCRUD.retrieve(unAssignListStorageNamespace);

    const isExitCache = assignListInStorage && unAssignListInStorage;

    // if (assignListInStorage && unAssignListInStorage) {
    //   setAssignList(assignListInStorage);
    //   setUnAssignList(unAssignListInStorage);
    //   return;
    // }
    const addExtraProperty = list =>
      list.map(k => ({
        ...k,
        title: k.displayName,
        dataIndex: k.itemId,
        key: k.itemId,
      }));
    const _assignList = addExtraProperty(
      defaultArray(salarySetDetail.items)
        .filter(item => (isExitCache ? assignListInStorage?.includes(item.itemId) : true) && item.defDisplay === '1')
        .sort((a, b) => a.displayNo - b.displayNo)
    );
    const isInAssignList = (item: Record<string, any>) => _assignList.findIndex(k => k.itemId === item.itemId) > -1;
    const _unAssignList = addExtraProperty(defaultArray(salarySetDetail.items).filter(item => !isInAssignList(item)));

    setAssignList(_assignList);
    setUnAssignList(_unAssignList);
  }, [salarySetDetail.items]);

  const handleAfterTaskStatus = async () => {
    if (isInAdjust && !isAdjustAll) {
      /** 调整中，则进行调整前没有选人，则调用此接口 */
      const newList =
        (
          await request({
            ...payProcessApis.payEmpProcess,
            params: {
              processId,
              pageSize: DEFAULT_PAGE_SIZE,
              payEmpIdList: dataSourceInAdjust.map(k => k.id).join(','),
            },
          })
        )?.list || [];
      if (newList.length > 0) {
        setDataSourceInAdjust(newList);
        setDataSourceInAdjustRender(newList);
        handleRestSelected();
      }
    } else {
      onFetchPayEmpProcess();
    }
  };

  /** 销毁任务弹窗modal */
  const handleDestroyTaskModal = useCallback((destroyArr = []) => {
    // 进行中弹窗 如果有 - 销毁
    if (taskPendingModalRef?.current && destroyArr.includes('pending')) {
      taskPendingModalRef.current?.modal?.destroy();
      taskPendingModalRef.current = null;
    }
    // 异常弹窗 如果有 - 销毁
    if (taskErrModalRef.current && destroyArr.includes('error')) {
      taskErrModalRef.current.destroy();
      taskErrModalRef.current = null;
    }
  }, []);

  /** 获取任务状态并提示 */
  const handleGetTaskStatus = () => {
    if (!processId) return;
    request({
      ...tasksApis.getTaskByBizId,
      params: {
        bizId: processId,
      },
      onSuccess: data => {
        if (data.status === ENUM_TASK_STATUS.DONE) {
          handleDestroyTaskModal(['error', 'pending']);
          handleAfterTaskStatus();
          return;
        }
        /** 已经初始化过 */
        /** 执行中或者准备中 */
        if ([ENUM_TASK_STATUS.D0ING, ENUM_TASK_STATUS.FREE].includes(data.status)) {
          setTimeout(() => {
            handleGetTaskStatus();
          }, 3000);
          /**
           * 任务弹窗不存在 或者 任务弹窗存在但是状态不是空闲状态
           */
          if (
            !taskPendingModalRef.current ||
            (taskPendingModalRef.current?.status === ENUM_TASK_STATUS.FREE && data.status === ENUM_TASK_STATUS.D0ING)
          ) {
            handleDestroyTaskModal(['error', 'pending']);
            const taskModalInfo = Modal.info({
              title: '任务提示',
              className: 'taskExceptionModal', // 添加自定义的class名
              getContainer: containerRef.current,
              content: data.status === ENUM_TASK_STATUS.D0ING ? '任务正在执行中...' : '任务初始化中',
              closable: false,
              maskClosable: false,
              closeIcon: false,
              okText: '请稍后...',
              okButtonProps: {
                disabled: true,
              },
            });
            taskPendingModalRef.current = {
              modal: taskModalInfo,
              status: data.status,
            };
            return;
          }
        }
        /** 异常 */
        if (data.status === ENUM_TASK_STATUS.EXCEPTION) {
          if (!taskErrModalRef.current) {
            handleDestroyTaskModal(['pending']);
            const taskErrModalInfo = Modal.info({
              title: '任务执行异常提示',
              content: renderException(data),
              okText: '重新执行',
              className: 'taskExceptionModal', // 添加自定义的class名
              getContainer: containerRef.current,
              onOk: () => {
                request({
                  ...tasksApis.reExecuteTask,
                  params: {
                    taskId: data.id,
                  },
                  onSuccess: () => {
                    showSucNotification('重新执行成功');
                    // Modal.destroyAll();
                    handleDestroyTaskModal(['error']);
                    handleGetTaskStatus();
                  },
                  onError: err => {
                    console.log('err', err);
                  },
                });
              },
              closable: false,
              maskClosable: false,
              closeIcon: false,
            });
            taskErrModalRef.current = taskErrModalInfo;
          }
        }
      },
      onError: err => {
        console.log('err', err);
      },
    });
  };

  /** 重新计算 */
  const handleReCalc = async (record?: Record<string, any>) => {
    setCurReCalcCol(record);
    setReCalcModalVisible(true);
  };

  /** 计算最终操作empIds */
  const calcAdjustEmpIds = () => {
    let finalAdjustEmpIds: string[] | undefined;
    if (selectedRowKeys.length > 0) {
      finalAdjustEmpIds = [...selectedRowKeys];
    } else if (isAdjustAll) {
      finalAdjustEmpIds = undefined;
    } else {
      finalAdjustEmpIds = dataSourceInAdjust?.map(k => k.empId);
    }
    return finalAdjustEmpIds;
  };

  /** 确认重新计算 */
  const handleConfirmReCalc = async values => {
    let isCheckPass = true;
    if (values.itemFormula) {
      setValidLoading(true);
      await request({
        ...cnbApis.expresstionValidate,
        data: {
          expression: values.itemFormula,
          itemId: curReCalcCol.itemId,
          salarySetId: salarySetDetail.id,
        },
      })
        .catch(() => {
          showErrNotification('校验不通过');
          isCheckPass = false;
        })
        .finally(() => {
          setValidLoading(false);
        });
    }
    if (!isCheckPass) return;

    const isConfirm = await new Promise(resolve => {
      Modal.confirm({
        title: `确认重新计算${values.title || ''}吗？`,
        content: '点击确定后，系统将按照配置的计算规则重新进行计算',
        onOk: () => resolve(true),
        onCancel: () => resolve(false),
      });
    });
    if (!isConfirm) return;
    const _empIds = calcAdjustEmpIds();
    const data = {
      processId,
      itemId: values?.itemId,
      empIds: _empIds,
      itemFormula: values?.itemFormula,
    };
    request({
      ...payProcessApis.relCalc,
      params: {
        processId,
      },
      data,
      onSuccess: res => {
        showSucNotification('重新计算提交成功，请留意任务执行状态');
        handleGetTaskStatus();
      },
      onError: err => {
        console.log('err', err);
      },
    }).finally(() => {
      setCurReCalcCol({});
      reCalcModalVisible && setReCalcModalVisible(false);
    });
  };

  /** 根据类型计算全部 */
  const handleReCalByType = async (values: Record<string, any>) => {
    setValidLoading(true);
    const _empIds = calcAdjustEmpIds();
    const data = {
      processId,
      empIds: _empIds,
      ...values,
    };
    request({
      ...payProcessApis.relCalc,
      params: {
        processId,
      },
      data,
      onSuccess: res => {
        showSucNotification('重新计算提交成功，请留意任务执行状态');
        handleGetTaskStatus();
      },
      onError: err => {
        console.log('err', err);
      },
    }).finally(() => {
      setRecCalBytypesModalVisible(false);
      setValidLoading(false);
    });
  };

  const mergeColumns = useMemo(() => {
    if (!isInAdjust) {
      return assignList;
    }
    return assignList.map(k => ({
      dataIndex: k.dataIndex,
      editable: true,
      renderEdit: CustomNumberEditor,
      headerComponent: () => (
        <div className="text-center h-60">
          <div>{k.title}</div>
          <Button type="primary" onClick={() => handleReCalc(k)}>
            重新计算
          </Button>
        </div>
      ),
      width: 200,
    }));
  }, [assignList, isInAdjust, selectedRowKeys]);

  const columns = [
    {
      title: '别名',
      dataIndex: 'empName',
      key: 'empName',
      pinned: 'left',
      // width: 140,
    },
    {
      title: '工号',
      dataIndex: 'empCode',
      key: 'empCode',
      pinned: 'left',
      // width: 120,
    },
    {
      title: '域账号',
      dataIndex: 'empAccount',
      key: 'empAccount',
      // width: 120,
    },
    {
      title: '部门',
      dataIndex: 'deptName',
      key: 'deptName',
    },
    {
      title: '用工关系类型',
      dataIndex: 'empTypeName',
      key: 'empTypeName',
    },
    {
      title: '任职状态',
      dataIndex: 'empStatusName',
      key: 'empStatusName',
    },
    {
      title: '发薪单位',
      dataIndex: 'payUnitName',
      key: 'payUnitName',
      // width: 200,
    },
    {
      title: '发薪开始时间',
      dataIndex: 'beginDate',
      key: 'beginDate',
      render: ({ value }) => toDateFormat(value),
      formatteFn: text => toDateFormat(text),
    },
    {
      title: '发薪结束时间',
      dataIndex: 'endDate',
      key: 'endDate',
      render: ({ value }) => toDateFormat(value),
      formatteFn: text => toDateFormat(text),
    },
    {
      title: '备注',
      dataIndex: 'paySlipRemark',
      key: 'paySlipRemark',
      render: ({ value }) => value || '-',
      // width: 300,
    },
  ].concat(mergeColumns);

  /** 导出 */
  const handleExportEmp = () => {
    const baseConfig = {
      columns,
      sheetName: payEmpProcessDetail.name,
    };
    if (selectedRowKeys.length > 0) {
      return exportMultiExcel(
        [
          {
            ...baseConfig,
            data: dataSource.filter(k => selectedRowKeys.includes(k.empId)),
          },
        ],
        payEmpProcessDetail.name
      );
    }
    const formValues = WULIFormActions.get(filterFormKey)?.getValue();
    const params = {
      fetchParams: {
        ...payProcessApis.payEmpProcess,
        params: {
          processId,
          ...formValues,
        },
      },
      xlsxName: payEmpProcessDetail.name,
      configs: {
        columns,
        maxPageSize: 100,
        extraHandle: handleRowsData,
      },
    };
    excelTask.add(params);
  };

  /** 生成备注 */
  const handleGenRemarks = () => {
    setCommonLoading(true);
    request({
      ...payProcessApis.createRemark,
      data: {
        empIds: selectedRowKeys,
        processId,
      },
    })
      .then(res => {
        showSucNotification('操作成功');
        onFetchPayEmpProcess();
        handleRestSelected();
      })
      .finally(() => {
        setCommonLoading(false);
      });
  };

  const onSetDisplayItem = (_assignList, _unAssignList) => {
    setAssignList(_assignList);
    setUnAssignList(_unAssignList);
    StorageCRUD.update({
      namespace: assignListStorageNamespace,
      data: _assignList.map(k => k.itemId),
    });
    StorageCRUD.update({
      namespace: unAssignListStorageNamespace,
      data: _unAssignList.map(k => k.itemId),
    });
    setDispalyItemSettingModalVisible(false);
  };

  const handleAdjust = () => {
    setAdjust(true);
    if (selectedRows.length === 0) {
      setAdjustAll(true);
      return;
    }
    setDataSourceInAdjust(selectedRows);
    setDataSourceInAdjustRender(selectedRows);
    handleRestSelected();
  };
  /** 重新初始化 */
  const handleReInit = () => {
    Modal.confirm({
      title: '确认重新初始化吗？',
      content: '点击确定后，系统将按照配置的计算规则重新进行计算',
      onOk: () => {
        setCommonLoading(true);
        request({
          ...payProcessApis.reInit,
          params: { processId },
          onSuccess: () => {
            showSucNotification('重新初始化成功');
            onFetchPayEmpProcess();
            onFetchSalarySetDetail(payEmpProcessDetail?.salarySetId);
            handleGetTaskStatus();
          },
          onError: err => {
            console.log('err', err);
          },
        }).finally(() => {
          setCommonLoading(false);
        });
      },
    });
  };

  /** 添加核算人员 */
  const handleBatchAddEmp = empIds => {
    setCommonLoading(true);
    request({
      ...payProcessApis.addEmpToProgress,
      params: {
        processId,
      },
      data: {
        empIds,
        processId,
      },
      onSuccess: data => {
        showSucNotification('添加核算人员成功');
        onFetchPayEmpProcess();
        handleGetTaskStatus();
        setBacthAddEmpModalVisible(false);
      },
    }).finally(() => {
      setCommonLoading(false);
    });
  };

  /** 删除人员 */
  const handleDeleteEmp = () => {
    Modal.confirm({
      title: '确认删除吗？',
      content: '点击确定后，系统将删除该人员',
      onOk: () => {
        setCommonLoading(true);
        request({
          ...payProcessApis.removeEmpFromProgress,
          params: {
            processId,
          },
          data: {
            empIds: selectedRowKeys,
            processId,
          },
          onSuccess: () => {
            showSucNotification('删除人员成功');
            onFetchPayEmpProcess();
            handleRestSelected();
          },
        }).finally(() => {
          setCommonLoading(false);
        });
      },
    });
  };

  /** 保存修改 */
  const handleSaveAdjust = () => {
    setCommonLoading(true);
    const _empIds = calcAdjustEmpIds();
    request({
      ...payProcessApis.saveRelCalc,
      data: {
        processId,
        empIds: _empIds,
      },
      onSuccess: () => {
        showSucNotification('保存成功');
        setAdjust(false);
        setAdjustAll(false);
        handleRestSelected();
        onFetchPayEmpProcess();
      },
    }).finally(() => {
      setCommonLoading(false);
    });
  };

  const filterFormItems = [
    {
      type: 'input',
      key: 'empName',
      label: '姓名',
      configs: {
        placeholder: '请输入姓名',
      },
    },
  ];

  const actionBtnItems = [
    {
      key: 'adjust',
      type: 'button',
      content: '添加核算人员',
      config: {
        type: 'primary',
        onClick: () => setBacthAddEmpModalVisible(true),
        className: !IS_RETRO_PAY ? 'hidden' : '',
      },
    },
    {
      key: 'adjust',
      type: 'button',
      content: '调整',
      config: {
        type: 'primary',
        onClick: handleAdjust,
        className: isInAdjust ? 'hidden' : '',
      },
    },
    {
      key: 'adjust',
      type: 'button',
      content: '保存修改',
      config: {
        type: 'primary',
        onClick: handleSaveAdjust,
        className: isInAdjust ? '' : 'hidden',
      },
    },
    {
      key: 'reCalc',
      type: 'button',
      content: '重新计算',
      config: {
        type: 'primary',
        onClick: () => setRecCalBytypesModalVisible(true),
        className: !isInAdjust ? 'hidden' : '',
      },
    },
    {
      key: 'dispaly',
      type: 'button',
      content: '设置显示项目',
      config: {
        type: 'primary',
        onClick: () => setDispalyItemSettingModalVisible(true),
      },
    },
    {
      key: 'append',
      type: 'button',
      content: '追加人员',
      config: {
        type: 'primary',
        onClick: () => setBacthAddEmpModalVisible(true),
        className: IS_RETRO_PAY || isInAdjust ? 'hidden' : '',
      },
    },
    {
      key: 'delete',
      type: 'button',
      content: '删除',
      config: {
        type: 'primary',
        onClick: handleDeleteEmp,
        disabled: selectedRowKeys.length === 0,
        className: isInAdjust ? 'hidden' : '',
        // className: selectedRowKeys.length === 0 ? 'c-disabled' : '',
      },
    },
    {
      key: 'export',
      type: 'button',
      content: '导出',
      config: {
        type: 'primary',
        onClick: handleExportEmp,
      },
    },
    {
      key: 'genRemarks',
      type: 'button',
      content: '生成备注',
      config: {
        type: 'primary',
        onClick: handleGenRemarks,
        className: isInAdjust ? 'hidden' : '',
      },
    },
    {
      key: 'reInit',
      type: 'button',
      content: '重新初始化',
      handler: handleReInit,
      config: {
        type: 'danger',
        onClick: handleReInit,
        className: isInAdjust ? 'hidden' : '',
      },
    },
    {
      key: 'cancelAdjust',
      type: 'button',
      content: '返回',
      config: {
        type: 'danger',
        onClick: () => {
          handleRestSelected();
          setAdjust(false);
          setAdjustAll(false);
          onFetchPayEmpProcess();
        },
        className: isInAdjust ? '' : 'hidden',
      },
    },
  ];

  const onFilterSearch = data => {
    const { empName } = data;
    if (isInAdjust && !isAdjustAll) {
      const _dataSource = !empName ? dataSourceInAdjust : dataSourceInAdjust.filter(k => k.empName.includes(empName));
      setDataSourceInAdjustRender(_dataSource);
      return;
    }
    onFetchPayEmpProcess();
  };

  const renderDataSource = isInAdjust && !isAdjustAll ? dataSourceInAdjustFinally : dataSource;
  const loading = compineLoading([payEmpProcessLoading, payEmpProcessDetailLoading, commonLoading]);
  return (
    <Spin spinning={loading}>
      <div className="CnbPayProcessWrap" ref={containerRef}>
        <div className="flex items-center">
          <h3>
            {payEmpProcessDetail.name}
            {isInAdjust && (
              <>
                <Tag icon={<ExclamationCircleOutlined />} color="#cd201f" className="ml-10">
                  调整中
                </Tag>
              </>
            )}
          </h3>
          <div className="flex items-center ml-20">
            <span>薪资组：{payEmpProcessDetail.groupName}</span>
            <span className="ml-10">薪资期间：{payEmpProcessDetail.periodName}</span>
            <span className="ml-10">薪资套：{payEmpProcessDetail.salarySetName}</span>
            <span className="ml-10">薪资核算业务类型: {payEmpProcessDetail.typeName}</span>
          </div>
        </div>
        <WULIWholeTable
          ref={editAgGridRef}
          columns={columns}
          data={renderDataSource}
          canSelect
          multiSelect
          onSelect={(selectedRow, isSelect, _selectedRowKeys, _selectedRows) => {
            setSelectedRowKeys(_selectedRowKeys);
            setSelectedRows(_selectedRows);
          }}
          paginationConfig={{
            current: payEmpProcessPagination.pageNum,
            pageSize: payEmpProcessPagination.pageSize || DEFAULT_PAGE_SIZE,
            size: 'small',
            total: payEmpProcessPagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (page, pageSize) => {
              onFetchPayEmpProcess({
                pageNum: page,
                pageSize,
              });
            },
          }}
          pagination
          rowKey="empId"
          action={actionBtnItems}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            actionNotSingleLine: true,
            onSearch: onFilterSearch,
          }}
          selectedRows={selectedRows}
          showSelectedDetail
          rowNameKey="empName"
          afterCancelSelect={newRows => {
            setSelectedRows(newRows);
            setSelectedRowKeys(newRows.map(k => k.empId));
          }}
        />
        <DisplayItemSettingModal
          open={dispalyItemSettingModalVisible}
          onCancel={() => setDispalyItemSettingModalVisible(false)}
          processId={processId}
          assignList={assignList}
          unAssignList={unAssignList}
          onConfirm={onSetDisplayItem}
        />
        <ReCalcModal
          open={reCalcModalVisible}
          processId={processId}
          onCancel={() => {
            setCurReCalcCol({});
            setReCalcModalVisible(false);
          }}
          onConfirm={handleConfirmReCalc}
          initialValue={curReCalcCol}
          salarySetId={salarySetDetail.id}
          confirmLoading={validLoading}
          selectedRows={selectedRows}
        />
        <BatchAddEmp
          open={bacthAddEmpModalVisible}
          processId={processId}
          onCancel={() => setBacthAddEmpModalVisible(false)}
          onConfirm={handleBatchAddEmp}
          title="请选择需要核算的人员"
        />
        <ReCalcModalByTypes
          open={recCalBytypesModalVisible}
          onConfirm={handleReCalByType}
          onCancel={() => setRecCalBytypesModalVisible(false)}
          confirmLoading={validLoading}
        />
      </div>
    </Spin>
  );
};

export default BaseInfo;
