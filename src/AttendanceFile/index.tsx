import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Tabs, Spin } from 'antd';
import axios from 'axios';

import employeeApis from '@apis/employee';
import { request as fetchApi } from '@utils/http';

import Business from './containers/Business';
import RecordInfo from './containers/RecordInfo';
import BaseScheme from './containers/BaseScheme';
import RuleScheme from './containers/RuleScheme';
import EmployeeCard from './containers/EmployeeCard';

import './style.less';

export interface IAppProps {
  empId: string;
  account: string;
  pageId: string;
  systemHelper: {
    history: {
      push: any;
    };
  };
}

const AttendanceFile: React.FC<IAppProps> = props => {
  const [avatarUrl, setAvatarUrl] = useState('');
  const [employeeInfo, setEmployeeInfo] = useState({});
  const [loading, setLoading] = useState<boolean>(false);
  const { empId, account } = props;

  const tabItems = useMemo(
    () => [
      {
        label: '档案信息',
        key: 'baseInfo',
        children: <RecordInfo {...props} />,
      },
      {
        label: '基础方案',
        key: 'baseScheme',
        children: <BaseScheme {...props} />,
      },
      {
        label: '规则方案',
        key: 'ruleScheme',
        children: <RuleScheme {...props} />,
      },
      // {
      //   label: '业务单据',
      //   key: 'business',
      //   children: <Business {...props} />,
      // },
    ],
    [props]
  );

  const onFetchEmployeeAvatar = useCallback(() => {
    const { baseURL, url } = employeeApis.employeeAvatar;
    avatarUrl && URL.revokeObjectURL(avatarUrl);
    axios
      .get(`${baseURL}${url}`, {
        params: {
          type: 'SYSTEM',
          username: account,
        },
        headers: {
          Accept: 'application/octet-stream',
        },
        responseType: 'blob',
      })
      .then(res => {
        const url = res?.data;
        const newAvatarUrl = url ? URL.createObjectURL(url) : '';
        setAvatarUrl(newAvatarUrl);
      });
  }, [account]);

  const onFetchEmployeeInfo = useCallback(() => {
    setLoading(true);
    fetchApi({
      ...employeeApis.employeeBaseInfo,
      params: {
        empId,
        queryDate: new Date().getTime(),
      },
      onSuccess: res => {
        setEmployeeInfo(res || {});
      },
    }).finally(() => setLoading(false));
  }, [empId]);

  useEffect(() => {
    empId && onFetchEmployeeInfo();
  }, [empId]);

  useEffect(() => {
    account && onFetchEmployeeAvatar();
  }, [account]);

  /** 清除对URL的引用 */
  useEffect(() => {
    return () => {
      avatarUrl && URL.revokeObjectURL(avatarUrl);
    };
  }, []);

  return (
    <Spin spinning={loading}>
      <div className="employeeAttendanceFileContainer">
        <EmployeeCard initValues={employeeInfo} avatarUrl={avatarUrl} />
        <Tabs items={tabItems} />
      </div>
    </Spin>
  );
};

export default AttendanceFile;
