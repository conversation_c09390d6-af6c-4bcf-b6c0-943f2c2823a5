import React from 'react';

import { Row, Col, Avatar, Tag } from 'antd';

import './style.less';

export interface IAppProps {
  initValues: any;
  avatarUrl: string;
}
const EmployeeCard: React.FC<IAppProps> = ({ initValues, avatarUrl }) => {
  const nameIntro = (initValues.name || '').split('').shift();
  return (
    <Row className="dhr-attendance-file-employee-card">
      <Col className="dhr-attendance-file-employee-card-avatar">
        <Avatar size={64} shape="square" style={{ border: '2px solid #eeeeee8c', borderRadius: '4px' }} src={avatarUrl}>
          {nameIntro || '-'}
        </Avatar>
      </Col>
      <Col span={22} className="dhr-attendance-file-employee-card-intro">
        <Row gutter={[14, 0]}>
          <Col>
            <span>{initValues.name || '-'}</span>
            {initValues.code && <span>｜{initValues.code}</span>}
          </Col>
          <Col>
            {initValues.empTypeName && <Tag>{initValues.empTypeName}</Tag>}
            {initValues.empStatusName && <Tag>{initValues.empStatusName}</Tag>}
            {initValues.socialSeniorityAge && <Tag>{`社会工龄 ${initValues.socialSeniorityAge} 年`}</Tag>}
          </Col>
        </Row>
        <Row gutter={14}>
          <Col>
            <span>行政组织：</span>
            <span>{initValues.deptFullPathName || '-'}</span>
          </Col>
        </Row>
      </Col>
    </Row>
  );
};

export default EmployeeCard;
