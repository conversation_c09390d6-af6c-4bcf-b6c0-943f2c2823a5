import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useEffect, useState } from 'react';

import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

import { Modal } from 'antd';

const formLayout = {
  col: 24,
  labelCol: 4,
  wrapperCol: 18,
};

export interface ModalProps {
  title: string;
  formKey: string;
  formItems: IFormItem[];
}

export interface IAppProps extends ModalProps {
  open: boolean;
  onAfterClose: any;
}
const AddModal: React.FC<IAppProps> = props => {
  const { open, title, formKey, formItems, onAfterClose } = props;

  const [visible, setVisible] = useState<boolean>(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleOk = () => {
    WULIFormActions.get(formKey).validate((result, data) => {
      console.log('daaa===', result, data);
    });
  };

  return (
    <Modal title={title} open={visible} onOk={handleOk} afterClose={onAfterClose} onCancel={() => setVisible(false)}>
      <WULIForm formKey={formKey} formItems={formItems} defaultLayout={formLayout} />
    </Modal>
  );
};

const withParams = (params: ModalProps): IAppProps => {
  return {
    ...params,
    open: true,
    onAfterClose: () => { },
  };
};

const addModal = (params: any) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
  };
  reactRender(<AddModal {...params} onAfterClose={onAfterClose} />, container);
};

const modal = AddModal as any;

modal.add = function addFn(props: ModalProps) {
  return addModal(withParams(props));
};

export default modal;
