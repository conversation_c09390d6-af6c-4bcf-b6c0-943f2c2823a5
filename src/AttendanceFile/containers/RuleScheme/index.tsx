import React, { useMemo, useState } from 'react';
import { Spin } from 'antd';

import Block from '@components/Block';
import { LCPPageTemplate } from '@components/LcpTemplate';

import './style.less';

export interface IAppProps {
  empId: string;
  systemHelper: {
    history: {
      push: any;
    };
  };
}
const RuleScheme: React.FC<IAppProps> = props => {
  const { empId, systemHelper } = props;

  const [vacationLoading, setVacationLoading] = useState<boolean>(true);
  const [overtimeLoading, setOvertimeLoading] = useState<boolean>(true);

  // // 新增 休假方案
  // const handleVacationAdd = () => {
  //   const query = {
  //     pageName: '休假方案-新建',
  //     apiName: 'TB_TMG_LEAVE_PLAN',
  //     appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
  //     classId: '5b08a3130c7943f7aae722e2b90e1dec',
  //     pageFlag: '5b08a3130c7943f7aae722e2b90e1dec',
  //     tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
  //   };
  //   systemHelper.history.push(`/portal/jg8gj4sq/jg8gj4sq_fe3x18gv_zugdcqb3/detail?${queryString.stringify(query)}`);
  // };

  // // 新增 加班方案
  // const handleOvertimeAdd = () => {
  //   const query = {
  //     pageName: '加班方案-新建',
  //     apiName: 'TB_TMG_OVERTIME_PLAN',
  //     appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
  //     pageFlag: '4c603660135c4ab6b82b915b305c64c1',
  //     classId: '4c603660135c4ab6b82b915b305c64c1',
  //     tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
  //   };
  //   systemHelper.history.push(`/portal/jg8gj4sq/jg8gj4sq_ykei6d4m_rbg0fwgb/detail?${queryString.stringify(query)}`);
  // };

  // 休假方案
  const vacationConfig = useMemo(
    () => ({
      pageType: 'LCP_VIEW',
      apiName: 'TB_TMG_LEAVE_EMP',
      appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
      classId: '7a9aefdcbdb9499fba9de5871710ef25',
      pageCode: '7a9aefdcbdb9499fba9de5871710ef25',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        compParams: {
          LCP_VIEW_TABLE_FLTER: {
            data: {
              C_EMP_ID: empId,
            },
          },
        },
      },
    }),
    [empId]
  );

  // 加班方案
  const overtimeConfig = useMemo(
    () => ({
      pageType: 'LCP_VIEW',
      apiName: 'TB_TMG_OVERTIME_EMP',
      appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
      classId: '3f6f7cf668d3487e87822856784c83d1',
      pageCode: '3f6f7cf668d3487e87822856784c83d1',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        compParams: {
          LCP_VIEW_TABLE_FLTER: {
            data: {
              C_EMP_ID: empId,
            },
          },
        },
      },
    }),
    [empId]
  );

  const pageLoading = [vacationLoading, overtimeLoading].some(loading => loading);
  return (
    <Spin spinning={pageLoading}>
      <div className="ruleScheme" style={{ paddingLeft: '10px', paddingRight: '10px' }}>
        <Block
          title="休假方案"
          subTitleClassName="ruleSchemeSubTitle"
          // subTitle={
          //   <Button type="link" onClick={handleVacationAdd}>
          //     添加
          //   </Button>
          // }
        >
          <div className="ruleLcpTemplateContainer">
            <LCPPageTemplate {...vacationConfig} onInitData={() => setVacationLoading(false)} />
          </div>
        </Block>
        <Block
          title="加班方案"
          subTitleClassName="ruleSchemeSubTitle"
          // subTitle={
          //   <Button type="link" onClick={handleOvertimeAdd}>
          //     添加
          //   </Button>
          // }
        >
          <div className="ruleLcpTemplateContainer">
            <LCPPageTemplate {...overtimeConfig} onInitData={() => setOvertimeLoading(false)} />
          </div>
        </Block>
      </div>
    </Spin>
  );
};

export default RuleScheme;
