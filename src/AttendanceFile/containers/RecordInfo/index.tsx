import React, { useMemo, useState } from 'react';
import { Spin } from 'antd';

import { LCPDetailTemplate } from '@components/LcpTemplate';

export interface IAppProps {
  pageId: string;
}
const RecordInfo: React.FC<IAppProps> = ({ pageId }) => {
  const [loading, setLoading] = useState<boolean>(true);

  const formTemplateConfig = useMemo(
    () => ({
      pageId,
      appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
      classId: 'd71b57d077424c7eab201115be45c67d',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        contextData: {
          viewType: 'read',
        },
      },
    }),
    [pageId]
  );

  return (
    <Spin spinning={loading}>
      <div className="recordInfo" style={{ padding: '20px 10px' }}>
        <LCPDetailTemplate {...formTemplateConfig} onInitData={() => setLoading(false)} />
      </div>
    </Spin>
  );
};

export default RecordInfo;
