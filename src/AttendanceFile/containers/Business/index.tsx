import React, { useMemo } from 'react';

import { Button } from 'antd';

import AddModal from '../AddModal';
import Block from '@components/Block';
import WULIWholeTable from '@components/WholeTable/AgGrid';

import './style.less';

const routineFormKey = 'routineFormKey';

export interface IAppProps {}
const Business: React.FC<IAppProps> = props => {
  // 常规单据
  const baseColumns = useMemo(
    () => [
      {
        title: '单据编码',
        dataIndex: 'billCode',
        key: 'billCode',
      },
      {
        title: '单据类型',
        dataIndex: 'billType',
        key: 'billType',
      },
      {
        title: '考勤项目',
        dataIndex: 'attendanceItem',
        key: 'attendanceItem',
      },
      {
        title: '考勤项目ID',
        dataIndex: 'attendanceItemId',
        key: 'attendanceItemId',
      },
      {
        title: '归属年份',
        dataIndex: 'year',
        key: 'year',
      },
      {
        title: '归属月份',
        dataIndex: 'month',
        key: 'month',
      },
      {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
      },
      {
        title: '结束时间',
        dataIndex: 'endTime',
        key: 'endTime',
      },
      {
        title: '说明',
        dataIndex: 'description',
        key: 'description',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
      },
      {
        title: '值',
        dataIndex: 'value',
        key: 'value',
      },
    ],
    []
  );

  // 变更单据
  const modifyColumns = useMemo(
    () => [
      {
        title: '单据编码',
        dataIndex: 'billCode',
        key: 'billCode',
      },
      {
        title: '变更项目',
        dataIndex: 'changeItem',
        key: 'changeItem',
      },
      {
        title: '考勤项目ID',
        dataIndex: 'attendanceItemId',
        key: 'attendanceItemId',
      },
      {
        title: '归属年份',
        dataIndex: 'year',
        key: 'year',
      },
      {
        title: '归属月份',
        dataIndex: 'month',
        key: 'month',
      },
      {
        title: '变更前值',
        dataIndex: 'previousValue',
        key: 'previousValue',
      },
      {
        title: '变更后值',
        dataIndex: 'updatedValue',
        key: 'updatedValue',
      },
      {
        title: '变更值',
        dataIndex: 'changeValue',
        key: 'changeValue',
      },
      {
        title: '说明',
        dataIndex: 'explanation',
        key: 'explanation',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
      },
    ],
    []
  );

  // 导入项目
  const importColumns = useMemo(
    () => [
      {
        title: '单据编码',
        dataIndex: 'billCode',
        key: 'billCode',
      },
      {
        title: '考勤项目',
        dataIndex: 'attendanceItem',
        key: 'attendanceItem',
      },
      {
        title: '考勤项目ID',
        dataIndex: 'attendanceItemId',
        key: 'attendanceItemId',
      },
      {
        title: '归属年份',
        dataIndex: 'year',
        key: 'year',
      },
      {
        title: '归属月份',
        dataIndex: 'month',
        key: 'month',
      },
      {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
      },
      {
        title: '结束时间',
        dataIndex: 'endTime',
        key: 'endTime',
      },
      {
        title: '说明',
        dataIndex: 'explanation',
        key: 'explanation',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
      },
      {
        title: '值',
        dataIndex: 'value',
        key: 'value',
      },
    ],
    []
  );

  const formItems = useMemo(
    () => [
      {
        type: 'select',
        key: 'billType',
        label: '单据类型',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: [
            {
              label: '122',
              value: '333',
            },
          ],
        },
      },
      {
        type: 'select',
        key: 'attendanceItem',
        label: '单据项目',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: [
            {
              label: '122',
              value: '333',
            },
          ],
        },
      },
      {
        type: 'rangePicker',
        key: 'year',
        label: '归属年份',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          picker: 'year',
          allowClear: true,
          placeholder: '请选择',
        },
      },
      {
        type: 'rangePicker',
        key: 'month',
        label: '归属月份',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          picker: 'month',
          allowClear: true,
          placeholder: '请选择',
        },
      },
      {
        type: 'datePicker',
        key: 'startTime',
        label: '开始时间',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          placeholder: '请选择',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        type: 'datePicker',
        key: 'endTime',
        label: '结束时间',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          placeholder: '请选择',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        type: 'textarea',
        key: 'remark',
        label: '说明',
        configs: {
          allowClear: true,
          placeholder: '请选择',
        },
      },
      {
        type: 'select',
        key: 'status',
        label: '状态',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: [
            {
              label: '122',
              value: '333',
            },
          ],
        },
      },
    ],
    []
  );

  const handleRoutineAdd = () => {
    AddModal.add({
      title: '添加常规单据',
      formItems: formItems,
      formKey: routineFormKey,
    });
  };
  return (
    <div className="businessContainer" style={{ paddingLeft: '10px', paddingRight: '10px' }}>
      <Block
        title="常规单据"
        subTitleClassName="businessSubTitle"
        subTitle={
          <Button type="link" onClick={handleRoutineAdd}>
            添加
          </Button>
        }
      >
        <WULIWholeTable height={300} rowKey="id" data={[]} columns={baseColumns} />
      </Block>
      <Block title="变更单据" subTitleClassName="businessSubTitle" subTitle={<Button type="link">添加</Button>}>
        <WULIWholeTable height={300} rowKey="id" data={[]} columns={modifyColumns} />
      </Block>
      <Block title="导入项目" subTitleClassName="businessSubTitle" subTitle={<Button type="link">添加</Button>}>
        <WULIWholeTable height={300} rowKey="id" data={[]} columns={importColumns} />
      </Block>
    </div>
  );
};

export default Business;
