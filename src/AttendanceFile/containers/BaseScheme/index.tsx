import React, { useMemo, useRef, useState } from 'react';
import queryString from 'query-string';
import { Button, Spin } from 'antd';

import Block from '@components/Block';
import { LCPPageTemplate } from '@components/LcpTemplate';

import './style.less';

export interface IAppProps {
  empId: string;
  systemHelper: {
    history: {
      push: any;
    };
  };
}

const BaseScheme: React.FC<IAppProps> = props => {
  const { systemHelper, empId } = props;
  const calendarRef = useRef(null);
  const attendanceRef = useRef(null);

  const [calendarLoading, setCalendarLoading] = useState(true);
  const [attendanceLoading, setAttendanceLoading] = useState(true);

  // // 添加日程表
  // const handleSchemeAdd = () => {
  //   systemHelper.history.push(
  //     `/portal/jg8gj4sq/cmPage?${queryString.stringify({
  //       pageName: '工作日程表-新建',
  //       apiName: 'TB_TMG_SCHEDULE_EMP',
  //       appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
  //       pageFlag: 'a0e9469abda14bf1b8b08d807036cfe5',
  //       classId: 'a0e9469abda14bf1b8b08d807036cfe5',
  //     })}`
  //   );
  // };

  // // 添加考勤周期
  // const handleAttendanceAdd = () => {
  //   const query = {
  //     pageName: '考勤周期-新建',
  //     apiName: 'TB_TMG_ATTENDANCE_CYCLE',
  //     appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
  //     pageFlag: '72979bb3745544ffa7d60a778061b55d',
  //     classId: '72979bb3745544ffa7d60a778061b55d',
  //     tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
  //   };
  //   systemHelper.history.push(`/portal/jg8gj4sq/jg8gj4sq_qfvoc16e_yqlvgmtn/detail?${queryString.stringify(query)}`);
  // };

  // 工作日历
  const calendarConfig = useMemo(
    () => ({
      pageType: 'LCP_VIEW',
      apiName: 'TB_TMG_SCHEDULE',
      appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
      classId: 'a0e9469abda14bf1b8b08d807036cfe5',
      pageCode: 'a0e9469abda14bf1b8b08d807036cfe5',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        compParams: {
          LCP_VIEW_TABLE_FLTER: {
            data: {
              C_EMP_ID: empId,
            },
          },
        },
      },
    }),
    [empId]
  );

  // 考勤周期
  const attendanceCycleConfig = useMemo(
    () => ({
      pageType: 'LCP_VIEW',
      apiName: 'TB_TMG_ATTENDANCE_CYCLE_EMP',
      appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
      classId: 'e02402cfb6a344db9dff18a52df90c8c',
      pageCode: 'e02402cfb6a344db9dff18a52df90c8c',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        compParams: {
          LCP_VIEW_TABLE_FLTER: {
            data: {
              C_EMP_ID: empId,
            },
          },
        },
      },
    }),
    [empId]
  );

  const pageLoading = [calendarLoading, attendanceLoading].some(loading => loading);
  return (
    <Spin spinning={pageLoading}>
      <div className="baseSchemeContainer" style={{ paddingLeft: '10px', paddingRight: '10px' }}>
        <Block
          title="工作日程表"
          subTitleClassName="baseSchemeSubTitle"
          // subTitle={
          //   <Button type="link" onClick={handleSchemeAdd}>
          //     添加
          //   </Button>
          // }
        >
          <div className="baseSchemeLcpTemplateContainer">
            <LCPPageTemplate {...calendarConfig} ref={calendarRef} onInitData={() => setCalendarLoading(false)} />
          </div>
        </Block>
        <Block
          title="考勤周期"
          subTitleClassName="baseSchemeSubTitle"
          // subTitle={
          //   <Button type="link" onClick={handleAttendanceAdd}>
          //     添加
          //   </Button>
          // }
        >
          <div className="baseSchemeLcpTemplateContainer">
            <LCPPageTemplate
              ref={attendanceRef}
              {...attendanceCycleConfig}
              onInitData={() => setAttendanceLoading(false)}
            />
          </div>
        </Block>
      </div>
    </Spin>
  );
};

export default BaseScheme;
