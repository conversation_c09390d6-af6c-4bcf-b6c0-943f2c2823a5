.employeeAttendanceFileContainer {
  .csb-page-container-block {
    &.add-padding {
      padding: 0px;
    }
    .page-generator-main {
      .lcp-view-table-filter,
      .ag-grid-whole-table-filter {
        display: none;
      }
      .lcp-page-view-table {
        padding: 0px !important;
        .ag-view-table {
          .wuli-ag-whole-table {
            .ag-grid-whole-table-action {
              display: none;
            }
            .ag-theme-alpine {
              &.wuli-ag-grid {
                .ag-header-container {
                  .ag-header-row {
                    .ag-header-cell {
                      .ag-header-cell-comp-wrapper {
                        .header-filter-block {
                          .header-filter-left-icon-block {
                            display: none;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .baseSchemeLcpTemplateContainer {
    height: 300px;
  }
}
