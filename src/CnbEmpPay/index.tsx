import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Spin, Modal, message, Popover } from 'antd';

import cnbApi from '@apis/cnb';
import tasksApis from '@apis/task';
import useFetch from '@hooks/useFetch';
import useViews from '@hooks/useViews';
import { renderException } from '@hooks/useTask';
import useDictCode from '@hooks/useDictCode';

import salarySlipApis from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';
import { defaultObj, showSucNotification } from '@utils/tools';

import PayrollActionModal, { ACTION_TYPES } from '@components/PayrollActionModal';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

import { ENUM_TASK_STATUS } from '@constants/task';
import { DICT_CODE_MAP_ID, CSB_VIEW_CODES } from '@constants/common';

import './style.less';

const filterFormKey = 'cnbEmpPayFormKey';

export interface IAppProps {
  processId: string;
  salarySetHid: string;
}
const CnbEmpPay: React.FC<IAppProps> = props => {
  const { processId, salarySetHid } = props;

  const containerRef = useRef(null);
  const taskErrModalRef = useRef(null);
  const taskPendingModalRef = useRef<{
    modal: any;
    status: ENUM_TASK_STATUS;
  }>();

  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [testLoading, setTestLoading] = useState<boolean>(false);
  const [provideLoading, setProvideLoading] = useState<boolean>(false);
  const { DHR_COMMON_WHETHER = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_COMMON_WHETHER]);
  const [actionType, setActionType] = React.useState<ACTION_TYPES | undefined>(undefined);

  // 角色权限
  const { Data: csbViews } = useViews(CSB_VIEW_CODES.CNB_EMP_PAY);

  const codeMap = useMemo(
    () =>
      (DHR_COMMON_WHETHER || []).reduce(
        (pre, cur) => ({
          ...pre,
          [cur.itemValue]: cur.name,
        }),
        {}
      ),
    [DHR_COMMON_WHETHER]
  );

  const columns = useMemo(
    () => [
      {
        title: '工号',
        dataIndex: 'code',
        key: 'code',
        pinned: 'left',
      },
      {
        title: '域账号',
        dataIndex: 'account',
        key: 'account',
        pinned: 'left',
      },
      {
        title: '别名',
        dataIndex: 'otherName',
        key: 'otherName',
        pinned: 'left',
      },
      {
        title: '是否为限制人员',
        dataIndex: 'isLimitEmp',
        key: 'isLimitEmp',
        render: ({ data: { isLimitEmp } }) => codeMap[isLimitEmp] || '',
      },
      {
        title: '工资表名称',
        dataIndex: 'processName',
        key: 'processName',
      },
      {
        title: '发薪期间',
        dataIndex: 'periodName',
        key: 'periodName',
      },
      {
        title: '是否匹配通知模板',
        dataIndex: 'isMatcherNotifyTpl',
        key: 'isMatcherNotifyTpl',
        render: ({ data: { isMatcherNotifyTpl } }) => codeMap[isMatcherNotifyTpl] || '-',
      },
      {
        title: '工资条是否已发放',
        key: 'hasSendSpNotice',
        dataIndex: 'hasSendSpNotice',
        render: ({ data: { hasSendSpNotice } }) => codeMap[hasSendSpNotice] || '-',
      },
      {
        title: '工资条发放提示',
        key: 'sendSpNoticeMsg',
        dataIndex: 'sendSpNoticeMsg',
        render: record => {
          const {
            data: { sendSpNoticeMsg },
          } = record;
          return sendSpNoticeMsg ? (
            <Popover content={sendSpNoticeMsg} title="提醒" trigger="hover">
              <a
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {sendSpNoticeMsg}
              </a>
            </Popover>
          ) : (
            '-'
          );
        },
      },
    ],
    [codeMap]
  );

  const filterFormItems: any[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'nameOrAccount',
        label: '姓名/域账号',
        labelCol: 8,
        wrapperCol: 16,
        configs: {
          allowClear: true,
          placeholder: '请输入',
        },
      },
      {
        type: 'select',
        key: 'isLimitEmp',
        label: '是否为限制人员',
        labelCol: 11,
        wrapperCol: 12,
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: (DHR_COMMON_WHETHER || []).map(codeItem => ({
            ...codeItem,
            label: codeItem.name,
            value: codeItem.itemValue,
            key: codeItem.itemValue,
          })),
        },
      },
      {
        type: 'select',
        key: 'isMatcherNotifyTpl',
        label: '是否匹配通知模板',
        labelCol: 12,
        wrapperCol: 12,
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: (DHR_COMMON_WHETHER || []).map(codeItem => ({
            ...codeItem,
            label: codeItem.name,
            value: codeItem.itemValue,
            key: codeItem.itemValue,
          })),
        },
      },
      {
        type: 'select',
        key: 'hasSendSpNotice',
        label: '工资条是否已发放',
        labelCol: 12,
        wrapperCol: 12,
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: (DHR_COMMON_WHETHER || []).map(codeItem => ({
            ...codeItem,
            label: codeItem.name,
            value: codeItem.itemValue,
            key: codeItem.itemValue,
          })),
        },
      },
    ],
    [DHR_COMMON_WHETHER]
  );

  useEffect(() => {
    processId && onStartTask();
  }, [processId]);

  /** 销毁任务弹窗modal */
  const handleDestroyTaskModal = useCallback((destroyArr = []) => {
    // 进行中弹窗 如果有 - 销毁
    if (taskPendingModalRef?.current && destroyArr.includes('pending')) {
      taskPendingModalRef.current?.modal?.destroy();
      taskPendingModalRef.current = null;
    }
    // 异常弹窗 如果有 - 销毁
    if (taskErrModalRef.current && destroyArr.includes('error')) {
      taskErrModalRef.current.destroy();
      taskErrModalRef.current = null;
    }
  }, []);

  // 执行任务
  const onStartTask = () => {
    fetchApi({
      ...tasksApis.getTaskByBizId,
      params: {
        bizId: processId,
      },
      onSuccess: data => {
        if (data.status === ENUM_TASK_STATUS.DONE) {
          handleDestroyTaskModal(['error', 'pending']);
          return onFetchEmpPayDetailList();
        }
        /** 已经初始化过 */
        /** 执行中或者准备中 */
        if ([ENUM_TASK_STATUS.D0ING, ENUM_TASK_STATUS.FREE].includes(data.status)) {
          setTimeout(() => {
            onStartTask();
          }, 3000);
          /**
           * 任务弹窗不存在 或者 任务弹窗存在但是状态不是空闲状态
           */
          if (
            !taskPendingModalRef.current ||
            (taskPendingModalRef.current?.status === ENUM_TASK_STATUS.FREE && data.status === ENUM_TASK_STATUS.D0ING)
          ) {
            handleDestroyTaskModal(['error', 'pending']);
            const taskModalInfo = Modal.info({
              title: '任务提示',
              className: 'taskExceptionModal', // 添加自定义的class名
              getContainer: containerRef.current,
              content: data.status === ENUM_TASK_STATUS.D0ING ? '任务正在执行中...' : '任务初始化中',
              closable: false,
              maskClosable: false,
              closeIcon: false,
              okText: '请稍后...',
              okButtonProps: {
                disabled: true,
              },
            });
            taskPendingModalRef.current = {
              modal: taskModalInfo,
              status: data.status,
            };
            return;
          }
        }

        /** 异常 */
        if (data.status === ENUM_TASK_STATUS.EXCEPTION) {
          if (!taskErrModalRef.current) {
            handleDestroyTaskModal(['pending']);
            const taskErrModalInfo = Modal.info({
              title: '任务执行异常提示',
              content: renderException(data),
              okText: '重新执行',
              className: 'taskExceptionModal', // 添加自定义的class名
              getContainer: containerRef.current,
              onOk: () => {
                fetchApi({
                  ...tasksApis.reExecuteTask,
                  params: {
                    taskId: data.id,
                  },
                  onSuccess: () => {
                    showSucNotification('重新执行成功');
                    // Modal.destroyAll();
                    handleDestroyTaskModal(['error']);
                    onStartTask();
                  },
                  onError: err => {
                    console.log('err', err);
                  },
                });
              },
              closable: false,
              maskClosable: false,
              closeIcon: false,
            });
            taskErrModalRef.current = taskErrModalInfo;
          }
        }
      },
    });
  };

  /** 请求过程 */
  const {
    Data: empPayDetail,
    Loading: loading,
    runAction: runActionOfEmpPayDetailList,
  } = useFetch({
    ...salarySlipApis.salaryslipEmpPayList,
    params: {
      processId,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  // 重新计算发薪人员的工资条
  const handleReCalculate = () => {
    const isHasLimitEmp = selectedRows.some(({ isLimitEmp }) => isLimitEmp === '1');
    if (isHasLimitEmp) {
      return message.warning('存在限制人员，不能发放工资条哦~');
    }
    if (selectedRows.length > 100) {
      return message.warning('选择的数据不要超过100条哦～');
    }
    setProvideLoading(true);
    const empPayIdList = selectedRows.map(({ id }) => id);
    fetchApi({
      ...cnbApi.empReCalculate,
      data: {
        processId,
        empIdList: selectedKeys,
        empPayIdList,
      },
      onSuccess: message => {
        if (message) {
          return Modal.info({
            title: '提醒',
            content: <div dangerouslySetInnerHTML={{ __html: message }} />,
          });
        }
        message.success('操作成功！');
        onFetchEmpPayDetailList();
      },
    }).finally(() => setProvideLoading(false));
  };

  // 测试工资条按钮
  const handleCalculateTest = useCallback(() => {
    // if (!salarySetHid) {
    //   return message.warning('请先配置工资套哦～');
    // }
    if (selectedRows.length > 1) {
      return message.warning('只能选择一条数据哦～');
    }
    setActionType(ACTION_TYPES.TEST);
  }, [salarySetHid, selectedRows]);

  // 测试工资条
  const handleActionConfirm = (values: Record<string, any>, type: ACTION_TYPES) => {
    const action = {
      [ACTION_TYPES.TEST]: {
        fetch: salarySlipApis.salaryslipTest,
        data: {
          processId,
          salarySlipIdList: [values.template],
          empPayIdList: selectedRows.map(({ id }) => id),
          empIdList: selectedRows.map(({ empId }) => empId),
        },
      },
    };
    console.log('values', values);
    setTestLoading(true);
    return fetchApi({
      ...action[type].fetch,
      data: action[type].data,
      onSuccess: data => {
        if (data?.msg) {
          return Modal.warning({
            title: '提醒',
            content: data?.msg,
          });
        }
        setActionType(undefined);
        showSucNotification('操作成功');
      },
      onError: err => {
        console.log('err', err);
      },
    }).finally(() => setTestLoading(false));
  };

  const actionBtnItems: any[] = useMemo(() => {
    const list = [
      {
        key: 'adjust',
        type: 'button',
        content: '发放工资条',
        viewCode: 'publish',
        config: {
          type: 'primary',
          loading: provideLoading,
          onClick: handleReCalculate,
          disabled: selectedRows.length === 0,
        },
      },
      {
        key: 'test',
        type: 'button',
        content: '测试工资条发放',
        viewCode: 'test',
        config: {
          type: 'primary',
          onClick: handleCalculateTest,
          disabled: selectedRows.length === 0,
        },
      },
    ].filter(({ viewCode }) => csbViews.includes(viewCode));

    return list;
  }, [selectedKeys, selectedRows, provideLoading, salarySetHid, csbViews]);

  /** 请求过程 */
  const onFetchEmpPayDetailList = (data: Record<string, any> = {}) => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    runActionOfEmpPayDetailList({
      params: {
        processId,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...formData,
        ...data,
      },
    });
  };

  const { list: empPayDetailList = [], pagination: empPayDetailPagination = {} } = defaultObj(empPayDetail);

  return (
    <Spin spinning={loading}>
      <div className="cnbEmpPayContainer" ref={containerRef}>
        <WULIWholeTable
          pagination
          canSelect
          multiSelect
          rowKey="empId"
          columns={columns}
          data={empPayDetailList}
          paginationConfig={{
            size: 'small',
            current: empPayDetailPagination.pageNum,
            pageSize: empPayDetailPagination.pageSize || DEFAULT_PAGE_SIZE,
            total: empPayDetailPagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (page, pageSize) => {
              onFetchEmpPayDetailList({
                pageNum: page,
                pageSize,
              });
            },
          }}
          action={actionBtnItems}
          filter={{
            formKey: filterFormKey,
            defaultLayout: {
              col: 6,
            },
            className: 'mb-20',
            filters: filterFormItems,
            onSearch: () => onFetchEmpPayDetailList(),
            onClear: () => {
              WULIFormActions.get(filterFormKey)?.reset();
              onFetchEmpPayDetailList();
            },
          }}
          onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => {
            setSelectedKeys(selectKeys);
            setSelectedRows(_selectedRows);
          }}
          selectedRows={selectedRows}
          showSelectedDetail
          rowNameKey="otherName"
          afterCancelSelect={newRows => {
            console.log('newRows=========', newRows);
            setSelectedRows(newRows);
            setSelectedKeys(newRows.map(k => k.empId));
          }}
        />
      </div>
      <PayrollActionModal
        type={actionType}
        open={!!actionType}
        confirmLoading={testLoading}
        selectedRow={{ salarySetHid }}
        onConfirm={handleActionConfirm}
        onCancel={() => setActionType(undefined)}
      />
    </Spin>
  );
};

export default CnbEmpPay;
