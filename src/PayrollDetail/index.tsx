import React, { useEffect, useMemo, useState } from 'react';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { Button, Row, Col, Spin } from 'antd';
import { WULIForm } from '@cvte/wuli-antd';
import dayjs from 'dayjs';

import cnbApis from '@apis/cnb';
import { request as fetchApi } from '@utils/http';

import Block from '@components/Block';

import './style.less';

const PAYROLL_DETAIL_INFO = 'PAYROLL_DETAIL_INFO';
const PAYROLL_DETAIL_BASE_INFO = 'PAYROLL_DETAIL_BASE_INFO';

const formLayout = {
  col: 8,
  labelCol: 6,
  wrapperCol: 16,
};

export interface IAppProps {
  id?: string;
}
const PayrollDetail: React.FC<IAppProps> = props => {
  const { id } = props;

  const [loading, setLoading] = useState<boolean>(false);
  const [groupEmpDetail, setGroupEmpDetail] = useState<Record<string, any>>({});

  const baseFormKeys: IFormItem[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'empOtherName',
        label: '别名',
        // wuliMode: 'edit',
        // configs: {
        //   placeholder: '请选择',
        // },
      },
      {
        type: 'input',
        key: 'empCode',
        label: '工号',
      },
      {
        type: 'input',
        key: 'empDeptFullPathName',
        label: '部门',
      },
      {
        type: 'input',
        key: 'empTypeName',
        label: '用工关系类型',
      },
      {
        type: 'input',
        key: 'empStatusName',
        label: '任职状态',
      },
      {
        type: 'input',
        key: 'empModeName',
        label: '任职方式',
      },
    ],
    []
  );

  const payrollGroupFormKeys: IFormItem[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'groupName',
        label: '薪资组',
        // wuliMode: 'edit',
        // configs: {
        //   placeholder: '请选择',
        // },
      },
      {
        type: 'input',
        key: 'payUnitName',
        label: '发薪单位',
        // wuliMode: 'edit',
        // configs: {
        //   placeholder: '请选择',
        // },
      },
      {
        type: 'input',
        key: 'beginDate',
        label: '开始时间',
        // wuliMode: 'edit',
        // configs: {
        //   placeholder: '请选择',
        // },
      },
    ],
    []
  );

  const onFetchGroupEmpDetail = () => {
    setLoading(true);
    fetchApi({
      ...cnbApis.groupEmpDetail,
      params: {
        id,
      },
      onSuccess: res => {
        const result = {};
        [
          'empOtherName',
          'empCode',
          'empDeptFullPathName',
          'empTypeName',
          'empStatusName',
          'empModeName',
          'groupName',
          'payUnitName',
          'beginDate',
        ].forEach(key => {
          const val = (res || {})[key];
          result[key] = key === 'beginDate' && key ? dayjs(val).format('YYYY-MM-DD') : val || '-';
        });
        setGroupEmpDetail(result);
        setLoading(false);
      },
      onError: err => {
        setLoading(false);
        console.log('获取数据失败===', err);
      },
    });
  };
  useEffect(() => {
    onFetchGroupEmpDetail();
  }, []);

  return (
    <div className="payrollDetailContainer ">
      <Spin spinning={loading}>
        {/* <Row>
          <Col>
            <Button type="primary">保存</Button>
          </Col>
        </Row> */}
        <Row>
          <Col span={24}>
            <Block title="基本信息">
              <WULIForm
                wuliMode="view"
                formItems={baseFormKeys}
                defaultLayout={formLayout}
                initialData={groupEmpDetail}
                formKey={PAYROLL_DETAIL_BASE_INFO}
                className="groupEmpFormContainer"
              />
            </Block>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Block title="薪资组信息">
              <WULIForm
                wuliMode="view"
                defaultLayout={formLayout}
                initialData={groupEmpDetail}
                formKey={PAYROLL_DETAIL_INFO}
                formItems={payrollGroupFormKeys}
                className="groupEmpFormContainer"
              />
            </Block>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default PayrollDetail;
