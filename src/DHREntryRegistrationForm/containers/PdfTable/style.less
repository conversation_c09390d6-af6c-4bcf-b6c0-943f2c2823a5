.dhr-entry-registration-form-pdf-wrap {
  display: none;
  .dhr-entry-registration-form-pdf-content {
    padding-left: 10pt;
    padding-right: 10pt;
    .dhr-entry-title > .ant-row .ant-form-item-control-input-content > div > h2 {
      font-size: 9pt;
      font-weight: bold;
      line-height: 20pt;
      height: 20pt;
    }
    .form-generator-main .cir-form-generator .dhr-entry-sub-title-wrap .ant-form-item-label .wuli-render-label,
    .ant-form .dhr-entry-sub-title-wrap .ant-form-item-control-input-content {
      font-size: 7pt;
      line-height: 20pt;
    }

    .dhr-entry-base-info-wrap > .ant-form-item > .ant-row .ant-form-item-label,
    .dhr-entry-base-info-wrap > .ant-form-item > .ant-row .ant-form-item-control,
    .dhr-entry-medical-history-wrap > .ant-form-item > .ant-row .ant-form-item-label,
    .dhr-entry-medical-history-wrap > .ant-form-item > .ant-row .ant-form-item-control,
    .dhr-entry-experience-wrap > .ant-form-item > .ant-row .ant-form-item-label,
    .dhr-entry-experience-wrap > .ant-form-item > .ant-row .ant-form-item-control {
      height: 18pt;
    }

    .form-generator-main .cir-form-generator .dhr-entry-base-info-wrap .ant-form-item-label .wuli-render-label {
      font-weight: 700;
    }

    .form-generator-main .cir-form-generator .dhr-entry-base-info-wrap .ant-form-item-label .wuli-render-label,
    .form-generator-main .cir-form-generator .dhr-entry-medical-history-wrap .ant-form-item-label .wuli-render-label,
    .form-generator-main .cir-form-generator .dhr-entry-experience-wrap .ant-form-item-label .wuli-render-label,
    .dhr-entry-statement-wrap
      .ant-form-item-control
      > .ant-form-item-control-input
      > .ant-form-item-control-input-content,
    .dhr-entry-base-info-wrap
      > .ant-form-item
      > .ant-row
      .ant-form-item-control
      > .ant-form-item-control-input
      > .ant-form-item-control-input-content,
    .dhr-entry-experience-wrap
      > .ant-form-item
      > .ant-row
      .ant-form-item-control
      > .ant-form-item-control-input
      > .ant-form-item-control-input-content
      .dhr-entry-base-info-wrap
      > .ant-form-item
      > .ant-row
      .ant-form-item-control
      > .ant-form-item-control-input
      > .ant-form-item-control-input-content,
    .dhr-entry-medical-history-wrap
      > .ant-form-item
      > .ant-row
      .ant-form-item-control
      > .ant-form-item-control-input
      > .ant-form-item-control-input-content,
    .dhr-entry-base-info-wrap
      > .ant-form-item
      > .ant-row
      .ant-form-item-control
      > .ant-form-item-control-input
      > .ant-form-item-control-input-content,
    .dhr-entry-table-avatar-wrap .dhr-entry-table-avatar-content,
    .dhr-entry-view-table-wrap
      .wuli-ag-grid
      .ag-header
      .ag-header-viewport
      .ag-header-row
      .ag-header-cell
      .ag-header-cell-comp-wrapper
      .form-table-header-filter-display,
    .dhr-entry-view-table-wrap .wuli-ag-grid .ag-body-viewport .ag-row > .ag-cell .ag-cell-value {
      font-size: 7pt;
      line-height: 1.5;
    }

    .dhr-entry-base-info-avatar .dhr-entry-table-avatar-wrap {
      height: 144pt;
    }

    .dhr-entry-view-table-wrap .wuli-ag-grid .ag-body-viewport > .ag-center-cols-clipper,
    .dhr-entry-view-table-wrap .wuli-ag-grid .ag-body-viewport > .ag-center-cols-clipper .ag-center-cols-container,
    .dhr-entry-view-table-wrap .wuli-ag-grid .ag-body-viewport > .ag-center-cols-clipper .ag-center-cols-container,
    .dhr-entry-view-table-wrap .wuli-ag-grid .ag-body-viewport .ag-row,
    .dhr-entry-view-table-wrap .ag-root .ag-overlay .ag-overlay-panel > .ag-overlay-wrapper {
      min-height: 18pt !important;
    }

    .dhr-entry-view-table-wrap .ag-root {
      & > .ag-header {
        min-height: 18pt !important;
        height: initial !important;
        overflow: initial !important;
        & > .ag-header-viewport {
          overflow: initial !important;
          .ag-header-row {
            min-height: 18pt !important;
            height: initial !important;
            & > .ag-grid-custom-col-header.ag-header-cell {
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}
