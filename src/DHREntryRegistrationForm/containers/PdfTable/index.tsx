import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useRef } from 'react';
import html2pdf from 'html2pdf.js';

import { LCPDetailTemplate } from '@components/LcpTemplate';

import './style.less';

export interface IAppProps {
  ref?: any;
  cofigs: Record<string, any>;
}
// eslint-disable-next-line react/display-name
const PdfTable: React.FC<IAppProps> = forwardRef(({ cofigs }, ref) => {
  const domRef = useRef(null);

  useImperativeHandle(ref, () => ({
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    getPdfFile: handlePdfFile,
    getHtml: () => domRef.current?.outerHTML,
  }));

  const pdfOpt = useMemo(
    () => ({
      margin: 0.5,
      filename: '入职登记表.pdf',
      image: { type: 'jpeg', quality: 0.98 },
      pagebreak: { mode: 'avoid-all' },
      html2canvas: {
        scale: 2, // 提高缩放比例以获得更清晰的渲染
        useCORS: true, // 支持跨域图片
        logging: false, // 关闭日志
        letterRendering: true, // 更好的文字渲染
      },
      jsPDF: {
        unit: 'pt',
        format: 'A4',
        orientation: 'portrait',
        compress: true, // 压缩输出
      },
    }),
    []
  );

  const handlePdfFile = useCallback(async () => {
    if (!domRef.current) {
      return;
    }
    // html2pdf().set(pdfOpt).from(domRef.current).save();
    const worker = html2pdf().set(pdfOpt).from(domRef.current);
    // 生成 PDF 并转换为 Blob 对象
    const pdfBlob = await worker.outputPdf('blob');
    console.log(pdfBlob); // 打印Blob对象
    // 将 Blob 转换为 File 对象 (可选)
    const pdfFile = new File([pdfBlob], '入职登记表.pdf', { type: 'application/pdf' });
    return pdfFile;
  }, []);

  return (
    <div className="dhr-entry-registration-form-pdf-wrap">
      <div ref={domRef}>
        <div className="dhr-entry-registration-form-pdf-content">
          <LCPDetailTemplate {...cofigs} />
          <div
            className="dhr-entry-registration-form-pdf-signature"
            style={{ textAlign: 'right', marginTop: 7, fontSize: '7pt' }}
          >
            <span style={{ marginRight: 10 }}>申请人签名：________________</span>
            <span>日期：________________</span>
          </div>
        </div>
      </div>
    </div>
  );
});

export default PdfTable;
