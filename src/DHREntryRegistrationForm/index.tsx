import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import Message from '@cvte/cir-message';
import { Button, Skeleton } from 'antd';
import dayjs from 'dayjs';
import * as R from 'ramda';

import entryApis from '@apis/entry';
import commonApis from '@apis/common';
import { request as fetchApi } from '@utils/http';

import { LCPDetailTemplate } from '@components/LcpTemplate';
import useAttachment, { ApiConfigParams } from '@hooks/useAttachment';
import { showSucNotification, showWarnNotification } from '@utils/tools';

// import PdfTable from './containers/PdfTable';

import './style.less';

const eventPrefix = 'dhrEntryTableClose';

// const EXCLUDED_FIELDS = new Set(['ID', 'id', 'C_WEIGHT', '__add', '__index', '__selectedOptions', 'C_TEL_RULE']);
const TABLE_NAMES = [
  /** 教育 */
  'TB_STA_ENTRY_INFO_DEGREE',
  /** 职称证书 */
  'TB_STA_ENTRY_INFO_TECH_TITLE_QUAL',
  /** 职业资格证书 */
  'TB_STA_ENTRY_INFO_QUAL_LEVEL',
  /** 语言证书 */
  'TB_STA_ENTRY_INFO_LANGUAGE_CERTIFICATE',
  /** 社会履历 */
  'TB_STA_ENTRY_INFO_SOCIAL_RESUME',
  /** 过往经历 */
  'TB_STA_ENTRY_INFO_EXPERIENCE',
  /** 家庭成员 */
  'TB_STA_ENTRY_INFO_SOCIAL_RELATION',
  /** 社会关系 */
  'TB_STA_ENTRY_INFO_SOCIAL_FRIENDS',
  /** 紧急联系人 */
  'TB_STA_ENTRY_INFO_EMERGENCY_CONTACT',
  /** 证件信息 */
  'TB_STA_ENTRY_INFO_PASSPORT',
] as const;

const TABLE_FIELDS = new Set(TABLE_NAMES);

// types.ts
interface TableItem {
  ID?: string | number;
  id?: string | number;
  C_WEIGHT?: any;
  __add?: boolean;
  __index?: number;
  __selectedOptions?: any;
  [key: string]: any;
}

export interface IAppProps {
  formData: Record<string, any>;
  apiConfigMap: ApiConfigParams;
  configs: {
    utils: {
      cache: {
        getCache: () => string[];
      };
    };
    context: {
      config: {
        id: string;
        classId: string;
      };
      globalContext: {
        pageTools: {
          refresh: (pageId: string) => void;
        };
      };
      setFormData: (values) => void;
      getFormData: () => Record<string, any>;
    };
  };
  submitData: {
    approveCode: string;
    approveName: string;
  };
  submitName?: string;
}
const DHREntryRegistrationForm: React.FC<IAppProps> = forwardRef((props, ref) => {
  const entryTableRef = useRef(null);
  const { formData, apiConfigMap, configs, submitName = '确认无误，提交' } = props;
  const submitData = props.submitData || {};
  /** 当前节点 */
  const currentNode = formData['CURRENT_NODE'] || '';
  const classId = configs?.context?.config?.classId || '';
  const downloadPrefix = apiConfigMap?.download?.url || '';
  const formInstanceId = configs?.context?.config?.id || '';
  const entryProcessId = formData['C_ENTRY_PROCESS_ID'] || '';
  const { onUpdateApiCongiMap, onUploadAttachment } = useAttachment({ manual: true });

  const errorDataRef = useRef<boolean>(false);
  const saveLoadingRef = useRef<boolean>(false);
  const [pageLoading, setPageLoading] = useState<boolean>(true);
  const [saveLoading, setSaveLoading] = useState<boolean>(false);
  const [entryProcessInfo, setEntryProcessInfo] = useState<Record<string, any> | null>(null);

  useImperativeHandle(ref, () => ({}));

  /** 获取入职报道信息 */
  const onFetchEntryProcessInfo = useCallback(processId => {
    fetchApi({
      ...entryApis.entryProcessInfo,
      params: {
        entryProcessId: processId,
      },
    })
      .then(res => {
        setEntryProcessInfo(res);
      })
      .catch(() => {
        errorDataRef.current = true;
        setPageLoading(false);
      });
  }, []);

  useEffect(() => {
    entryProcessId && onFetchEntryProcessInfo(entryProcessId);
  }, [entryProcessId]);

  useEffect(() => {
    apiConfigMap && onUpdateApiCongiMap(apiConfigMap);
  }, [apiConfigMap]);

  /** 表单默认数据 */
  const formInitValues = useMemo(() => {
    if (!formData || !entryProcessInfo || Object.keys(formData).length === 0) {
      return undefined;
    }
    const eduList = formData['TB_STA_ENTRY_INFO_DEGREE'] || [];
    const [experienceInfo] = formData['TB_STA_ENTRY_INFO_EXPERIENCE'] || [];
    const {
      C_CRIME,
      C_LABOR_DISPUTE,
      C_COMPETITION_AGREEMENT,
      C_COMPETE_END_DATE,
      C_LABOR_CONTACT,
      C_CONTRACT_END_DATE,
      C_SOCAIL_INSURANCE,
      C_COMPANY_NAME,
    } = experienceInfo || {};
    const highestData = eduList.find(({ C_IS_HIGHEST_EDU }) => C_IS_HIGHEST_EDU === '1');
    const initValues = {
      ...formData,
      C_CRIME,
      C_LABOR_DISPUTE,
      C_COMPETITION_AGREEMENT,
      C_COMPETE_END_DATE,
      C_LABOR_CONTACT,
      C_CONTRACT_END_DATE,
      C_SOCAIL_INSURANCE,
      C_COMPANY_NAME,
      // 入职公司
      JOINING_THE_COMPANY: entryProcessInfo?.laborName || '',
      // 入职部门
      DEPARTMENT_OF_EMPLOYMENT: entryProcessInfo?.deptName || '',
      // 入职岗位
      ENTRY_POSITION: entryProcessInfo?.positionName || '',
      // 入职时间
      C_BEGIN_DATE: formData.C_BEGIN_DATE || entryProcessInfo?.entryDate,
      // 日期
      CODE_62AA92B0A2F613C2: dayjs().startOf('day').valueOf(),
      // 最高学历
      C_HIGHEST_EDUCATION: highestData?.C_EDUCATION,
      /** 渲染头像 */
      C_ENTRY_AVATAR_BLOCK:
        !!formData['C_PERSONAL_PHOTO'] && !!downloadPrefix
          ? `${downloadPrefix}/${formData['C_PERSONAL_PHOTO']}`
          : formData['C_PERSONAL_PHOTO'],
    };
    return initValues;
  }, [downloadPrefix, formData, entryProcessInfo]);

  const formInitValuesStr = JSON.stringify(formInitValues);
  const formTemplateConfig = useMemo(() => {
    if (!formInitValues) {
      return;
    }
    return {
      appId: 'efa37869ee1c4930b434a4c7b1548d46',
      classId: 'f14ed1d12a76477f92a2329276d91593',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        contextData: {
          viewType: 'read',
        },
        formDefaultData: formInitValues,
      },
    };
  }, [formInitValuesStr]);

  const handleFormatTableList = list => {
    if (list.length === 0) {
      return [];
    }
    return list.map(listItem => ({
      dataList: Object.keys(listItem).map(key => ({
        apiName: key,
        attrValue: listItem[key],
      })),
    }));
  };

  const formatBizDataMap = useCallback((data: Record<string, TableItem[]>) => {
    return TABLE_NAMES.map(tableName => ({
      tableName: tableName,
      rowList: handleFormatTableList(data[tableName] || []),
    }));
  }, []);

  const handleAssembleFormValues = useCallback(values => {
    if (!values) return { objAttrItemList: [], tableList: [] };
    const objAttrItemList = [];
    for (const key in values) {
      if (!TABLE_FIELDS.has(key) && !R.isNil(values[key])) {
        objAttrItemList.push({ apiName: key, attrValue: values[key] });
      }
    }
    return {
      objAttrItemList,
      tableList: formatBizDataMap(values),
    };
  }, []);

  const handleFormSave = currentData =>
    new Promise(resolve => {
      const updateFormValues = handleAssembleFormValues(currentData);
      fetchApi({
        ...commonApis.formDataUpdateApp,
        data: {
          ...updateFormValues,
          id: formInstanceId,
          formClassId: classId,
          classId: classId,
        },
        params: {
          formClassId: classId,
        },
        headers: {
          'x-app-id': 'efa37869ee1c4930b434a4c7b1548d46',
        },
        onSuccess: res => {
          return resolve(true);
        },
        onError: () => {
          saveLoadingRef.current = false;
          setSaveLoading(false);
          return resolve(false);
        },
      });
    });

  /** 流程提交 */
  const handleFlowSubmit = useCallback(
    () =>
      new Promise((resolve, reject) => {
        fetchApi({
          ...commonApis.flowAuditSubmit,
          data: {
            classId,
            formInstanceId,
            comment: '同意',
            bizId: classId,
            nodeCode: currentNode,
            ...submitData,
          },
          headers: {
            'x-app-id': 'efa37869ee1c4930b434a4c7b1548d46',
          },
          onSuccess: res => {
            if (res?.success) {
              resolve(true);
            }
            resolve(false);
          },
          onError: reject,
        }).finally(() => {
          saveLoadingRef.current = false;
          setSaveLoading(false);
        });
      }),
    [classId, formInstanceId, currentNode, submitData]
  );

  /** 发送关闭通知 */
  const handleCloseDraw = useCallback(() => {
    Message.emit(eventPrefix, {
      data: {
        data: {},
        response: { success: true },
      },
    });
  }, []);

  // // 工具函数，将 px 转换为 pt
  // const pxToPt = px => px / 1.333;

  // // 示例转换函数，将所有元素的 px 单位转换为 pt
  // const convertPxToPt = html => {
  //   return html.replace(/(\d+(\.\d+)?)px/g, (_, value) => {
  //     return `${pxToPt(parseFloat(value))}pt`;
  //   });
  // };

  // 工具函数：生成带内联样式的 HTML 字符串
  const getStyledHtml = element => {
    // 1. 深度克隆节点
    const clone = element.cloneNode(true);
    const fragment = document.createDocumentFragment();
    fragment.appendChild(clone);
    // 使用迭代代替递归
    const nodeStack = [{ node: clone, original: element }];

    // 选择常用样式
    const commonStyles = [
      'color',
      'flex',
      'top',
      'left',
      'width',
      'right',
      'height',
      'margin',
      'bottom',
      'display',
      'position',
      'overflow',
      'flex-flow',
      'box-sizing',
      'max-width',
      'font-size',
      'flex-wrap',
      'max-height',
      'min-width',
      'min-height',
      'text-align',
      'line-height',
      'font-weight',
      'align-items',
      'white-space',
      'padding-top',
      'padding-left',
      'padding-right',
      'text-overflow',
      'flex-direction',
      'padding-bottom',
      'justify-content',
      'border-top-color',
      'border-top-style',
      'border-top-width',
      'border-left-color',
      'border-left-style',
      'border-left-width',
      'border-right-color',
      'border-right-style',
      'border-right-width',
      'border-bottom-color',
      'border-bottom-style',
      'border-bottom-width',
    ];

    let inlineStyles = '';
    while (nodeStack.length > 0) {
      const { node, original } = nodeStack.pop();

      if (node.nodeType !== Node.ELEMENT_NODE) continue;

      // 批量处理样式
      const computed = window.getComputedStyle(original);
      // 使用白名单过滤样式
      for (const styleKey of commonStyles) {
        const value = computed.getPropertyValue(styleKey);
        if (['width', 'height'].includes(styleKey) && parseFloat(value) > 900) {
          continue;
        }
        if (value) {
          inlineStyles += `${styleKey}:${value};`;
        }
      }

      // 使用 cssText 批量设置样式
      if (inlineStyles) {
        node.style.cssText = inlineStyles;
      }

      inlineStyles = '';

      // 反向添加子节点到堆栈（深度优先）
      if (node.children.length > 0) {
        const children = Array.from(node.children);
        const originalChildren = Array.from(original.children);
        for (let i = children.length - 1; i >= 0; i--) {
          nodeStack.push({
            node: children[i],
            original: originalChildren[i],
          });
        }
      }
    }

    inlineStyles = null;
    // 5. 返回带样式的 HTML 字符串
    return clone.outerHTML;
  };

  const handleHtmlPdf = node =>
    new Promise((resolve, reject) => {
      // 获取带样式的 HTML
      const styledHtml = getStyledHtml(node);
      /** 塞入签名域 */
      const footerSignatureNode = `<div class="dhr-entry-registration-form-pdf-signature" style="color: rgba(0, 0, 0, 0.85); flex: 0 1 auto; inset: auto; width: 639.1597899474868pt; height: 16.67411852963241pt; margin: 15.003750937734434pt 0pt 0pt; display: block; position: static; overflow: visible; box-sizing: border-box; max-width: none; font-size: 10.002475618904725pt; flex-flow: row; max-height: none; min-width: 0pt; min-height: 0pt; text-align: right; line-height: 16.670892723180796pt; font-weight: 400; align-items: normal; white-space: normal; padding: 0pt; text-overflow: clip; justify-content: normal; border-color: rgba(0, 0, 0, 0.85); border-style: none; border-width: 0pt;"><span style="color: rgba(0, 0, 0, 0.85); flex: 0 1 auto; inset: auto; width: auto; height: auto; margin: 0pt 7.501875468867217pt 0pt 0pt; display: inline; position: static; overflow: visible; box-sizing: border-box; max-width: none; font-size: 10.002475618904725pt; flex-flow: row; max-height: none; min-width: 0pt; min-height: 0pt; text-align: right; line-height: 16.670892723180796pt; font-weight: 400; align-items: normal; white-space: normal; padding: 0pt; text-overflow: clip; justify-content: normal; border-color: rgba(0, 0, 0, 0.85); border-style: none; border-width: 0pt;">申请人签名：________________</span><span style="color: rgba(0, 0, 0, 0.85); flex: 0 1 auto; inset: auto; width: auto; height: auto; margin: 0pt; display: inline; position: static; overflow: visible; box-sizing: border-box; max-width: none; font-size: 10.002475618904725pt; flex-flow: row; max-height: none; min-width: 0pt; min-height: 0pt; text-align: right; line-height: 16.670892723180796pt; font-weight: 400; align-items: normal; white-space: normal; padding: 0pt; text-overflow: clip; justify-content: normal; border-color: rgba(0, 0, 0, 0.85); border-style: none; border-width: 0pt;">日期：________________</span></div>`;
      const lastDivIndex = styledHtml.lastIndexOf('</div>');
      const updatedHtmlString = [
        styledHtml.slice(0, lastDivIndex), // 原始内容到最后一个 </div> 之前
        footerSignatureNode, // 新插入的内容
        styledHtml.slice(lastDivIndex), // 最后一个 </div> 及其后续内容
      ].join('');
      fetchApi({
        ...entryApis.htmlPdf,
        data: {
          html: updatedHtmlString,
        },
        onSuccess: res => {
          const uint8Array = new Uint8Array(res.data);
          const blob = new Blob([uint8Array], { type: 'application/pdf' });
          const file = new File([blob], `入职登记表.pdf`, {
            type: 'application/pdf',
          });
          resolve(file);
        },
        onError: reject,
      });
    });

  const handleOk = async () => {
    if (!entryTableRef.current) {
      return;
    }
    saveLoadingRef.current = true;
    setSaveLoading(true);
    try {
      const file = await handleHtmlPdf(entryTableRef.current);
      const fileData = await onUploadAttachment(file, { configs });
      const fileId = fileData?.fileIds?.[0] || '';
      if (!fileId) {
        saveLoadingRef.current = false;
        setSaveLoading(false);
        return showWarnNotification('入职登记表更新失败，请稍后重试');
      }
      /** 使用最新的formValues */
      const currentFormValues = configs?.context?.getFormData() || formData || {};
      /** 更新入职登记表字段 */
      const updateData = {
        ...currentFormValues,
        C_ENTRY_REGISTRATION_FORM: [fileId],
      };
      const isSuccess = await handleFormSave(updateData);
      if (isSuccess) {
        // 更新流程
        handleFlowSubmit().then(isPass => {
          if (isPass) {
            showSucNotification('操作成功');
            handleCloseDraw();
            // 刷新页面
            configs.context?.globalContext?.pageTools?.refresh?.(formInstanceId);
          }
        });
      } else {
        saveLoadingRef.current = false;
        setSaveLoading(false);
      }
    } catch (error) {
      console.log('入职登记表更新失败:', error);
      saveLoadingRef.current = false;
      setSaveLoading(false);
    }
  };

  /** 是否起草节点 */
  const isDraftNode = useMemo(() => currentNode === 'n_sys_draft', [currentNode]);
  return (
    <div className="dhr-entry-registration-form-wrap">
      {pageLoading && <Skeleton active paragraph={{ rows: 20 }} />}
      {formTemplateConfig && (
        <div ref={entryTableRef} style={{ width: 852 }}>
          <LCPDetailTemplate {...formTemplateConfig} onInitData={() => setPageLoading(false)} />
        </div>
      )}
      {!pageLoading && (
        <div className="dhr-entry-registration-form-footer-wrap">
          <div className="dhr-entry-registration-form-footer-content">
            {
              <Button
                size="middle"
                type="primary"
                onClick={handleOk}
                danger={isDraftNode}
                loading={saveLoading}
                disabled={saveLoadingRef.current || errorDataRef.current}
              >
                {submitName}
              </Button>
            }
            {isDraftNode && <div className="dhr-entry-registration-form-footer-tip">注意：提交后无法修改</div>}
          </div>
          <a className="dhr-entry-registration-form-back-edit-btn" onClick={handleCloseDraw}>
            返回修改
          </a>
        </div>
      )}
    </div>
  );
});

export default DHREntryRegistrationForm;
