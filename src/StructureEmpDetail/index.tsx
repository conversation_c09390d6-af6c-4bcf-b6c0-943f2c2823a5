import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Spin, Row, Col, Table } from 'antd';

import { request as fetchApi } from '@utils/http';
import cnbApis from '@apis/cnb';

import { toDateFormat } from '@utils/tools';

import Block from '@components/Block';
import { WULIForm } from '@cvte/wuli-antd';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

import './style.less';

const formLayout = {
  col: 8,
  labelCol: 6,
  wrapperCol: 16,
};

const baseFormKey = 'STRUCTURE_DETAIL_BASE_INFO';

export interface IAppProps {
  pageId: string;
}
const StructureEmpDetail: React.FC<IAppProps> = props => {
  const { pageId } = props;
  const [baseInfo, setBaseInfo] = useState({});
  const [tableList, setTableList] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);

  const baseFields: IFormItem[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'empOtherName',
        label: '别名',
        // wuliMode: 'edit',
        // configs: {
        //   placeholder: '请选择',
        // },
      },
      {
        type: 'input',
        key: 'empCode',
        label: '工号',
      },
      {
        type: 'input',
        key: 'empDeptFullPathName',
        label: '部门',
      },
      {
        type: 'input',
        key: 'empTypeName',
        label: '用工关系类型',
      },
      {
        type: 'input',
        key: 'empStatusName',
        label: '任职状态',
      },
      {
        type: 'input',
        key: 'empModeName',
        label: '任职方式',
      },
      {
        type: 'input',
        key: 'structureName',
        label: '薪资结构类型',
      },
      {
        type: 'input',
        key: 'groupName',
        label: '关联薪资组',
      },
    ],
    []
  );

  const columns = useMemo(
    () => [
      {
        title: '薪资项目',
        key: 'itemName',
        dataIndex: 'itemName',
      },
      {
        title: '值',
        key: 'value',
        dataIndex: 'value',
      },
      {
        title: '生效时间',
        key: 'effectiveDate',
        dataIndex: 'effectiveDate',
        render: text => toDateFormat(text) || '-',
      },
    ],
    []
  );

  const onFetchStructureDetail = useCallback(() => {
    setLoading(true);
    fetchApi({
      ...cnbApis.structureEmpDetail,
      params: {
        id: pageId,
      },
      onSuccess: res => {
        const employeeItems = res.employeeItems || [];
        setTableList(employeeItems);
        setBaseInfo(res || {});
        setLoading(false);
      },
      onError: () => {
        setLoading(false);
      },
    });
  }, [pageId]);

  useEffect(() => {
    pageId && onFetchStructureDetail();
  }, [pageId]);

  return (
    <div className="structureEmpDetailContainer">
      <Spin spinning={loading}>
        <Row>
          <Col span={24}>
            <Block title="基本信息">
              <WULIForm
                wuliMode="view"
                initialData={baseInfo}
                formKey={baseFormKey}
                formItems={baseFields}
                defaultLayout={formLayout}
                className="structureDetailFormContainer"
              />
            </Block>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Block title="人员固定薪资结构信息">
              <Table columns={columns} pagination={false} dataSource={tableList} />
            </Block>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default StructureEmpDetail;
