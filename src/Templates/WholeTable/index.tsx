import React, { useMemo } from 'react';
import { Spin } from 'antd';

import useFetch from '@hooks/useFetch';
import useDictCode from '@hooks/useDictCode';
import salarySlipApis from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';
import { defaultObj, showSucNotification, toDateFormat } from '@utils/tools';

import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

import { DICT_CODE_MAP_ID } from '@constants/common';

import { IOriginalProps } from '../types/index';

const filterFormKey = 'tempFormKey';

export interface IAppProps extends IOriginalProps {
  channel: string;
  processId: string;
}

const BaseInfo: React.FC<IAppProps> = ({ processId, channel }) => {
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);

  const { DHR_NOTIFICATION_STATUS = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_NOTIFICATION_STATUS]);

  /** 请求过程 */
  const {
    Data: payroll,
    Loading: payrollListLoading,
    runAction: runActionFetchNotofyList,
  } = useFetch({
    ...salarySlipApis.salaryslipNotifyList,
    params: {
      channel,
      processId,
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  /** 请求过程 */
  const onFetchNotifyList = (data: Record<string, any> = {}) => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    runActionFetchNotofyList({
      params: {
        channel,
        processId,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...formData,
        ...data,
      },
    });
  };

  const columns = useMemo(
    () => [
      {
        title: '接收人',
        dataIndex: 'receiverCode',
        key: 'receiverCode',
      },
      {
        title: '渠道',
        dataIndex: 'channelTypeName',
        key: 'channelTypeName',
      },
      {
        title: '状态',
        dataIndex: 'statusName',
        key: 'statusName',
      },
      {
        title: '类型',
        dataIndex: 'typeName',
        key: 'typeName',
      },
      {
        title: '重试次数',
        dataIndex: 'sendCount',
        key: 'sendCount',
      },
      {
        title: '计划发送时间',
        dataIndex: 'definedSendTime',
        key: 'definedSendTime',
        render: ({ data: { definedSendTime } }) => toDateFormat(definedSendTime),
      },
      {
        title: '发送时间',
        dataIndex: 'sendTime',
        key: 'sendTime',
        render: ({ data: { sendTime } }) => toDateFormat(sendTime),
      },
      {
        title: '创建时间',
        dataIndex: 'crtTime',
        key: 'crtTime',
        render: ({ data: { crtTime } }) => toDateFormat(crtTime),
      },
    ],
    []
  );

  const filterFormItems: any[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'receiverCode',
        label: '接收人',
        configs: {
          allowClear: true,
          placeholder: '请输入',
        },
        col: 8,
      },
      {
        type: 'select',
        key: 'status',
        label: '状态',
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: (DHR_NOTIFICATION_STATUS || []).map(codeItem => ({
            ...codeItem,
            label: codeItem.name,
            value: codeItem.itemValue,
            key: codeItem.itemValue,
          })),
        },
        col: 8,
      },
    ],
    [DHR_NOTIFICATION_STATUS]
  );

  const actionBtnItems: any[] = useMemo(
    () => [
      {
        key: 'publish',
        content: '重发',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: () => {},
          disabled: selectedRowKeys.length === 0,
        },
      },
    ],
    [selectedRowKeys]
  );

  const { list: payrollList = [], pagination = {} } = defaultObj(payroll);
  return (
    <Spin spinning={payrollListLoading}>
      <div className="CnbPayProcessWrap">
        <WULIWholeTable
          pagination
          canSelect
          multiSelect
          rowKey="id"
          columns={columns}
          data={payrollList}
          paginationConfig={{
            current: pagination.pageNum,
            pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (pageNum, pageSize) => onFetchNotifyList({ pageNum, pageSize }),
          }}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            onSearch: () => onFetchNotifyList(),
            onClear: () => {
              WULIFormActions.get(filterFormKey)?.reset();
              onFetchNotifyList();
            },
            defaultLayout: {
              col: 6,
              labelCol: 6,
              wrapperCol: 18,
            },
          }}
          action={actionBtnItems}
          onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => {
            setSelectedRowKeys(selectKeys);
            setSelectedRows(_selectedRows);
          }}
          selectedRows={selectedRows}
          showSelectedDetail
          rowNameKey="receiverCode"
          afterCancelSelect={newRows => {
            setSelectedRows(newRows);
            setSelectedRowKeys(newRows.map(k => k.id));
          }}
        />
      </div>
    </Spin>
  );
};
export default BaseInfo;
