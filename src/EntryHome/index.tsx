import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import coreHrApis from '@apis/coreHr';
import entryApis from '@apis/entry';
import commonApis from '@apis/common';
import { request as fetchApi } from '@utils/http';

import ActMenu from './containers/ActMenu';
import EntryList from './containers/EntryList';
import InviteModal from './containers/InviteModal';

import { DOWNLOAD_URL_PREFIX } from '@constants/common';
import { ENTRY_DISPLAY_TYPE, ENTRY_ACT_STATUS } from '@constants/entry';

import './style.less';

let timeout = null;
const ENTRY_INVITE_CODE = 'sta_entry_invite';

interface JumpToParams {
  record: Record<string, any>;
  processInfo: Record<string, any>;
}
export interface IAppProps {
  entryProcessId: string;
  onJumpToDetail: (params: JumpToParams) => void;
}
const EntryHome: React.FC<IAppProps> = ({ entryProcessId, onJumpToDetail }) => {
  const isScrollRef = useRef(false);
  const entryListRef = useRef(null);
  const selectActIdRef = useRef(null);
  const inviteLoadingRef = useRef(false);
  const inviteProcessId = useRef<string>('');

  const [inviteOpen, setInviteOpen] = useState<boolean>(false);
  const [inviteLoading, setInviteLoading] = useState<boolean>(false);
  const [invitePageLoading, setInvitePageLoading] = useState<boolean>(false);
  /** 入职邀约信息 */
  const [inviteInfo, setInviteInfo] = useState<Record<string, any>>({});
  const [activities, setActivities] = useState<Record<string, any>[]>([]);
  /** 入职部门地点时间等相关信息 */
  const [entryProcessInfo, setEntryProcessInfo] = useState<Record<string, any> | null>({});

  // 使用useState来管理选中的活动
  const [selectedActivity, setSelectedActivity] = useState<typeof activities[0] | null>(null);

  const handleClearTimeout = useCallback(() => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  }, []);

  useEffect(() => {
    return () => {
      handleClearTimeout();
    };
  }, []);

  // 处理活动点击事件
  const handleActivityClick = (activity: typeof activities[0]) => {
    isScrollRef.current = true;
    const sectionsRef = entryListRef.current.getsectionRefs();
    selectActIdRef.current = activity.id;
    setSelectedActivity(activity);
    sectionsRef[activity.id].getRef().scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' });
    handleClearTimeout();
    timeout = setTimeout(() => {
      isScrollRef.current = false;
    }, 500);
  };

  /**
   * 处理可操作按钮权限
   *
   */
  const handleAssembleEditable = useCallback((list: any[]) => {
    // 处理完的节点order
    const statusOrderMap = new Map();
    // 将所有状态缓存到orderNum为key的Map
    for (const item of list) {
      const { orderNum, status } = item;
      if (statusOrderMap.has(orderNum)) {
        statusOrderMap.get(orderNum).push(status);
      } else {
        statusOrderMap.set(orderNum, [status]);
      }
    }
    // 从大到小排序 -> 最少时间找出最后全部完成的索引
    const sortedKeys = [...statusOrderMap.keys()].sort((a, b) => Number(b) - Number(a));
    // 默认为最小的那个
    // 查找最后一个全部完成的索引
    let maxEditeOrderIndex = sortedKeys[sortedKeys.length - 1];
    for (let i = 0; i < sortedKeys.length; i++) {
      const currentOrder = sortedKeys[i];
      const statusList = statusOrderMap.get(currentOrder);
      // 检查是否所有状态都是完成
      if (statusList.every(status => status === ENTRY_ACT_STATUS.COMPLETE)) {
        maxEditeOrderIndex = i < sortedKeys.length - 1 ? sortedKeys[i + 1] : currentOrder;
        break;
      }
    }
    // 使用判断条件直接返回新数组，避免多余的变量
    return list.map(item => ({
      ...item,
      disabled: item.orderNum > maxEditeOrderIndex,
    }));
  }, []);

  /** 过滤掉不需展示的活动 */
  const filterActivities = useCallback((activities: any[]) => {
    /** 过滤掉入职邀约 */
    const hidenItems = [ENTRY_INVITE_CODE];
    const filterList = (activities || []).filter(
      activity => activity.displayType !== ENTRY_DISPLAY_TYPE.NOT_SHOW && !hidenItems.includes(activity.actItemCode)
    );
    const list = handleAssembleEditable(filterList);
    const sortList = list.sort((a, b) => a.displayOrder - b.displayOrder);
    return sortList;
  }, []);

  /** 获取form表单参数信息 */
  const dhrGetFormParamsMap = useMemo(
    () => ({
      inviteInfo: {
        onlyMain: true,
        keyType: 'CAMEL',
        appId: 'efa37869ee1c4930b434a4c7b1548d46',
        formClassId: 'df6203787fef447099634f39a0072444',
      },
      inviteTemplate: {
        onlyMain: true,
        keyType: 'CAMEL',
        appId: 'efa37869ee1c4930b434a4c7b1548d46',
        formClassId: '473c5914519f413fbcd02f20e8fc61a5',
      },
    }),
    []
  );

  const onFetchFormData = useCallback(params => {
    return fetchApi({
      ...commonApis.formDetailInfo,
      params,
      headers: {
        'x-app-id': params.appId,
      },
    });
  }, []);

  /** 获取入职邀约模板 */
  const onFetchEntryInviteData = useCallback((record: Record<string, any>) => {
    setInviteOpen(true);
    setInvitePageLoading(true);
    inviteProcessId.current = record.id;
    onFetchFormData({
      id: record.instanceId,
      ...dhrGetFormParamsMap.inviteInfo,
    })
      .then(res => {
        const templateId = res?.mainData?.cWelcomeTemplateId;
        onFetchFormData({
          id: templateId,
          ...dhrGetFormParamsMap.inviteTemplate,
        })
          .then(res => {
            const newInviteInfo = res?.mainData || {};
            setInviteInfo(newInviteInfo);
          })
          .finally(() => setInvitePageLoading(false));
      })
      .catch(() => {
        setInvitePageLoading(false);
      });
  }, []);

  /** 获取活动列表 */
  const onFetchActList = useCallback(bizId => {
    fetchApi({
      ...coreHrApis.dhrActList,
      params: {
        bizId, // 业务id
        classId: 'd0dc99c608494b05b5471e71c92b0c48', // 活动classId
      },
    }).then(_res => {
      const itemViews = _res.itemViews || [];
      console.log('_res_res', itemViews);
      const entryInviteDetail = itemViews.find(({ actItemCode }) => actItemCode === ENTRY_INVITE_CODE);
      entryInviteDetail?.status === ENTRY_ACT_STATUS.PROCESSING && onFetchEntryInviteData(entryInviteDetail);
      setActivities(filterActivities(itemViews));
    });
  }, []);

  /** 获取入职报道信息 */
  const onFetchEntryProcessInfo = useCallback(processId => {
    fetchApi({
      ...entryApis.entryProcessInfo,
      params: {
        entryProcessId: processId,
      },
    }).then(res => {
      setEntryProcessInfo(res || {});
    });
  }, []);

  useEffect(() => {
    if (entryProcessId) {
      onFetchActList(entryProcessId);
      onFetchEntryProcessInfo(entryProcessId);
    }
  }, [entryProcessId]);

  /** 滚动对应右侧item */
  const handleActiveSection = useCallback(
    selectId => {
      if (selectActIdRef.current === selectId || isScrollRef.current) {
        return;
      }
      selectActIdRef.current = selectId;
      const activeSectionInfo = activities.find(({ id }) => id === selectId);
      setSelectedActivity(activeSectionInfo);
    },
    [activities.length]
  );

  /** 完成入职邀约 */
  const handleEntryInviteOk = useCallback(() => {
    if (inviteLoadingRef.current) {
      return;
    }
    setInviteLoading(true);
    inviteLoadingRef.current = true;
    fetchApi({
      ...entryApis.entryActProcessUpdate,
      data: {
        prjProcessItemId: inviteProcessId.current,
      },
      onSuccess: () => {
        setInviteOpen(false);
        onFetchActList(entryProcessId);
      },
    }).finally(() => {
      setInviteLoading(false);
      inviteLoadingRef.current = false;
    });
  }, [entryProcessId]);

  const footerBtnImgMap = useMemo(
    () => ({
      pc: `${DOWNLOAD_URL_PREFIX}/630dbc30b9874c958ced0d1b3b2b7f5a`,
      mobile: `${DOWNLOAD_URL_PREFIX}/8f5f6cbbc25048b482bbd9e8f52af1a2`,
    }),
    []
  );

  /** 跳转 */
  const handleJumpToDetail = useCallback(
    record => {
      /** 按钮跳转 */
      if ((record.displayType = ENTRY_DISPLAY_TYPE.BTN_JUMP_URL)) {
        const itemObj = (record?.itemParams || []).reduce((pre, cur) => {
          pre[cur.paramCode] = cur.value;
          return pre;
        }, {});
        /** 根据入职的用工类型打开对应的链接 */
        const openUrl = itemObj[entryProcessInfo?.empCategory];
        if (openUrl) {
          return window.open(openUrl, '_blank');
        }
      }
      onJumpToDetail({
        record,
        processInfo: entryProcessInfo || {},
      });
    },
    [entryProcessInfo]
  );

  return (
    <div className="dhr-entry-container">
      {/* 左边主要内容区域 */}
      <EntryList
        ref={entryListRef}
        list={activities}
        onClick={handleJumpToDetail}
        onActivityClick={handleActivityClick}
        onActiveSection={handleActiveSection}
        entryProcessInfo={entryProcessInfo}
      />

      {/* 右边活动列表区域 */}
      <div className="dhr-entry-right-activity-list-wrap">
        <ActMenu items={activities} selectedActivity={selectedActivity} onActivityClick={handleActivityClick} />
      </div>
      <InviteModal
        centered
        footer={null}
        bodyStyle={{
          padding: 0,
        }}
        width={800}
        keyboard={false}
        closable={false}
        open={inviteOpen}
        maskClosable={false}
        inviteInfo={inviteInfo}
        onOk={handleEntryInviteOk}
        btnImgMap={footerBtnImgMap}
        confirmLoading={inviteLoading}
        pageLoading={invitePageLoading}
      />
    </div>
  );
};

export default EntryHome;
