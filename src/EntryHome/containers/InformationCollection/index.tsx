import React from 'react';

import EntryTitle from '../EntryTitle';

import { Button } from 'antd';

import './style.less';

export interface IAppProps {
  bgUrl: string;
  iconUrl: string;
  disabled: boolean;
  actItemName: string;
  onClick: (params) => void;
}
const PhysicalExamination: React.FC<IAppProps> = props => {
  const { bgUrl, iconUrl, actItemName, disabled, onClick } = props;
  return (
    <div className="dhr-entry-information-collection-wrap">
      <EntryTitle title={actItemName || '信息采集'} />
      <div className="dhr-entry-information-collection-content-wrap">
        <img src={bgUrl} width="100%" height="auto" alt="" />
        <div className="dhr-entry-information-collection-content">
          <div className="dhr-entry-information-collection-content-left">
            <div>
              <span className="dhr-entry-content-tip">您需要在入职前2个工作日前，完成入职资料的信息采集。</span>
              <span className="dhr-entry-content-tip">期待与您的见面！</span>
            </div>
            <Button className="dhr-entry-btn" disabled={disabled} onClick={() => onClick({ ...props })}>
              立即填写
            </Button>
          </div>
          <div className="dhr-entry-information-collection-content-right">
            <img src={iconUrl} width="140" height="auto" alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhysicalExamination;
