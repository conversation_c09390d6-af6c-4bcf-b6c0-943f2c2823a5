.dhr-entry-information-collection-wrap {
  .dhr-entry-information-collection-content-wrap {
    position: relative;
    min-height: 160px;
    .dhr-entry-information-collection-content {
      position: absolute;
      left: 60px;
      right: 60px;
      top: 20px;
      bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .dhr-entry-information-collection-content-left {
        // display: flex;
        // height: 100%;
        // padding-top: 10px;
        // flex-direction: column;
        // justify-content: space-between;
        .dhr-entry-btn {
          margin-top: 20px;
        }
      }
    }
  }
}
