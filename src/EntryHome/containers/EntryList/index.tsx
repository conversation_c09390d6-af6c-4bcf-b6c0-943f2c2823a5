import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import * as uuid from 'uuid';

import EntryItem from '../EntryItem';

import { DOWNLOAD_URL_PREFIX } from '@constants/common';

import './style.less';

export interface IAppProps {
  ref?: any;
  list: Record<string, any>[];
  onClick: (params) => void;
  onActivityClick?: (params) => void;
  entryProcessInfo: Record<string, any>;
  onActiveSection: (activeSectionId: string) => void;
}

const EntryList: React.FC<IAppProps> = forwardRef(
  ({ list = [], entryProcessInfo, onClick, onActivityClick, onActiveSection }, ref) => {
    const sectionRefs = useRef({});
    const containRef = useRef(null);

    const imageConfig = useMemo(
      () => ({
        headerBg: `${DOWNLOAD_URL_PREFIX}/669e8033b9524a86a15ab42f77c78858`,
        tipIcon: `${DOWNLOAD_URL_PREFIX}/956a8788da7e4dd6b8be9cd15f8a706c`,
        remindIcon: `${DOWNLOAD_URL_PREFIX}/8e4c428ecc9849e1be338e221bcfabd2`,
        addressIcon: `${DOWNLOAD_URL_PREFIX}/ecb2d4626dfe4d06a7dda93ca870c714`,

        /** 体检 */
        physicalExaminationBg: `${DOWNLOAD_URL_PREFIX}/813f4bff969e4e18a1deebcddf7b8fb0`,
        physicalExaminationIcon: `${DOWNLOAD_URL_PREFIX}/ea3f1ecafe474dfea3d7539e8b6ff89a`,

        /** 信息采集 */
        informationCollectionBg: `${DOWNLOAD_URL_PREFIX}/3d88cd4830894520b311ed40dfbc62b5`,
        informationCollectionIcon: `${DOWNLOAD_URL_PREFIX}/8c0a9dae6a7c43e6aee5db7e7a7a745d`,

        /** 入职报道 */
        inductionReportIcon: `${DOWNLOAD_URL_PREFIX}/996a02f859d3440492a07812b66f74cd`,

        /** 入职指引 */
        entryGuideBg: `${DOWNLOAD_URL_PREFIX}/f2962fbbf188479abd9d6df7decdafca`,
        entryGuideIcon: `${DOWNLOAD_URL_PREFIX}/d4d6ee466d304a799aaaa2c91f81a558`,

        /** 工卡制作 */
        workCardMakeBg: `${DOWNLOAD_URL_PREFIX}/ae48305cedc34abb89464dbb42bedd22`,
        workCardMakeIcon: `${DOWNLOAD_URL_PREFIX}/aafa175f19ef4ec091e2c3adfba6e4fa`,

        /** 入职申报 */
        entryApplicationIcon: `${DOWNLOAD_URL_PREFIX}/80955eeb0c834520a1672fb828decc3d`,
        /** 文件签署 */
        documentSignIcon: `${DOWNLOAD_URL_PREFIX}/cfa1c96437584b1bbc28e47fcf8c4651`,
        /** 住宿申请 */
        accommodationApplicationBg: `${DOWNLOAD_URL_PREFIX}/f2962fbbf188479abd9d6df7decdafca`,
        accommodationApplicationIcon: `${DOWNLOAD_URL_PREFIX}/cd5a1a62ee824b9e82b271fe52aa28fc`,
        /** 实习生入场培训 */
        internTrainIcon: `${DOWNLOAD_URL_PREFIX}/137b2a24e9bc49db9811a330fce8545e`,
        /** 其他操作 */
        otherInfoIcon: `${DOWNLOAD_URL_PREFIX}/8c0a9dae6a7c43e6aee5db7e7a7a745d`,
      }),
      []
    );

    useImperativeHandle(ref, () => ({
      getsectionRefs: () => {
        return sectionRefs.current;
      },
    }));

    const entryItemPropMap = useMemo(
      () => ({
        /** 体检预约 */
        sta_entry_physical: {
          onClick,
          bgUrl: imageConfig.physicalExaminationBg,
          iconUrl: imageConfig.physicalExaminationIcon,
        },
        /** 信息采集 */
        sta_entry_info: {
          onClick,
          bgUrl: imageConfig.informationCollectionBg,
          iconUrl: imageConfig.informationCollectionIcon,
        },
        /** 入职报道 */
        sta_entry_report: {
          onClick,
          initValues: entryProcessInfo,
          tipIconUrl: imageConfig.tipIcon,
          addressIcon: imageConfig.addressIcon,
          remindIconUrl: imageConfig.remindIcon,
          iconUrl: imageConfig.inductionReportIcon,
        },
        /** 入职指引 */
        sta_entry_guide_learn: {
          onClick,
          bgUrl: imageConfig.entryGuideBg,
          iconUrl: imageConfig.entryGuideIcon,
        },
        /** 入职指引 - 实习生 */
        sta_entry_intern_guide_learn: {
          onClick,
          bgUrl: imageConfig.entryGuideBg,
          iconUrl: imageConfig.entryGuideIcon,
        },
        /** 文件签署 */
        sta_entry_contract_sign: {
          onClick,
          iconUrl: imageConfig.documentSignIcon,
        },
        /** 工卡制作 */
        sta_entry_work_card: {
          bgUrl: imageConfig.workCardMakeBg,
          iconUrl: imageConfig.workCardMakeIcon,
          addressIcon: imageConfig.addressIcon,
        },
        /** 入职申报 */
        sta_entry_declaration: {
          onClick,
          iconUrl: imageConfig.entryApplicationIcon,
        },
        /** 住宿申请 */
        sta_entry_dormitory: {
          onClick,
          bgUrl: imageConfig.accommodationApplicationBg,
          iconUrl: imageConfig.accommodationApplicationIcon,
        },
        /** 实习生入场培训 */
        sta_entry_intern_train: {
          onClick,
          iconUrl: imageConfig.internTrainIcon,
        },
        /** 其他操作 */
        sta_entry_other_info: {
          initValues: entryProcessInfo,
          iconUrl: imageConfig.otherInfoIcon,
        },
      }),
      [entryProcessInfo]
    );

    useEffect(() => {
      if (list.length > 0 && containRef.current) {
        try {
          const observer = new IntersectionObserver(
            entries => {
              entries.forEach(entry => {
                if (entry.isIntersecting) {
                  onActiveSection(entry.target.id);
                }
              });
            },
            {
              root: containRef.current,
              rootMargin: '0px',
              threshold: 0.5, // 当 50% 的元素进入视口时触发
            }
          );
          (list || []).forEach(section => {
            if (sectionRefs.current[section.id]) {
              observer.observe(sectionRefs.current[section.id].getRef());
            }
          });

          return () => {
            (list || []).forEach(section => {
              if (sectionRefs.current[section.id]) {
                observer.unobserve(sectionRefs.current[section.id].getRef());
              }
            });
          };
        } catch (error) {
          console.log('报错：', error);
        }
      }
    }, [list.length, containRef]);

    const otherEntryItem = useMemo(
      () => ({
        id: uuid.v4(),
        actItemId: uuid.v4(),
        actItemCode: 'sta_entry_other_info',
      }),
      []
    );

    // 导航到 其他 模块
    const handleNavigateToOther = useCallback(() => {
      onActivityClick(otherEntryItem);
    }, []);
    return (
      <div className="dhr-entry-left-content" ref={containRef}>
        <div className="dhr-entrt-act-list-wrap">
          <div className="dhr-entry-act-list-header-wrap">
            <img src={imageConfig.headerBg} width="100%" height="auto" />
            <span className="dhr-header-other-action-navigation-wrap">
              <span>如需修改入职时间等操作，</span>
              <span onClick={handleNavigateToOther}>
                <a>请点击此处</a>
              </span>
            </span>
          </div>
          <div className="dhr-entry-act-list-content-wrap">
            {list.map(listItem => (
              <EntryItem
                {...listItem}
                key={listItem.id}
                {...entryItemPropMap[listItem.actItemCode]}
                ref={el => (sectionRefs.current[listItem.id] = el)}
              />
            ))}
            <EntryItem
              {...otherEntryItem}
              key={otherEntryItem.id}
              {...entryItemPropMap[otherEntryItem.actItemCode]}
              ref={el => (sectionRefs.current[otherEntryItem.id] = el)}
            />
          </div>
        </div>
      </div>
    );
  }
);

export default EntryList;
