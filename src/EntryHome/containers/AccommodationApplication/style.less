.dhr-entry-accommodation-application-wrap {
  .dhr-entry-accommodation-application-content-wrap {
    position: relative;
    min-height: 210px;
    .dhr-entry-accommodation-application-content {
      position: absolute;
      left: 60px;
      right: 60px;
      top: 40px;
      bottom: 56px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .dhr-entry-accommodation-application-content-left {
        display: flex;
        height: 100%;
        // padding-top: 20px;
        flex-direction: column;
        justify-content: space-between;
        .dhr-entry-content-tip {
          margin-bottom: 10px;
        }
      }
    }
  }
}
