import React from 'react';

import EntryTitle from '../EntryTitle';

import { Button } from 'antd';

import './style.less';

export interface IAppProps {
  bgUrl: string;
  iconUrl: string;
  disabled: boolean;
  actItemName: string;
  onClick: (params) => void;
}
const AccommodationApplication: React.FC<IAppProps> = props => {
  const { bgUrl, iconUrl, actItemName, disabled, onClick } = props;
  return (
    <div className="dhr-entry-accommodation-application-wrap">
      <EntryTitle title={actItemName || '住宿申请'} />
      <div className="dhr-entry-accommodation-application-content-wrap">
        <img src={bgUrl} width="100%" height="auto" alt="" />
        <div className="dhr-entry-accommodation-application-content">
          <div className="dhr-entry-accommodation-application-content-left">
            <div>
              <span className="dhr-entry-content-tip">您可以选择公司宿舍，或非公司宿舍。</span>
              <span className="dhr-entry-content-sub-tip">
                若您选择公司宿舍（仅限广州、苏州、合肥），请提前3天完成申请；
              </span>
              <span className="dhr-entry-content-sub-tip">若您选择非公司宿舍，自行安排住宿即可。 </span>
            </div>
            <Button className="dhr-entry-btn" disabled={disabled} onClick={() => onClick({ ...props })}>
              立即申请
            </Button>
          </div>
          <div className="dhr-entry-accommodation-application-content-right">
            <img src={iconUrl} width="180" height="auto" alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccommodationApplication;
