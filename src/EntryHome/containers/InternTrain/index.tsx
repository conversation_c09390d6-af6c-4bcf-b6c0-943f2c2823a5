import React from 'react';

import EntryTitle from '../EntryTitle';

import { Button } from 'antd';

import './style.less';

export interface IAppProps {
  iconUrl: string;
  disabled: boolean;
  actItemName: string;
  onClick: (params) => void;
}
const InternTrain: React.FC<IAppProps> = props => {
  const { iconUrl, actItemName, disabled, onClick } = props;
  return (
    <div className="dhr-entry-intern-train-wrap">
      <EntryTitle title={actItemName || '入场培训'} />
      <div className="dhr-entry-intern-train-content-wrap">
        <div className="dhr-entry-intern-train-content">
          <div className="dhr-entry-intern-train-content-left">
            <div>
              <span className="dhr-entry-content-tip">《礼仪素养（必修）》学习训练营</span>
            </div>
            <Button className="dhr-entry-btn" disabled={disabled} onClick={() => onClick({ ...props })}>
              立即学习
            </Button>
          </div>
          <div className="dhr-entry-intern-train-content-right">
            <img src={iconUrl} width="240" height="auto" alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default InternTrain;
