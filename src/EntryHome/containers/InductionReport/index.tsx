import React, { useMemo } from 'react';
import dayjs from 'dayjs';

import EntryTitle from '../EntryTitle';

import { Button } from 'antd';

import './style.less';

export interface IAppProps {
  iconUrl: string;
  tipIconUrl: string;
  disabled: boolean;
  addressIcon: string;
  actItemName: string;
  remindIconUrl: string;
  onClick: (params) => void;
  initValues: Record<string, any>;
}
const InductionReport: React.FC<IAppProps> = props => {
  const { iconUrl, tipIconUrl, addressIcon, initValues, actItemName, remindIconUrl, disabled, onClick } = props;
  const entryDiffInDay = useMemo(() => {
    if (!initValues.entryDate) {
      return 0;
    }
    // 获取当前时间的时间戳
    const today = dayjs().startOf('day');
    const target = dayjs(initValues.entryDate).startOf('day');
    // 计算相差的天数
    const diffInDays = target.diff(today, 'day');
    // 如果目标时间是今天之前，返回0
    const daysDifference = diffInDays < 0 ? 0 : diffInDays;

    return daysDifference;
  }, [initValues.entryDate]);

  return (
    <div className="dhr-entry-induction-report-wrap">
      <EntryTitle title={actItemName || '入职报到'} />
      <div className="dhr-entry-induction-report-content-wrap">
        <div className="dhr-entry-induction-report-content">
          <div className="dhr-entry-induction-report-content-left">
            <div className="dhr-entry-induction-report-content-left-header">
              <img src={remindIconUrl} width="18" height="18" alt="" />
              <span>距离您正式入职还有</span>
            </div>
            <div className="dhr-entry-induction-report-content-left-date-wrap">
              <img src={iconUrl} width="188" height="auto" alt="" />
              <div className="dhr-entry-induction-report-content-left-day-wrap">
                <span className="dhr-entry-induction-report-content-left-day-num">{entryDiffInDay}</span>
                <span className="dhr-entry-induction-report-content-left-day-text">天</span>
              </div>
            </div>
          </div>
          <div className="dhr-entry-induction-report-content-right">
            <div className="dhr-entry-induction-report-content-right-header">
              <img src={addressIcon} width="18" height="18" alt="" />
              <span>您的报到地点是：{initValues.workPlace || '-'}。</span>
            </div>
            <div className="dhr-entry-induction-report-content-flow-wrap">
              <div className="dhr-entry-induction-report-content-flow-item">
                <i className="dhr-entry-induction-report-content-flow-icon"></i>
                <div className="dhr-entry-induction-report-content-flow-content">
                  <span className="dhr-entry-content-tip">广州-云埔、连云、神舟、知识城园区</span>
                  <span className="dhr-entry-content-sub-tip">
                    请携带本人身份证原件入园，您自助制作工卡时需要用到。
                  </span>
                </div>
              </div>
              <div className="dhr-entry-induction-report-content-flow-item">
                <i className="dhr-entry-induction-report-content-flow-icon"></i>
                <div className="dhr-entry-induction-report-content-flow-content">
                  <span className="dhr-entry-content-tip">入职当天请凭入园短信进入公司</span>
                  <span className="dhr-entry-content-sub-tip">
                    建议入职当日9点前到达公司。入园短信最晚将在入职当天12点前发出，若还未收到短信，暂时不可入园哦。
                  </span>
                </div>
              </div>
            </div>
            <div className="dhr-entry-induction-report-content-tip">
              <img src={tipIconUrl} width="12" height="12" alt="" />
              <span className="dhr-entry-content-sub-tip">
                如有疑问，可在【遇见CVTE】微信公众号点击“智能助手”发起问询/发起人工咨询，亦可电话联系HRSSC中心020-86154380
              </span>
              {/* <span className="dhr-entry-content-sub-tip">需定位到CVTE园区或办事处，才能标记打卡成功</span> */}
            </div>
          </div>
        </div>
        <div className="dhr-entry-induction-report-footer-btn">
          <Button className="dhr-entry-btn" disabled={disabled} onClick={() => onClick({ ...props })}>
            立即打卡
          </Button>
        </div>
      </div>
    </div>
  );
};

export default InductionReport;
