.dhr-entry-induction-report-wrap {
  .dhr-entry-induction-report-content-wrap {
    min-height: 378px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 24px;
    color: #232626;
    padding: 46px 60px 50px 60px;
    background-image: linear-gradient(120deg, #ffffff -58%, #d9f7ff 129%);
    .dhr-entry-induction-report-content {
      display: flex;
      justify-content: space-between;
      .dhr-entry-induction-report-content-left {
        .dhr-entry-induction-report-content-left-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          & > img {
            margin-right: 8px;
          }
        }
        .dhr-entry-induction-report-content-left-date-wrap {
          position: relative;
          .dhr-entry-induction-report-content-left-day-wrap {
            position: absolute;
            bottom: 62px;
            left: 0px;
            right: 40px;
            white-space: nowrap;
            text-align: center;
            .dhr-entry-induction-report-content-left-day-num {
              font-size: 100px;
              letter-spacing: -6px;
              -webkit-background-clip: text; /* 将背景裁剪到文字 */
              -webkit-text-fill-color: transparent; /* 文字颜色透明 */
              background-clip: text; /* 标准属性 */
              color: transparent; /* 兼容性 */
              background-image: linear-gradient(175deg, #0887fd 1%, #49f6f6 267%);
            }
            .dhr-entry-induction-report-content-left-day-text {
              font-size: 20px;
              margin-left: 10px;
              display: inline-block;
              -webkit-background-clip: text; /* 将背景裁剪到文字 */
              -webkit-text-fill-color: transparent; /* 文字颜色透明 */
              background-clip: text; /* 标准属性 */
              color: transparent; /* 兼容性 */
              background-image: linear-gradient(173deg, #0887fd 1%, #49f6f6 258%);
            }
          }
        }
      }

      .dhr-entry-induction-report-content-right {
        width: 60%;
        .dhr-entry-content-sub-tip {
          margin-top: 8px;
        }
        .dhr-entry-induction-report-content-right-header {
          display: flex;
          margin-bottom: 20px;
          & > img {
            margin-top: 3px;
            margin-right: 8px;
          }
        }
        .dhr-entry-induction-report-content-flow-wrap {
          .dhr-entry-content-tip {
            font-weight: 500;
          }
          .dhr-entry-induction-report-content-flow-item {
            display: flex;
            position: relative;
            margin-bottom: 20px;
            .dhr-entry-induction-report-content-flow-content {
              margin-left: 8px;
              flex: 1;
            }
            .dhr-entry-induction-report-content-flow-icon {
              width: 18px;
              height: 18px;
              margin-top: 3px;
              border-radius: 50%;
              position: relative;
              background-color: #d6eaff;
              &::before {
                content: '';
                position: absolute;
                left: 3px;
                top: 3px;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                position: absolute;
                background-color: #fff;
              }
              &::after {
                content: '';
                position: absolute;
                left: 5px;
                top: 5px;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                position: absolute;
                background-color: #1299fc;
              }
            }
          }
        }

        .dhr-entry-induction-report-content-tip {
          display: flex;
          & > img {
            margin-top: 6px;
            margin-right: 8px;
          }
          .dhr-entry-content-sub-tip {
            margin-top: 0px;
          }
        }
      }
    }
    .dhr-entry-induction-report-footer-btn {
      text-align: center;
      margin-top: 20px;
    }
  }
}
