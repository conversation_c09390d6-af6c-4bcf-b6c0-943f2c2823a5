import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Drawer, ConfigProvider } from 'antd';
import Message from '@cvte/cir-message';
import zhCN from 'antd/lib/locale/zh_CN';

import EntryTitle from '../EntryTitle';
import { LCPDetailTemplate } from '@components/LcpTemplate';

import { Button } from 'antd';

import { ENTRY_EMPLOYMENT_TYPE } from '@constants/entry';

import 'antd/dist/antd.css';
import './style.less';

const eventPrefix = 'dhrEntryOtherInfoActionTableClose';

// 放弃入职
const ENTRY_ABANDON_PROCESS = 'ENTRY_ABANDON_PROCESS';
// 入职时间修改
const ENTRY_DATE_CHANGE_PROCESS = 'ENTRY_DATE_CHANGE_PROCESS';

export interface IAppProps {
  iconUrl: string;
  disabled: boolean;
  actItemName: string;
  initValues: Record<string, any>;
}
const EntryApplication: React.FC<IAppProps> = props => {
  const { iconUrl, actItemName, disabled, initValues } = props;

  const [open, setOpen] = useState<boolean>(false);
  const [lcpConfig, setLcpConfig] = useState(undefined);

  const formTemplateConfig = useMemo(
    () => ({
      // 放弃入职
      ENTRY_ABANDON_PROCESS: {
        appId: 'efa37869ee1c4930b434a4c7b1548d46',
        classId: 'c6e735f5e78e4d808ad017c910041b44',
        tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      },
      // 修改入职时间
      ENTRY_DATE_CHANGE_PROCESS: {
        appId: 'efa37869ee1c4930b434a4c7b1548d46',
        classId: 'b0d8a7f2e1724a409decc2d0c141a1b9',
        tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      },
    }),
    []
  );

  // 入职时间/放弃入职
  const handleOpenDraw = useCallback(
    key => {
      const newLcpConfig = {
        ...formTemplateConfig[key],
        entryProcessId: initValues?.entryProcessId,
        pageId:
          key === ENTRY_DATE_CHANGE_PROCESS ? initValues?.entryDateChangeProcessId : initValues?.entryAbandonProcessId,
      };
      // 实习生
      if (initValues?.empCategory === ENTRY_EMPLOYMENT_TYPE.INTERN) {
        newLcpConfig['metaConfig'] = {
          contextData: {
            viewType: 'intern',
          },
        };
      }
      setOpen(true);
      setLcpConfig(newLcpConfig);
    },
    [initValues]
  );

  const handleClose = useCallback(() => {
    setOpen(false);
    setLcpConfig(undefined);
  }, []);

  useEffect(() => {
    Message.on(eventPrefix, () => {
      handleClose();
    });
  }, []);

  return (
    <div className="dhr-entry-other-info-wrap">
      <EntryTitle title={actItemName || '其他'} />
      <div className="dhr-entry-other-info-content-wrap">
        <div className="dhr-entry-other-info-content">
          <div className="dhr-entry-other-info-content-left">
            <div>
              <span className="dhr-entry-content-tip">其他操作</span>
              {/* <span className="dhr-entry-content-tip">则签署，感谢您的配合</span> */}
            </div>
            <div style={{ display: 'flex' }}>
              <Button
                disabled={disabled}
                style={{ width: 120 }}
                className="dhr-entry-btn"
                onClick={() => handleOpenDraw(ENTRY_DATE_CHANGE_PROCESS)}
              >
                入职时间修改
              </Button>
              <Button
                disabled={disabled}
                className="dhr-entry-btn"
                style={{ marginLeft: '10px' }}
                onClick={() => handleOpenDraw(ENTRY_ABANDON_PROCESS)}
              >
                放弃入职
              </Button>
            </div>
          </div>
          <div className="dhr-entry-other-info-content-right">
            <img src={iconUrl} width="140" height="auto" alt="" />
          </div>
        </div>
      </div>
      <ConfigProvider locale={zhCN}>
        <Drawer width="50%" open={open} maskClosable={false} onClose={handleClose}>
          {lcpConfig && <LCPDetailTemplate {...lcpConfig} />}
        </Drawer>
      </ConfigProvider>
    </div>
  );
};

export default EntryApplication;
