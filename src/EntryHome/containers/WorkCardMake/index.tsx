import React from 'react';

import EntryTitle from '../EntryTitle';

import './style.less';

export interface IAppProps {
  bgUrl: string;
  iconUrl: string;
  actItemName: string;
  addressIcon: string;
}
const WorkCardMake: React.FC<IAppProps> = props => {
  const { bgUrl, iconUrl, addressIcon, actItemName } = props;
  return (
    <div className="dhr-entry-work-card-making-wrap">
      <EntryTitle title={actItemName || '工卡制作'} />
      <div className="dhr-entry-work-card-making-content-wrap">
        <img src={bgUrl} width="100%" height="auto" alt="" />
        <div className="dhr-entry-work-card-making-content">
          <div className="dhr-entry-work-card-making-content-left">
            <div>
              <img height="18" width="18" src={addressIcon} />
              <div className="dhr-entry-work-card-making-content-address">
                <span className="dhr-entry-content-tip">广州-云埔、连云、神舟、知识城园区</span>
                <span className="dhr-entry-content-sub-tip">请持本人身份证原件至园区大堂自助制卡</span>
              </div>
            </div>
          </div>
          <div className="dhr-entry-work-card-making-content-right">
            <img src={iconUrl} width="170" height="auto" alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkCardMake;
