import React from 'react';

import EntryTitle from '../EntryTitle';

import { Button } from 'antd';

import './style.less';

export interface IAppProps {
  iconUrl: string;
  disabled: boolean;
  actItemName: string;
  onClick: (params) => void;
}
const EntryApplication: React.FC<IAppProps> = props => {
  const { iconUrl, actItemName, disabled, onClick } = props;
  return (
    <div className="dhr-entry-application-wrap">
      <EntryTitle title={actItemName || '入职申报'} />
      <div className="dhr-entry-application-content-wrap">
        <div className="dhr-entry-application-content">
          <div className="dhr-entry-application-content-left">
            <div>
              <span className="dhr-entry-content-tip">请在入职两天内完成利益冲突申报及商业行为准</span>
              <span className="dhr-entry-content-tip">则签署，感谢您的配合</span>
            </div>
            <Button className="dhr-entry-btn" disabled={disabled} onClick={() => onClick({ ...props })}>
              立即申报
            </Button>
          </div>
          <div className="dhr-entry-application-content-right">
            <img src={iconUrl} width="240" height="auto" alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntryApplication;
