.dhr-entry-application-wrap {
  .dhr-entry-application-content-wrap {
    position: relative;
    min-height: 254px;
    border-radius: 6px;
    background-image: linear-gradient(111deg, #ffffff -52%, #d9f7ff 130%);
    .dhr-entry-application-content {
      position: absolute;
      left: 62px;
      right: 48px;
      top: 40px;
      bottom: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .dhr-entry-application-content-left {
        display: flex;
        height: 100%;
        padding-top: 30px;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
}
