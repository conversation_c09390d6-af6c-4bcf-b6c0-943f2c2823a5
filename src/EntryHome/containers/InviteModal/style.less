.dhr-entry-invite-modal-content-wrap {
  position: relative;
  min-width: 600px;
  min-height: 400px;
  border-radius: 12px;
  background-color: transparent;
  .ant-skeleton {
    width: 80%;
    margin: 0 auto;
    padding-top: 8%;
  }
  .dhr-entry-invite-modal-content-bg {
    left: 0;
    right: 0;
    top: -56px;
    bottom: 0px;
    position: absolute;
    border-radius: 12px;
  }
  .dhr-entry-invite-modal-content {
    left: 88px;
    right: 88px;
    top: 160px;
    position: absolute;
    .dhr-entry-invite-modal-rich-text-content {
      font-size: 16px;
      line-height: 38px;
      color: #232626;
      margin-bottom: 10px;
      p {
        text-indent: 2em;
        margin-bottom: 0px;
        font-family: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, 楷体;
        text-underline-offset: 6px;
        text-decoration: underline #c7d2e5;
      }
    }
  }
  .dhr-entry-invite-modal-footer {
    height: 32px;
    position: relative;
    text-align: center;
    .dhr-entry-invite-modal-footer-btn {
      // display: inline-block;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      transition: all 0.2s;
      & > img {
        cursor: pointer;
      }
      &:active {
        transform: translate(-1px, 2px);
      }
    }
  }
}

.dhr-entry-invite-modal {
  .ant-modal-content {
    border-radius: 12px;
  }
}
