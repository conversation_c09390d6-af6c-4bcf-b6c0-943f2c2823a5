import { LoadingOutlined } from '@ant-design/icons';
import { ModalProps } from 'antd/lib/modal';
import { Modal, Spin, Skeleton } from 'antd';
import React, { useCallback } from 'react';

import { DOWNLOAD_URL_PREFIX } from '@constants/common';

import './style.less';
import 'antd/es/skeleton/style/index';

export interface IAppProps extends ModalProps {
  pageLoading: boolean;
  inviteInfo: Record<string, any>;
  btnImgMap: Record<string, any>;
}
const InviteModal: React.FC<IAppProps> = props => {
  const { inviteInfo, btnImgMap, confirmLoading, pageLoading, onOk, ...otherProps } = props;

  const LoadingOutlinedIcon = useCallback(() => <LoadingOutlined style={{ fontSize: 24 }} spin />, []);
  return (
    <Modal className="dhr-entry-invite-modal" {...otherProps}>
      <div className="dhr-entry-invite-modal-content-wrap">
        <Skeleton active loading={pageLoading} paragraph={{ rows: 8 }}>
          <div>
            <img
              alt=""
              width="100%"
              height="auto"
              className="dhr-entry-invite-modal-content-bg"
              src={`${DOWNLOAD_URL_PREFIX}/${inviteInfo.cBackgroundImage}`}
            />
            <div className="dhr-entry-invite-modal-content">
              <div
                className="dhr-entry-invite-modal-rich-text-content"
                dangerouslySetInnerHTML={{ __html: inviteInfo.cTemplateContent }}
              ></div>
              <div className="dhr-entry-invite-modal-footer">
                {confirmLoading ? (
                  <Spin indicator={<LoadingOutlinedIcon />} />
                ) : (
                  <div className="dhr-entry-invite-modal-footer-btn" onClick={onOk}>
                    <img src={btnImgMap.pc} height="32" width="auto" alt="" />
                  </div>
                )}
              </div>
            </div>
          </div>
        </Skeleton>
      </div>
    </Modal>
  );
};

export default InviteModal;
