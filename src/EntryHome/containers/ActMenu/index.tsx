import React, { useMemo } from 'react';
import { Affix } from 'antd';

import classnames from 'classnames';

import { DOWNLOAD_URL_PREFIX } from '@constants/common';

import { ENTRY_ACT_STATUS } from '@constants/entry';

import './style.less';

export interface IAppProps {
  items: Record<string, any>[];
  selectedActivity: Record<string, any>;
  onActivityClick: (params) => void;
}
const ActMenu: React.FC<IAppProps> = ({ items, selectedActivity = {}, onActivityClick }) => {
  /** 导航图标 */
  const navigationIcons = useMemo(
    () => ({
      /** 体检预约 */
      sta_entry_physical: `${DOWNLOAD_URL_PREFIX}/fffd360499cf4689ad6220df688f3863`,
      /** 信息采集 */
      sta_entry_info: `${DOWNLOAD_URL_PREFIX}/774e48cb062346bbbaed6feba07b42eb`,
      /** 入职报道 */
      sta_entry_report: `${DOWNLOAD_URL_PREFIX}/254901ed246e4ccb92ee72a9598c2490`,
      /** 入职指引 */
      sta_entry_guide_learn: `${DOWNLOAD_URL_PREFIX}/c6cd42460d7f403b853135f64ede720c`,
      /** 入职指引 - 实习生 */
      sta_entry_intern_guide_learn: `${DOWNLOAD_URL_PREFIX}/c6cd42460d7f403b853135f64ede720c`,
      /** 文件签署 */
      sta_entry_contract_sign: `${DOWNLOAD_URL_PREFIX}/3a742b4055b54a2e8cfa021ccad51665`,
      /** 工卡制作 */
      sta_entry_work_card: `${DOWNLOAD_URL_PREFIX}/ed681066e0a1473cb197e2e2a8d90540`,
      /** 入职申报 */
      sta_entry_declaration: `${DOWNLOAD_URL_PREFIX}/a6397c60b7b64c43b50d62fbba70b83c`,
      /** 住宿申请 */
      sta_entry_dormitory: `${DOWNLOAD_URL_PREFIX}/fffd360499cf4689ad6220df688f3863`,
      /** 默认图标 */
      defaultIcon: `${DOWNLOAD_URL_PREFIX}/fffd360499cf4689ad6220df688f3863`,
    }),
    []
  );

  return (
    <Affix offsetTop={10}>
      <div className="dhr-entry-right-activity-list">
        <ul className="dhr-activity-items">
          {items.map(activity => (
            <li
              className={classnames('dhr-activity-item', {
                'activity-item-active': selectedActivity?.id === activity.id,
              })}
              key={activity.id}
              onClick={() => onActivityClick(activity)}
            >
              <div className="dhr-activity-item-left">
                <i
                  className={classnames('dhr-activity-item-icon-wrap', {
                    'dhr-activity-item-icon-active': selectedActivity?.id === activity.id,
                  })}
                >
                  <i
                    className="dhr-activity-item-icon"
                    style={{
                      background: `url(${
                        navigationIcons[activity.actItemCode] || navigationIcons.defaultIcon
                      }) no-repeat`,
                    }}
                  ></i>
                </i>
                <span>{activity.actItemName}</span>
              </div>
              <span className="dhr-activity-item-status-wrap">
                <i
                  className={classnames('dhr-activity-item-status', {
                    'dhr-activity-item-status-success': activity.status === ENTRY_ACT_STATUS.COMPLETE,
                  })}
                ></i>
              </span>
            </li>
          ))}
        </ul>
      </div>
    </Affix>
  );
};

export default ActMenu;
