.dhr-entry-right-activity-list {
  padding: 6px;
  width: 230px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  .dhr-activity-items {
    list-style: none;
    padding: 0;
    margin: 0;
    .dhr-activity-item {
      display: flex;
      height: 50px;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      padding-left: 32px;
      padding-right: 16px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #8c8c8c;
      font-size: 14px;
      &:hover {
        color: #fff;
        background: #32c7f9;
        .dhr-activity-item-left {
          .dhr-activity-item-icon-wrap {
            .dhr-activity-item-icon {
              left: -18px;
              filter: drop-shadow(#fff 18px 0px);
            }
          }
        }
        .dhr-activity-item-status-wrap {
          .dhr-activity-item-status {
            &.dhr-activity-item-status-success {
              background-color: #fff;
              &::before {
                border-left-color: #59df40;
                border-bottom-color: #59df40;
              }
            }
          }
        }
      }

      &.activity-item-active {
        color: #fff;
        font-size: 18px;
        background: linear-gradient(129deg, #49f6f6 -38%, #0887fd 82%);
      }

      .dhr-activity-item-left {
        display: flex;
        align-items: center;
        .dhr-activity-item-icon-wrap {
          width: 18px;
          height: 18px;
          overflow: hidden;
          margin-right: 12px;
          display: inline-block;
          &.dhr-activity-item-icon-active {
            width: 24px;
            height: 24px;
            .dhr-activity-item-icon {
              width: 24px;
              height: 24px;
              left: -24px;
              filter: drop-shadow(#fff 24px 0px);
            }
          }
          .dhr-activity-item-icon {
            width: 18px;
            height: 18px;
            position: relative;
            display: inline-block;
            background-size: cover !important;
          }
        }
      }
      .dhr-activity-item-status-wrap {
        height: 20px;
        width: 20px;
        display: inline-block;
        .dhr-activity-item-status {
          height: 20px;
          width: 20px;
          position: relative;
          border-radius: 50%;
          display: inline-block;
          &.dhr-activity-item-status-success {
            background-color: #59df40;
            &::before {
              content: '';
              position: absolute;
              right: 4px;
              bottom: 7px;
              z-index: 2;
              width: 13px;
              height: 9px;
              border: 3px solid;
              border-radius: 2px;
              transform: rotate(-55deg);
              border-color: transparent transparent #fff #fff;
            }
          }
        }
      }
    }
  }
}
