import React from 'react';

import EntryTitle from '../EntryTitle';

import { Button } from 'antd';

import './style.less';

export interface IAppProps {
  bgUrl: string;
  iconUrl: string;
  disabled: boolean;
  actItemName: string;
  onClick: (params) => void;
}
const EntryGuide: React.FC<IAppProps> = props => {
  const { bgUrl, iconUrl, actItemName, disabled, onClick } = props;
  return (
    <div className="dhr-entry-guide-wrap">
      <EntryTitle title={actItemName || '入职指引'} />
      <div className="dhr-entry-guide-content-wrap">
        <img src={bgUrl} width="100%" height="auto" alt="" />
        <div className="dhr-entry-guide-content">
          <div className="dhr-entry-guide-content-left">
            <div>
              <span className="dhr-entry-content-tip">为帮助更便捷的完成入职手续， </span>
              <span className="dhr-entry-content-tip">HRSSC为您制作了线上入职指引。</span>
              <span className="dhr-entry-content-sub-tip">HRSSC为您制作了线上入职指引。</span>
            </div>
            <Button className="dhr-entry-btn" disabled={disabled} onClick={() => onClick({ ...props })}>
              立即查看
            </Button>
          </div>
          <div className="dhr-entry-guide-content-right">
            <img src={iconUrl} width="290" height="auto" alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntryGuide;
