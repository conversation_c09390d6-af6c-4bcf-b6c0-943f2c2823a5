.dhr-entry-guide-wrap {
  .dhr-entry-guide-content-wrap {
    position: relative;
    .dhr-entry-guide-content {
      position: absolute;
      left: 60px;
      right: 60px;
      top: 50px;
      bottom: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .dhr-entry-guide-content-left {
        display: flex;
        height: 100%;
        flex-direction: column;
        justify-content: space-between;
        .dhr-entry-content-sub-tip {
          margin-top: 6px;
        }
      }
    }
  }
}
