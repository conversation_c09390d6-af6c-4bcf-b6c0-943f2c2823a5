import React, { forwardRef, memo, useImperativeHandle, useMemo, useRef } from 'react';

import EntryGuide from '../EntryGuide';
import InternTrain from '../InternTrain';
import WorkCardMake from '../WorkCardMake';
import DocumentSign from '../DocumentSign';
import EntryOtherInfo from '../EntryOtherInfo';
import InductionReport from '../InductionReport';
import EntryApplication from '../EntryApplication';
import PhysicalExamination from '../PhysicalExamination';
import InformationCollection from '../InformationCollection';
import AccommodationApplication from '../AccommodationApplication';

const componentMap = {
  /** 体检预约 */
  sta_entry_physical: PhysicalExamination,
  /** 信息采集 */
  sta_entry_info: InformationCollection,
  /** 入职报道 */
  sta_entry_report: InductionReport,
  /** 入职指引 */
  sta_entry_guide_learn: EntryGuide,
  /** 入职指引-实习生 */
  sta_entry_intern_guide_learn: EntryGuide,
  /** 文件签署 */
  sta_entry_contract_sign: DocumentSign,
  /** 工卡制作 */
  sta_entry_work_card: WorkCardMake,
  /** 入职申报 */
  sta_entry_declaration: EntryApplication,
  /** 住宿申请 */
  sta_entry_dormitory: AccommodationApplication,
  /** 实习生入场培训 */
  sta_entry_intern_train: InternTrain,
  /** 其他信息 */
  sta_entry_other_info: EntryOtherInfo,
} as const;

// 定义 actItemCode 的类型
type ActItemCode = keyof typeof componentMap;

export interface IAppProps {
  ref?: any;
  id?: string;
  actItemCode?: ActItemCode;
}

// eslint-disable-next-line react/display-name
const EntryItem: React.FC<IAppProps> = forwardRef((props, ref) => {
  const sectionRef = useRef(null);
  useImperativeHandle(ref, () => ({
    getRef: () => {
      return sectionRef.current;
    },
  }));

  const { actItemCode } = props;
  // 使用 useMemo 缓存组件，避免不必要的重新渲染
  const Component = useMemo(() => {
    return actItemCode ? componentMap[actItemCode] : null;
  }, [actItemCode]);

  if (!Component) {
    return null; // 如果组件不存在，直接返回 null
  }

  return (
    <div id={props.id} className="dhr-entry-act-list-item-wrap" ref={sectionRef}>
      <Component {...props} />
    </div>
  );
});

export default memo(EntryItem);
