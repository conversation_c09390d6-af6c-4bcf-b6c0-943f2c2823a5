import { Button } from 'antd';
import React from 'react';

import EntryTitle from '../EntryTitle';

import './style.less';

export interface IAppProps {
  iconUrl: string;
  disabled: boolean;
  actItemName: string;
  onClick: (params) => void;
}
const DocumentSign: React.FC<IAppProps> = props => {
  const { iconUrl, actItemName, disabled, onClick } = props;
  return (
    <div className="dhr-document-signing-wrap">
      <EntryTitle title={actItemName || '文件签署'} />
      <div className="dhr-document-signing-content-wrap">
        <div className="dhr-document-signing-content">
          <div>
            <span className="dhr-entry-content-tip">
              请等待人事HR触发电子劳动合同&电子入职资料签署短信给您，并在收到短信的两天内完成签署，如有疑问可联系人事HR。
            </span>
          </div>
          <div className="dhr-document-signing-flow-wrap">
            <img src={iconUrl} height="auto" width="75%" alt="" />
            <div className="dhr-document-signing-flow-tip-wrap">
              <span>HR发送签署短信</span>
              <span>成功接收短信</span>
              <span>两天内完成签署</span>
            </div>
          </div>
          <div className="dhr-document-signing-footer-btn">
            <Button className="dhr-entry-btn" disabled={disabled} onClick={() => onClick({ ...props })}>
              立即签署
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentSign;
