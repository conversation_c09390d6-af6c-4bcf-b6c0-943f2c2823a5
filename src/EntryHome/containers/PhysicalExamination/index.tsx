import React from 'react';

import EntryTitle from '../EntryTitle';

import { Button } from 'antd';

import './style.less';

export interface IAppProps {
  id: string;
  bgUrl: string;
  iconUrl: string;
  disabled: boolean;
  actItemName: string;
  onClick?: (params) => void;
}
const PhysicalExamination: React.FC<IAppProps> = props => {
  const { bgUrl, iconUrl, actItemName, disabled, onClick } = props;

  return (
    <div className="dhr-entry-physical-examination-wrap">
      <EntryTitle title={actItemName || '信息采集'} />
      <div className="dhr-entry-physical-examination-content-wrap">
        <img src={bgUrl} width="100%" height="auto" alt="" />
        <div className="dhr-entry-physical-examination-content">
          <div className="dhr-entry-physical-examination-content-left">
            <span className="dhr-entry-content-tip">您可以选择公司体检，或非公司体检。</span>
            <Button className="dhr-entry-btn" disabled={disabled} onClick={() => onClick({ ...props })}>
              立即预约
            </Button>
          </div>
          <div className="dhr-entry-physical-examination-content-right">
            <img src={iconUrl} width="160" height="auto" alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhysicalExamination;
