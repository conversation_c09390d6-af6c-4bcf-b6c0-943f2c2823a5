.dhr-entry-container {
  display: flex;
  gap: 24px;
  min-height: 600px;
  .dhr-entry-btn.ant-btn {
    width: 100px;
    color: #fff;
    padding: 0px;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    line-height: 40px;
    border-radius: 8px;
    border-width: 0px;
    background-image: linear-gradient(128deg, #49f6f6 -90%, #0887fd 104%);
    &:active {
      border-color: #1da6fb;
    }
  }
  .dhr-entry-btn.ant-btn[type='button'] {
    & > span {
      line-height: 40px;
      vertical-align: initial;
      margin-top: 0px;
    }
  }
  .dhr-entry-btn.ant-btn.disabled,
  .dhr-entry-btn.ant-btn[disabled] {
    opacity: 0.4;
  }

  .dhr-entry-left-content {
    flex: 1;
    background: #fff;
    border-radius: 6px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    height: calc(100vh - 160px);
    overflow-y: auto;
    -ms-overflow-style: none; /* IE 10+ */
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    .dhr-entry-block-title-wrap {
      margin-bottom: 12px;
      .dhr-entry-block-title {
        position: relative;
        display: inline-block;
        padding-left: 8px;
        font-weight: 600;
        color: #232626;
        line-height: 1;
        font-size: 18px;
        &::before {
          content: '';
          width: 4px;
          top: 0;
          bottom: 0;
          left: -0px;
          position: absolute;
          background-image: linear-gradient(178deg, #0887fd 2%, #49f6f6 274%);
        }
      }
    }

    .dhr-entry-content-tip {
      font-size: 14px;
      color: #232626;
      display: block;
      line-height: 24px;
    }

    .dhr-entry-content-sub-tip {
      font-size: 12px;
      color: #232626;
      line-height: 24px;
      display: block;
    }
  }
}

.lcp-base-style {
  .dhr-entry-container {
    .dhr-entry-btn.ant-btn {
      width: 100px;
      color: #fff;
      height: 40px;
      padding: 0px;
      font-size: 16px;
      font-weight: 600;
      line-height: 40px;
      text-align: center;
      border-radius: 8px;
      border-width: 0px;
      background-image: linear-gradient(128deg, #49f6f6 -90%, #0887fd 104%);
      &:active {
        border-color: #1da6fb;
      }
    }
    .dhr-entry-btn.ant-btn[type='button'] {
      & > span {
        line-height: 40px;
        vertical-align: initial;
        margin-top: 0px;
      }
    }
  }
}
