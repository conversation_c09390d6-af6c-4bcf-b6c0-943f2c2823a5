/**
 * @description 用于处理行转列的逻辑
 */
import React, { useEffect, useState } from 'react';
import { StorageCRUD } from '@utils/tools/storage';
import { defaultArray } from '@utils/tools';

// const assignListStorageNamespace = `${processId}-itemsDisplay`;
// const unAssignListStorageNamespace = `${processId}-itemsUnDisplay`;

export interface IAppProps {
  onConfirm: () => void;
  rowKey?: string;
  rowNameKey?: string;
  /**
   * 排序字段
   *
   */
  sortKey?: string;
  /**
   * 默认展示字段的值
   */
  defaultShowKey?: string;
  /**
   * 默认展示字段的值
   */
  defaultShowKeyValue?: string;
  /**
   * 是否启用 配置缓存本地
   */
  isEnableCache?: boolean;
  itemList: Record<string, any>[];
  assignListStorageNamespace: string;
  unAssignListStorageNamespace: string;
}

export interface IUseRowToCols {
  onSetDisplayItem: (assignList: Record<string, any>[], unAssignList: Record<string, any>[]) => void; // 设置显示项目
  assignList: Record<string, any>[]; // 显示的项目
  unAssignList: Record<string, any>[]; // 隐藏的项目
}
export default ({
  itemList,
  rowKey,
  sortKey,
  rowNameKey,
  isEnableCache,
  defaultShowKey,
  defaultShowKeyValue,
  assignListStorageNamespace,
  unAssignListStorageNamespace,
  onConfirm,
}: IAppProps): IUseRowToCols => {
  const itemListJson = JSON.stringify(itemList);

  const [assignList, setAssignList] = useState<Record<string, any>[]>([]);
  const [unAssignList, setUnAssignList] = useState<Record<string, any>[]>([]);
  /**
   * @description 设置显示项目
   * @param _assignList  显示的项目
   * @param _unAssignList  隐藏的项目
   */
  const onSetDisplayItem = (_assignList, _unAssignList) => {
    setAssignList(_assignList);
    setUnAssignList(_unAssignList);
    if (isEnableCache) {
      StorageCRUD.update({
        namespace: assignListStorageNamespace,
        data: _assignList.map(k => k[rowKey]),
      });
      StorageCRUD.update({
        namespace: unAssignListStorageNamespace,
        data: _unAssignList.map(k => k[rowKey]),
      });
    }
    onConfirm?.();
  };

  /** 显示/隐藏工资套的项目 */
  useEffect(() => {
    let isExitCache = false;
    let assignListInStorage = undefined;
    // 启用缓存
    if (isEnableCache) {
      /**  查找是否有本地缓存数据 */
      assignListInStorage = StorageCRUD.retrieve(assignListStorageNamespace);
      const unAssignListInStorage = StorageCRUD.retrieve(unAssignListStorageNamespace);
      isExitCache = assignListInStorage && unAssignListInStorage;
    }

    const addExtraProperty = list =>
      list.map(k => ({
        ...k,
        title: k[rowNameKey],
        dataIndex: k[rowKey],
        key: k[rowKey],
      }));

    let _assignList = addExtraProperty(
      defaultArray(itemList).filter(item =>
        isExitCache
          ? assignListInStorage?.includes(item[rowKey])
          : defaultShowKey
          ? item[defaultShowKey] === defaultShowKeyValue
          : true
      )
    );

    //
    const assignIds = _assignList.map(listItem => listItem[rowKey]);
    // const isInAssignList = (item: Record<string, any>) => _assignList.findIndex(k => k.itemId === item.itemId) > -1;
    let _unAssignList = addExtraProperty(defaultArray(itemList).filter(item => !assignIds.includes(item[rowKey])));

    // 有排序字段-排序
    if (sortKey) {
      _assignList = _assignList.sort((a, b) => a[sortKey] - b[sortKey]);
      _unAssignList = _unAssignList.sort((a, b) => a[sortKey] - b[sortKey]);
    }

    setAssignList(_assignList);
    setUnAssignList(_unAssignList);
  }, [isEnableCache, itemListJson]);
  return {
    onSetDisplayItem,
    assignList,
    unAssignList,
  };
};
