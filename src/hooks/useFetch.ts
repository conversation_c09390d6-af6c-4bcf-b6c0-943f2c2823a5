import React, { useEffect, useState } from 'react';
import { fetchApi, IAxiosRequestConfig } from '@utils/http';

const DHR_SASS_PROXY = '/apis/common/proxy/lcpGw/tz_api/dhr_api/hcm/dhr-lcp-hcm';
/**
 * 网络请求钩子参数配置
 *
 * @export
 * @interface IApiHookConfigs
 */
export interface IApiHookConfigs<T> {
  /**
   * 网络请求配置
   *
   * @type {IAxiosRequestConfig}
   * @memberof IApiHookConfigs
   */
  configs?: IAxiosRequestConfig & { cacheExpire?: number; isImmediate?: boolean };
  /**
   * 网络请求成功回调方法
   *
   * @memberof IApiHookConfigs
   */
  onSuccess?: (response?: T) => T | Promise<T> | undefined;
  /**
   * 回调请求失败回调方法
   *
   * @memberof IApiHookConfigs
   */
  onError?: (response?: T) => T | Promise<T> | undefined;
}

/**
 * 网络请求钩子
 *
 * @template T
 * @param {IAxiosRequestConfig} [configs]
 * @param {(response?: T): T | Promise<T> | undefined} [onSuccess]
 * @param {(response?: T): T | Promise<T> | undefined} [onError]
 * @return {{ Data: T; Loading: boolean; setConfigs: React.Dispatch<React.SetStateAction<IApiHookConfigs<T>>> }}
 */
const useFetchAPI = <T>(
  configs?: IApiHookConfigs<T>['configs'],
  onSuccess?: (response?: T) => T | Promise<T> | undefined,
  onError?: (response?: T) => T | Promise<T> | undefined
): {
  Data?: T;
  Loading: boolean;
  setConfigs: React.Dispatch<React.SetStateAction<IApiHookConfigs<T>>>;
  runAction: (configsData?: IApiHookConfigs<T>['configs']) => void;
  reset: () => void; // 重置数据
} => {
  const [Data, setData] = useState<T>();
  const [Loading, setLoading] = useState<boolean>(false);
  const [_configs, setConfigs] = useState<IApiHookConfigs<T>>({ configs, onError, onSuccess });

  const runAction = (configsData?: IApiHookConfigs<T>['configs']) => {
    if (!_configs?.configs) return;
    setLoading(true);
    const { configs: fetchConfigs, onError: _onError, onSuccess: _onSuccess } = _configs;
    Object.hasOwnProperty.call(fetchConfigs, 'isImmediate') && delete fetchConfigs.isImmediate;
    fetchApi<T>({
      configs: {
        baseURL: DHR_SASS_PROXY,
        ...fetchConfigs,
        ...(configsData || {}),
      },
      setData: data => setData((data as Record<string, any>)?.data),
      setLoading,
      onSuccess: response => _onSuccess?.(response),
      onError: response => _onError?.(response),
    });
  };
  // 重置
  const reset = () => {
    setData(undefined);
    setLoading(false);
  };

  useEffect(() => {
    (!Object.hasOwnProperty.call(_configs?.configs, 'isImmediate') ||
      [undefined, null].includes(_configs?.configs.isImmediate) ||
      _configs?.configs.isImmediate) &&
      runAction();
  }, []);
  return { Data, Loading, setConfigs, runAction, reset };
};

export default useFetchAPI;
