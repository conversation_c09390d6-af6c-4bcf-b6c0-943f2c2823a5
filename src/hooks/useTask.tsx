import React, { useCallback, useRef } from 'react';
import { request } from '@utils/http';
import { Modal, Collapse } from 'antd';
import { ENUM_TASK_STATUS } from '@constants/task';
import { showSucNotification } from '@utils/tools';

const { Panel } = Collapse;

// 异常信息渲染
export const renderException = (data: IObject = {}) => {
  return (
    <div>
      <Collapse ghost>
        <Panel header={<div>{data.msg}</div>} key="1">
          <p>{data.exception || '-'}</p>
        </Panel>
      </Collapse>
    </div>
  );
};

interface ITask {
  handleAfterTaskStatus: (status: ENUM_TASK_STATUS) => void;
  queryTaskRequest: IObject; // 查询任务请求，包含url和params等参数
  reExecuteTaskRequest: IObject; // 重新执行任务请求, 包含url和params等参数
  containerRef: any;
}

export default function useTask({
  handleAfterTaskStatus,
  queryTaskRequest = {},
  reExecuteTaskRequest = {},
  containerRef,
}: ITask) {
  const taskErrModalRef = useRef(null);
  const taskPendingModalRef = useRef<{
    modal: any;
    status: ENUM_TASK_STATUS;
  }>();

  /** 销毁任务弹窗modal */
  const handleDestroyTaskModal = useCallback((destroyArr = []) => {
    // 进行中弹窗 如果有 - 销毁
    if (taskPendingModalRef?.current && destroyArr.includes('pending')) {
      taskPendingModalRef.current?.modal?.destroy();
      taskPendingModalRef.current = null;
    }
    // 异常弹窗 如果有 - 销毁
    if (taskErrModalRef.current && destroyArr.includes('error')) {
      taskErrModalRef.current.destroy();
      taskErrModalRef.current = null;
    }
  }, []);

  const handleGetTaskStatus = () => {
    request({
      ...queryTaskRequest,
      onSuccess: data => {
        if (!data || data?.status === ENUM_TASK_STATUS.DONE) {
          handleDestroyTaskModal(['error', 'pending']);
          handleAfterTaskStatus && handleAfterTaskStatus(data?.status);
          return;
        }
        /** 已经初始化过 */
        /** 执行中或者准备中 */
        if ([ENUM_TASK_STATUS.D0ING, ENUM_TASK_STATUS.FREE].includes(data.status)) {
          setTimeout(() => {
            handleGetTaskStatus();
          }, 3000);
          /**
           * 任务弹窗不存在 或者 任务弹窗存在但是状态不是空闲状态
           */
          if (
            !taskPendingModalRef.current ||
            (taskPendingModalRef.current?.status === ENUM_TASK_STATUS.FREE && data.status === ENUM_TASK_STATUS.D0ING)
          ) {
            handleDestroyTaskModal(['error', 'pending']);
            const taskModalInfo = Modal.info({
              title: '任务提示',
              className: 'taskExceptionModal', // 添加自定义的class名
              getContainer: containerRef.current,
              content: data.status === ENUM_TASK_STATUS.D0ING ? '任务正在执行中...' : '任务初始化中',
              closable: false,
              maskClosable: false,
              closeIcon: false,
              okText: '请稍后...',
              okButtonProps: {
                disabled: true,
              },
            });
            taskPendingModalRef.current = {
              modal: taskModalInfo,
              status: data.status,
            };
            return;
          }
        }
        /** 异常 */
        if (data.status === ENUM_TASK_STATUS.EXCEPTION) {
          if (!taskErrModalRef.current) {
            handleDestroyTaskModal(['pending']);
            const taskErrModalInfo = Modal.info({
              title: '任务执行异常提示',
              content: renderException(data),
              okText: '重新执行',
              className: 'taskExceptionModal', // 添加自定义的class名
              getContainer: containerRef.current,
              onOk: () => {
                request({
                  ...reExecuteTaskRequest,
                  data: {
                    taskId: data.taskId || data.id, // 重新执行任务
                    ...(reExecuteTaskRequest.data || {}),
                  },
                  onSuccess: () => {
                    showSucNotification('重新执行成功');
                    handleDestroyTaskModal(['error']);
                    handleGetTaskStatus();
                  },
                  onError: err => {
                    console.log('err', err);
                  },
                });
              },
              closable: false,
              maskClosable: false,
              closeIcon: false,
            });
            taskErrModalRef.current = taskErrModalInfo;
          }
        }
      },
      onError: err => {
        console.log('err', err);
      },
    });
  };

  return {
    handleGetTaskStatus,
  };

  // const tasks =
}
