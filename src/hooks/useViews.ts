import commonApis from '@apis/common';
import { request as fetchApi } from '@utils/http';
import { useCallback, useEffect, useState } from 'react';

export default (
  code: string
): {
  Loading: boolean;
  Data: string[];
} => {
  if (!code) {
    return {
      Data: [],
      Loading: false,
    };
  }

  const [loading, setLoading] = useState<boolean>(false);
  const [csbViews, setCsbViews] = useState<string[]>([]);

  const onFetchCsbViews = useCallback(code => {
    setLoading(true);
    fetchApi({
      ...commonApis.csbViews,
      params: {
        code,
      },
      onSuccess: res => {
        const btnViews: any = Object.values(res || {}).reduce((pre: any[], cur: any[]) => [...pre, ...(cur || [])], []);
        const newCsbViews = btnViews.map(({ btnCode }) => btnCode);
        setCsbViews(newCsbViews);
      },
    }).finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    code && onFetchCsbViews(code);
  }, [code]);

  return {
    Loading: loading,
    Data: csbViews,
  };
};
