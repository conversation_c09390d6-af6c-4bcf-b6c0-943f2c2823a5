import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useCallback, useState, useEffect, useRef, useMemo } from 'react';
import { Modal, Button, message } from 'antd';
import * as R from 'ramda';

import commonApis from '@apis/common';
import { request as fetchApi, request } from '@utils/http';

import UploadModal, { UploadModalParams } from '@components/UploadModal';

export const defaultApiCongiMap = {
  preview: undefined,
  upload: undefined,
  download: undefined,
  getInfo: undefined,
};

const IMG = ['bmp', 'jpg', 'jpeg', 'png', 'gif'];

const defaultFieldNameMap = {
  fileId: 'fileId',
  fileType: 'fileType',
  fileName: 'fileName',
};

export interface ApiConfigParams {
  /** 预览地址 */
  preview: {
    url: string;
  };
  /** 上传地址 */
  upload: {
    url: string;
    params: {
      catalogId: string;
      categoryId: string;
    };
    method: string;
  };
  /** 下载地址 */
  download: {
    url: string;
  };
  /** 获取附件信息 */
  getInfo: {
    method: string;
    url: string;
  };
}

/** 预览附件参数信息 */
interface FileParams {
  /** 附件类型 */
  fileType: string;
  /** 附件id */
  fileId: string;
  /** 附件名称 */
  fileName: string;
}

/** 字段映射 */
interface FileKeyNameMap {
  /** 附件id */
  fileId?: string;
  /** 附件名称 */
  fileName?: string;
  /** 附件类型 */
  fileType?: string;
}

interface DownloadFileTypes {
  /** 附件id */
  fileId: string;
}

interface ImgPerviewParams {
  /** 图片名称 */
  name: string;
  /** 图片地址 */
  previewUrl: string;
}

interface ImgPerviewProps extends ImgPerviewParams {
  open: boolean;
  /** 关闭弹窗后 */
  onAfterClose?: () => void;
}

interface AttachmentParams {
  /** 文件储存文件夹名称  - 默认存到files文件下 */
  uploadSaveFileName?: string;
  /** 是否手动触发请求 - 默认为否 自动 */
  manual?: boolean;
}

interface AttachmentResult {
  catalogId?: string;
  fileIds?: string[];
  fileId?: string | undefined;
}

interface FileCallbackType {
  /** classId */
  classId: string;
  /** classId */
  objClassId: string;
  /** pageId 页面id/预账号 */
  objId: string;
  /** appId */
  appId: string;
}

interface FileCallbackFetchType extends FileCallbackType {
  /** 附件list */
  fileList: {
    /** 附件id fileId */
    id: string;
    /** 附件名称 */
    fileName: string;
    /** 附件size */
    fileSize: number;
    /** 附件类型 */
    fileType: string;
    /** 附件id fileId */
    csbFileId: string;
  }[];
}

/** 上传回调的参数 */
interface UploadAttachmentCallbackParamsType {
  /** 外部传入 */
  callbackConfig?: FileCallbackType;
  /** 直接内部处理 - 加上需要的参数 */
  configs?: Record<string, any>;
}

interface AttachmentType {
  /** 上传相关配置 */
  uploadConfig: Partial<UploadModalParams>;
  /** 接口地址等相关配置 */
  apiCongiMap: ApiConfigParams;
  /** 上传弹窗组件 */
  opUpload: (config?: UploadModalParams) => void;
  /** 预览 */
  onPreview: (record: FileParams, fieldNamesMap?: FileKeyNameMap) => void;
  /** 下载 */
  onDownload: (record: DownloadFileTypes, fieldNamesMap?: FileKeyNameMap) => void;
  /* 获取下载地址 */
  getDownloadUrl: (fileId: string) => string;
  /** 根据附件id 获取附件信息 */
  onGetFileInfo: (fileIds: string[] | string) => any;
  /**
   * 附件上传完成后接口回调天舟云
   * params  file数据是否回调天舟云 默认：空(不回调) - 决定后端库里能不能查到数据
   *  - callbackConfig 回调必须的参数
   *  - configs 天舟云的configs 内部自动活动必要参数
   */
  onFileInfoCallbackLcp: (file, params?: UploadAttachmentCallbackParamsType) => Promise<boolean>;
  /**
   * 接口上传附件
   * 注意：是否需要回调到天舟云
   * params  file数据是否回调天舟云 默认：空(不回调) - 决定后端库里能不能查到数据
   *  - callbackConfig 回调必须的参数
   *  - configs 天舟云的configs 内部自动活动必要参数
   * */
  onUploadAttachment: (
    file,
    params?: UploadAttachmentCallbackParamsType,
    fetchConfig?: Record<string, any>
  ) => Promise<AttachmentResult>;
  /** 手动触发获取附件信息 */
  runAsync: (path?: string) => Promise<ApiConfigParams>;
  /** 如果使用了缓存的apiConfig 请使用这个fn更新一下内部的apiConfig 避免内部的apiConfig空值 */
  onUpdateApiCongiMap: (newApiCongiMap: ApiConfigParams) => void;
}

const ImgPerview: React.FC<ImgPerviewProps> = ({ name, open, previewUrl, onAfterClose }) => {
  const [visible, setVisible] = useState<boolean>(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  useEffect(() => {
    return () => {
      onAfterClose?.();
    };
  }, []);

  const handleDownImg = useCallback(url => {
    const link = document.createElement('a');
    link.href = url;
    link.download = 'downloaded_image.jpg'; // 设置下载的文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  return (
    <Modal
      width={500}
      title={name}
      open={visible}
      onCancel={() => setVisible(false)}
      afterClose={() => onAfterClose?.()}
      footer={
        <Button type="primary" onClick={() => handleDownImg(previewUrl)}>
          下载
        </Button>
      }
      style={{ top: 20 }}
      bodyStyle={{ maxHeight: 'calc(100vh - 40px - 40px - 48px)' }}
    >
      <img alt={name} width="100%" height="auto" src={previewUrl} />
    </Modal>
  );
};

function useAttachment(params: AttachmentParams = {}): AttachmentType {
  const { uploadSaveFileName, manual = false } = params;
  const containerRef = useRef(null);
  const uploadContainerrRef = useRef(null);
  const apiConfigRef = useRef(defaultApiCongiMap);
  const [apiCongiMap, setApiCongiMap] = useState<ApiConfigParams>(defaultApiCongiMap);

  /** 更新apiCongiMap配置 - 兼容使用缓存的附件信息而没有自动调用附件信息的场景 */
  const handleUpdateApiCongiMap = useCallback((newApiCongiMap: ApiConfigParams) => {
    apiConfigRef.current = newApiCongiMap;
    setApiCongiMap(newApiCongiMap);
  }, []);

  /**
   * 获取附件地址信息
   * path 附件 储存地址 默认在files文件下
   */
  const onFetchUploadUrl = useCallback(
    (path?: string): Promise<ApiConfigParams> =>
      new Promise((resolve, reject) => {
        const savePath = path ? path : 'files';
        fetchApi({
          ...commonApis.apiCongiMapData,
          params: {
            classId: savePath,
          },
          onSuccess: res => {
            handleUpdateApiCongiMap(res || defaultApiCongiMap);
            resolve(res);
          },
          onError: reject,
        });
      }),
    []
  );

  /** body创建一个div */
  const creatElement = useCallback(() => {
    const element = document.createElement('div'); // 创建一个新的 div 元素
    document.body.appendChild(element); // 将该 div 元素添加到 body 中
    containerRef.current = element;
    return element;
  }, []);

  /** body创建一个div */
  const creatUploadElement = useCallback(() => {
    const element = document.createElement('div'); // 创建一个新的 div 元素
    document.body.appendChild(element); // 将该 div 元素添加到 body 中
    uploadContainerrRef.current = element;
    return element;
  }, []);

  useEffect(() => {
    !manual && onFetchUploadUrl(uploadSaveFileName);
  }, [uploadSaveFileName, manual]);

  /** 打开预览图片弹窗 */
  const onRenderPerviewModal = useCallback((params: ImgPerviewParams) => {
    let container = containerRef.current;
    if (!container) {
      container = creatElement();
    }

    const handleUnmount = () => {
      reactUnmount(container);
    };

    reactRender(<ImgPerview onAfterClose={handleUnmount} open={true} {...params} />, container);
  }, []);

  /** 打开上传弹窗 */
  const onRenderUploadModal = useCallback((params: UploadModalParams) => {
    const { children, ...props } = params;
    let container = uploadContainerrRef.current;
    if (!container) {
      container = creatUploadElement();
    }

    const handleUnmount = () => {
      reactUnmount(container);
    };

    reactRender(
      <UploadModal onAfterClose={handleUnmount} {...props}>
        {children}
      </UploadModal>,
      container
    );
  }, []);

  /**
   * 预览地址
   * @params record FileParams
   * @params fieldNamesMap 字段名映射
   * @params propsApiCongiMap
   * */
  const onPreview = useCallback((record: FileParams, fieldNamesMap?: FileKeyNameMap) => {
    const FieldKeyNamesMap = {
      ...defaultFieldNameMap,
      ...(fieldNamesMap || {}),
    };
    const newApiCongiMap = apiConfigRef.current;
    const fileId = record[FieldKeyNamesMap.fileId];
    const fileType = record[FieldKeyNamesMap.fileType];
    const type = (fileType || '').split('/').pop();
    const fileName = record[FieldKeyNamesMap.fileName];
    const previewUrlPrefix = newApiCongiMap.preview?.url || '';
    const downloadUrlPrefix = newApiCongiMap.download?.url || '';
    if (IMG.includes(type)) {
      const previewUrl = `${downloadUrlPrefix}/${fileId}`;
      return onRenderPerviewModal({ name: fileName, previewUrl });
    }
    window.open(`${previewUrlPrefix}/${fileId}`);
  }, []);

  /* 获取下载地址 */
  const getDownloadUrl = useCallback((fileId: string) => {
    const newApiCongiMap = apiConfigRef.current;
    const downloadUrlPrefix = newApiCongiMap.download?.url || '';
    const downloadUrl = `${downloadUrlPrefix}/${fileId}`;
    return downloadUrl;
  }, []);

  /** 下载 */
  const onDownload = useCallback((record: DownloadFileTypes, fieldNamesMap?: FileKeyNameMap) => {
    const FieldKeyNamesMap = {
      ...defaultFieldNameMap,
      ...(fieldNamesMap || {}),
    };
    const newApiCongiMap = apiConfigRef.current;
    const downloadUrlPrefix = newApiCongiMap.download?.url || '';
    const downloadUrl = `${downloadUrlPrefix}/${record[FieldKeyNamesMap.fileId]}`;
    window.open(downloadUrl);
  }, []);

  /** 上传 */
  const opUpload = useCallback((config = {}) => {
    const newApiCongiMap = apiConfigRef.current;
    const upload = newApiCongiMap.upload;
    const downloadUrlPrefix = newApiCongiMap.download?.url || '';
    const data = {
      ...(upload.params || {}),
      // 文件储存路径
      categoryId: `/dhr/common/${upload?.params?.categoryId || ''}`,
    };
    const uploadUrl = upload.url;

    const params: UploadModalParams = {
      action: uploadUrl,
      downUrlPrefix: downloadUrlPrefix,
      uploadParams: {
        data,
      },
      ...config,
    };
    onRenderUploadModal(params);
  }, []);

  /** 附件信息回调到天舟云 */
  const onFetchFileCallback = useCallback(
    (params: Partial<FileCallbackFetchType>) =>
      new Promise(resolve => {
        request({
          ...commonApis.fileCallback,
          data: params,
          headers: {
            'x-app-id': params?.appId,
          },
          onSuccess: () => {
            resolve(true);
          },
          onError: () => {
            resolve(false);
          },
        });
      }),
    []
  );

  /**
   * 附件上传完后接口回调天舟
   *
   */
  const onFileInfoCallbackLcp = useCallback(
    (file, params?: UploadAttachmentCallbackParamsType): Promise<boolean> =>
      new Promise(resolve => {
        const newApiCongiMap = apiConfigRef.current;
        const upload = newApiCongiMap.upload;
        const categoryId = R.path(['params', 'categoryId'], upload);
        const callbackParams: Partial<FileCallbackFetchType> = {
          fileList: [
            {
              id: file.fileId,
              csbFileId: file.fileId,
              fileName: file.name,
              fileSize: file.size,
              fileType: file.type,
            },
          ],
        };
        const { callbackConfig, configs } = params;
        callbackConfig && Object.assign(callbackParams, callbackConfig || {});
        /** 直接传configs - 内部自动加上 */
        if (configs) {
          const { appId, classId, pageId } = configs?.context?.globalContext?.pageTools?.getDetailEngineProps?.() || {};
          if (!appId || !classId) {
            message.warning('附件信息回调失败，缺少appId和classId');
            return resolve(false);
          }
          Object.assign(callbackParams, { appId, classId, objClassId: classId, objId: pageId || categoryId });
        }
        onFetchFileCallback({
          objId: categoryId,
          ...callbackParams,
        }).then((isPass: boolean) => resolve(isPass));
      }),
    []
  );

  /**
   * 接口上传附件
   * isCallbackSave boolean file数据是否回调天舟云 默认：否 - 决定后端库里能不能查到数据
   * callbackConfig配置 FileCallbackType
   * */
  const onUploadAttachment = useCallback(
    (file, params?: UploadAttachmentCallbackParamsType, fetchConfig?: Record<string, any>): Promise<AttachmentResult> =>
      new Promise((resolve, reject) => {
        const newApiCongiMap = apiConfigRef.current;
        const upload = newApiCongiMap.upload;
        const categoryId = R.path(['params', 'categoryId'], upload);
        /** 文件储存目录 */
        const fileSaveDirectory = `/dhr/common/${categoryId || ''}`;
        const data = new FormData();
        data.append('files', file);
        data.append('categoryId', fileSaveDirectory);
        request({
          baseURL: '',
          url: upload.url,
          method: upload?.method || 'post',
          data,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          ...(fetchConfig || {}),
          onSuccess: async res => {
            if (params) {
              const fileId = R.path(['result', 'fileIds', 0], res);
              const isPass = await onFileInfoCallbackLcp(
                {
                  fileId,
                  name: file.name,
                  size: file.size,
                  type: file.type,
                },
                params
              );

              if (isPass) {
                resolve(res?.result || {});
              } else {
                message.warning('上传失败，请重新上传');
                reject(false);
              }
              return;
            }
            resolve(res?.result || {});
          },
          onError: reject,
        });
      }),
    []
  );

  /** 根据附件id 获取附件信息 */
  const onGetFileInfo = useCallback(
    (fileIds: string[] | string) =>
      new Promise((resolve, reject) => {
        let id = fileIds;
        if (typeof fileIds === 'string') {
          id = fileIds.split(',');
        }
        const newApiCongiMap = apiConfigRef.current;
        const getInfo = newApiCongiMap?.getInfo || {};
        fetchApi({
          baseURL: '',
          ...getInfo,
          data: {
            id,
          },
          onSuccess: resolve,
          onError: reject,
        });
      }),
    []
  );

  /** 上传相关配置 */
  const uploadConfig: Partial<UploadModalParams> = useMemo(() => {
    if (!apiCongiMap.upload) return {};
    const upload = apiCongiMap.upload;
    const downloadUrlPrefix = apiCongiMap.download?.url || '';
    const data = {
      ...(upload.params || {}),
      // 文件储存路径
      categoryId: `/dhr/common/${upload?.params?.categoryId || ''}`,
    };
    const uploadUrl = upload.url;
    const uploadData: UploadModalParams = {
      action: uploadUrl,
      downUrlPrefix: downloadUrlPrefix,
      uploadParams: {
        data,
      },
    };
    return uploadData;
  }, [apiCongiMap]);

  return {
    // 上传相关配置
    uploadConfig,
    /** apiCongiMap */
    apiCongiMap,
    /** 弹窗-上传附件 */
    opUpload,
    /** 预览 */
    onPreview,
    /** 下载 */
    onDownload,
    /** 获取下载地址 */
    getDownloadUrl,
    /** 获取附件信息 */
    onGetFileInfo,
    /**
     * 附件上传完成后接口回调天舟云
     */
    onFileInfoCallbackLcp,
    /**
     * 接口上传附件
     * 注意：是否需要回调到天舟云
     * params  file数据是否回调天舟云 默认：空(不回调) - 决定后端库里能不能查到数据
     *  - callbackConfig 回调必须的参数
     *  - configs 天舟云的configs 内部自动活动必要参数
     * */
    onUploadAttachment,
    /** 手动触发获取附件信息 */
    runAsync: onFetchUploadUrl,
    /** 如果使用了缓存apiConfig 请使用这个fn更新一下内部的apiConfig 避免内部的apiConfig空值 */
    onUpdateApiCongiMap: handleUpdateApiCongiMap,
  };
}

export default useAttachment;
