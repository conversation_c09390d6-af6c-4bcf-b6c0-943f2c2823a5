import { useEffect, useState } from 'react';
import commonApis from '@apis/common';
import { request } from '@utils/http';

const X_APP_ID = '1269620726624013b98cf4baadaa759f';

/**
 *  根据字典编码获取字典数据
 *
 * @param {string[]} codes 字典编码
 * @return {*}
 */
export default (codes: string[]): Record<string, any> => {
  if (!codes || codes.length <= 0) {
    return {};
  }
  const [dicts, setDicts] = useState<Record<string, any>>({});
  const onFetchDicts = async (_codes: string[]) => {
    const res = await request({
      ...commonApis.dictList,
      data: _codes,
      headers: {
        'x-app-id': X_APP_ID,
      },
    });
    const dicts = res?.content || {};
    setDicts(dicts);
  };
  useEffect(() => {
    onFetchDicts(codes);
  }, [JSON.stringify(codes)]);
  return dicts;
};
