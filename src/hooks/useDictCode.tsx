import { useEffect, useState, useMemo } from 'react';
import { DICT_CODE_MAP_ID } from '@constants/common';
import Loader from '@cvte/resource-center-sdk';

/**
 * 根据字典分类ID获取该分类下的所有字典数据集
 *
 * @param {string} dictId 字典分类ID
 * @return {*}
 */
export default (codes: string[]) => {
  if (!codes && codes.length <= 0) {
    return {};
  }

  const codesStr = JSON.stringify(codes);
  const [dicts, setDicts] = useState<Record<string, any>>({});

  const idMapCode = useMemo(
    () => Object.keys(DICT_CODE_MAP_ID).reduce((pre, cur) => ({ ...pre, [DICT_CODE_MAP_ID[cur]]: cur }), {}),
    []
  );

  const onFetchDicts = async () => {
    const dicRes = await new Promise(resolve => {
      const loader = new Loader({
        appName: 'tz-dev',
        name: 'tz-system',
        env: `${document.getElementById?.('ENV')?.getAttribute?.('value')}` as any,
      });

      loader
        .load('cirDictionary.Entity', {
          mode: 'page',
          useShared: true,
        })
        .then(({ default: DictEntity }) => {
          const result = {};
          const errKeyMap = {};
          // 最大重试次数
          const RETRY_MAX_COUNT = 10;
          const queueList = [...codes];
          while (queueList.length > 0) {
            const code = queueList.pop();
            (DictEntity as any)
              ?.fetchItemList?.(code)
              .then(res => {
                result[idMapCode[code]] = res || [];
                const isEnd = Object.keys(result).length === codes.length;
                isEnd && resolve(result);
              })
              .catch(() => {
                // 小于重试次数 继续加入请求队列
                if (errKeyMap[code] <= RETRY_MAX_COUNT) {
                  errKeyMap[code] = (errKeyMap[code] || 0) + 1;
                  return queueList.push(code);
                }
                result[idMapCode[code]] = [];
                const isEnd = Object.keys(result).length === codes.length;
                isEnd && resolve(result);
              });
          }
        });
    });
    setDicts(dicRes);
  };
  useEffect(() => {
    onFetchDicts();
  }, [codesStr]);
  return dicts;
};
