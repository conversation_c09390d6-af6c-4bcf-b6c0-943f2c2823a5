import { useEffect, useRef, useCallback } from 'react';

export default (fn, limit, dep = []) => {
  const { current } = useRef({ fn, lastRan: 0 });

  useEffect(() => {
    current.fn = fn;
  }, [fn]);

  return useCallback(function throttledFunction(...args) {
    const now = Date.now();
    if (!current.lastRan || now - current.lastRan >= limit) {
      current.fn(...args);
      current.lastRan = now;
    }
  }, dep);
};
