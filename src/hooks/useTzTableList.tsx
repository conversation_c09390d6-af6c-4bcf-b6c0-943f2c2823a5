import React, { useState, useCallback, useEffect } from 'react';
import { request as fetchApi } from '@utils/http';
import commomApis from '@apis/common';
import * as R from 'ramda';

interface IParam {
  pageSize?: number;
  onlyMain?: boolean;
  keyType?: string;
  mainParamsGroups?: any[];
  appId: string;
  formClassId: string;
  dataTransform?: (data: any) => any;
  immediate?: boolean; // 是否立即请求
}

interface IOptions {
  key: string;
  label: string;
  dataIndex: string;
  [key: string]: any;
}

export default (
  params: IParam
): {
  // 返回到数据
  options: IOptions[];
  // 触发请求
  runAction: (data?: any) => void;
} => {
  const [options, setOptions] = useState<IOptions[]>([]);
  const { appId, formClassId, mainParamsGroups, keyType, onlyMain, pageSize, dataTransform, immediate } = params;
  const onFetchList = async (queryParams?: IObject) => {
    const _queryParams = queryParams || {};
    const _params = {
      pageSize: pageSize || 500,
      onlyMain: !R.isNil(onlyMain) ? onlyMain : true,
      keyType: keyType || 'CAMEL',
      mainParamsGroups: mainParamsGroups || [],
      appId,
      formClassId,
      ..._queryParams,
    };

    const res = await fetchApi({
      ...commomApis.formTableList,
      data: {
        ..._params,
      },
      headers: {
        'x-app-id': params.appId,
      },
    });
    const list = res?.content || [];
    const options = dataTransform
      ? dataTransform(list)
      : list?.map(({ mainData }) => ({
        ...mainData,
        value: mainData?.id,
        label: mainData?.cName,
        dataIndex: mainData?.id,
      })) || [];
    setOptions(options);
    return options;
  };

  useEffect(() => {
    // immediate未定义或者定义为immediate则 立即请求
    (R.isNil(immediate) || immediate) && onFetchList();
  }, [immediate]);

  return {
    options,
    runAction: onFetchList,
  };
};
