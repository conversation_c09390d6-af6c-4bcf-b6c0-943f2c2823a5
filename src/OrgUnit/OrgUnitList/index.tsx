import React, { useState, useEffect, useMemo, useRef } from 'react';
import { DatePicker, Spin } from 'antd';
import moment from 'moment';

import orgApis from '@apis/org';
import Message from '@cvte/cir-message';
import useFetch from '@hooks/useFetch';
import { request } from '@utils/http';
import { exportMultiExcel } from '@utils/excel';
import { showErrNotification, showSucNotification, toDateFormat } from '@utils/tools';

import OrgSort from '../containers/OrgSort';
import { IOriginalProps } from '@src/types';
import TreeSelect from '@components/TreeSelect';
import DrawerDetail from '@components/DrawerDetail';
import { LCPDetailTemplate } from '@components/LcpTemplate';
import WULIWholeTable, { DEFAULT_PAGE_SIZE } from '@components/WholeTable/AgGrid';

import '@src/styles/atom.less';
export interface IAppProps extends IOriginalProps {
  systemHelper: {
    history: any;
    menu: {
      currentUrl: string;
    };
  };
}

// 操作类型
enum OPT_TYPE {
  CREATE = 'CREATE',
  DETAIL = 'DETAIL',
  ADJUST = 'ADJUST',
  TRANSFER = 'TRANSFER',
  MERGE = 'MERGE',
  REVOKE = 'REVOKE',
}

const OrgUnitList: React.FC<IAppProps> = props => {
  const { systemHelper, ...restProps } = props;

  const [orgHid, setOrgHid] = useState<string>(undefined);
  const [orgNode, setOrgNode] = useState<any>(undefined);
  const [queryDate, setQueryDate] = useState<number>(moment().startOf('day').valueOf());
  const [selectedRows, setSelectedRows] = useState<{ cdefault?: string; id?: string }[]>([]);

  console.log('restPropsrestProps', restProps, systemHelper);
  const tzFormDetailRef = useRef(null);
  const treeRef = useRef(null);
  const editAgGridRef = useRef(null);

  /** 请求过程 */
  const {
    Data: _orgList,
    Loading: orgUnitLoading,
    runAction: runActionOfOrgList,
    reset: resetOrgList,
  } = useFetch({
    ...orgApis.orgUnitLsit,
    params: {},
    isImmediate: false,
  });

  const orgList = useMemo(
    () => (_orgList || []).map(k => ({ ...k, sortNo: k.sortNo || 0 })).sort((a, b) => a.sortNo - b.sortNo),
    [_orgList]
  );

  const onFetchOrgList = (data = {}) => {
    if (!orgHid) return;
    // const formData = WULIFormActions.get(filterFormKey)?.getValue();
    runActionOfOrgList({
      params: {
        hid: orgHid,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        // ...formData,
        ...data,
        queryDate,
      },
    });
  };

  const handleRestSelected = () => {
    editAgGridRef.current?.setSelected(
      selectedRows.map(k => ({
        rowKey: k.hid,
        isSelected: false,
      }))
    );
    setSelectedRows([]);
  };

  //  使 OPT_TYPE转为 'CREATE' | 'DETAIL'的形式
  const formTemplateConfig = (
    optType: OPT_TYPE.CREATE | OPT_TYPE.DETAIL | OPT_TYPE.ADJUST | OPT_TYPE.TRANSFER | OPT_TYPE.REVOKE | OPT_TYPE.MERGE
  ) => {
    // 表单默认数据映射
    const formDefaultDataMap = {
      [OPT_TYPE.CREATE]: {
        // 成立日期
        C_EST_DATE: moment().startOf('day').valueOf(),
        // 生效日期
        C_BEGIN_DATE: moment().startOf('day').valueOf(),
        // 行政维护信息 - 生效日期
        C_ADMINISTRATION_BEGIN_DATE: moment().startOf('day').valueOf(),
        // FEE维度信息 - 生效日期
        C_BUDGET_BEGIN_DATE: moment().startOf('day').valueOf(),
        // 成本中心维度信息 - 生效日期
        C_COST_CENTER_BEGIN_DATE: moment().startOf('day').valueOf(),
      },
    };

    return {
      appId: '7bd9d50b97454c2c805d2f56f803992e',
      classId: 'd9a9346159f04a50aed9a0f01f8f861e',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        contextData: {
          biz_opt_type: optType, // 表单方案
        },
        formDefaultData: {
          C_QUERY_DATE: moment(queryDate).startOf('day').format('YYYY-MM-DD'),
          ...(formDefaultDataMap[optType] || {}),
        },
      },
      orgHid: orgHid,
      selectedRows,
      queryDate,
      optType,
    };
  };
  // 统一操作打开抽屉
  const handleOpenDrawer = ({ eventPrefix, formConfig, drawerConfig }) => {
    let destroy;
    DrawerDetail.detail({
      width: '70%',
      title: '组织操作',
      ...drawerConfig,
      content: <LCPDetailTemplate {...formConfig} ref={tzFormDetailRef} queryDate={queryDate} />,
      onInit: _destroy => (destroy = _destroy),
    });
    // setTimeout(() => {
    //   destroy && destroy();
    // }, 3000);
    // 监听天舟云保存事件
    Message.on(eventPrefix, data => {
      console.log('收到天舟云保存事件：', eventPrefix, data);
      if (data.data.success) {
        // DrawerDetail.close();
        destroy && destroy();
        Message.remove(eventPrefix);
        // 刷新树数据
        treeRef.current?.fetchTreeData({
          isAuth: '1',
          status: '1',
          queryDate,
        });
        handleRestSelected();
        // 保存成功后刷新列表
        onFetchOrgList(); // 刷新列表
      }
    });
  };

  // 新增或调整
  const handleCreate = (optType: OPT_TYPE.CREATE | OPT_TYPE.ADJUST) => {
    if (selectedRows.length > 1) {
      return showErrNotification('新增只运行选择一个上级');
    }
    const formConfig = formTemplateConfig(optType);
    // const eventPrefix = `${selectedRows[0]?.hid || formConfig.classId}_org_unit_saveHandle`;
    const isCreate = optType === OPT_TYPE.CREATE;
    const eventPrefix = isCreate
      ? `${formConfig.classId}_org_unit_saveHandle`
      : `${selectedRows[0]?.id}_org_unit_saveHandle`;
    if (!isCreate) {
      if (selectedRows.length !== 1) {
        return showErrNotification('请选择一条数据进行操作');
      }
      formConfig.pageId = selectedRows[0].id;
    }
    handleOpenDrawer({
      eventPrefix,
      formConfig,
      drawerConfig: {
        title: isCreate ? `新增组织` : `调整组织-${selectedRows[0]?.name}`,
      },
    });
  };

  const selectedKeys = useMemo(() => {
    return selectedRows.map(k => k.hid);
  }, [selectedRows]);

  // 转移 || 并转
  const handleTransfer = (optType: OPT_TYPE.TRANSFER | OPT_TYPE.MERGE) => {
    if (selectedKeys.includes(orgHid) && selectedKeys.length > 1) {
      return showErrNotification('所选组织不允许包含上下级关系，请重新选择！');
    }
    const formConfig = formTemplateConfig(optType);
    const typeMappings = {
      [OPT_TYPE.TRANSFER]: {
        title: '转移组织',
        eventPrefix: `${formConfig.classId}_org_unit_transfer_saveHandle`,
      },
      [OPT_TYPE.MERGE]: {
        title: '并转组织',
        eventPrefix: `${formConfig.classId}_org_unit_merge_saveHandle`,
      },
    };

    const eventPrefix = typeMappings[optType].eventPrefix;
    formConfig.transferIds = selectedRows.map(k => k.hid).join(',');
    if (selectedRows.length === 0) {
      return showErrNotification('请选择数据进行操作');
    }
    handleOpenDrawer({
      eventPrefix,
      formConfig,
      drawerConfig: {
        title: typeMappings[optType].title,
      },
    });
  };

  // 撤销
  const handleRevoke = () => {
    // 如果包含当前组织 并且还有下级组织则不允许操作
    if (selectedKeys.includes(orgHid) && selectedKeys.length > 1) {
      return showErrNotification('所选组织不允许包含上下级关系，请重新选择！');
    }
    const formConfig = formTemplateConfig(OPT_TYPE.REVOKE);
    const eventPrefix = `${formConfig.classId}_org_unit_revoke_saveHandle`;
    formConfig.transferIds = selectedRows.map(k => k.hid).join(',');
    if (selectedRows.length === 0) {
      return showErrNotification('请选择数据进行操作');
    }
    handleOpenDrawer({
      eventPrefix,
      formConfig,
      drawerConfig: {
        title: '撤销组织',
      },
    });
  };

  // 查看详情
  const handleViewDetail = (data: Record<string, any>) => {
    const formConfig = {
      ...formTemplateConfig(OPT_TYPE.DETAIL),
      pageId: data.id,
      pageName: data.name,
    };
    DrawerDetail.detail({
      width: '70%',
      title: `行政组织查看-${data.name}`,
      content: <LCPDetailTemplate {...formConfig} queryDate={queryDate} />,
    });
  };

  const handleOrder = () => {
    const readySortData = orgList.filter(item => item.hid !== orgHid);
    if (readySortData.length === 0) {
      return showErrNotification(`${orgNode.name}下无可排序数据`);
    }

    // 排序
    let destroy;
    const onConfirmSort = data => {
      request({
        ...orgApis.orgSort,
        data: data.map((item, index) => ({
          orgHid: item.hid,
          sortNo: index + 1,
        })),
        onSuccess: () => {
          showSucNotification('排序成功');
          destroy?.();
          onFetchOrgList();
        },
      });
    };
    DrawerDetail.detail({
      width: '30%',
      title: '排序下属部门',
      content: <OrgSort dataList={readySortData} onConfirm={onConfirmSort} orgNode={orgNode} />,
      onInit: _destroy => (destroy = _destroy),
    });
  };

  const columns = [
    {
      title: '组织名称',
      key: 'name',
      dataIndex: 'name',
      render: ({ data, value }) => <a onClick={() => handleViewDetail(data)}>{value}</a>,
    },
    {
      title: '成立日期',
      key: 'estDate',
      dataIndex: 'estDate',
      render: ({ value }) => toDateFormat(value) || '-',
      formatteFn: text => toDateFormat(text) || '-',
    },
    {
      title: '机构类型',
      key: 'institutionType',
      dataIndex: 'institutionType',
    },
    {
      title: '组织类型',
      key: 'category',
      dataIndex: 'category',
    },
    {
      title: '上级组织',
      key: 'parentName',
      dataIndex: 'parentName',
    },
    {
      title: '组织维度',
      key: 'functions',
      dataIndex: 'functions',
    },
    {
      title: '关联法律实体',
      key: 'laborRelName',
      dataIndex: 'laborRelName',
    },
    {
      title: '组织状态',
      key: 'status',
      dataIndex: 'status',
      render: ({ value }) => (value === '1' ? '生效' : '失效'),
    },
    {
      title: '所在城市',
      key: 'city',
      dataIndex: 'city',
      // render: ({ value }) => value || '-',
    },
    {
      title: '工作地点',
      key: 'address',
      dataIndex: 'address',
    },
  ];

  const handleExport = async () => {
    const exportObj = {
      columns,
      data: null,
      sheetName: 'Sheet1',
    };

    if (selectedRows.length === 0) {
      const allData = await request({
        ...orgApis.orgUnitLsit,
        params: {
          hid: orgHid,
          pageNum: 1,
          pageSize: DEFAULT_PAGE_SIZE,
          queryDate,
        },
      });
      if (allData) {
        exportObj.data = allData || [];
      }
    } else {
      exportObj.data = selectedRows;
    }
    exportObj.data && exportMultiExcel([exportObj], `${orgNode.name}-组织列表.xlsx`);
  };
  const actionBtnItems: any[] = [
    {
      key: 'create',
      content: '新增',
      type: 'button',
      config: {
        type: 'primary',
        onClick: () => handleCreate(OPT_TYPE.CREATE),
      },
    },
    {
      key: 'adjust',
      content: '调整',
      type: 'button',
      config: {
        type: 'primary',
        onClick: () => handleCreate(OPT_TYPE.ADJUST),
      },
    },
    {
      key: 'transfer',
      content: '转移',
      type: 'button',
      config: {
        type: 'primary',
        onClick: () => handleTransfer(OPT_TYPE.TRANSFER),
      },
    },
    {
      key: 'revoke',
      content: '撤销',
      type: 'button',
      config: {
        type: 'primary',
        onClick: () => handleRevoke(),
      },
    },
    {
      key: 'merge',
      content: '并转',
      type: 'button',
      config: {
        type: 'primary',
        onClick: () => handleTransfer(OPT_TYPE.MERGE),
      },
    },
    {
      key: 'sort',
      content: '排序',
      type: 'button',
      config: {
        type: 'primary',
        onClick: () => handleOrder(),
      },
    },
    // {
    //   key: 'batchImport',
    //   content: '批量导入',
    //   type: 'button',
    //   config: {
    //     type: 'primary',
    //   },
    // },
    {
      key: 'export',
      content: '导出',
      type: 'button',
      config: {
        type: 'primary',
        onClick: () => handleExport(),
      },
    },
  ];

  useEffect(() => {
    if (orgHid) {
      onFetchOrgList();
    } else {
      resetOrgList();
    }
  }, [orgHid]);
  useEffect(() => {
    treeRef.current?.fetchTreeData({
      isAuth: '1',
      status: '1',
      queryDate,
    });
  }, [queryDate]);

  const onDimDataUpdate = data => {
    if (orgHid) {
      if (data?.some(k => k.hid === orgHid)) {
        return onFetchOrgList();
      }
      setOrgHid(undefined);
      setOrgNode(undefined);
      handleRestSelected();
    }
  };

  const handleSelectNode = (data, e, selectedNodes) => {
    setOrgHid(data?.[0]);
    setOrgNode(selectedNodes?.[0]);
    // 左侧树选了 要清空右侧已选的
    handleRestSelected();
  };
  console.log('orgListorgList', orgHid, orgList);
  return (
    <Spin spinning={orgUnitLoading}>
      <div className="flex">
        <div
          style={{
            width: '300px',
          }}
          className="pt-20 mr-20"
        >
          <TreeSelect
            ref={treeRef}
            fetchParams={{
              isAuth: '1',
              status: '1',
              queryDate,
            }}
            isImmediateFetch={false}
            onSelectNode={handleSelectNode}
            onDimDataUpdate={onDimDataUpdate}
            extraContent={
              <div>
                <DatePicker
                  placeholder="选择组织时间"
                  defaultValue={moment()}
                  format="YYYY-MM-DD"
                  className="w-full mb-10"
                  onChange={value => {
                    // console.log('选择时间', value.startOf('day').valueOf());
                    setQueryDate(value.startOf('day').valueOf());
                  }}
                />
              </div>
            }
          />
        </div>
        <div className="flex-1 ml-20 mt-20">
          <WULIWholeTable
            canSelect
            pagination={false}
            rowKey="hid"
            columns={columns}
            selectType="checkbox"
            data={orgList}
            action={actionBtnItems}
            onSelect={(newSelectedRow, isSelect, selectKeys, selectRows) => {
              setSelectedRows(selectRows);
            }}
          />
        </div>
      </div>
    </Spin>
  );
};
export default OrgUnitList;
