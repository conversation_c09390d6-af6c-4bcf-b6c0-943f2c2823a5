import React, { FC, useEffect, useState } from 'react';
import { ReactSortable } from 'react-sortablejs';
import { HolderOutlined } from '@ant-design/icons';
import { Button } from 'antd';

interface ItemType {
  hid: string;
  name: string;
  [key: string]: any;
}

interface TAppProps {
  dataList: ItemType[];
  orgNode: Record<string, any>;
  onConfirm: (dataList: any[]) => void;
}

const OrgSort: FC<TAppProps> = ({ dataList, orgNode, onConfirm }) => {
  const [list, setList] = useState<ItemType[]>([]);

  useEffect(() => {
    setList(dataList);
  }, []);

  return (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
        }}
      >
        <Button type="primary" size="small" onClick={() => onConfirm(list)}>
          确认
        </Button>
      </div>
      <p>拖拽【{orgNode.name}】下属部门，进行顺序调整</p>
      <ReactSortable list={list} setList={newList => setList([...newList])}>
        {list.map(item => (
          <div key={item.hid}>
            <HolderOutlined
              style={{
                marginRight: '6px',
                cursor: 'pointer',
              }}
            />
            {item.name}
          </div>
        ))}
      </ReactSortable>
    </div>
  );
};

export default OrgSort;
