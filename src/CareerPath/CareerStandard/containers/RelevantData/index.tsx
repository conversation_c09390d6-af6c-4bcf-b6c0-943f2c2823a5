import React, { useCallback, useEffect, useState } from 'react';
import { Button, Spin, Row, Col, Modal, message } from 'antd';
import filesize from 'filesize';

import { toDateFormat } from '@utils/tools';
import careerPathApis from '@apis/careerPath';
import { request as fetchApi } from '@utils/http';

import Block from '@components/Block';

import './style.less';

export interface IAppProps {
  modelId: string;
  opUpload: (params: any) => void;
  onPreview: (record: any, nameMap?: any) => void;
  onDownload: (record: any, nameMap?: any) => void;
}

const RelevantData: React.FC<IAppProps> = ({ modelId, opUpload, onDownload, onPreview }) => {
  const [list, setList] = useState<any[]>([]);
  const [pageLoading, setPageLoading] = useState<boolean>(false);

  // 获取相关资料
  const onFetchAttachmentList = useCallback(
    (isStartLoad = false) => {
      // 首次加载不需要loading
      !isStartLoad && setPageLoading(true);
      fetchApi({
        ...careerPathApis.standardAttachmentList,
        params: {
          modelId,
        },
        onSuccess(list) {
          setList(list || []);
        },
      }).finally(() => {
        !isStartLoad && setPageLoading(false);
      });
    },
    [modelId]
  );

  useEffect(() => {
    modelId && onFetchAttachmentList(true);
  }, [modelId]);

  // 上传附件
  const handleUploadFile = useCallback(() => {
    opUpload({
      onOk: (values, close, updateLoading) => {
        updateLoading(true);
        const files = values.files || [];
        const uploadParams = files.map(({ name: fileName, size: fileSize, type: fileType, fileId: csbFileId }) => ({
          fileName,
          fileSize,
          fileType,
          csbFileId,
          description: '',
        }));
        fetchApi({
          ...careerPathApis.standardAttachmentUpload,
          params: { modelId },
          data: uploadParams,
          onSuccess: () => {
            message.success('操作成功');
            close();
            onFetchAttachmentList();
          },
        }).finally(() => {
          updateLoading(false);
        });
      },
    });
  }, [modelId]);

  /** 删除 */
  const handleRemove = useCallback(
    record => {
      Modal.confirm({
        title: '提醒',
        content: `确定要删除附件 ${record.fileName} 吗？`,
        onOk: () =>
          new Promise((resolve, reject) => {
            fetchApi({
              ...careerPathApis.standardAttachmentDel,
              params: {
                modelId,
                csbFileId: record.csbFileId,
              },
              onSuccess: () => {
                resolve(true);
                message.success('操作成功');
                onFetchAttachmentList();
              },
              onError: reject,
            });
          }),
      });
    },
    [modelId]
  );

  const RightAttachmentActionBtn = useCallback(() => {
    return (
      <Button type="primary" onClick={handleUploadFile}>
        上传附件
      </Button>
    );
  }, []);

  return (
    <Block title="相关资料" subTitleClassName="dhr-career-standard-action-btn" subTitle={<RightAttachmentActionBtn />}>
      <Spin spinning={pageLoading}>
        <div>
          {list.map((fileItem, i) => {
            return (
              <Row key={i}>
                <Col span={20}>
                  <span>{fileItem.fileName}</span>
                  <span className="lcp-margin-left-10 lcp-margin-right-10">
                    {toDateFormat(fileItem.crtTime, 'YYYY/MM/DD HH:mm')}
                  </span>
                  <span>{filesize(fileItem.fileSize, { bit: true })}</span>
                </Col>
                <Col span={4} style={{ textAlign: 'right' }}>
                  <a onClick={() => onDownload(fileItem, { fileId: 'csbFileId' })}>下载</a>
                  <a
                    className="lcp-margin-left-10 lcp-margin-right-10"
                    onClick={() => onPreview(fileItem, { fileId: 'csbFileId' })}
                  >
                    预览
                  </a>
                  <a style={{ color: 'var(--adm-color-danger)' }} onClick={() => handleRemove(fileItem)}>
                    删除
                  </a>
                </Col>
              </Row>
            );
          })}
        </div>
      </Spin>
    </Block>
  );
};

export default RelevantData;
