import React, { useCallback, useEffect, useState } from 'react';
import { Row, Col, Tooltip, Pagination } from 'antd';
import type { PaginationProps } from 'antd';
import filesize from 'filesize';

import { toDateFormat } from '@utils/tools';
import careerPathApis from '@apis/careerPath';
import { request as fetchApi } from '@utils/http';

import Block from '@components/Block';

import './style.less';

const defaultPageData = {
  list: [],
  pages: 1,
  total: 1,
  pageNum: 1,
  pageSize: 30,
};

interface paginationType {
  pages: number;
  total: number;
  pageNum: number;
  pageSize: number;
}

export interface IAppProps {
  modelId: string;
  onDownload: (record: any, nameMap?: any) => void;
  onPreview: (record: any, nameMap?: any) => void;
}
const HistoryAccessory: React.FC<IAppProps> = ({ modelId, onDownload, onPreview }) => {
  const [historyFileData, setHistoryFileData] = useState<any>(defaultPageData);

  // 获取历史附件
  const onFetchHistoryFiles = useCallback(
    (values = { pageNum: 1, pageSize: 30 }) => {
      fetchApi({
        ...careerPathApis.careerStandardHistoryFiles,
        params: {
          modelId,
          ...values,
        },
        onSuccess(res) {
          setHistoryFileData(res || {});
        },
      });
    },
    [modelId]
  );

  useEffect(() => {
    modelId && onFetchHistoryFiles();
  }, [modelId]);

  // 切换历史附件页码
  const handleChangePages = useCallback(
    (pageNum, pageSize) => {
      onFetchHistoryFiles({
        pageNum,
        pageSize,
      });
    },
    [modelId]
  );

  // 下载附件
  const handleDownload = useCallback(file => {
    onDownload(file, { fileId: 'csbFileId' });
  }, []);

  // 预览
  const handlePreview = useCallback(record => {
    const { fileType, fileId, fileName } = record;
    onPreview(
      {
        fileId,
        fileType,
        fileName,
      },
      { fileId: 'csbFileId' }
    );
  }, []);

  const showTotal: PaginationProps['showTotal'] = useCallback(total => `共 ${total} 条`, []);

  const list = historyFileData.list || [];
  const pagination: paginationType = historyFileData.pagination || defaultPageData;
  return (
    <Block title="历史附件">
      <div className="dhr-history-accessory">
        {list.map((listItem, i) => {
          return (
            <Row key={listItem.csbFileId}>
              <Col span={20}>
                <span>{listItem.fileName}</span>
                <span className="lcp-margin-left-10">{toDateFormat(listItem.crtTime, 'YYYY/MM/DD HH:mm')}</span>
                <span className="lcp-margin-left-10">{filesize(listItem.fileSize, { bit: true })}</span>
                {listItem.desc && (
                  <span>
                    <span className="dhr-update-reason-label">[变更原因]:</span>
                    <Tooltip title={listItem.desc} placement="top">
                      <span className="dhr-update-reason-tooltip">{listItem.desc}</span>
                    </Tooltip>
                  </span>
                )}
              </Col>
              <Col span={4} style={{ textAlign: 'right' }}>
                <a onClick={() => handleDownload(listItem)}>下载</a>
                <a className="lcp-margin-left-10" onClick={() => handlePreview(listItem)}>
                  预览
                </a>
              </Col>
            </Row>
          );
        })}
      </div>
      <Row justify="end" className="lcp-margin-top-30">
        <Col>
          <Pagination
            showSizeChanger
            showTotal={showTotal}
            total={pagination?.total}
            onChange={handleChangePages}
            current={pagination?.pageNum}
            pageSize={pagination?.pageSize}
            pageSizeOptions={['20', '30', '50', '100']}
          />
        </Col>
      </Row>
    </Block>
  );
};

export default HistoryAccessory;
