import { PlusCircleOutlined, MinusCircleOutlined, UpCircleOutlined, DownCircleOutlined } from '@ant-design/icons';
import { Modal, Form, Row, Col, Select, DatePicker, Input, Spin } from 'antd';
import React, { useCallback, useEffect, useMemo } from 'react';
import classnames from 'classnames';
import moment from 'moment';

import './style.less';

const { Item: FormItem } = Form;

export interface IAppProps {
  list: any[];
  open: boolean;
  options: any[];
  loading: boolean;
  onCancel: () => void;
  confirmLoading: boolean;
  onOk: (values: any) => void;
}
const AbilityItems: React.FC<IAppProps> = ({ loading, list = [], open, options, confirmLoading, onCancel, onOk }) => {
  const [form] = Form.useForm();
  const listJsonStr = JSON.stringify(list);

  useEffect(() => {
    const isInitData = (list || []).length > 0;
    if (isInitData) {
      list.map(listItem => {
        listItem['beginDate'] = listItem.beginDate && moment(listItem.beginDate);
        listItem['endDate'] = listItem.endDate && moment(listItem.endDate);
        return listItem;
      });
      form.setFieldsValue({
        abilityItems: list,
      });
    }
  }, [listJsonStr]);

  const disabledStartDate = (startValue, index) => {
    const endValue = form.getFieldValue(['abilityItems', index, 'endDate']);
    if (!startValue || !endValue) {
      return false;
    }
    return startValue.valueOf() > endValue.valueOf();
  };

  const disabledEndDate = (endValue, index) => {
    const startValue = form.getFieldValue(['abilityItems', index, 'beginDate']);
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.valueOf() <= startValue.valueOf();
  };

  /** 上移 */
  const handleMoveUp = useCallback((startIndex, move) => {
    if (startIndex === 0) {
      return;
    }
    const endIndex = startIndex - 1;
    return move(startIndex, endIndex);
  }, []);

  /** 下移 */
  const handleMoveDown = useCallback((startIndex, move, maxIndex) => {
    if (startIndex === maxIndex) {
      return;
    }
    const endIndex = startIndex + 1;
    return move(startIndex, endIndex);
  }, []);

  const handleOk = () => {
    form.validateFields().then(values => {
      onOk(values);
    });
  };

  const headers = useMemo(() => ['序号', '能力项分类', '能力项', '生效日期', '失效日期'], []);

  console.log('options===', options);

  return (
    <Modal
      open={open}
      width={900}
      title="设置能力项"
      onCancel={onCancel}
      maskClosable={false}
      confirmLoading={confirmLoading}
      onOk={handleOk}
    >
      <Spin spinning={loading}>
        <div className="dhr-career-standard-ability-items">
          <Form
            form={form}
            initialValues={{
              abilityItems: [
                {
                  itemClass: undefined,
                  itemName: undefined,
                  beginDate: undefined,
                  endDate: moment('2199-12-31'),
                },
              ],
            }}
          >
            <Row style={{ marginBottom: '10px' }}>
              {headers.map((title, index) => (
                <Col key={index} span={4} className="dhr-career-standard-header-col">
                  {title}
                </Col>
              ))}
            </Row>
            <Form.List name="abilityItems">
              {(fields, { add, remove, move }) => (
                <Row gutter={[8, 8]}>
                  {fields.map((field, index) => (
                    <React.Fragment key={index}>
                      <Col span={4} className="dhr-career-standard-header-col" style={{ lineHeight: '32px' }}>
                        {index + 1}
                      </Col>
                      <Col span={4}>
                        <FormItem
                          noStyle
                          name={[field.name, 'itemClass']}
                          rules={[{ required: true, message: '请选择' }]}
                        >
                          <Select
                            showSearch
                            allowClear
                            options={options}
                            placeholder="请选择"
                            optionFilterProp="name"
                            fieldNames={{ label: 'name', value: 'itemValue' }}
                          />
                        </FormItem>
                      </Col>
                      <Col span={4}>
                        <FormItem
                          name={[field.name, 'itemName']}
                          noStyle
                          rules={[
                            { required: true, message: '请填写' },
                            { max: 20, message: '文本长度不超过20个字符' },
                          ]}
                        >
                          <Input placeholder="请填写" />
                        </FormItem>
                      </Col>
                      <Col span={4}>
                        <FormItem
                          name={[field.name, 'beginDate']}
                          noStyle
                          rules={[{ required: true, message: '请选择' }]}
                        >
                          <DatePicker
                            placeholder="请选择"
                            disabledDate={startValue => disabledStartDate(startValue, index)}
                          />
                        </FormItem>
                      </Col>
                      <Col span={4}>
                        <FormItem
                          name={[field.name, 'endDate']}
                          noStyle
                          rules={[{ required: true, message: '请选择' }]}
                        >
                          <DatePicker placeholder="请选择" disabledDate={endDate => disabledEndDate(endDate, index)} />
                        </FormItem>
                      </Col>
                      <Col span={4}>
                        <PlusCircleOutlined
                          className="dhr-ability-item-icon-add"
                          onClick={() =>
                            add(
                              {
                                itemClass: undefined,
                                itemName: undefined,
                                beginDate: undefined,
                                endDate: moment('2199-12-31'),
                              },
                              index + 1
                            )
                          }
                        />
                        <MinusCircleOutlined className="dhr-ability-item-icon-del" onClick={() => remove(field.name)} />
                        <UpCircleOutlined
                          onClick={() => handleMoveUp(index, move)}
                          className={classnames('dhr-ability-item-icon-up', {
                            'dhr-ability-item-icon-disabled': index === 0,
                          })}
                        />
                        <DownCircleOutlined
                          onClick={() => handleMoveDown(index, move, fields.length - 1)}
                          className={classnames('dhr-ability-item-icon-down', {
                            'dhr-ability-item-icon-disabled': index === fields.length - 1,
                          })}
                        />
                      </Col>
                    </React.Fragment>
                  ))}
                </Row>
              )}
            </Form.List>
          </Form>
        </div>
      </Spin>
    </Modal>
  );
};

export default AbilityItems;
