import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { Table, Button, Form, Input } from 'antd';
import { ColumnProps } from 'antd/lib/table';
import { WULIForm } from '@cvte/wuli-antd';
import queryString from 'query-string';

import useDictCode from '@hooks/useDictCode';
import careerPathApis from '@apis/careerPath';
import useAttachment from '@hooks/useAttachment';
import { request as fetchApi } from '@utils/http';
import { exportMultiExcel, IExport } from '@utils/excel';
import { toDateFormat, ellipsisTag, showSucNotification } from '@utils/tools';

import Block from '@components/Block';
import FreeCard from '@components/FreeCard';
import RelevantData from './containers/RelevantData';
import AbilityItems from './containers/AbilityItems';
import XLSXImportModal from '@components/XLSXImportModal';
import HistoryAccessory from './containers/HistoryAccessory';

import { DICT_CODE_MAP_ID } from '@constants/common';

import './style.less';

const { TextArea } = Input;
const { Item: FormItem } = Form;

const baseFormKey = 'dhrCareerStandardFormKey';

const defaultInitValues = {
  detailList: undefined,
  pathGradeList: undefined,
  modelItemList: undefined,
  careerPathView: undefined,
};

interface StandardValuesType {
  detailList?: any[];
  pathGradeList?: any[];
  modelItemList?: any[];
  careerPathView?: Record<string, any>;
}

interface RouterParamsType {
  /** 模型id */
  modelId?: string;
  careerPathId?: string;
}

export interface IAppProps {}
const CareerStandard: React.FC<IAppProps> = props => {
  const [abilityItems, setAbilityItems] = useState<any[]>([]);
  const [abilityVisible, setAbilityVisible] = useState<boolean>(false);
  const [abilityLoading, setAbilityLoading] = useState<boolean>(false);
  const [abilityUpdateLoading, setAbilityUpdateLoading] = useState<boolean>(false);
  const [initValues, setInitValues] = useState<StandardValuesType>(defaultInitValues);
  const [templateValues, setTemplateValues] = useState<StandardValuesType>(defaultInitValues);

  const { opUpload, onDownload, onPreview } = useAttachment();

  const { DHR_COMMON_STATUS_TYPE = [], DHR_CAREERPATH_ITEM_CLASS = [] } = useDictCode([
    DICT_CODE_MAP_ID.DHR_COMMON_STATUS_TYPE,
    DICT_CODE_MAP_ID.DHR_CAREERPATH_ITEM_CLASS,
  ]);

  const { careerPathId, modelId } = queryString.parse(window.location.search) as RouterParamsType;

  const careerPathView = initValues.careerPathView || {};

  const detailList = initValues.detailList || [];
  const modelItemList = initValues.modelItemList || [];
  const pathGradeList = initValues.pathGradeList || [];

  const tableList = useMemo(() => {
    const dataSource = [];
    // 临时记录 levelId 不为空，gradeId 为空情况。10 代表有值， 00 代表无值
    const tmp = {};
    pathGradeList.forEach(
      ({ gradeId, levelId, gradePlanDetailName: gradeName, levelPlanDetailName: levelName }, index) => {
        const data = {
          id: index,
          gradeId,
          levelId,
          gradeName,
          levelName,
          rowSpan: 0,
        };
        if (/\.1$/g.test(gradeName)) data.rowSpan = pathGradeList.filter(v => v.levelId === levelId).length;
        modelItemList.forEach(({ id: itemId }) => {
          if (!tmp[`${levelId}_${itemId}`]) {
            tmp[`${levelId}_${itemId}`] = '00';
            let itemDescribe;
            try {
              itemDescribe = detailList.find(
                v => v.levelId === levelId && v.itemId === itemId && !v.gradeId
              ).requirement;
            } catch {}
            if (itemDescribe) {
              tmp[`${levelId}_${itemId}`] = '10';
              tmp[`${levelId}_${itemId}_requirement`] = itemDescribe;
            }
          }
          data[itemId] = itemId;
          // 需要合并单元格子
          if (tmp[`${levelId}_${itemId}`] === '10') {
            if (/\.1$/g.test(gradeName)) {
              data[`${gradeId}_${itemId}`] = tmp[`${levelId}_${itemId}_requirement`];
              data[`${gradeId}_${itemId}_rowSpan`] = data.rowSpan;
            } else {
              data[`${gradeId}_${itemId}_rowSpan`] = 0;
            }
          } else {
            // 不需要合并单元格
            data[`${gradeId}_${itemId}`] = (
              detailList.find(v => v.gradeId === gradeId && v.itemId === itemId) || {}
            ).requirement;
            data[`${gradeId}_${itemId}_rowSpan`] = 1;
          }
        });
        dataSource.push(data);
      }
    );
    return dataSource;
  }, [pathGradeList, modelItemList, detailList]);

  const statusNameMap = useMemo(
    () =>
      (DHR_COMMON_STATUS_TYPE || []).reduce(
        (pre, cur) => ({
          ...pre,
          [cur.itemValue]: cur.name,
        }),
        {}
      ),
    [DHR_COMMON_STATUS_TYPE]
  );

  const formFields: IFormItem[] = useMemo(
    () => [
      {
        label: '通道编号',
        type: 'input',
        key: 'code',
      },
      {
        label: '生效日期',
        type: 'custom',
        key: 'beginDate',
        render: data => toDateFormat(data.value) || '-',
      },
      {
        label: '状态',
        type: 'input',
        key: 'status',
        render: data => statusNameMap[data.value] || '-',
      },
      {
        label: '岗位族',
        type: 'input',
        key: 'jobNationName',
      },
      {
        label: '失效日期',
        type: 'custom',
        key: 'endDate',
        render: data => toDateFormat(data.value) || '-',
      },
      {
        label: '职业秘书',
        type: 'custom',
        col: 24,
        labelCol: 2,
        wrapperCol: 22,
        key: 'secretaryEmpNames',
        render: data => ellipsisTag(data.value, 10),
      },
      // {
      //   label: '关联岗位',
      //   type: 'custom',
      //   key: 'jobList',
      //   col: 24,
      //   labelCol: 2,
      //   wrapperCol: 22,
      //   render: data => {
      //     const names = (data.value || []).map(({ name }) => name).join(',');
      //     return names || '-';
      //   },
      // },
    ],
    [statusNameMap]
  );

  // 获取能力设置列表
  const onFetchCareerPathItemAbility = useCallback(() => {
    setAbilityLoading(true);
    fetchApi({
      ...careerPathApis.careerStandardItemAbility,
      params: {
        modelId,
      },
      onSuccess(list) {
        setAbilityItems(list || []);
      },
    }).finally(() => setAbilityLoading(false));
  }, [modelId]);

  // 获取职级信息/基本信息
  const onFetchModelDetailTable = useCallback(careerPathId => {
    fetchApi({
      ...careerPathApis.careerStandardDetail,
      params: {
        careerPathId,
      },
      onSuccess(res) {
        setInitValues(res || defaultInitValues);
      },
    });
  }, []);

  // 获取导入模板数据源
  const onFetchTemplateData = useCallback(careerPathId => {
    fetchApi({
      ...careerPathApis.getModalDetailExcel,
      params: {
        careerPathId,
      },
      onSuccess(res) {
        setTemplateValues(res || defaultInitValues);
      },
    });
  }, []);

  useEffect(() => {
    modelId && onFetchCareerPathItemAbility();
  }, [modelId]);

  useEffect(() => {
    careerPathId && onFetchTemplateData(careerPathId);
    careerPathId && onFetchModelDetailTable(careerPathId);
  }, [careerPathId]);

  const columns = useMemo(() => {
    const render = (itemId, row) => {
      const obj = {
        children: row[`${row.gradeId}_${itemId}`],
        props: { rowSpan: row[`${row.gradeId}_${itemId}_rowSpan`] },
      };
      return obj;
    };
    const list = modelItemList || [];
    const professional = list.filter(({ itemClass }) => itemClass === 'PROFESSIONAL');
    const general = list.filter(({ itemClass }) => itemClass === 'GENERAL');
    const columns: ColumnProps<any>[] = [
      {
        title: '职级',
        dataIndex: 'levelName',
        fixed: 'left',
        width: 80,
        render: (value, row) => {
          const obj = {
            children: value,
            props: { rowSpan: row.rowSpan },
          };
          return obj;
        },
      },
      { title: '职级等级', dataIndex: 'gradeName', fixed: 'left', width: 80 },
      {
        title: '专业能力',
        dataIndex: 'pro',
        children: professional.map((v, i) => ({
          title: v.itemName,
          dataIndex: v.id,
          render,
          width: 220,
        })),
      },
      {
        title: '通用能力',
        dataIndex: 'common',
        children: general.map((v, i) => ({
          title: v.itemName,
          dataIndex: v.id,
          render,
          width: 220,
        })),
      },
    ];
    return columns;
  }, [modelItemList]);

  // 打开设置能力项弹窗
  const handleAbility = useCallback(() => {
    setAbilityVisible(true);
  }, []);

  // 更新能力项数据
  const handleUpdateAbility = useCallback(
    ({ abilityItems }) => {
      setAbilityUpdateLoading(true);
      const newAbilityItems = [...abilityItems].map((abilityItem, index) => ({
        ...abilityItem,
        order: index,
      }));
      fetchApi({
        ...careerPathApis.careerStandardItemAbilityUpdate,
        params: {
          modelId,
          careerPathId,
        },
        data: newAbilityItems,
        onSuccess: () => {
          showSucNotification('操作成功');
          setAbilityVisible(false);
          onFetchCareerPathItemAbility();
        },
      }).finally(() => setAbilityUpdateLoading(false));
    },
    [careerPathId, modelId]
  );

  // 导入模版
  const templateColumns: any[] = useMemo(() => {
    const { modelItemList } = templateValues;
    const list = modelItemList || [];
    const professional = list.filter(({ itemClass }) => itemClass === 'PROFESSIONAL');
    const general = list.filter(({ itemClass }) => itemClass === 'GENERAL');
    const professionalColumns = professional.map((v, i) => ({
      width: 16,
      title: v.itemName,
      dataIndex: v.id,
    }));
    const generalColumns = general.map((v, i) => ({ width: 16, dataIndex: v.id, title: v.itemName }));
    const columns: ColumnProps<any>[] = [
      { width: 16, title: '职级', dataIndex: 'levelName' },
      ...professionalColumns,
      ...generalColumns,
    ];
    return columns;
  }, [templateValues]);

  const templateDataSource: any[] = useMemo(() => {
    const { pathGradeList, modelItemList, detailList } = templateValues;
    const list = [];
    (pathGradeList || []).forEach(gradeItem => {
      let colData = {};
      colData['levelName'] = gradeItem.levelPlanDetailName;
      (modelItemList || []).forEach(modelItem => {
        const target =
          (detailList || []).find(detailItem => {
            if (!/\./.test(gradeItem.levelPlanDetailName)) {
              return (
                !detailItem.gradeId && detailItem.levelId === gradeItem.levelId && modelItem.id === detailItem.itemId
              );
            } else {
              return detailItem.gradeId === gradeItem.levelId && modelItem.id === detailItem.itemId;
            }
          }) || {};
        colData[modelItem.id] = target.requirement || '';
      });
      list.push({ ...colData });
      colData = null;
    });
    return list;
  }, [templateValues]);

  // 导入职级标准
  const handleImport = useCallback(() => {
    XLSXImportModal.import({
      // 校验接口
      validateConfig: {
        fetchConfig: {
          ...careerPathApis.standardImportCheck,
          params: {
            modelId,
          },
        },
        // 格式化
        dataHandler: (list, { description }) => {
          const updateList = [];
          list.forEach(listItem => {
            const rows = listItem['importDTOs_rows'] || [];
            const itemNames = rows[0];
            const importDTOs = [];
            rows.slice(1).forEach(element => {
              const levelName = element[0];
              for (let i = 1; i < element.length; i++) {
                importDTOs.push({
                  levelName,
                  itemName: itemNames[i],
                  content: element[i] ? element[i] : '',
                });
              }
            });
            const updateParams = {
              importDTOs,
              objFileSaveDTO: {
                ...listItem.objFileSaveDTO,
                description,
              },
            };
            updateList.push(updateParams);
          });
          return updateList;
        },
      },
      importConfig: {
        fetchConfig: {
          ...careerPathApis.standardImport,
          params: {
            modelId,
          },
        },
      },
      // sheetConfigs: [
      // {
      //   names: templateColumns.map(({ dataIndex }) => dataIndex),
      // },
      // ],
      defineTemplate: () => {
        const exportArr: IExport[] = [
          {
            sheetName: 'Sheet1',
            data: templateDataSource,
            columns: templateColumns,
          },
        ];
        exportMultiExcel(exportArr, `${(careerPathView || {}).name || '模版'}.xlsx`);
      },
      content: (
        <div className="dhr-career-standard-import-reson">
          <FormItem name="description" label="变更原因及变更内容" rules={[{ required: true, message: '请填写' }]}>
            <TextArea rows={2} placeholder="请填写变更原因及变更内容简述" maxLength={200} allowClear />
          </FormItem>
        </div>
      ),
    });
  }, [modelId, templateDataSource, templateColumns, careerPathView]);

  const RightActionBtn = useCallback(() => {
    return (
      <>
        <Button onClick={handleAbility}>设置能力项</Button>
        <Button type="primary" className="lcp-margin-left-10" onClick={handleImport}>
          导入职级标准
        </Button>
      </>
    );
  }, [careerPathId, modelId, careerPathView, templateColumns, templateDataSource]);

  return (
    <div className="dhr-career-standard">
      <h2 className="lcp-margin-top-10">{careerPathView.name}</h2>
      <Block title="基本信息">
        <WULIForm
          wuliMode="view"
          formKey={baseFormKey}
          formItems={formFields}
          initialData={careerPathView}
          className="dhr-career-Standard-form"
          defaultLayout={{
            col: 8,
            labelCol: 6,
            wrapperCol: 16,
          }}
        />
      </Block>
      <Block title="职级标准" subTitleClassName="dhr-career-standard-action-btn" subTitle={<RightActionBtn />}>
        <FreeCard title="T-测试类-软件测试类-应用软件测试">
          <Table
            bordered
            size="small"
            columns={columns}
            pagination={false}
            dataSource={tableList}
            rowKey={record => record.id}
            style={{
              marginLeft: 2,
              // maxWidth: '100%',
              // minWidth: (modelItemList.length || 1) * 220 + 160,
            }}
            scroll={{
              y: 500,
              x: (modelItemList.length || 1) * 220 + 160,
            }}
          />
        </FreeCard>
      </Block>
      <RelevantData modelId={modelId} onPreview={onPreview} onDownload={onDownload} opUpload={opUpload} />
      <HistoryAccessory modelId={modelId} onPreview={onPreview} onDownload={onDownload} />
      <AbilityItems
        list={abilityItems}
        open={abilityVisible}
        loading={abilityLoading}
        onOk={handleUpdateAbility}
        confirmLoading={abilityUpdateLoading}
        options={DHR_CAREERPATH_ITEM_CLASS || []}
        onCancel={() => setAbilityVisible(false)}
      />
    </div>
  );
};

export default CareerStandard;
