import React, { useState, createContext, useMemo, useRef, useEffect } from 'react';
import { Steps, Button, Modal, Spin } from 'antd';
import BasicInfo from './contianers/BasicInfo';
import MainSettings from './contianers/MainSettings';
import SalarySettings from './contianers/SalarySettings';
import { request } from '@utils/http';
import careerPathApi from '@apis/careerPath';
import '@src/styles/atom.less';
import { showSucNotification } from '@utils/tools';
import queryString from 'query-string';

export interface IContext {
  currentStep?: number;
  careerPathDetail: IObject;
  setCareerPathDetail?: (data: IObject) => void;
  handleRefreshData?: () => void;
  handleSaveData?: (boolean: boolean) => Promise<boolean>;
}

export interface IAppProps {
  careerPathId?: string;
}

export const CareerPathContext = createContext<IContext>({
  currentStep: 0,
  careerPathDetail: {},
  setCareerPathDetail: () => { },
  handleRefreshData: () => { },
  handleSaveData: () => Promise.resolve(true),
}); // 创建上下文

const BaseInfo: React.FC<IAppProps> = props => {
  console.log('props=========', props);
  const { careerPathId: careerPathIdFromProps } = props;
  const [current, setCurrent] = useState(0);
  const [careerPathDetail, setCareerPathDetail] = useState<IObject>({});
  const [globalLoading, setGlobalLoading] = useState(false);
  const baseInfoRef = useRef(null);
  const mainSettingsRef = useRef(null);
  const salarySettingsRef = useRef(null);
  const [newCareerPathId, setNewCareerPathId] = useState<string | undefined>(undefined);

  const careerPathId = careerPathIdFromProps || newCareerPathId;

  const getCurrentStepRef = () => {
    return [baseInfoRef, mainSettingsRef, salarySettingsRef][current];
  };
  // 创建上下文
  const steps = [
    {
      title: '设置基础信息',

      // content: <BasicInfo />,
      // content: <BasicInfo />,
    },
    {
      title: '设置职衔、职等、职级',
      // content: <MainSettings />,
    },
    {
      title: '设置职级薪资',
      // content: <SalarySettings />,
    },
  ];

  const renderContent = (current: number) => {
    switch (current) {
      case 0:
        return <BasicInfo ref={baseInfoRef} />;
      case 1:
        return <MainSettings ref={mainSettingsRef} />;
      case 2:
        return <SalarySettings ref={salarySettingsRef} />;
      default:
        return null;
    }
  };

  const getCareerPathDetail = (careerPathId: string) => {
    request({
      ...careerPathApi.careerPathDetail,
      params: {
        careerPathId,
      },
    }).then(res => {
      setCareerPathDetail(res || {});
    });
  };

  useEffect(() => {
    careerPathId && getCareerPathDetail(careerPathId);
  }, [careerPathId]);

  const handleNext = async (isNext = true): Promise<boolean> => {
    return new Promise(async resolve => {
      const currentStepRef = getCurrentStepRef();
      if (currentStepRef && currentStepRef.current) {
        const data = await currentStepRef.current.collectData();
        if (!data) return;
        // 请求数据
        setGlobalLoading(true);
        request({
          ...careerPathApi.careerPathSave,
          data: {
            ...careerPathDetail,
            ...data,
          },
        })
          .then(res => {
            // getCareerPathDetail(careerPathDetail.careerPathId || res);
            // console.log('创建结果', res);
            showSucNotification('保存成功');
            if (!careerPathId) {
              const routerQueryParams = queryString.parse(window.location.search);
              const query = queryString.stringify({
                ...routerQueryParams,
                careerPathId: careerPathDetail.careerPathId || res,
              });
              props.systemHelper.history.replace({
                search: `?${query}`,
              });
            } else {
              getCareerPathDetail(careerPathId);
            }

            isNext && setCurrent(current + 1);
            !careerPathDetail.careerPathId && setNewCareerPathId(res);
            resolve(true);
          })
          .catch(err => {
            resolve(false);
          })
          .finally(() => {
            setGlobalLoading(false);
          });
      }
    });
  };

  const handlePrev = () => {
    setCurrent(current - 1);
  };

  // todo 冗余接口 防止后续需要
  const handleRefreshData = () => {
    // console.log('handleRefreshData');
  };
  return (
    <CareerPathContext.Provider
      value={{
        currentStep: current,
        careerPathDetail,
        setCareerPathDetail,
        handleRefreshData,
        handleSaveData: handleNext,
      }}
    >
      <Spin spinning={globalLoading}>
        <div className="relative">
          <Steps current={current} direction="horizontal" items={steps} labelPlacement="vertical" />
          <div>{renderContent(current)}</div>
          <div className="flex justify-end my-20">
            {/* <div className="flex justify-end mt-20 absolute right-20 bottom-50"> */}
            {/* 上一步、下一步、保存 */}
            {current > 0 && (
              <Button type="primary" className="mr-10" onClick={handlePrev}>
                上一步
              </Button>
            )}
            {current !== 2 && (
              <Button type="primary" className="mr-10" onClick={() => handleNext(true)}>
                下一步
              </Button>
            )}
            <Button type="primary" onClick={() => handleNext(false)}>
              保存
            </Button>
          </div>
        </div>
      </Spin>
    </CareerPathContext.Provider>
  );
};

export default BaseInfo;
