import React, { forwardRef, useImperativeHandle, useMemo, useRef, useEffect } from 'react';
// careerPathJobSaves
import { CareerPathContext } from '@src/CareerPath/CareerPathMain';

import { LCPDetailTemplate } from '@components/LcpTemplate';
import { showErrNotification } from '@utils/tools';

export interface IAppProps {}
// 以下组件修改为formRef组件

const BaseInfo: React.FC<IAppProps> = forwardRef((props, ref) => {
  const formRef = useRef<any>(null);
  const [formLoad, setFormLoad] = React.useState<boolean>(false);
  const { careerPathDetail } = React.useContext(CareerPathContext);

  useImperativeHandle(ref, () => ({
    collectData: async () => {
      const context = formRef.current?.RenderRef?.current?.getContext() ?? {};
      console.log('表单上下文', context);
      const validateRes = await context.form.validateFields();
      if (!validateRes.success) {
        return showErrNotification('请完善当前表单字段');
      }
      const formData = validateRes.value;
      const {
        C_NAME,
        C_BEGIN_DATE,
        C_END_DATE,
        C_TYPE,
        C_STATUS,
        C_DESC,
        TB_CAREER_PATH_JOB = [],
        TB_CAREER_PATH_SECRETARY = [],
      } = formData;
      const data = {
        name: C_NAME,
        beginDate: C_BEGIN_DATE,
        endDate: C_END_DATE,
        type: C_TYPE,
        status: C_STATUS,
        desc: C_DESC, //
        careerPathJobSaves: TB_CAREER_PATH_JOB.map(k => ({
          jobId: k.C_JOB_ID, // 岗位id
          careerPathJobId: k.__add ? undefined : k.ID, // 岗位id
        })), // 职级岗位
        secretarySaves: TB_CAREER_PATH_SECRETARY.map(k => ({
          empId: k.C_EMP_ID, // 员工id
          secretaryId: k.__add ? undefined : k.ID, // 岗位id
        })),
      };
      return data;
    },
  }));
  // 职级ranged
  const formTemplateConfig = useMemo(
    () => ({
      appId: '0858b14df02b4b198acd05c7c285122b',
      classId: 'f9d7e973e8e44a7f8b891fa64892e7ad',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        contextData: {
          formStep: '1',
        },
      },
    }),
    []
  );

  console.log('formRef', formRef);

  useEffect(() => {
    console.log('formRef', formRef);
    if (!careerPathDetail.careerPathId || !formLoad) {
      return;
    }
    const context = formRef.current?.RenderRef?.current?.getContext() ?? {};
    context.setFormData({
      C_NAME: careerPathDetail.name,
      C_BEGIN_DATE: careerPathDetail.beginDate,
      C_END_DATE: careerPathDetail.endDate,
      C_TYPE: careerPathDetail.type,
      C_STATUS: careerPathDetail.status,
      C_DESC: careerPathDetail.desc,
      TB_CAREER_PATH_JOB: careerPathDetail.careerPathJobViews?.map(k => ({
        C_JOB_ID: k.jobId,
        ID: k.careerPathJobId,
      })),
      TB_CAREER_PATH_SECRETARY: careerPathDetail.secretaryViews?.map(k => ({
        C_EMP_ID: k.empId,
        ID: k.secretaryId,
      })),
    });
    console.log('表单上下文', context);
  }, [careerPathDetail.careerPathId && formLoad]);
  return (
    <div>
      <LCPDetailTemplate
        {...formTemplateConfig}
        ref={formRef}
        onCompleted={ref => {
          setFormLoad(true);
        }}
      />
    </div>
  );
});

export default BaseInfo;
