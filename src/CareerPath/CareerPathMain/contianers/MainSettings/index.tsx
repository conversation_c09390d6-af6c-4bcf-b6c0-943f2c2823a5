import React, { useState, useContext, forwardRef, useImperativeHandle, useMemo, useRef, useEffect } from 'react';
import { CareerPathContext } from '@src/CareerPath/CareerPathMain';

import { LCPDetailTemplate } from '@components/LcpTemplate';
import { showErrNotification } from '@utils/tools';
import {
  MAIN_INFO_MAPPINGS,
  TB_CAREER_PATH_LEVEL_RANGE_MAPPINGS,
  TB_CAREER_PATH_GRADE_RANGE_MAPPINGS,
} from './formField';

export interface IAppProps { }

/**
 * @description: 表单数据转换，
 * @param {object} data 表单数据
 * @param {object} mappings 映射关系
 * @param {boolean} isSubmit 是否为提交模式数据转换
 * @returns {object} 转换后的数据
 * 如 C_TITLE_PLAN_ID字段转为对象的titlePlanId字段
 * isSubmit为fasle则为反转换
 *
 */
const transFormData = (data, mappings, isSubmit: boolean) => {
  const _value = {};
  Object.keys(mappings).forEach(k => {
    isSubmit ? (_value[k] = data[mappings[k]]) : (_value[mappings[k]] = data[k]);
  });
  return _value;
};

// 以下组件修改为formRef组件

const BaseInfo: React.FC<IAppProps> = forwardRef((props, ref) => {
  const formRef = useRef<any>(null);
  const [formLoad, setFormLoad] = useState<boolean>(false);
  const { careerPathDetail } = useContext(CareerPathContext);

  useImperativeHandle(ref, () => ({
    collectData: async () => {
      const context = formRef.current?.RenderRef?.current?.getContext() ?? {};
      console.log('表单上下文', context);
      const validateRes = await context.form.validateFields();
      if (!validateRes.success) {
        return showErrNotification('请完善当前表单字段');
      }
      const formData = validateRes.value;

      const baseInfo = transFormData(formData, MAIN_INFO_MAPPINGS, true);
      const { TB_CAREER_PATH_LEVEL_RANGE = [], TB_CAREER_PATH_GRADE_RANGE = [] } = formData;

      const data = {
        ...baseInfo,
        // 职别信息
        levelRangeSaves: TB_CAREER_PATH_LEVEL_RANGE.map(rowData => ({
          levelRangeId: rowData.__add ? undefined : rowData.ID,
          ...transFormData(rowData, TB_CAREER_PATH_LEVEL_RANGE_MAPPINGS, true),
        })),
        // 级等信息
        gradeRangeSaves: TB_CAREER_PATH_GRADE_RANGE.map(rowData => ({
          gradeRangeId: rowData.__add ? undefined : rowData.ID,
          ...transFormData(rowData, TB_CAREER_PATH_GRADE_RANGE_MAPPINGS, true),
        })),
      };
      return data;

      // console.log('ChildComponent is focused');
    },
  }));
  // 职级ranged
  const formTemplateConfig = useMemo(
    () => ({
      appId: '0858b14df02b4b198acd05c7c285122b',
      classId: 'f9d7e973e8e44a7f8b891fa64892e7ad',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        contextData: {
          formStep: '2',
        },
      },
    }),
    []
  );

  useEffect(() => {
    if (!careerPathDetail.careerPathId || !formLoad) {
      return;
    }
    const context = formRef.current?.RenderRef?.current?.getContext() ?? {};

    const initInfo = {
      // 主表单信息
      ...transFormData(careerPathDetail, MAIN_INFO_MAPPINGS, false),
      // 职别信息
      TB_CAREER_PATH_GRADE_RANGE: careerPathDetail.gradeRangeViews?.map(k => ({
        ID: k.gradeRangeId,
        ...transFormData(k, TB_CAREER_PATH_GRADE_RANGE_MAPPINGS, false),
      })),
      // 级等信息
      TB_CAREER_PATH_LEVEL_RANGE: careerPathDetail.levelRangeViews?.map(k => ({
        ID: k.levelRangeId,
        ...transFormData(k, TB_CAREER_PATH_LEVEL_RANGE_MAPPINGS, false),
      })),
    };
    context.setFormData(initInfo);
    console.log('表单上下文', initInfo);
  }, [careerPathDetail.careerPathId && formLoad]);
  return (
    <div>
      <LCPDetailTemplate
        {...formTemplateConfig}
        ref={formRef}
        // onInitData={ref => console.log('初始化完成', ref)}
        onCompleted={ref => {
          setFormLoad(true);
          console.log('步骤2表单初始化完成');
        }}
      />
    </div>
  );
});

export default BaseInfo;
