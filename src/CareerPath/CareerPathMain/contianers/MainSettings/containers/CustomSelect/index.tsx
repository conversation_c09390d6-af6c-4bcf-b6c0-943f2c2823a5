import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { InputNumber } from 'antd';

import payProcessApis from '@apis/payProcess';
import { request as fetchApi } from '@utils/http';

export interface IAppProps {
  value: number;
  ref: any;
  data: any;
  colDef: Record<string, any>;
  stopEditing: any;
}
const CustomNumberEditor: React.FC<IAppProps> = forwardRef((props, ref) => {
  const { value, colDef, stopEditing, data } = props;
  const [inputVal, setInputVal] = useState<number>(value);
  useImperativeHandle(ref, () => ({
    getValue: () => inputVal,
  }));

  const handleBlur = () => {
    stopEditing();
    const { dataIndex } = colDef || {};
    const { payRecordMaps } = data || {};
    const detail = payRecordMaps?.[dataIndex] || {};
    const { itemId, empId, itemName, processId } = detail;
    // fetchApi({
    //   ...payProcessApis.formulaTempValUpdate,
    //   data: {
    //     processId,
    //     itemId,
    //     empIds: [empId],
    //     tempValue: inputVal,
    //   },
    //   onSuccess: () => console.log(itemName, ':更新成功==', inputVal),
    // });
  };

  return <InputNumber className="w-full" value={inputVal} onChange={value => setInputVal(value)} onBlur={handleBlur} />;
});

export default CustomNumberEditor;
