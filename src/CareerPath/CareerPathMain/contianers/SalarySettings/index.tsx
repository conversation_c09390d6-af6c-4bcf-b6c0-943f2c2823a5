import { Row, Col, Modal, Button } from 'antd';
import React, { useState, useContext, forwardRef, useImperativeHandle, useMemo, useRef, useEffect } from 'react';
import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { SingleTable, XiooTableActions } from '@cvte/xioo-ag-table';
import type { ITableInfo } from '@cvte/xioo-ag-table/es/SingleTable/type';
import useTzTableList from '@hooks/useTzTableList';
import { CareerPathContext } from '@src/CareerPath/CareerPathMain';
import careerPathApis from '@apis/careerPath';
import { request } from '@utils/http';
import classnames from 'classnames';
import './style.less';
import { showSucNotification, toDateFormat } from '@utils/tools';
import XLSXImportModal from '@components/XLSXImportModal';
import { exportMultiExcel, IExport } from '@utils/excel';

const initStandardList = [];
// 薪资信息form
const SALARY_FORM = 'careerPathTitle';
// 关联城市Table
const RELATE_CITY_TABLE = 'relateCityTable';

// 薪资信息
const SALARY_INFO_TABLE = 'salaryInfo';

export interface IAppProps { }
const SalarySettings: React.FC<IAppProps> = forwardRef((props, ref) => {
  const [emptyStandardList, setEmptyStandardList] = useState(initStandardList);
  // const [salaryStandardCityViews, setSalaryStandardCityViews] = useState<any>([]);
  // const [SalaryViews, setSalaryViews] = useState<any>([]);
  const { careerPathDetail, handleSaveData } = useContext(CareerPathContext);
  const { careerPathId, salaryStandardViews = [] } = careerPathDetail;
  const [curStandard, setCurStandard] = useState<any>({});
  const standardList = [...emptyStandardList, ...salaryStandardViews];

  const collectData = async () => {
    const basicInfo = await WULIFormActions.get(SALARY_FORM).asyncValidate();
    const relateCity = await XiooTableActions.getTable(RELATE_CITY_TABLE)?.asyncValidate();
    const salaryInfo = await XiooTableActions.getTable(SALARY_INFO_TABLE)?.asyncValidate();
    console.log('薪资数据收集', basicInfo, relateCity, salaryInfo);
    const curIndex = standardList.findIndex(k => k.salaryStandardId === curStandard.salaryStandardId);
    console.log('curIndex', curIndex);
    const _curStandardData = {
      ...curStandard,
      ...basicInfo,
      salaryStandardId: curStandard.salaryStandardId,
      careerPathSalaryStandardCitySaves: relateCity.map(k => ({
        salaryStandardCityId: k.salaryStandardCityId,
        careerPathId,
        salaryStandardId: basicInfo.salaryStandardId,
        city: k.city,
      })),
      careerPathSalarySaves: salaryInfo.map(k => ({
        ...k,
        salaryId: k.salaryId,
        careerPathId,
        salaryStandardId: basicInfo.salaryStandardId,
        salaryLevel: k.salaryLevel,
      })),
      careerPathId,
    };
    const newStandardList = [...standardList];
    newStandardList[curIndex] = _curStandardData;

    // 其他原本的
    const finalList = newStandardList.map(k => {
      if (k.salaryStandardId === _curStandardData.salaryStandardId) {
        return k;
      }
      return {
        ...k,
        careerPathSalaryStandardCitySaves: (k.salaryStandardCityViews || []).map(city => ({
          ...city,
          salaryStandardId: k.salaryStandardId,
          careerPathId,
        })),
        careerPathSalarySaves: (k.careerPathSalaryViews || [])?.map(salary => ({
          ...salary,
          salaryStandardId: k.salaryStandardId,
        })),
      };
    });
    console.log('newStandardList', finalList);
    const data = {
      salaryStandardSaves: finalList,
    };
    return data;
  };

  useImperativeHandle(ref, () => ({
    collectData: collectData,
    // reset: () => {
    //   setCurStandard({});
    //   setEmptyStandardList([]);
    // },
  }));

  // const [currentDetail, setCurrentDetail] = useState<any>(null);
  // 获取行政区划
  const { options: cityList } = useTzTableList({
    appId: 'bac9d8edadd14879b4de05d519ecd086',
    formClassId: 'e7f0f0a5026e4e01992604fba23ef07c',
  });
  console.log('cityListcityList', cityList);

  // 获取职级标准

  const salaryFormItems: IFormItem[] = [
    {
      type: 'input',
      key: 'name',
      label: '薪资标准名称',
      required: true,
    },
    {
      type: 'datePicker',
      key: 'beginDate',
      label: '生效日期',
      required: true,
    },
  ];

  // 关联城市表格配置
  const relateCityColumnsTableInfo: ITableInfo = {
    dvProgNo: RELATE_CITY_TABLE,
    columnsList: [
      {
        columnName: '城市',
        columnNo: 'city',
        isEdit: true,
        flex: 1,
        columnType: 'Select',
      },
    ],
    initialData: [],
    columnSelectOptions: {
      city: cityList,
    },
    canEdit: true,
    showSettingColumn: true,
  };
  // 关联城市行数据操作配置
  const genCellSetting = (tableCode: string) => ({
    width: 50,
    pinned: 'right',
    actionList: ['delete'],
    actionEvent: {
      handleDelete: (data: any, { node }: any) => {
        XiooTableActions.getTable(tableCode)?.onRemoveRowItems(data);
      },
    },
  });
  // 新增信息表格配置
  const salaryColumnsTableInfo: ITableInfo = {
    dvProgNo: SALARY_INFO_TABLE,
    columnsList: [
      {
        columnName: '职衔',
        columnNo: 'titlePlanDetailName',
        flex: 1,
      },
      {
        columnName: '级别',
        columnNo: 'levelPlanDetailName',
        width: 80,
      },
      {
        columnName: '级等名称',
        columnNo: 'gradePlanDetailName',
        flex: 1,
      },
      {
        columnName: '职级顺序码',
        columnNo: 'gradePlanDetailOrder',
        flex: 1,
        // columnType: 'input',
      },
      {
        columnName: '对应最低薪资',
        columnNo: 'minimumSalary',
        // required: true,
        isEdit: true,
        columnType: 'InputNumber',
      },
      {
        columnName: '对应最高薪资',
        columnNo: 'highestSalary',
        // required: true,
        isEdit: true,
        columnType: 'InputNumber',
      },
      {
        columnName: '职级薪资生效日期',
        columnNo: 'beginDate',
        isEdit: true,
        columnType: 'DatePicker',
        // required: true,
        format: 'YYYY-MM-DD',
        formatteFn: text => toDateFormat(text) || '-',
      },
    ],
    initialData: [],
    canEdit: true,
  };

  // 收集
  // const collectData = async () => {
  //   const basicInfo = await WULIFormActions.get(SALARY_FORM).asyncValidate();
  //   const relateCity = await XiooTableActions.getTable(RELATE_CITY_TABLE)?.asyncValidate();
  //   const salaryInfo = await XiooTableActions.getTable(SALARY_INFO_TABLE)?.asyncValidate();
  //   console.log('薪资数据收集', basicInfo, relateCity, salaryInfo);
  // };

  const relateCitycellSetting = useMemo(() => genCellSetting(RELATE_CITY_TABLE), []);
  const salaryCellSetting = useMemo(() => genCellSetting(SALARY_INFO_TABLE), []);

  const initFormData = (data: IObject) => {
    const { name, beginDate, salaryStandardId, salaryStandardCityViews, careerPathSalaryViews } = data;
    WULIFormActions.get(SALARY_FORM).setValue({
      name,
      beginDate,
    });
    XiooTableActions.getTable(RELATE_CITY_TABLE)?.updateData(salaryStandardCityViews || []);
    XiooTableActions.getTable(SALARY_INFO_TABLE)?.updateData(careerPathSalaryViews || []);
  };

  // 获取职级标准模版
  const handelFetchSalaryStandardTpl = async () => {
    // const res = console.log('resresres', res);
    const res = await request({
      ...careerPathApis.salaryStandardEmptyTpl,
      params: {
        careerPathId: careerPathDetail.careerPathId,
      },
    });

    console.log('resresres', res);
    if (res) {
      setEmptyStandardList([res]);
      setCurStandard(res);
      initFormData(res);
    }
  };

  useEffect(() => {
    if (!careerPathDetail.careerPathId) {
      return;
    }
    if (salaryStandardViews.length > 0) {
      const firstStandard = salaryStandardViews[0];
      setCurStandard(firstStandard);
      initFormData(firstStandard);
      setEmptyStandardList([]);
      return;
    }
    // 请求标准
    handelFetchSalaryStandardTpl();
  }, [careerPathDetail.careerPathId, salaryStandardViews.length]);

  // 新增标准
  const handleAddStandard = () => {
    Modal.confirm({
      title: '提醒',
      content: `保存当前数据才能继续新增标准哦?`,
      okText: '保存',
      cancelText: '返回修改',
      onOk: async () => {
        // 保存当前页面内容
        const isSuccess = await handleSaveData(false);
        if (isSuccess) {
          // 清空当前页面内容
          WULIFormActions.get(SALARY_FORM).reset();
          XiooTableActions.getTable(RELATE_CITY_TABLE)?.updateData([]);
          XiooTableActions.getTable(SALARY_INFO_TABLE)?.updateData([]);
          // 重新请求标准
          handelFetchSalaryStandardTpl();
        }
      },
      onCancel: () => {
        // 切换页面
      },
    });
  };
  console.log('standardList', standardList);

  // 切换标准
  const handleSwitchStandard = (standard: IObject) => {
    if (!curStandard.salaryStandardId) return showSucNotification('请先保存当前标准再切换');
    setCurStandard(standard);
    WULIFormActions.get(SALARY_FORM).reset();
    initFormData(standard);
  };

  // 导入薪资数据
  const handleImportSalaryData = async () => {
    const salaryColumns = salaryColumnsTableInfo.columnsList.map(k => ({
      key: k.columnNo,
      title: k.columnName,
      dataIndex: k.columnNo,
      render: k.formatteFn && k.formatteFn,
    }));
    const salaryList = await XiooTableActions.getTable(SALARY_INFO_TABLE)?.getAllData();
    XLSXImportModal.import({
      isUpload: false,
      sheetConfigs: [
        {
          names: [
            'titlePlanDetailName',
            'levelPlanDetailName',
            'gradePlanDetailName',
            'gradePlanDetailOrder',
            'minimumSalary',
            'highestSalary',
            'beginDate',
          ],
        },
      ],
      onOk: (excelData, cb) => {
        const exportData = excelData.list[0]?.['Sheet1'];
        if (exportData.length > 0) {
          // 匹配原来数据，需职衔-级别-级等一致
          const newData =
            salaryList
              .map(salaryRow => {
                // let diffKey = ``
                const target = exportData.find(exportRow => {
                  return (
                    `${salaryRow.titlePlanDetailName}-${salaryRow.levelPlanDetailName}-${salaryRow.gradePlanDetailName}` ===
                    `${exportRow.titlePlanDetailName}-${exportRow.levelPlanDetailName}-${exportRow.gradePlanDetailName}`
                  );
                });
                return target
                  ? {
                    ...salaryRow,
                    ...target,
                  }
                  : undefined;
              })
              ?.filter(k => k) || [];

          XiooTableActions.getTable(SALARY_INFO_TABLE)?.updateData(newData);
          showSucNotification('导入成功');
          cb();
        }
      },
      defineTemplate: () => {
        const exportArr: IExport[] = [
          {
            sheetName: 'Sheet1',
            data: salaryList,
            columns: salaryColumns,
          },
        ];
        exportMultiExcel(exportArr, `${(careerPathDetail || {}).name || '模版'}.xlsx`);
      },
    });
  };

  return (
    <Row className="salarySettings">
      <Col span={4} className="text-center lcp-margin-top-20">
        <Button className="text-primary text-center mb-20 pointer" onClick={() => handleAddStandard()}>
          + 添加标准
        </Button>
        <div>
          {standardList.map(standard => (
            <div
              key={standard.salaryStandardId || 'new'}
              className={classnames(
                'mt-20 pointer',
                standard.salaryStandardId === curStandard.salaryStandardId ? 'preFlag text-primary text-bold' : ''
              )}
              onClick={() => {
                handleSwitchStandard(standard);
              }}
            >
              {standard.name || '未保存-新薪资标准'}
            </div>
          ))}
        </div>
      </Col>
      <Col span={18}>
        <WULIForm
          formItems={salaryFormItems}
          formKey={SALARY_FORM}
          initialData={{}}
          defaultLayout={{
            col: 8,
            labelCol: 6,
            wrapperCol: 16,
          }}
        />
        {/* 关联城市 */}
        <SingleTable
          tableInfo={relateCityColumnsTableInfo}
          hasPagination={false}
          tableHeight={200}
          cellSetting={relateCitycellSetting}
          toolExtraContent={{
            right: (
              <Button
                type="primary"
                // onClick={() => setSalaryStandardCityViews([...salaryStandardCityViews, { id: v4(), city: '' }])}
                onClick={() =>
                  XiooTableActions.getTable(RELATE_CITY_TABLE)?.onAddRowItems(
                    [{}],
                    XiooTableActions.getTable(RELATE_CITY_TABLE).getAllData()?.length || 0
                  )
                }
              >
                新增
              </Button>
            ),
            left: <h4 className="text-16 text-bold">关联城市</h4>,
          }}
        />
        {/* 薪资信息 */}
        <div className="mt-20"></div>
        <SingleTable
          tableInfo={salaryColumnsTableInfo}
          hasPagination={false}
          // tableHeight={200}
          cellSetting={salaryCellSetting}
          toolExtraContent={{
            right: (
              <Button type="primary" onClick={() => handleImportSalaryData()}>
                导入薪资数据
              </Button>
            ),
            left: <h4 className="text-16 text-bold">薪资信息</h4>,
          }}
        />
      </Col>
    </Row>
  );
});

export default SalarySettings;
