import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactCrop, { centerCrop, makeAspectCrop } from 'react-image-crop';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Upload, Button, message, Modal } from 'antd';
import { useEventAsync } from '@cvte/kylin-hooks';
import { PDFDocument } from 'pdf-lib';
import classnames from 'classnames';
import * as uuid from 'uuid';
import * as R from 'ramda';
import Big from 'big.js';

import useAttachment, { ApiConfigParams } from '@hooks/useAttachment';
import { isString, fileDimensionMap, isStandardSizePage } from '@utils/tools';

import { UPLOAD_LIST_TYPE, WULI_MODE } from '@constants/common';

import './style.less';
import 'react-image-crop/dist/ReactCrop.css';

const ImageTypeReg = /.(jpg|jpeg|png)$/i;

export interface IAppProps {
  value: any;
  onChange: (val) => void;
  configs: {
    wuliMode: 'view' | 'edit';
    containerType: 'form' | 'table';
    config: {
      baseConfig: {
        dictConfig: {
          /** 文件展示形式 - 默认text */
          listType: string;
          /** 是否允许多选文件 - 默认否 */
          multiple: boolean;
          /** 上传控件类型 - 默认按钮 */
          controlType: string;
          /** 文件格式限制列表 - 默认不限制 */
          limitFileTypes: string[];
          /** 文件上限数量 - 默认不限制 */
          maxCount: number | undefined;
          /** 文件大小 - 默认4M */
          fileSize: number | undefined;
          // pdf保存尺寸 - 默认无
          savePdfDimension: string | undefined;
          /** 是否启用自由裁剪 */
          isTailor: boolean | undefined;
          /** 自由裁剪比例 */
          aspect: number;
          /** 图片是否保存为PDF - 默认否 */
          imgSaveFileType: string | undefined;
        };
      };
    };
    context: {
      apiConfigMap?: ApiConfigParams;
      globalContext?: {
        pageTools?: {
          getDetailEngineProps?: () => Record<string, any>;
        };
      };
    };
  };
  data: {
    record: any;
    value: string | undefined;
    options: any[] | undefined;
    optionsMap: Record<string, any>;
  };
  events: {
    onEvent: (params) => any;
  };
}
const CustomUpload: React.FC<IAppProps> = props => {
  const { configs, onChange, data, events } = props;
  const wuliMode = configs.wuliMode;
  const isView = wuliMode === WULI_MODE.VIEW;
  const apiCongiMap = (configs?.context?.apiConfigMap || {}) as ApiConfigParams;
  const { onUpdateApiCongiMap, onPreview, onUploadAttachment } = useAttachment({ manual: true });

  const [files, setFiles] = useState<any[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const [previewImage, setPreviewImage] = useState('');
  const [saveCroppedLoading, setSaveCroppedLoading] = useState<boolean>(false);
  const [crop, setCrop] = useState({
    x: 0,
    y: 0,
    unit: 'px',
    width: 50,
    height: 50,
    aspect: 2 / 5,
  });

  const imgRef = useRef(null);
  const cropRef = useRef(null);
  const cacheFileRef = useRef(null);
  const areaPixelsRef = useRef(null);
  const fileValues = useRef<any[]>([]);
  // 标志已经加载过的附件
  const getFilesFlag = useRef<Record<string, any>>({});

  const { onEvent } = events || {};
  const { config, code } = configs || {};
  const { value, options, optionsMap, record } = data || {};

  const dictConfig: any = config?.baseConfig?.dictConfig || {};
  const { savePdfDimension, maxCount, fileSize, imgSaveFileType } = dictConfig;

  const isTailor = !!dictConfig.isTailor;
  const multiple = !!dictConfig.multiple;
  const aspect = dictConfig.aspect || 26 / 32;
  const listType = dictConfig.listType || 'text';
  const isImgSaveFileType = imgSaveFileType === '1';
  const limitFileTypes = dictConfig.limitFileTypes || [];
  const controlType = dictConfig.fileControlType || 'button';

  useEffect(() => {
    apiCongiMap && onUpdateApiCongiMap(apiCongiMap);
  }, [apiCongiMap]);

  const formatValue = useCallback(value => {
    let _value;
    if (isString(value)) {
      if (value === '') {
        _value = [];
      } else {
        _value = (value as string).split(',');
      }
    } else {
      _value = (value || []).concat();
    }
    // 去除重复
    return Array.from(new Set(_value)) as string[];
  }, []);

  const updateFilesByValue = useEventAsync(async () => {
    // 如果相同则不往下执行 - 则无需翻译
    const isValueEquals = R.equals(fileValues.current, formatValue(value));
    if (isValueEquals) {
      return;
    }
    const { files: _files, getFilesFlag: _getFilesFlag } = (await onEvent?.({
      type: 'getCache',
      attrCode: code,
      record,
    })) ?? {
      files,
      getFilesFlag: getFilesFlag.current,
    };

    fileValues.current = formatValue(value);
    const getOpt = (_optionsMap: Record<string, any>) => {
      return fileValues.current.reduce(
        (sum, cur) => {
          const item = _optionsMap?.[cur as string];
          if (isString(cur) && item) {
            sum.opt.push(item);
            sum.map[cur as string] = item;
            return sum;
          }
          return sum;
        },
        { opt: [], map: {} }
      );
    };

    if (!_files?.length) {
      const { opt: _opt, map: _map } = getOpt(optionsMap);
      await onEvent?.({ type: 'setCache', data: { files: _opt, getFilesFlag: _map }, attrCode: code, record });
      getFilesFlag.current = _map;
      setFiles(_opt);
      return;
    }
    const __getFilesFlag = { ...(_getFilesFlag || {}), ...(optionsMap || {}) };
    const { opt: _opt, map } = getOpt(__getFilesFlag);

    await onEvent?.({ type: 'setCache', data: { files: _opt, getFilesFlag: map }, attrCode: code, record });
    getFilesFlag.current = map;
    setFiles(_opt);
  });

  useEffect(() => {
    updateFilesByValue();
  }, [value, options, optionsMap]);

  const UploadComponent = useCallback(() => {
    if (listType === UPLOAD_LIST_TYPE.PICTURE_CARD) {
      return (
        <div>
          <PlusOutlined />
          <div style={{ marginTop: 8, fontSize: '12px' }}>拖拽或点击上传</div>
        </div>
      );
    }

    if (controlType === 'button') {
      return (
        <Button size="middle" style={{ width: '100%' }} icon={<UploadOutlined style={{ fontSize: '14px' }} />}>
          拖拽或点击上传
        </Button>
      );
    }
    return (
      <div className="dhr-custom-upload-card">
        <div>
          <PlusOutlined />
          <div className="lcp-margin-top-8" style={{ fontSize: '12px' }}>
            拖拽或点击上传
          </div>
        </div>
      </div>
    );
  }, [listType, controlType]);

  //
  const fileLimitConfig = useMemo(() => {
    if (limitFileTypes.length > 0) {
      const fileRegStr = limitFileTypes.join('|');
      const fileReg = new RegExp(`\\.(${fileRegStr})$`, 'i');
      return {
        reg: fileReg,
        tip: fileRegStr,
      };
    }

    return {
      reg: undefined,
      tip: undefined,
    };
  }, [limitFileTypes.length]);

  // 计算文件字节大小
  const formatFileSize = useCallback(bytes => {
    const b = new Big(bytes);
    let result;
    if (b.lt(1024)) {
      result = b.toString() + 'B'; // 字节
    } else if (b.lt(1024 ** 2)) {
      result = b.div(1024).toString() + ' KB'; // 千字节
    } else if (b.lt(1024 ** 3)) {
      result = b.div(1024 ** 2).toString() + 'MB'; // 兆字节
    } else {
      result = b.div(1024 ** 3).toString() + 'GB'; // 吉字节
    }
    // 如果结果是整数，则移除小数点
    return result.replace(/(\.0+)(?!\S)/, '');
  }, []);

  // 上传前
  const handleBeforeUpload = useCallback(
    async file => {
      // 文件格式校验
      if (fileLimitConfig.reg && !fileLimitConfig.reg?.test(file.name)) {
        message.error(`仅支持${fileLimitConfig.tip}格式的文件`);
        file.status = 'error';
        return Promise.reject(false);
      }

      // 文件大小校验
      const limitSize = fileSize ? fileSize : 4 * 1024 * 1024;
      if (file.size > limitSize) {
        const limitSizeText = formatFileSize(limitSize);
        message.error(`文件大小不可超过${limitSizeText}`);
        file.status = 'error';
        return Promise.reject(false);
      }

      /** 自由裁剪 */
      if (isTailor && ImageTypeReg.test(file.type)) {
        const reader = new FileReader();
        reader.onload = (e: any) => {
          setPreviewImage(e?.target?.result);
          setOpen(true);
        };
        cacheFileRef.current = file;
        reader.readAsDataURL(file);
        /**  不执行onChange -> 不展示 */
        return Promise.resolve(Upload.LIST_IGNORE);
      }

      // 如果是图片类型 需要将图片转为pdf
      if (file.type.includes('image') && isImgSaveFileType) {
        try {
          // 设置标准尺寸，默认为A4
          const standard = fileDimensionMap[savePdfDimension] || fileDimensionMap.A4;
          const { width: SAVE_WIDTH, height: SAVE_HEIGHT } = standard;

          const arrayBuffer = await file.arrayBuffer();
          const pdfDoc = await PDFDocument.create();
          const page = pdfDoc.addPage([SAVE_WIDTH, SAVE_HEIGHT]);

          // 根据图片类型嵌入
          let image;
          const extension = file.type.split('/').pop().toLowerCase();
          if (['jpg', 'jpeg'].includes(extension)) {
            image = await pdfDoc.embedJpg(arrayBuffer);
          } else if (['png'].includes(extension)) {
            image = await pdfDoc.embedPng(arrayBuffer);
          } else {
            message.error(`不支持的图像格式: ${extension}，仅支持jpg、jpeg和png`);
            return Promise.reject(false);
          }

          // 计算保持图片原始比例的尺寸
          const imgWidth = image.width;
          const imgHeight = image.height;

          // 计算缩放比例以使图像适合页面（减去边距）
          const margin = 40; // 页边距
          const maxWidth = SAVE_WIDTH - margin * 2;
          const maxHeight = SAVE_HEIGHT - margin * 2;

          let scale = 1;
          let scaledWidth = imgWidth;
          let scaledHeight = imgHeight;

          // 判断图片是否需要缩放
          if (imgWidth > maxWidth || imgHeight > maxHeight) {
            // 图片太大，需要缩小
            scale = Math.min(maxWidth / imgWidth, maxHeight / imgHeight);
            scaledWidth = imgWidth * scale;
            scaledHeight = imgHeight * scale;
          } else if (imgWidth < maxWidth / 4 && imgHeight < maxHeight / 4) {
            // 图片太小（小于页面的1/4），适当放大但不超过内容区域的一半
            scale = Math.min(maxWidth / 2 / imgWidth, maxHeight / 2 / imgHeight);
            scaledWidth = imgWidth * scale;
            scaledHeight = imgHeight * scale;
          }

          // 在页面中心绘制图像
          page.drawImage(image, {
            x: (SAVE_WIDTH - scaledWidth) / 2,
            y: (SAVE_HEIGHT - scaledHeight) / 2,
            width: scaledWidth,
            height: scaledHeight,
          });

          const pdfBytes = await pdfDoc.save();
          const pdfFile: any = new File([pdfBytes], `${file.name.split('.')[0]}.pdf`, { type: 'application/pdf' });
          pdfFile.uid = file.uid; // 保留原始 uid

          return Promise.resolve(pdfFile);
        } catch (error) {
          console.error('图片转PDF失败:', error);
          message.error('图片转PDF失败，请重新上传');
          return Promise.reject(false);
        }
      }

      // 保存pdf尺寸
      if (file.type.includes('pdf') && !!savePdfDimension) {
        try {
          // 提前检查配置和文件有效性
          const standard = fileDimensionMap[savePdfDimension];
          if (!standard) {
            return Promise.resolve(file); // 如果没有配置标准尺寸，直接返回原文件
          }

          const { width: SAVE_WIDTH, height: SAVE_HEIGHT } = standard;

          // 使用ArrayBuffer只读取一次文件内容
          const arrayBuffer = await file.arrayBuffer();
          const pdfDoc = await PDFDocument.load(arrayBuffer);
          const pages = pdfDoc.getPages();

          if (pages.length === 0) {
            return Promise.resolve(file); // 空PDF直接返回
          }

          // 快速判断：检查PDF页面尺寸是否需要调整
          let needsResize = false;
          for (let i = 0; i < pages.length; i++) {
            const { width, height } = pages[i].getSize();
            if (!isStandardSizePage(width, height, savePdfDimension)) {
              needsResize = true;
              break; // 只要有一页不匹配就需要调整，立即退出循环
            }
          }

          // 如果所有页面都符合尺寸要求，直接返回原文件
          if (!needsResize) {
            return Promise.resolve(file);
          }

          // 仅在需要调整尺寸时创建新的PDF文档
          const newPdfDoc = await PDFDocument.create();

          // 复用变量减少内存分配
          let embeddedPage, scale, scaledWidth, scaledHeight;

          for (const page of pages) {
            const { width, height } = page.getSize();
            // 创建标准尺寸的页面
            const newPage = newPdfDoc.addPage([SAVE_WIDTH, SAVE_HEIGHT]);

            // 判断是否需要缩放
            if (width > SAVE_WIDTH || height > SAVE_HEIGHT) {
              // 计算缩放比例 - 取最小缩放比例以确保内容完全可见
              scale = Math.min(SAVE_WIDTH / width, SAVE_HEIGHT / height);
              scaledWidth = Number((width * scale).toFixed(3));
              scaledHeight = Number((height * scale).toFixed(3));

              // 嵌入并居中绘制页面
              embeddedPage = await newPdfDoc.embedPage(page);
              newPage.drawPage(embeddedPage, {
                x: (SAVE_WIDTH - scaledWidth) / 2,
                y: (SAVE_HEIGHT - scaledHeight) / 2,
                width: scaledWidth,
                height: scaledHeight,
              });
            } else {
              // 内容小于目标尺寸，只需居中绘制
              embeddedPage = await newPdfDoc.embedPage(page);
              newPage.drawPage(embeddedPage, {
                x: (SAVE_WIDTH - width) / 2,
                y: (SAVE_HEIGHT - height) / 2,
              });
            }
          }

          // 优化保存逻辑
          const newPdfBytes = await newPdfDoc.save();
          const pdfFile: any = new File([newPdfBytes], file.name, { type: 'application/pdf' });
          pdfFile.uid = file.uid; // 保留原始 uid
          return Promise.resolve(pdfFile);
        } catch (error) {
          console.error('PDF处理失败:', error);
          message.error('PDF处理失败，请重新上传');
          return Promise.reject(false);
        }
      }

      return Promise.resolve(file);
    },
    [fileLimitConfig.reg, savePdfDimension, fileSize, isTailor, isImgSaveFileType]
  );

  // 预览附件
  const handlePreview = useCallback(
    file => {
      onPreview({
        fileId: file?.fileId,
        fileName: file.name,
        fileType: file.type,
      });
    },
    [apiCongiMap]
  );

  // 下载附件url prefix
  const downUrlPrefix = useMemo(() => {
    if (apiCongiMap.download) {
      return apiCongiMap.download?.url;
    }
    return '';
  }, [apiCongiMap.download]);

  const handleChange = useCallback(
    async info => {
      if (info.file.status !== 'uploading') {
        const fileIds = [];
        info.fileList.map(file => {
          if (file.response) {
            if (file.response.status !== '0') file.status = 'error';
            const fileId = file.response?.data?.result?.fileIds?.[0];
            file.fileId = fileId;
            if (downUrlPrefix) {
              file.url = `${downUrlPrefix}/${fileId}`;
            }
            getFilesFlag.current[fileId] = file;
          }
          file.fileId && fileIds.push(file.fileId);
          return file;
        });
        const updateIds = fileIds.length > 0 ? fileIds.join(',') : undefined;
        await onEvent?.({
          record,
          type: 'setCache',
          attrCode: code,
          data: { files: info.fileList, getFilesFlag: getFilesFlag.current },
        });
        fileValues.current = fileIds;
        onChange(updateIds);
      }
      setFiles(info.fileList);
    },
    [downUrlPrefix]
  );

  const handleCropChange = useCallback(
    newCrop => {
      setCrop(newCrop);
    },
    [aspect]
  );

  /** 获取原始图片裁剪后的file */
  const handleCroppedImg = useCallback(
    (image, pixelCrop, fileInfo) =>
      new Promise(resolve => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;
        canvas.width = pixelCrop.width * scaleX;
        canvas.height = pixelCrop.height * scaleY;
        ctx.drawImage(
          image,
          pixelCrop.x * scaleX,
          pixelCrop.y * scaleY,
          pixelCrop.width * scaleX,
          pixelCrop.height * scaleY,
          0,
          0,
          pixelCrop.width * scaleX,
          pixelCrop.height * scaleY
        );
        const { name, type } = fileInfo || {};
        canvas.toBlob((blob: any) => {
          const file = new File([blob], name, { type });
          resolve(file);
        }, type);
      }),
    []
  );

  const handleCropComplete = useCallback(croppedAreaPixels => {
    setCrop(croppedAreaPixels);
    areaPixelsRef.current = croppedAreaPixels;
  }, []);

  /** 加载图片 */
  const handleImageLoad = useCallback(
    (e: React.SyntheticEvent<HTMLImageElement>) => {
      const { naturalWidth: width, naturalHeight: height } = e.currentTarget;
      const newCrop: any = centerCrop(
        makeAspectCrop(
          {
            unit: '%',
            width: 50,
          },
          aspect,
          width,
          height
        ),
        width,
        height
      );
      setCrop(newCrop);
    },

    [aspect]
  );

  const handleCancel = useCallback(() => {
    setOpen(false);
    /** 取消 则-> 清除裁剪的 */
    const newFiles = [...files];
    newFiles.pop();
    setFiles(newFiles);
  }, [files.length]);

  const calculatePercentage = useCallback((base, percentageValue) => {
    const bigValue = new Big(base);
    const bigPercentage = new Big(percentageValue);
    const result = bigValue.times(bigPercentage).div(100);
    return Number(result.toString());
  }, []);

  /** 自定义上传 */
  const handleCustomRequest = useCallback(
    async ({ file, onSuccess, onError, onProgress }) => {
      try {
        const resp = await onUploadAttachment(
          file,
          { configs },
          {
            onUploadProgress: progressEvent => {
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress({ percent });
            },
          }
        );
        onSuccess({
          data: {
            result: resp,
          },
          status: '0',
          message: 'success',
        });
      } catch (error) {
        onError(error);
      }
    },
    [configs]
  );

  /** 保存图片 */
  const handleSaveImg = useCallback(async () => {
    setSaveCroppedLoading(true);
    let croppedAreaPixels = areaPixelsRef.current;
    if (!croppedAreaPixels && cropRef.current) {
      const { width = 0, height = 0 } = imgRef.current || {};
      croppedAreaPixels = {
        x: calculatePercentage(width, crop.x),
        y: calculatePercentage(height, crop.y),
        width: calculatePercentage(width, crop.width),
        height: calculatePercentage(height, crop.height),
      };
    }
    const updateCroppedImageFile: any = await handleCroppedImg(imgRef.current, croppedAreaPixels, cacheFileRef.current);
    onUploadAttachment(updateCroppedImageFile, { configs })
      .then(async (result: any) => {
        const fileId = result?.fileIds?.[0] || '';
        updateCroppedImageFile.percent = 100;
        updateCroppedImageFile.status = 'done';
        updateCroppedImageFile.fileId = fileId;
        updateCroppedImageFile.uid = fileId || uuid.v4();
        if (downUrlPrefix) {
          updateCroppedImageFile.url = `${downUrlPrefix}/${fileId}`;
        }
        getFilesFlag.current[fileId] = updateCroppedImageFile;
        const newFileIds = [...fileValues.current, fileId];
        const updateIds = newFileIds.join(',');
        const newFileList = [...files, updateCroppedImageFile];
        await onEvent?.({
          record,
          type: 'setCache',
          attrCode: code,
          data: { files: newFileList, getFilesFlag: getFilesFlag.current },
        });
        fileValues.current = newFileIds;
        setOpen(false);
        setFiles(newFileList);
        onChange(updateIds);
      })
      .finally(() => setSaveCroppedLoading(false));
  }, [crop, downUrlPrefix, files.length, configs]);

  const isShowUpload = useMemo(() => maxCount !== files.length && !isView, [maxCount, files.length, isView]);
  return (
    <div className="dhr-custom-upload">
      <Upload
        name="files"
        fileList={files}
        disabled={isView}
        multiple={multiple}
        listType={listType}
        maxCount={maxCount}
        onChange={handleChange}
        onPreview={handlePreview}
        beforeUpload={handleBeforeUpload}
        customRequest={handleCustomRequest}
        // action={apiCongiMap?.upload?.url}
        showUploadList={{
          showPreviewIcon: true,
          showDownloadIcon: true,
          showRemoveIcon: !isView,
        }}
        className={classnames({
          'dhr-custom-button-upload': controlType === 'button' && listType !== UPLOAD_LIST_TYPE.PICTURE_CARD,
        })}
        data={{
          categoryId: `/dhr/common/${apiCongiMap?.upload?.params?.categoryId || ''}`,
        }}
      >
        {isShowUpload && <UploadComponent />}
      </Upload>
      <Modal
        open={open}
        width={400}
        footer={null}
        title="裁剪照片"
        keyboard={false}
        maskClosable={false}
        onCancel={handleCancel}
      >
        <div className="dhr-file-crop-wrap">
          <div className="dhr-file-crop-items">
            <ReactCrop
              crop={crop}
              ref={cropRef}
              aspect={aspect}
              minHeight={100}
              keepSelection={true}
              onChange={handleCropChange}
              onComplete={handleCropComplete}
            >
              <img ref={imgRef} className="dhr-react-crop-image" src={previewImage} onLoad={handleImageLoad} />
            </ReactCrop>
          </div>
        </div>
        <div className="dhr-file-crop-footer-wrap">
          <Button type="primary" size="middle" loading={saveCroppedLoading} onClick={handleSaveImg}>
            保存照片
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default CustomUpload;
