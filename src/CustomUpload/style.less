.dhr-custom-upload-sample-render {
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 2px;
  height: 104px;
  width: 104px;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
}

.dhr-custom-upload {
  .dhr-custom-upload-card {
    background-color: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 2px;
    height: 104px;
    width: 104px;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
    cursor: pointer;
    &:hover {
      border-color: var(--adm-color-primary);
    }
  }
  .dhr-custom-button-upload {
    .ant-upload.ant-upload-select {
      display: block;
    }
  }

  .ant-upload-list-picture-card {
    .ant-upload-list-item-list-type-picture-card {
      .ant-upload-list-item-card-actions-btn {
        padding: 0px !important;
      }
    }
  }

  .ant-upload-list-item-card-actions {
    .ant-upload-list-item-card-actions-btn {
      vertical-align: middle;
      font-size: 14px;
      padding: 0px !important;
    }
  }
}

.dhr-file-crop-footer-wrap {
  text-align: center;
  .ant-btn {
    border-radius: 4px;
  }
}
