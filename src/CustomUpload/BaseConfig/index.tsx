import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { UPLOAD_LIST_TYPE, UPLOAD_LIMIT_FILE_TYPE } from '@constants/common';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

const fileTypeOptions = Object.keys(UPLOAD_LIMIT_FILE_TYPE).map(key => ({
  label: key,
  value: UPLOAD_LIMIT_FILE_TYPE[key],
  key: UPLOAD_LIMIT_FILE_TYPE[key],
}));
export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const {
      aspect,
      multiple,
      maxCount,
      listType,
      fileSize,
      isTailor,
      imgSaveFileType,
      limitFileTypes,
      fileControlType,
      savePdfDimension,
    } = dictConfigByProps || {};

    const curFormData = formRef?.current?.getFormItem?.();
    const defFormData = formRef?.current?.getInitFormData?.();
    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFileControlType',
      curFormData?.dictConfigFileControlType || defFormData?.dictConfigFileControlType || fileControlType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigListType',
      curFormData?.dictConfigListType || defFormData?.dictConfigListType || listType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigMaxCount',
      curFormData?.dictConfigMaxCount || defFormData?.dictConfigMaxCount || maxCount
    );
    formRef?.current?.setFormItem?.(
      'dictConfigMultiple',
      curFormData?.dictConfigMultiple || defFormData?.dictConfigMultiple || multiple
    );
    formRef?.current?.setFormItem?.(
      'dictConfigLimitFileTypes',
      curFormData?.dictConfigLimitFileTypes || defFormData?.dictConfigLimitFileTypes || limitFileTypes
    );
    formRef?.current?.setFormItem?.(
      'dictConfigSavePdfDimension',
      curFormData?.dictConfigSavePdfDimension || defFormData?.dictConfigSavePdfDimension || savePdfDimension
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFileSize',
      curFormData?.dictConfigFileSize || defFormData?.dictConfigFileSize || fileSize
    );
    formRef?.current?.setFormItem?.(
      'dictConfigIsTailor',
      curFormData?.dictConfigIsTailor || defFormData?.dictConfigIsTailor || isTailor
    );
    formRef?.current?.setFormItem?.(
      'dictConfigAspect',
      curFormData?.dictConfigAspect || defFormData?.dictConfigAspect || aspect
    );
    formRef?.current?.setFormItem?.(
      'dictConfigImgSaveFileType',
      curFormData?.dictConfigImgSaveFileType || defFormData?.dictConfigImgSaveFileType || imgSaveFileType
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'select',
      key: 'dictConfigLimitFileTypes',
      label: '上传文件格式',
      configs: {
        mode: 'multiple',
        allowClear: true,
        placeholder: '请选择',
        options: fileTypeOptions,
        onChange: val => {
          context?.onConfirm?.('dictConfigLimitFileTypes', val);
          setDictConfig(formRef, 'limitFileTypes', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigFileControlType',
      label: '上传控件类型',
      configs: {
        allowClear: true,
        placeholder: '请选择，默认按钮',
        options: [
          {
            label: '按钮',
            key: 'button',
            value: 'button',
          },
          {
            label: '卡片',
            key: 'card',
            value: 'card',
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigFileControlType', val);
          setDictConfig(formRef, 'fileControlType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigListType',
      label: '文件展示形式',
      configs: {
        allowClear: true,
        placeholder: '请选择文件，默认文本形式',
        options: [
          {
            label: '文本',
            key: UPLOAD_LIST_TYPE.TEXT,
            value: UPLOAD_LIST_TYPE.TEXT,
          },
          {
            label: '图形',
            key: UPLOAD_LIST_TYPE.PICTURE,
            value: UPLOAD_LIST_TYPE.PICTURE,
          },
          {
            label: '图形卡片',
            key: UPLOAD_LIST_TYPE.PICTURE_CARD,
            value: UPLOAD_LIST_TYPE.PICTURE_CARD,
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigListType', val);
          setDictConfig(formRef, 'listType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigSavePdfDimension',
      label: '保存PDF尺寸',
      configs: {
        allowClear: true,
        placeholder: '请选择',
        options: [
          {
            label: 'A4',
            key: 'A4',
          },
          {
            label: 'A3',
            key: 'A3',
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigSavePdfDimension', val);
          setDictConfig(formRef, 'savePdfDimension', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigImgSaveFileType',
      label: '图片是否保存为PDF',
      configs: {
        allowClear: true,
        placeholder: '请选择',
        options: [
          {
            label: '是',
            key: '1',
            value: '1',
          },
          {
            label: '否',
            key: '0',
            value: '0',
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigImgSaveFileType', val);
          setDictConfig(formRef, 'imgSaveFileType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigIsTailor',
      label: '启用自由裁剪',
      configs: {
        allowClear: true,
        placeholder: '仅限图片可裁剪',
        options: [
          {
            label: '是',
            key: true,
            value: true,
          },
          {
            label: '否',
            key: false,
            value: false,
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigIsTailor', val);
          setDictConfig(formRef, 'isTailor', val, {
            context,
          });
        },
      },
    },
    {
      type: 'inputNumber',
      key: 'dictConfigAspect',
      label: '自由裁剪比例',
      configs: {
        placeholder: '请输入',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigAspect');
          context?.onConfirm?.('dictConfigAspect', value);
          setDictConfig(formRef, 'aspect', value, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigMultiple',
      label: '多选上传',
      configs: {
        allowClear: true,
        placeholder: '请选择, 默认否',
        options: [
          {
            label: '是',
            key: true,
            value: true,
          },
          {
            label: '否',
            key: false,
            value: false,
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigMultiple', val);
          setDictConfig(formRef, 'multiple', val, {
            context,
          });
        },
      },
    },
    {
      type: 'inputNumber',
      key: 'dictConfigFileSize',
      label: '文件大小',
      configs: {
        placeholder: '请输入限制文件上传大小',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigFileSize');
          context?.onConfirm?.('dictConfigFileSize', value);
          setDictConfig(formRef, 'fileSize', value, {
            context,
          });
        },
      },
    },
    {
      type: 'inputNumber',
      key: 'dictConfigMaxCount',
      label: '文件数量',
      configs: {
        placeholder: '请输入限制上传数量',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigMaxCount');
          context?.onConfirm?.('dictConfigMaxCount', value);
          setDictConfig(formRef, 'maxCount', value, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
