import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { cityCode, provinceCode, districtCode, addressCode, addressFullath } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigProvinceCode',
      curFormData?.dictConfigProvinceCode || defFormData?.dictConfigProvinceCode || provinceCode
    );
    formRef?.current?.setFormItem?.(
      'dictConfigCityCode',
      curFormData?.dictConfigCityCode || defFormData?.dictConfigCityCode || cityCode
    );
    formRef?.current?.setFormItem?.(
      'dictConfigDistrictCode',
      curFormData?.dictConfigDistrictCode || defFormData?.dictConfigDistrictCode || districtCode
    );
    formRef?.current?.setFormItem?.(
      'dictConfigAddressCode',
      curFormData?.dictConfigAddressCode || defFormData?.dictConfigAddressCode || addressCode
    );
    formRef?.current?.setFormItem?.(
      'dictConfigAddressFullath',
      curFormData?.dictConfigAddressFullath || defFormData?.dictConfigAddressFullath || addressFullath
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigProvinceCode',
      label: '省份字段名称',
      configs: {
        placeholder: '请输入省份的组件编码',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigProvinceCode');
          context?.onConfirm?.('dictConfigProvinceCode', value);
          setDictConfig(formRef, 'provinceCode', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigCityCode',
      label: '城市字段名称',
      configs: {
        placeholder: '请输入城市的组件编码',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigCityCode');
          context?.onConfirm?.('dictConfigCityCode', value);
          setDictConfig(formRef, 'cityCode', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigDistrictCode',
      label: '区域字段名称',
      configs: {
        placeholder: '请输入省份的组件编码',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigDistrictCode');
          context?.onConfirm?.('dictConfigDistrictCode', value);
          setDictConfig(formRef, 'districtCode', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigAddressCode',
      label: '地址字段名称',
      configs: {
        placeholder: '请输入地址的组件编码',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigAddressCode');
          context?.onConfirm?.('dictConfigAddressCode', value);
          setDictConfig(formRef, 'addressCode', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigAddressFullath',
      label: '地址全路径字段名称',
      configs: {
        placeholder: '请输入地址全路径的组件编码',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigAddressFullath');
          context?.onConfirm?.('dictConfigAddressFullath', value);
          setDictConfig(formRef, 'addressFullath', value, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
