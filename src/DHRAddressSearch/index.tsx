import React, { useCallback, useEffect, useMemo } from 'react';
import { Select, Spin, Tooltip } from 'antd';

import { request } from '@utils/http';
import commonApis from '@apis/common';
import useDebounce from '@hooks/useDebounce';

import { WULI_MODE } from '@constants/common';

import './style.less';

export interface IAppProps {
  value: string;
  onChange: (hid: string) => void;
  configs: {
    context: {
      setFormData: any;
      getFormData: any;
    };
    wuliMode: string;
    config: {
      baseConfig?: {
        dictConfig?: {
          cityCode: string;
          provinceCode: string;
          districtCode: string;
          addressCode: string;
          addressFullath: string;
        };
      };
    };
  };
}

const DHRAddressSearch: React.FC<IAppProps> = props => {
  const [options, setOptions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const { configs, value, onChange } = props;
  const { context, wuliMode } = configs || {};
  const { getFormData, setFormData } = context || {};
  const { dictConfig } = configs.config?.baseConfig || {};
  const { cityCode, provinceCode, districtCode, addressCode, addressFullath } = dictConfig || {};

  const isView = wuliMode === WULI_MODE.VIEW;

  /** 字段映射 */
  const optionFieldMap = useMemo(() => {
    const _fieldMap = {
      /** 省份 */
      provinceCode: provinceCode,
      /** 城市 */
      cityCode: cityCode,
      /** 区域 */
      districtCode: districtCode,
      /** 地址 */
      address: addressCode,
      /** 地址全路径 */
      formatAddress: addressFullath,
    };
    const result = Object.keys(_fieldMap)
      .filter(fieldKey => !!_fieldMap[fieldKey])
      .reduce(
        (pre, cur) => ({
          ...pre,
          [cur]: _fieldMap[cur],
        }),
        {}
      );
    return result;
  }, [cityCode, provinceCode, districtCode, addressCode, addressFullath]);

  // 回显
  useEffect(() => {
    if (value && options.length === 0) {
      const formValues = getFormData?.() || {};
      const _option = {
        label: value,
        value,
      };
      Object.keys(optionFieldMap).forEach(key => {
        _option[key] = formValues[optionFieldMap[key]];
      });
      setOptions([_option]);
    }
  }, [value, optionFieldMap]);

  const handleSearch = useDebounce(
    value => {
      /** 空的 不搜索 */
      if (!value) {
        return;
      }
      setLoading(true);
      request({
        ...commonApis.addressSearch,
        params: {
          address: value,
        },
        onSuccess: list => {
          const _options = (list || []).map(listItem => ({
            ...listItem,
            value: listItem.formatAddress,
            label: listItem.formatAddress,
          }));
          setOptions(_options);
        },
        onError: error => {
          console.log('地址搜索请求失败：', error);
          setOptions([]);
        },
      }).finally(() => setLoading(false));
    },
    500,
    []
  );

  const handleChange = useCallback(
    (value, option) => {
      const isUpdate = Object.keys(optionFieldMap).length > 0;
      if (!isUpdate) {
        return onChange(value);
      }
      const _option = option || {};
      const updateValues = {};
      Object.keys(optionFieldMap).forEach(fieldKey => {
        updateValues[optionFieldMap[fieldKey]] = _option[fieldKey];
      });
      onChange(value);
      setFormData?.(updateValues);
    },
    [optionFieldMap]
  );

  return (
    <div className="dhr-address-search-wrap">
      {isView ? (
        <Tooltip placement="topLeft" title={value || ''}>
          <span className="text-ellipsis">{value || ''}</span>
        </Tooltip>
      ) : (
        <Select
          allowClear
          showSearch
          value={value}
          options={options}
          showArrow={false}
          filterOption={false}
          onSearch={handleSearch}
          onChange={handleChange}
          placeholder="请搜索地址"
          notFoundContent={loading ? <Spin size="small" /> : null}
        />
      )}
    </div>
  );
};

export default DHRAddressSearch;
