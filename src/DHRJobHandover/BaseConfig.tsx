import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

// 离职交接类型选项
const HANDOVER_TYPE_OPTIONS = [
  {
    label: '实习生',
    key: 'intern',
    value: 'intern',
  },
  {
    label: '外协',
    key: 'outsource',
    value: 'outsource',
  },
  {
    label: '正编',
    key: 'regular',
    value: 'regular',
  },
];

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { handoverType } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigHandoverType',
      curFormData?.dictConfigHandoverType || defFormData?.dictConfigHandoverType || handoverType
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'select',
      key: 'dictConfigHandoverType',
      label: '离职交接类型',
      configs: {
        options: HANDOVER_TYPE_OPTIONS,
        placeholder: '请选择离职交接类型',
        onSelect: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigHandoverType');
          context?.onConfirm?.('dictConfigHandoverType', value);
          setDictConfig(formRef, 'handoverType', value, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
