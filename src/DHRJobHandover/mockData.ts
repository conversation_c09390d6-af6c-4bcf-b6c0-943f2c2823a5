const mockData = [
  {
    systemName: 'WORK_HANDOVER',
    id: '1ff51459a9e14217830c85e4742afff8',
    type: 'QUERY_ONLY',
    errorMsg: '工作交接单未提交',
    registerTitle: '工作交接信息',
    tips: '离职前，请填写好工作交接单，提交审批后，提醒主管去OA系统审批，审批结束后交接完成',
    contentType: 'form',
    condition: 'auditPass',
    sortNo: 10,
    menuType: null,
    msg: [
      [
        {
          fileId: 'exitEmpId',
          fileName: '离职人员ID',
          value: '3ef17dcd42b2497a828ba75d3fc66c61',
          type: 'hide',
        },
        {
          fileId: 'fileId',
          fileName: '工作交接单',
          // value: 'eead5da1c23641048e87b713d657d31e',
          value: '',
          type: 'file',
        },
        {
          fileId: 'fileName',
          fileName: '工作交接单文件名',
          value: '离职工作交接单.pdf',
          type: 'hide',
        },
        {
          fileId: 'fileSize',
          fileName: '工作交接单大小',
          value: '1089200',
          type: 'hide',
        },
        {
          fileId: 'fileContentType',
          fileName: '工作交接单ContentType',
          value: 'pdf',
          type: 'hide',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: '{"empId":"0ec222e6b3c5433f900a8da42aa3243b","domainCode":"hewenhui","othername":"何文辉"}',
          type: 'employee',
        },
        {
          fileId: 'handoverEmpEmail',
          fileName: '交接人邮箱',
          value: '<EMAIL>',
          type: 'input',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '已完成',
          type: 'hide',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: 'FINISH',
          type: 'hide',
        },
        {
          fileId: 'holdingCompanyAssets',
          fileName: '是否持有公司以下资产',
          // value: 'NONE',
          value: '1',
          type: 'selectWhether',
        },
        {
          fileId: 'assetsDetails',
          fileName: '具体的资产信息',
          value: null,
          type: 'input',
        },
        {
          fileId: 'isLeaveSubmit',
          fileName: '请假是否已提交系统',
          value: '1',
          type: 'selectWhether',
        },
      ],
    ],
  },
  {
    systemName: 'TUTOR_INFO',
    id: '4ac066b6504a4285b525212b198b9758',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '导师信息',
    tips: '数据来源于eHR系统。',
    contentType: 'table',
    condition: 'auditPass',
    sortNo: 10,
    menuType: null,
    msg: [
      [
        {
          fileId: 'referenceId',
          fileName: '行主键',
          value: '8a7d839c87d94e3c9d8a7e5f2c1b3a6d',
          type: 'hide',
        },
        {
          fileId: 'name',
          fileName: '姓名',
          value: '张三',
          type: 'readonly',
        },
        {
          fileId: 'account',
          fileName: '域账号',
          value: 'zhangsan',
          type: 'readonly',
        },
        {
          fileId: 'type',
          fileName: '新人类型',
          value: '应届生',
          type: 'readonly',
        },
        {
          fileId: 'tutorNames',
          fileName: '导师',
          value: '李四',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: '{"empId":"0ec222e6b3c5433f900a8da42aa3243b","domainCode":"wangwu","othername":"王五"}',
          type: 'employee',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: '赵六',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '已完成',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: 'FINISH',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'EXT_LEAD',
    id: '4ac066b6504a4285b525212b198b9758',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '外包信息',
    tips: '数据来源于eHR系统。',
    contentType: 'table',
    condition: 'auditPass',
    sortNo: 10,
    menuType: null,
    msg: [
      [
        {
          fileId: 'extEmpId',
          fileName: 'id',
          value: '5f7e9b2c8d3a4e1f6b0c9a8d7e6f5c4b',
          type: 'hide',
        },
        {
          fileId: 'name',
          fileName: '姓名',
          value: '李明',
          type: 'readonly',
        },
        {
          fileId: 'account',
          fileName: '域账号',
          value: 'liming',
          type: 'readonly',
        },
        {
          fileId: 'extEmpType',
          fileName: '人员类型',
          value: '外包人员',
          type: 'readonly',
        },
        {
          fileId: 'leaderName',
          fileName: '负责人',
          value: '陈曼婷',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: '{"empId":"7a9b8c7d6e5f4a3b2c1d0e9f8a7b6c5d","domainCode":"liuqiang","othername":"刘强"}',
          type: 'employee',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: '王建国',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '处理中',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: 'PROCESSING',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'extEmpId',
          fileName: 'id',
          value: '1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p',
          type: 'hide',
        },
        {
          fileId: 'name',
          fileName: '姓名',
          value: '张伟',
          type: 'readonly',
        },
        {
          fileId: 'account',
          fileName: '域账号',
          value: 'zhangwei',
          type: 'readonly',
        },
        {
          fileId: 'extEmpType',
          fileName: '人员类型',
          value: '外协顾问',
          type: 'readonly',
        },
        {
          fileId: 'leaderName',
          fileName: '负责人',
          value: '陈曼婷',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '未开始',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: 'NOT_START',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'EAM',
    id: '4ac066b6504a4285b525212b198b9758',
    type: 'QUERY_AND_CALLBACK',
    errorMsg: null,
    registerTitle: '资产详情',
    tips: '数据来源于EAM系统。离职前，请确认所有个人资产交接完成：\n1、原则上，优先选择“归还入库”，并及时归还资产，资产管理员确认验收入库后，流程完结\n2、若选择“资产交接”，请提前线下与资产接收人沟通达成一致，发起交接后跟进接收人审批完成，并及时完成实物交接',
    contentType: 'table',
    condition: 'emptyList',
    sortNo: 40,
    menuType: 'BATCH',
    msg: [
      [
        {
          fileId: 'material_code',
          fileName: '物资编码',
          value: '1101002000573',
          type: 'hide',
        },
        {
          fileId: 'asset_code',
          fileName: '管理编号',
          value: 'IT00066854',
          type: 'readonly',
        },
        {
          fileId: 'material_name',
          fileName: '物资名称',
          value: 'Windows笔记本',
          type: 'readonly',
        },
        {
          fileId: 'billTypeCodePlaceholder',
          fileName: '物资类型',
          value: '领用物资',
          type: 'readonly',
        },
        {
          fileId: 'admin_name',
          fileName: '物资管理员',
          value: '江大伟',
          type: 'readonly',
        },
        {
          fileId: 'user_name',
          fileName: '资产使用人',
          value: '何晶',
          type: 'readonly',
        },
        {
          fileId: 'incharge_name',
          fileName: '责任人',
          value: '何晶',
          type: 'readonly',
        },
        {
          fileId: 'return_type',
          fileName: '操作',
          value: '',
          type: 'radio',
        },
        {
          fileId: 'handoverEmp',
          fileName: '资产交接人',
          value: '',
          type: 'employee',
        },
        {
          fileId: 'admin_account',
          fileName: '仓管域账号',
          value: 'jiangdawei',
          type: 'hide',
        },
        {
          fileId: 'currentProcessor',
          fileName: '当前处理人',
          value: '',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '流程状态',
          value: '',
          type: 'readonly',
        },
        {
          fileId: 'bill_type_code',
          fileName: '物资类型(隐藏)',
          value: 'transfers_receive',
          type: 'hide',
        },
        {
          fileId: 'asset_id',
          fileName: '物资单据id(隐藏)',
          value: '1831598656369459200',
          type: 'hide',
        },
        {
          fileId: 'sync',
          fileName: '是否已同步',
          value: '',
          type: 'hide',
        },
        {
          fileId: 'isSubmit',
          fileName: '是否提交',
          value: '',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'BABY',
    id: '32f49cbab8644fc3800a469949e60d0e',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '幼早教信息查询',
    tips: '数据来源于简道云系统。如有相关幼儿在园数据，请知悉，并在离职前与相关人员妥善安排退园工作。',
    contentType: 'table',
    condition: 'auditPass',
    sortNo: 120,
    menuType: 'BATCH',
    msg: [
      [
        {
          fileId: 'babyId',
          fileName: '婴幼儿ID',
          value: '00168',
          type: 'hide',
        },
        {
          fileId: 'babyName',
          fileName: '婴幼儿姓名',
          value: '柳清月',
          type: 'readonly',
        },
        {
          fileId: 'relation',
          fileName: '关系',
          value: '女儿',
          type: 'readonly',
        },
        {
          fileId: 'birthday',
          fileName: '出生日期',
          value: '2022-07-08',
          type: 'readonly',
        },
        {
          fileId: 'kindergartenName',
          fileName: '园区',
          value: 'IC',
          type: 'readonly',
        },
        {
          fileId: 'eduPhaseName',
          fileName: '教育阶段',
          value: '绵羊班',
          type: 'readonly',
        },
        {
          fileId: 'statusName',
          fileName: '状态',
          value: '在园',
          type: 'readonly',
        },
        {
          fileId: 'isSpouseInCompany',
          fileName: '配偶是否在公司任职',
          value: '0',
          type: 'selectWhether',
        },
        {
          fileId: 'isDropOut',
          fileName: '是否退园',
          value: '1',
          type: 'selectWhether',
        },
        {
          fileId: 'dropOutTime',
          fileName: '退园时间',
          value: '1747929600000',
          type: 'datePicker',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '已完成',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: 'FINISH',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'RES_CAR_CERT',
    id: '0f2e3b2e6b254469ba56e46fb75650fa',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '行政车贴查询',
    tips: '数据来源于车辆管理系统，如有领用车贴，并在办理离职前归还至前台工作人员。',
    contentType: 'table',
    condition: 'auditPass',
    sortNo: 100,
    menuType: 'BATCH',
    msg: [
      [
        {
          fileId: 'receiver',
          fileName: '领用人员',
          value: '邹淑瑞',
          type: 'readonly',
        },
        {
          fileId: 'licensePlate',
          fileName: '车牌号',
          value: '粤A1N9R7',
          type: 'readonly',
        },
        {
          fileId: 'carStickerCode',
          fileName: '车贴',
          value: 'SY01556',
          type: 'readonly',
        },
        {
          fileId: 'park',
          fileName: '园区',
          value: 'CVTE第二产业园',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '已完成',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: 'FINISH',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'KMS',
    id: 'ee92929cf4a549f7b75b71348cc58b55',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '知识产权信息查询',
    tips: '数据来源于知识产权系统，如有相关的知识产权数据，请参照下述指引文件办理知识产权确认手续：https://drive.cvte.com/p/DQ5ftQoQ7KQDGObAIiAA',
    contentType: 'table',
    condition: 'auditPass',
    sortNo: 135,
    menuType: null,
    msg: [
      [
        {
          fileId: 'empName',
          fileName: '姓名',
          value: '陈曼婷',
          type: 'readonly',
        },
        {
          fileId: 'caseNumber',
          fileName: '案号',
          value: 'CN20232020D',
          type: 'readonly',
        },
        {
          fileId: 'caseNumber',
          fileName: '申请号',
          value: 'PCT/CN2023/118952',
          type: 'readonly',
        },
        {
          fileId: 'caseName',
          fileName: '案件名称',
          value: '显示控制方法、装置、触控设备及存储介质',
          type: 'readonly',
        },
        {
          fileId: 'caseName',
          fileName: '案件等级',
          value: 'C',
          type: 'readonly',
        },
        {
          fileId: 'applyTime',
          fileName: '申请日期',
          value: '2023-09-15',
          type: 'readonly',
        },
        {
          fileId: 'applyTime',
          fileName: '申请国家',
          value: '世界知识产权组织(WIPO)(国际局)',
          type: 'readonly',
        },
        {
          fileId: 'caseType',
          fileName: '案件类型',
          value: '专利',
          type: 'readonly',
        },
        {
          fileId: 'applyType',
          fileName: '申请类型',
          value: '发明',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '已完成',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: 'FINISH',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'empName',
          fileName: '姓名',
          value: '陈曼婷',
          type: 'readonly',
        },
        {
          fileId: 'caseNumber',
          fileName: '案号',
          value: 'CN20241641J',
          type: 'readonly',
        },
        {
          fileId: 'caseNumber',
          fileName: '申请号',
          value: '202430790363.4',
          type: 'readonly',
        },
        {
          fileId: 'caseName',
          fileName: '案件名称',
          value: '电子设备的输入框悬浮操作图形用户界面',
          type: 'readonly',
        },
        {
          fileId: 'caseName',
          fileName: '案件等级',
          value: 'C',
          type: 'readonly',
        },
        {
          fileId: 'applyTime',
          fileName: '申请日期',
          value: '2024-12-12',
          type: 'readonly',
        },
        {
          fileId: 'applyTime',
          fileName: '申请国家',
          value: '中国',
          type: 'readonly',
        },
        {
          fileId: 'caseType',
          fileName: '案件类型',
          value: '专利',
          type: 'readonly',
        },
        {
          fileId: 'applyType',
          fileName: '申请类型',
          value: '外观设计',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '已完成',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: 'FINISH',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'OA',
    id: 'a3c5db9fc808401aae657abb837fa5b3',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '待办流程详情',
    tips: '数据来源于OA系统。离职前，请设置交接人并发起交接',
    contentType: 'table',
    condition: 'emptyList',
    sortNo: 50,
    menuType: 'BATCH',
    msg: [
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '196d2201c4a8ebd66c3c7644d8b86272',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: '个人旅游奖励_宁晶晶_2025-05-15',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-05-15',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '1967a8ee10626da00a66c394939832f7',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202504280011',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-04-28',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '1947db30a340d902b840c1749f398011',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: '幼儿园缴费申请流程',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-01-19',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '1962e067c7897861f37e7b84784a9ade',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: '部件-采购更改单-SKGG202504130005',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-04-13',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '195ebb1dac851d5d1de0ae64535a141c',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202503310018',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-03-31',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '195ad3545d1b4b4b48ca1e6445b98dba',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202503190014',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-03-19',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '1956584578435f8d9c40050450696ca5',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'INV_其他出入库SCMINVSK20250305001861',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-03-05',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '195461ac0bd96b06b4642d54c35a46a0',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202502270012',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-02-27',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '194d9feb0e3501481a8c5694e249eca5',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202502060011',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-02-06',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '194880a2f309e094b1aba994c119cf2b',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202501210015',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-01-21',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '19488076cca4f8e06788362475291855',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202501210013',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-01-21',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '19467beb84eca1be8b6dbb84802be465',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202501150004',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2025-01-15',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '193fcdcce761503ba869ee74e359657a',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202412250022',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2024-12-25',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '193fc9e129ae944ff95644c4288b122f',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'PMS_特采单据_SKPZ202412250016',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2024-12-25',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '18f9937198858ebd245b6484ac3aa4e2',
          type: 'hide',
        },
        {
          fileId: 'workItemName',
          fileName: '流程名称',
          value: 'INV_其他出入库SCMINVSK202405210619',
          type: 'readonly',
        },
        {
          fileId: 'createDomainCode',
          fileName: '提交人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2024-05-21',
          type: 'readonly',
        },
        {
          fileId: 'docStatus',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'domainCode',
          fileName: '当前处理人',
          value: '宁晶晶',
          type: 'readonly',
        },
        {
          fileId: 'handoverEmp',
          fileName: '离职交接人',
          value: null,
          type: 'employee',
        },
        {
          fileId: 'routeTag',
          fileName: 'OA路由标识',
          value: 'newOA',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'FEE',
    id: '12d3b910324b47be96d54ce33c5efe20',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '费控未结单据',
    tips: '数据来源于Fee费控系统。离职前，请做好工作交接。',
    contentType: 'table',
    condition: 'emptyList',
    sortNo: 60,
    menuType: 'BATCH',
    msg: [
      [
        {
          fileId: 'billCode',
          fileName: '单号',
          value: 'LZ2505190001',
          type: null,
        },
        {
          fileId: 'billStatus',
          fileName: '状态',
          value: '草稿',
          type: null,
        },
        {
          fileId: 'handover',
          fileName: '交接人',
          value: '无需交接',
          type: null,
        },
        {
          fileId: 'handoverConfirmStatus',
          fileName: '交接人确认状态',
          value: '无需交接',
          type: null,
        },
        {
          fileId: 'sourceOrderUrl',
          fileName: '慧捷报地址',
          value: 'https://fee.cvte.com/jiebao-plus//#/app/smart_expense/quit_process/LZ/1200647362232754176',
          type: null,
        },
      ],
      [
        {
          fileId: 'billCode',
          fileName: '单号',
          value: 'EC2505120252',
          type: null,
        },
        {
          fileId: 'billStatus',
          fileName: '状态',
          value: '待审批',
          type: null,
        },
        {
          fileId: 'handover',
          fileName: '交接人',
          value: '-',
          type: null,
        },
        {
          fileId: 'handoverConfirmStatus',
          fileName: '交接人确认状态',
          value: '待确认',
          type: null,
        },
        {
          fileId: 'sourceOrderUrl',
          fileName: '慧捷报地址',
          value: 'https://fee.cvte.com/jiebao-plus//#/app/smart_expense/quit_process/LZ/1200647362232754176',
          type: null,
        },
      ],
    ],
  },
  {
    systemName: 'OA_CONTRACT',
    id: '174acaacf6d847ada5ca92b01e424661',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '待交接合同盖章流程',
    tips: '数据来源于OA系统。离职流程提交后可启动分支由部门主管确认。',
    contentType: 'table',
    condition: 'requiredForm',
    sortNo: 90,
    menuType: 'BATCH',
    msg: [
      [
        {
          fileId: 'processId',
          fileName: '流程id',
          value: '17da2f15b43d5308013b4a0440b928eb',
          type: 'hide',
        },
        {
          fileId: 'docSubject',
          fileName: '流程名称',
          value:
            '【盖章申请流程】_姚敏_广州视睿电子科技有限公司_赣州市南康区2021年信息化教学带头人、骨干教师技能提升培训合作协议_赣州市南康区教育事业发展中心',
          type: 'readonly',
        },
        {
          fileId: 'srcSystem',
          fileName: '来源系统',
          value: 'OA',
          type: 'readonly',
        },
        {
          fileId: 'creatorName',
          fileName: '提交人',
          value: '姚敏',
          type: 'readonly',
        },
        {
          fileId: 'docCreateTime',
          fileName: '提交日期',
          value: '2021-12-10',
          type: 'readonly',
        },
        {
          fileId: 'status',
          fileName: '流程状态编码',
          value: '20',
          type: 'hide',
        },
        {
          fileId: 'statusName',
          fileName: '流程状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'currentHandler',
          fileName: '当前处理人',
          value: '于文静',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: '张艳娆',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '审批中',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态',
          value: 'PROCESSING',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'TRAVEL',
    id: 'ace3caf2422849968404558930913431',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: '差旅信息查询',
    tips: '数据来源于行政管理系统，如有差旅账号，离职流程提交后可启动至行政负责人确认相关差旅费用。',
    contentType: 'table',
    condition: 'auditPass',
    sortNo: 130,
    menuType: 'BATCH',
    msg: [
      [
        {
          fileId: 'supplier',
          fileName: '供应商',
          value: '携程',
          type: 'readonly',
        },
        {
          fileId: 'accountNumberReceiver',
          fileName: '差旅账号领用人',
          value: 'lihonggang',
          type: 'readonly',
        },
        {
          fileId: 'collectionTime',
          fileName: '领取时间',
          value: '2024-06-04',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: '',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'supplier',
          fileName: '供应商',
          value: '滴滴用车',
          type: 'readonly',
        },
        {
          fileId: 'accountNumberReceiver',
          fileName: '差旅账号领用人',
          value: 'lihonggang',
          type: 'readonly',
        },
        {
          fileId: 'collectionTime',
          fileName: '领取时间',
          value: '2024-06-04',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: '',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'supplier',
          fileName: '供应商',
          value: '企业美团',
          type: 'readonly',
        },
        {
          fileId: 'accountNumberReceiver',
          fileName: '差旅账号领用人',
          value: 'lihonggang',
          type: 'readonly',
        },
        {
          fileId: 'collectionTime',
          fileName: '领取时间',
          value: '2024-12-31',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: '',
          type: 'hide',
        },
      ],
      [
        {
          fileId: 'supplier',
          fileName: '供应商',
          value: '阿里商旅',
          type: 'readonly',
        },
        {
          fileId: 'accountNumberReceiver',
          fileName: '差旅账号领用人',
          value: 'lihonggang',
          type: 'readonly',
        },
        {
          fileId: 'collectionTime',
          fileName: '领取时间',
          value: '2023-10-10',
          type: 'readonly',
        },
        {
          fileId: 'wfAuditName',
          fileName: '当前审核人',
          value: null,
          type: 'readonly',
        },
        {
          fileId: 'wfAuditStatus',
          fileName: '交接状态',
          value: '',
          type: 'readonly',
        },
        {
          fileId: 'wfStatus',
          fileName: '交接状态Code',
          value: '',
          type: 'hide',
        },
      ],
    ],
  },
  {
    systemName: 'CRM',
    id: '13d3b910324b47be96d54ce33c5efe20',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: 'SCRM样机',
    tips: '数据来源于SCRM系统。离职前，请做好工作交接，确认工作交接清晰，如有疑问请联系对应样机内勤。',
    contentType: 'table',
    condition: 'emptyList',
    sortNo: 140,
    menuType: 'BATCH',
    msg: [
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '1.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'DZ0826A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23030100029',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '台式计算机',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '1.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'DP0822A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23042800017',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '台式计算机',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '2.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'M242A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23030100029',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '液晶显示器',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '2.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'M242A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23051800008',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '液晶显示器',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '2.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: '',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23030100029',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '有线键鼠套装',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '2.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'I012A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23051800008',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '键盘',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '1.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'DP0822A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23030100029',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '台式计算机',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '1.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'DZ0826A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23042800017',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '台式计算机',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '2.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'M242A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23042800017',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '液晶显示器',
          type: 'readonly',
        },
      ],
      [
        {
          fileId: 'willRtnQty',
          fileName: '未归还数量',
          value: '2.0',
          type: 'readonly',
        },
        {
          fileId: 'loanUseName',
          fileName: '借用用途',
          value: '推广宣传样机',
          type: 'readonly',
        },
        {
          fileId: 'keepName',
          fileName: '保管人',
          value: '李洪刚',
          type: 'readonly',
        },
        {
          fileId: 'actualSaleModel',
          fileName: '实发销售型号',
          value: 'D0825A',
          type: 'readonly',
        },
        {
          fileId: 'loanId',
          fileName: '借用单号',
          value: 'SRSPLAI-23051800008',
          type: 'readonly',
        },
        {
          fileId: 'actualItemName',
          fileName: '实发物料名称',
          value: '分体式教学终端',
          type: 'readonly',
        },
      ],
    ],
  },
  {
    systemName: 'JIRA',
    id: 'cfd3b910324b47be96d54ce33c5efe20',
    type: 'QUERY_ONLY',
    errorMsg: null,
    registerTitle: 'JIRA待办任务',
    tips: '数据来源于JIRA系统。离场前，请做好工作交接，确认工作交接清晰，以免离场后研发任务无人认领。',
    contentType: 'table',
    condition: 'emptyList',
    sortNo: 80,
    menuType: null,
    msg: [
      [
        { fileId: 'issueId', fileName: '任务编号', type: 'readonly', value: '' },
        { fileId: 'issueType', fileName: '任务类型', type: 'readonly', value: '' },
        { fileId: 'summary', fileName: '任务摘要', type: 'readonly', value: '' },
        { fileId: 'assignee', fileName: '负责人', type: 'readonly', value: '' },
        { fileId: 'priority', fileName: '优先级', type: 'readonly', value: '' },
        { fileId: 'status', fileName: '状态', type: 'readonly', value: '' },
        { fileId: 'dueDate', fileName: '截止日期', type: 'readonly', value: '' },
        { fileId: 'projectName', fileName: '所属项目', type: 'readonly', value: '' },
      ],
      [
        { fileId: 'issueId', value: 'DHR-2345' },
        { fileId: 'issueType', value: '任务' },
        { fileId: 'summary', value: '员工自助服务平台性能优化' },
        { fileId: 'assignee', value: '张三(zhangsan)' },
        { fileId: 'priority', value: '高' },
        { fileId: 'status', value: '进行中' },
        { fileId: 'dueDate', value: '2023-06-27' },
        { fileId: 'projectName', value: '数字人力资源系统' },
      ],
      [
        { fileId: 'issueId', value: 'OA-1234' },
        { fileId: 'issueType', value: '缺陷' },
        { fileId: 'summary', value: '离职流程审批按钮点击无反应' },
        { fileId: 'assignee', value: '张三(zhangsan)' },
        { fileId: 'priority', value: '紧急' },
        { fileId: 'status', value: '待解决' },
        { fileId: 'dueDate', value: '2023-06-26' },
        { fileId: 'projectName', value: '办公自动化系统' },
      ],
      [
        { fileId: 'issueId', value: 'HCM-5678' },
        { fileId: 'issueType', value: '需求' },
        { fileId: 'summary', value: '员工档案管理增加证书管理功能' },
        { fileId: 'assignee', value: '张三(zhangsan)' },
        { fileId: 'priority', value: '中' },
        { fileId: 'status', value: '待办' },
        { fileId: 'dueDate', value: '2023-07-04' },
        { fileId: 'projectName', value: '人力资本管理系统' },
      ],
    ],
  },
];

export default mockData;
