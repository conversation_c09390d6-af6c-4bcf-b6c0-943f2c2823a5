import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { LeaveTransferProvider } from './context';
import { useModuleRegistry } from './hooks/useModuleRegistry';
import { DefaultSystemBlock } from './module';
import { ModuleRenderer } from './components/ModuleRenderer';
import { XiooTableActions } from '@cvte/xioo-ag-table';
import { WULIFormActions } from '@cvte/wuli-antd';
import mockData from './mockData';
import { Collapse } from 'antd';
import { CheckCircleFilled, ClockCircleFilled } from '@ant-design/icons';
import leaveApis from '@apis/leave';
import { request } from '@utils/http';
import useDict from '@hooks/useDict';
import { formatTipsStatus, TipsStatus } from './module/DefaultSystemBlock';
import { IHandoverData, ILeaveTransferContextType, IUpdateConfig } from './index.d';
import { IGlobalProps } from '../types';

import '@src/styles/atom.less';
// 工作交接相关模块,需要特殊处理

const { Panel } = Collapse;
const Main: React.FC<IGlobalProps> = forwardRef((props, ref) => {
  console.log('props===========111', props);
  const { configs } = props;
  // const [handoverData, setHandoverData] = useState<IHandoverData[]>(mockData);
  // 初次渲染数据
  const [handoverData, setHandoverData] = useState<IHandoverData[]>([]);
  // 草稿数据
  const draftHandoverData = useRef<IHandoverData[]>([]);
  // 统一获取字典
  const dicts = useDict(['DHR_EXIT_HELD_ASSET']);
  // 记录每个block的状态
  const blockStatusRef = useRef<IObject>({
    // WORK_HANDOVER: true, // true为通过，false为不通过
  });

  const { registerCoreModules, modules } = useModuleRegistry();
  // 初始化注册核心模块
  useEffect(() => {
    // 注册核心模块 - 可区分PC端和移动端、不同人员类别去注册
    registerCoreModules();
  }, []);

  useImperativeHandle(ref, () => ({
    // 获取草稿数据
    getDraftHandoverData: () => draftHandoverData.current,
  }));

  // 天舟云表单数据
  const formData = configs.context?.getFormData();
  console.log('全局表单数据===========', formData);

  const user = configs.context?.getContext()?.session?.user ?? {};
  // 离职人是否为当前登录人本人
  const leaverIsOwn = formData['C_EMP_ID'] === user.id;
  // const fieldConfig = configs.context.getConfig({ formCode: 'BASIC_INFO_GROUP', attrCode: 'C_IS_TRANSFER' });
  /// 是否有该字段权限，流程里有没有勾选
  // const hasPermission = fieldConfig.config.baseConfig.isVisible === '1';
  // 当前处理人是否为登录人
  const isHandler = useMemo(() => {
    return formData['CURRENT_HANDLER']?.split(',')?.includes(user.account);
  }, [formData['CURRENT_HANDLER'], user.account]);

  // 更新交接数据
  const onUpdateHandoverData = (value: any, config: IUpdateConfig) => {
    // 需要区分是表单里的还是列表里的
    console.log('更新数据===========', value, config);
    const { systemName, fileId, contentType, rowKey } = config || {};
    // 深拷贝当前数据，避免直接修改状态
    const updatedHandoverData = [...handoverData];

    // 查找要更新的系统模块
    const systemIndex = updatedHandoverData.findIndex(item => item.systemName === systemName);
    if (systemIndex === -1) return;

    // 根据内容类型更新数据
    if (contentType === 'form') {
      // 表单类型更新
      const msgData = updatedHandoverData[systemIndex].msg?.[0] || [];
      const fieldIndex = msgData.findIndex(field => field.fileId === fileId);

      const formKey = `${systemName}_FORM`;
      WULIFormActions.get(formKey)?.setValue({
        [fileId]: value,
      });

      if (fieldIndex !== -1) {
        // 更新表单字段值
        updatedHandoverData[systemIndex].msg[0][fieldIndex].value = value;
      }
      return;
    }

    if (contentType === 'table') {
      // rowKey 是唯一标识，可以用来找到对应的数据， 格式如：rowKey: 'key1', rowIdx: 1
      const rowIdx = rowKey?.split('key')[1];
      if (!rowIdx) return;
      // 表格类型更新 - 需要更新特定行的数据
      const tableData = updatedHandoverData[systemIndex].msg || [];
      if (tableData[rowIdx]) {
        // 找到对应字段并更新
        const fieldIndex = tableData[rowIdx].findIndex(field => field.fileId === fileId);
        if (fieldIndex !== -1) {
          const rowData = updatedHandoverData[systemIndex].msg[rowIdx];
          rowData[fieldIndex].value = value;
          const tableRef = XiooTableActions.getTable(config.tableId);
          const rowNode = tableRef.getRowNode(rowKey);
          rowNode.setData({
            ...rowNode.data,
            [fileId]: value,
          });
        }
      }
    }

    // 更新状态
    draftHandoverData.current = updatedHandoverData;
  };

  // 组件全局上下文
  const contextValue: ILeaveTransferContextType = {
    readonly: false,
    isHandler,
    leaverIsOwn,
    handoverData,
    onUpdateHandoverData, // 将更新函数添加到上下文中
    form: configs.context.form, // 表单实例
    tzContext: configs.context, // 天舟上下文
    tzConfigs: configs,
    currentHandler: formData['CURRENT_HANDLER'],
    empAccount: formData['C_EMP_ACCOUNT'],
    empId: formData['C_EMP_ID'],
    empName: formData['C_EMP_NAME'],
    // currentNode: formData['CURRENT_NODE'],
    currentNodeName: formData['CURRENT_NODE_NAME'],
    blockStatusRef,
    dicts,
  };

  // 获取交接数据
  const getHandoverData = () => {
    formData['C_EMP_ACCOUNT'] &&
      formData['C_EMP_ID'] &&
      request({
        ...leaveApis.handoverList,
        params: {
          empAccount: formData['C_EMP_ACCOUNT'],
          empId: formData['C_EMP_ID'],
          scene: 'STA_EXIT',
          empTypeCategory: 'INSIDE',
        },
      }).then(res => {
        console.log('res===========', res);
        Array.isArray(res) && setHandoverData(res);
      });
  };

  // 请求交接数据
  useEffect(() => {
    getHandoverData();
  }, [formData['C_EMP_ACCOUNT'], formData['C_EMP_ID']]);

  const renderExtra = (tipsStatus: TipsStatus) => {
    const isFinished = tipsStatus === 'success';
    const _status = isFinished ? '已完成交接' : '交接中';
    return (
      <div>
        <span className="mx-10">{_status}</span>
        {isFinished ? (
          <CheckCircleFilled style={{ color: '#b7eb67' }} />
        ) : (
          <ClockCircleFilled style={{ color: '#5793df' }} />
        )}
      </div>
    );
  };

  // 需要特殊渲染的才注册
  const sortedHandoverData = handoverData.sort((a, b) => {
    const aTipsStatus: TipsStatus = formatTipsStatus(a, leaverIsOwn);
    const bTipsStatus: TipsStatus = formatTipsStatus(b, leaverIsOwn);
    // 记录状态，避免重新计算
    blockStatusRef.current[a.systemName] = aTipsStatus;
    blockStatusRef.current[b.systemName] = bTipsStatus;
    if (aTipsStatus === 'error' && bTipsStatus === 'success') return -1;
    if (aTipsStatus === 'success' && bTipsStatus === 'error') return 1;
    return 0;
  });
  return (
    <div className="dhr-job-handover-container">
      <LeaveTransferProvider value={contextValue}>
        <Collapse expandIconPosition="end" defaultActiveKey={['WORK_HANDOVER']}>
          {sortedHandoverData.map(item => {
            const { systemName } = item;
            const tipsStatus: TipsStatus = blockStatusRef.current[systemName] || formatTipsStatus(item, leaverIsOwn);
            const module = modules.find(module => module.systemName === systemName);
            if (module && !module?.component) return null;
            return (
              <Panel
                header={
                  <span className="text-16  text-bold  relative " style={{ color: '#333' }}>
                    {item.registerTitle}
                  </span>
                }
                key={systemName}
                extra={renderExtra(tipsStatus)}
              >
                {module && <ModuleRenderer key={systemName} data={item} module={module} config={{ tipsStatus }} />}
                {!module && <DefaultSystemBlock key={systemName} data={item} config={{ noTitle: true, tipsStatus }} />}
              </Panel>
            );
          })}
        </Collapse>
      </LeaveTransferProvider>
    </div>
  );
});
export default Main;
