import React, { createContext, useContext, useState } from 'react';
import { ILeaveTransferContextType } from './index.d';

// 创建上下文
const LeaveTransferContext = createContext<ILeaveTransferContextType | null>(null);

// 上下文提供者组件
export const LeaveTransferProvider: React.FC<{
  value: Omit<ILeaveTransferContextType, 'moduleStatuses' | 'updateModuleStatus'>;
  children: React.ReactNode;
}> = ({ value, children }) => {
  const [moduleStatuses, setModuleStatuses] = useState<Record<string, 'pending' | 'success' | 'error'>>({});

  const updateModuleStatus = (systemName: string, status: 'pending' | 'success' | 'error') => {
    setModuleStatuses(prev => ({
      ...prev,
      [systemName]: status,
    }));
  };

  const contextValue = {
    ...value,
    moduleStatuses,
    updateModuleStatus,
  };

  return <LeaveTransferContext.Provider value={contextValue}>{children}</LeaveTransferContext.Provider>;
};

// 消费者 Hook
export const useLeaveTransfer = () => {
  const context = useContext(LeaveTransferContext);
  if (!context) {
    throw new Error('useLeaveTransfer must be used within a LeaveTransferProvider');
  }
  return context;
};
