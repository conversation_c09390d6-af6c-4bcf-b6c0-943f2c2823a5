/**
 * 交接数据项-Msg
 */
export interface IMsgItem {
  fileId: string;
  fileName: string;
  type: string;
  value: string;
}
export type IMsg = IMsgItem[];

/**
 * 交接数据项
 */
export interface IHandoverData {
  systemName: string;
  id: string;
  type: string;
  errorMsg: string | null;
  registerTitle: string;
  tips: string;
  contentType: string;
  condition: string;
  sortNo: number;
  menuType: string;
  msg: IMsg[];
}

/**
 * 交接上下文类型
 */
export interface ILeaveTransferContextType {
  readonly: boolean; // 是否只读
  isHandler: boolean; // 是否是处理人
  leaverIsOwn: boolean; // 是否是离职人
  handoverData: IHandoverData[]; // 交接数据
  tzConfigs: IObject; // 天舟配置
  tzContext: IObject; // 天舟上下文
  onUpdateHandoverData: (value: any, config: IObject) => void; // 更新交接数据
  form: IObject; // 表单实例
  currentHandler: string; // 当前处理人
  empAccount: string; // 离职人账号
  empId: string; // 离职人id
  empName: string; // 离职人姓名
  currentNodeName: string; // 当前节点名称
  blockStatusRef: React.RefObject<IObject>; // 每个block的状态
  dicts: Record<string, any>; // 字典数据
}

/**
 * 更新交接数据配置
 */
export interface IUpdateConfig {
  systemName: string;
  fileId: string;
  contentType: 'form' | 'table';
  rowKey?: string;
  tableId?: string;
}
