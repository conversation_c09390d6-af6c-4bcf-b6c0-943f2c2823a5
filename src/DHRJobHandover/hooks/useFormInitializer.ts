import { useRef, useCallback } from 'react';
import { IHandoverData } from '../context';
import { setFormInitialValues } from '../utils/formInitializer';

/**
 * 表单初始化 Hook
 * 用于手动处理 DHRJobHandover 模块中特定表单的初始化
 */
export const useFormInitializer = () => {
  const initializedFormsRef = useRef<Set<string>>(new Set());

  // 初始化特定表单
  const initializeForm = useCallback((systemName: string, systemData: IHandoverData, timeout = 100) => {
    if (initializedFormsRef.current.has(systemName)) {
      console.log(`表单 ${systemName} 已经初始化过，跳过`);
      return;
    }

    setFormInitialValues(systemName, systemData, timeout);
    initializedFormsRef.current.add(systemName);
    console.log(`手动初始化表单: ${systemName}`);
  }, []);

  // 强制重新初始化表单（即使已经初始化过）
  const forceInitializeForm = useCallback((systemName: string, systemData: IHandoverData, timeout = 100) => {
    setFormInitialValues(systemName, systemData, timeout);
    initializedFormsRef.current.add(systemName);
    console.log(`强制重新初始化表单: ${systemName}`);
  }, []);

  // 检查表单是否已初始化
  const isFormInitialized = useCallback((systemName: string): boolean => {
    return initializedFormsRef.current.has(systemName);
  }, []);

  // 重置特定表单的初始化状态
  const resetFormInitializationState = useCallback((systemName: string) => {
    initializedFormsRef.current.delete(systemName);
    console.log(`重置表单 ${systemName} 的初始化状态`);
  }, []);

  // 重置所有表单的初始化状态
  const resetAllInitializationState = useCallback(() => {
    initializedFormsRef.current.clear();
    console.log('重置所有表单的初始化状态');
  }, []);

  // 获取已初始化的表单列表
  const getInitializedForms = useCallback((): string[] => {
    return Array.from(initializedFormsRef.current);
  }, []);

  return {
    initializeForm,
    forceInitializeForm,
    isFormInitialized,
    resetFormInitializationState,
    resetAllInitializationState,
    getInitializedForms,
  };
};
