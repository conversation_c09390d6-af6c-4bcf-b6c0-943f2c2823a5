import { useState, useCallback } from 'react';
import { LEAVE_JOIN_MODULE } from '@constants/leave';

// 模块配置类型
export interface ModuleConfig {
  systemName?: string;
  group?: string[];
  component: React.ComponentType<any>;
  priority?: number;
  condition?: (props: any) => boolean;
}

// 模块组
export const MODULE_GROUPS = {
  WORK: 'work',
  ASSET: 'asset',
  ADMIN: 'admin',
  OTHER: 'other',
};

export const useModuleRegistry = () => {
  const [modules, setModules] = useState<ModuleConfig[]>([]);

  // 注册单个模块
  const registerModule = (config: ModuleConfig) => {
    setModules(prev => {
      // 检查是否已存在相同systemName的模块
      const existingIndex = prev.findIndex(m => m.systemName === config.systemName);
      if (existingIndex >= 0) {
        // 替换已存在的模块
        const newModules = [...prev];
        newModules[existingIndex] = config;
        return newModules;
      }
      // 添加新模块
      return [...prev, config];
    });
  };

  // 注册多个模块
  const registerModules = (configs: ModuleConfig[]) => {
    setModules(prev => {
      const newModules = [...prev];
      configs.forEach(config => {
        const existingIndex = newModules.findIndex(m => m.systemName === config.systemName);
        if (existingIndex >= 0) {
          newModules[existingIndex] = config;
        } else {
          newModules.push(config);
        }
      });
      return newModules;
    });
  };

  // 注册核心模块
  const registerCoreModules = () =>
    import('../module').then(({ WorkSystemBlock }) => {
      registerModules([
        // 工作交接模块
        {
          systemName: LEAVE_JOIN_MODULE.WORK_HANDOVER,
          component: WorkSystemBlock,
          group: [LEAVE_JOIN_MODULE.WORK_HANDOVER, LEAVE_JOIN_MODULE.TUTOR_INFO, LEAVE_JOIN_MODULE.EXT_LEAD],
          priority: 10,
        },
        {
          systemName: LEAVE_JOIN_MODULE.TUTOR_INFO,
          component: null,
        },
        {
          systemName: LEAVE_JOIN_MODULE.EXT_LEAD,
          component: null,
        },
      ]);
    });

  // 获取特定模块
  const getModule = (systemName: string) => {
    console.log('modules=====1111', modules, systemName);
    return modules.find(m => m.systemName === systemName);
  };

  return {
    modules,
    registerModule,
    registerModules,
    registerCoreModules,
    getModule,
  };
};
