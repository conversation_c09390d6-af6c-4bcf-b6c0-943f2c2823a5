import React from 'react';
import { LEAVE_JOIN_MODULE } from '@constants/leave';
import EAMOperations from './modules/EAMOperations';
import BabyOperations from './modules/BabyOperations';
import OAOperations from './modules/OAOperations';
import FeeOperations from './modules/FeeOperations';
import WorkHandoverOperations from './modules/WorkHandoverOperations';
import TutorOperations from './modules/TutorOperations';
import ExtLeadOperations from './modules/ExtLeadOperations';
import JiraOperations from './modules/JiraOperations';

// 模块操作按钮映射表
const MODULE_OPERATIONS_MAP = {
  [LEAVE_JOIN_MODULE.EAM]: EAMOperations,
  [LEAVE_JOIN_MODULE.BABY]: BabyOperations,
  [LEAVE_JOIN_MODULE.OA]: OAOperations,
  [LEAVE_JOIN_MODULE.FEE]: FeeOperations,
  [LEAVE_JOIN_MODULE.WORK_HANDOVER]: WorkHandoverOperations,
  [LEAVE_JOIN_MODULE.TUTOR_INFO]: TutorOperations,
  [LEAVE_JOIN_MODULE.EXT_LEAD]: ExtLeadOperations,
  [LEAVE_JOIN_MODULE.JIRA]: JiraOperations,
};

interface ModuleOperationsProps {
  moduleType: string;
  data: any;
  context: any;
}

const ModuleOperations: React.FC<ModuleOperationsProps> = ({ moduleType, data, context }) => {
  // 获取对应模块的操作组件
  const OperationsComponent = MODULE_OPERATIONS_MAP[moduleType];

  // 如果没有对应的操作组件，返回空
  if (!OperationsComponent) {
    return null;
  }

  return <OperationsComponent data={data} context={context} />;
};

export default ModuleOperations;
