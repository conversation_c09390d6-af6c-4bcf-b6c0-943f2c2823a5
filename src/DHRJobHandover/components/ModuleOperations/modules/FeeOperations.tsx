import React, { useState } from 'react';
import { Button, Space, message, Modal } from 'antd';
import { XiooTableActions } from '@cvte/xioo-ag-table';
import EmpSelectModal from '../../EmpSelectModal';
import { request } from '@utils/http';
import leaveApis from '@apis/leave';

interface FeeOperationsProps {
  data: any;
  context: any;
}

const TABLE_ID = 'FEE_TABLE';

/**
 * 费控模块用于处理员工在费控系统中的未结单据
 *
 * 功能:
 * - 设置交接人: 弹出选人弹窗，选择人，然后确认之后就把人都设置进table form中的交接人字段
 * - 更新交接人: 选择好费控交接人后，调用费控更新接口。更新完毕，调用获取费控未结单据接口获取最新列表
 */
const FeeOperations: React.FC<FeeOperationsProps> = ({ data, context }) => {
  const { readonly, moduleStatuses } = context;
  const [loading, setLoading] = useState(false);
  // 设置交接人
  const handleSetTransfereeFee = () => {
    const selectedRows = XiooTableActions.getTable(TABLE_ID)?.getSelectedRows();
    if (selectedRows.length === 0) {
      message.warning('请选择数据设置交接人');
      return;
    }
    const isExistNoNeedHandover = selectedRows.some(row => row.handover === '无需交接');
    if (isExistNoNeedHandover) {
      message.warning('不需要选择无需交接的数据');
      return;
    }
    EmpSelectModal.render({
      title: '选择交接人',
      placeholder: '请搜索并选择交接人',
      onOk: (value: string, option: any) => {
        selectedRows.forEach(row => {
          row.handover = JSON.stringify({
            empId: option.empId,
            domainCode: option.empAccount,
            othername: option.empName,
          });
        });
        XiooTableActions.getTable(TABLE_ID)?.onUpdateItems(selectedRows);
      },
    });
  };

  // 更新交接人
  const handleUpdateTransferee = () => {
    const selectedRows = XiooTableActions.getTable(TABLE_ID)?.getSelectedRows();
    if (selectedRows.length === 0) {
      message.warning('请选择数据更新交接人');
      return;
    }
    console.log('selectedRows===========', selectedRows);
    const isExistNoNeedHandover = selectedRows.some(row => row.handover === '无需交接');
    if (isExistNoNeedHandover) {
      message.warning('不需要选择无需交接的数据');
      return;
    }
    const isExistHandover = selectedRows.every(row => row.handover !== '-' || !!row.handover);
    if (!isExistHandover) {
      message.warning('请选择数据更新交接人');
      return;
    }
    const feeData = selectedRows.map(row => {
      const handover = JSON.parse(row.handover);
      return {
        billCode: row.billCode,
        handoverAccount: handover.domainCode,
      };
    });
    console.log('feeData===========', feeData);
    setLoading(true);
    request({
      ...leaveApis.handoverFeeUpdate,
      data: feeData,
    })
      .then(res => {
        console.log('更新交接人===========', res);
        Modal.info({
          title: '成功提交! 刷新主页面后即可查看交接人，请及时跟进完成交接。',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 如果是只读模式，不显示操作按钮
  if (readonly) {
    return null;
  }

  // 如果模块已经成功提交，显示已完成状态
  if (moduleStatuses && moduleStatuses[data.systemName] === 'success') {
    return (
      <Space>
        <Button type="primary" disabled>
          已完成
        </Button>
      </Space>
    );
  }

  return (
    <Space>
      <Button onClick={handleSetTransfereeFee}>设置交接人</Button>
      <Button onClick={handleUpdateTransferee} loading={loading}>
        更新交接人
      </Button>
    </Space>
  );
};

export default FeeOperations;
