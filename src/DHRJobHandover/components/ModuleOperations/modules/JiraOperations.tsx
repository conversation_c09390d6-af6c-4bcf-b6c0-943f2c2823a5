import React from 'react';
import { Button, Space, message } from 'antd';

interface BabyOperationsProps {
  data: any;
  context: any;
}

/**
 * 幼早教信息模块操作组件
 *
 * 功能:
 * - 发起交接: 提交幼早教信息确认
 */
const BabyOperations: React.FC<BabyOperationsProps> = ({ data, context }) => {
  const { moduleStatuses } = context;
  // const showButton = isSPECIAL || (leaverIsOwn && nodeName === NODE_NAMES.EMPLOYEE_LEAVE_OFFICE);

  // 如果模块已经成功提交，显示已完成状态
  if (moduleStatuses && moduleStatuses[data.systemName] === 'success') {
    return (
      <Space>
        <Button type="primary" disabled>
          已完成
        </Button>
      </Space>
    );
  }

  const handleJumpToJira = () => {
    window.open('https://jira.cvte.com/secure/Dashboard.jspa', '_blank');
  };

  return (
    <Space>
      <Button type="primary" onClick={handleJumpToJira}>
        点我跳转
      </Button>
    </Space>
  );
};

export default BabyOperations;
