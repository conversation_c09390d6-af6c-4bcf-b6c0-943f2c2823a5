import React from 'react';
import { Button, Space, message, Modal } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import XiooTableActions from '@cvte/xioo-ag-table/es/XiooTableActions';
import EmpSelectModal from '../../EmpSelectModal';

interface TutorOperationsProps {
  data: any;
  context: any;
}

const TABLE_ID = 'TUTOR_INFO_TABLE';
/**
 * 导师信息模块用于处理离职员工作为导师的交接
 *
 * 功能:
 * - 设置交接人: 为每个被指导人选择新导师
 */
const TutorOperations: React.FC<TutorOperationsProps> = ({ data, context }) => {
  const { readonly, moduleStatuses, updateModuleStatus } = context;

  // 如果是只读模式，不显示操作按钮
  if (readonly) {
    return null;
  }

  const handleSetTransfereeFee = () => {
    console.log('设置交接人');
    const selectedRows = XiooTableActions.getTable(TABLE_ID)?.getSelectedRows();
    if (selectedRows.length === 0) {
      message.warning('请选择数据设置交接人');
      return;
    }
    EmpSelectModal.render({
      title: '选择交接人',
      placeholder: '请搜索并选择交接人',
      onOk: (value: string, option: any) => {
        selectedRows.forEach(row => {
          row.handoverEmp = JSON.stringify({
            empId: option.empId,
            domainCode: option.empAccount,
            othername: option.empName,
          });
        });
        XiooTableActions.getTable(TABLE_ID)?.onUpdateItems(selectedRows);
      },
    });
  };

  // 如果模块已经成功提交，显示已完成状态
  if (moduleStatuses && moduleStatuses[data.systemName] === 'success') {
    return (
      <Space>
        <Button type="primary" disabled>
          已完成
        </Button>
      </Space>
    );
  }
  return (
    <Space>
      <Button type="primary" onClick={handleSetTransfereeFee}>
        设置交接人
      </Button>
    </Space>
  );
};

export default TutorOperations;
