import React from 'react';
import { Button, Space, message, Modal } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import EmpSelectModal from '../../EmpSelectModal';
import { XiooTableActions } from '@cvte/xioo-ag-table';
import form from 'antd/lib/form';
import { request } from '@utils/http';
import leaveApis from '@apis/leave';

interface OAOperationsProps {
  data: any;
  context: any;
}

/**
 * OA模块用于处理员工在OA系统中的待办事项
 *
 * 功能:
 * - 设置交接人: 为待办事项设置交接人 (批量设置功能)
 * - 发起交接: 提交OA待办交接申请 (过滤掉包含"离职"的待办事项)
 */
const OAOperations: React.FC<OAOperationsProps> = ({ data, context }) => {
  const { readonly, moduleStatuses, updateModuleStatus, form, tzContext } = context;

  // 设置交接人
  const handleSetTransferee = () => {
    const modal = EmpSelectModal.render({
      title: '选择负责人',
      placeholder: '请搜索并选择负责人',
      onOk: (value: string, option: any) => {
        console.log('选择的人员信息:', option);
        const selectedRows = XiooTableActions.getTable(`OA_TABLE`)?.getSelectedRows();
        console.log('selectedRows===========', selectedRows);
        selectedRows.forEach(row => {
          row.handoverEmp = JSON.stringify({
            empId: option.empId,
            domainCode: option.empAccount,
            othername: option.empName,
          });
        });
        XiooTableActions.getTable(`OA_TABLE`)?.onUpdateItems(selectedRows);
      },
    });
  };

  // 发起交接
  // 发起oa交接
  const handleChangeOaHandoverInitiate = () => {
    const oaTable = XiooTableActions.getTable(`OA_TABLE`);
    const selectedRows = oaTable.getSelectedRows();
    const mainFormValues = form.getFieldsValue();
    // const empDomainCode = mainFormValues['C_EMP_ID'];
    console.log('selectedRows===========', selectedRows);
    if (selectedRows.length === 0) {
      message.warning('请选择数据发起交接');
      return;
    }
    const sysWfExtendDTOS = [];
    let isValidatePass = true;
    selectedRows.forEach((row, rowIndex) => {
      // const toHandler = form.getFieldValue(`OA&${row.key}&handoverEmp`);
      if (!row.handoverEmp) {
        message.warning(`请完善选择数据中第${rowIndex + 1}行数据的离职交接人`);
        isValidatePass = false;
        return;
      }
      const handoverEmp = JSON.parse(row.handoverEmp);
      sysWfExtendDTOS.push({
        curHandler: mainFormValues['C_EMP_ACCOUNT'],
        processId: tzContext.config.id,
        toHandler: handoverEmp.domainCode,
        auditNote: '同意',
        routeTag: row.routeTag,
      });
    });
    if (!isValidatePass) return;
    console.log('sysWfExtendDTOS===========', sysWfExtendDTOS);
    Modal.confirm({
      title: '确定要发起交接吗？',
      onOk: () => {
        return new Promise((resolve, reject) => {
          request({
            ...leaveApis.handoverOaHandover,
            data: sysWfExtendDTOS,
            onSuccess: res => {
              message.success('发起交接成功');
              console.log('oa交接res===========', res);
              resolve(true);
            },
            onError: err => {
              message.error('发起交接失败');
              console.log('oa交接err===========', err);
              reject(false);
            },
          });
          // entity.initiateOaHandover.fetch({
          //   data: sysWfExtendDTOS,
          //   sucCallback: res => {
          //     this.setState({
          //       useOAkey: selectedRows.map(item => item.key),
          //       selectedRowKeys: [],
          //       selectedRows: [],
          //     });
          //     resolve();
          //     this.handleJoinTips();
          //   },
          //   errCallback: res => {
          //     reject();
          //   },
          // });
        }).catch(() => console.log('Oops errors!'));
      },
    });
  };

  // 如果是只读模式，不显示操作按钮
  if (readonly) {
    return null;
  }

  // 如果模块已经成功提交，显示已完成状态
  if (moduleStatuses && moduleStatuses[data.systemName] === 'success') {
    return (
      <Space>
        <Button type="primary" disabled>
          已完成
        </Button>
      </Space>
    );
  }

  return (
    <Space>
      <Button onClick={handleSetTransferee}>设置交接人</Button>
      <Button type="primary" onClick={handleChangeOaHandoverInitiate}>
        发起交接
      </Button>
    </Space>
  );
};

export default OAOperations;
