import React, { useEffect, useState } from 'react';
import { Button, message, Space } from 'antd';
import { WULIFormActions } from '@cvte/wuli-antd';
import { XiooTableActions } from '@cvte/xioo-ag-table';
import { toDateFormat } from '@utils/tools';
import { useFormInitializer } from '../../../hooks/useFormInitializer';
import { request } from '@utils/http';
import classnames from 'classnames';
import commonApis from '@apis/common';

interface WorkHandoverOperationsProps {
  data: any;
  context: any;
}
/**
 * 工作交接是离职交接中的核心模块
 *
 * 功能:
 * - 工作交接单上传: 上传工作交接文档
 * - 设置交接人: 选择工作交接人
 * - 提交审批: 发起工作交接流程
 */
const WorkHandoverOperations: React.FC<WorkHandoverOperationsProps> = ({ data, context }) => {
  const { readonly, moduleStatuses, form } = context;
  const [flowStatus, setFlowStatus] = useState<string>('');
  const [flowStatusName, setFlowStatusName] = useState<string>('');

  useEffect(() => {
    const msg = data.msg[0];
    console.log('msg======', msg);
    const flowStatus = msg?.find(item => item.fileId === 'wfStatus')?.value;
    const flowStatusName = msg?.find(item => item.fileId === 'wfAuditStatus')?.value;
    console.log('开始初始化工作交接表单:', data, data.systemName);
    console.log('flowStatus======', flowStatus, flowStatusName);
    setFlowStatus(flowStatus);
    setFlowStatusName(flowStatusName);
  }, [data.systemName, data.contentType]);

  // 获取婴幼儿明细表数据
  const genTB_EXIT_KG_CONFIRM_ENTRY_Data = () => {
    const table = XiooTableActions.getTable(`BABY_TABLE`);
    const data = table?.getAllData() || [];
    console.log('婴幼儿明细表数据======', data);
    // 校验规则， isSpouseInCompany 不能为空， 加入 isSpouseInCompany为 '0', 则C_DROP_DATE 不能为空， C_DROP 不能为 '0'也不能为空
    const errorList = [];
    data.forEach(row => {
      if (!row.isSpouseInCompany) {
        errorList.push(`配偶是否在司任职 不能为空`);
      }
      if (row.isSpouseInCompany === '0') {
        if (!row.isDropOut || row.isDropOut === '0') {
          errorList.push('退园需确认为是');
        }
        if (!row.dropOutTime) {
          errorList.push('退园日期不能为空');
        }
      }
    });
    if (errorList.length > 0) {
      message.error(errorList.join('\n'));
      return null;
    }
    return data.map(row => ({
      dataList: [
        {
          apiName: 'C_BABY_NAME', // 姓名
          attrValue: row.babyName || '',
        },
        {
          apiName: 'C_BABY_ID', // 婴幼儿ID
          attrValue: row.babyId || '',
        },
        {
          apiName: 'C_BABY_RELA', // 关系
          attrValue: row.relation || '',
        },
        {
          apiName: 'C_BABY_BIRTH', // 出生日期
          attrValue: row.birthday || '',
        },
        {
          apiName: 'C_WORK_PLACE', // 园区
          attrValue: row.kindergartenName || '',
        },
        {
          apiName: 'C_EDU_STAGE', // 教育阶段
          attrValue: row.eduPhaseName || '',
        },
        {
          apiName: 'C_STATUS', // 状态
          attrValue: row.statusName || '',
        },
        {
          apiName: 'C_SPOUSE_IN_JOB', // 配偶是否在司任职
          attrValue: row.isSpouseInCompany || '',
        },
        {
          apiName: 'C_DROP', // 是否退园
          attrValue: row.isDropOut || '',
        },
        {
          apiName: 'C_DROP_DATE', // 退园日期
          attrValue: row.dropOutTime || '',
        },
      ],
    }));
  };

  const genSubFormData = (mainFormValues: Record<string, any>) => {
    const wfName = `婴幼儿交接_${mainFormValues['C_EMP_NAME']}_${toDateFormat(new Date(), 'YYYY_MM_DD')}`;
    return [
      {
        apiName: 'C_WF_NAME', // 离职流程
        attrValue: wfName,
      },
      {
        apiName: 'DESCRIPTION', // 流程标题_系统用
        attrValue: wfName,
      },
      {
        apiName: 'C_EXIT_APPLY_ID', // 离职流程
        attrValue: context.tzContext.config.id,
      },
      {
        apiName: 'C_EMP_ID', // 员工
        attrValue: mainFormValues['C_EMP_ID'],
      },
      {
        apiName: 'C_EMP_POSITION', // 员工职位
        attrValue: mainFormValues['C_JOB_HID'],
      },
      {
        apiName: 'C_EMP_DEPT', // 员工部门
        attrValue: mainFormValues['C_EMP_DEPT'],
      },
      {
        apiName: 'C_EXIT_DATE', // 离职日期
        attrValue: mainFormValues['C_EXIT_DATE'],
      },
      // {
      //   apiName: 'C_EMP_TYPE_CATEGORY', // 员工类型大类
      //   attrValue: mainFormValues['C_EMP_TYPE_CATEGORY'],
      // },
      // {
      //   apiName: 'C_UNIT_MANAGER', // 部门主管
      //   attrValue: mainFormValues.unitManager,
      // },
      // {
      //   apiName: 'C_HRMO', // HRMO
      //   attrValue: mainFormValues.hrmo,
      // },
      // {
      //   apiName: 'C_HRD', // HRD
      //   attrValue: mainFormValues.hrd,
      // },
    ];
  };

  // 提交审批
  const handleSubmitApproval = () => {
    const mainFormValues = form.getFieldsValue();
    console.log('主表数据======', mainFormValues);
    const subFormData = genSubFormData(mainFormValues);
    const babyTableData = genTB_EXIT_KG_CONFIRM_ENTRY_Data();
    if (!babyTableData) return;
    console.log('婴幼儿明细表数据======', babyTableData);
    const formClassId = '954551dd59a241a6ae5f4cdca7642955';
    const formAppId = 'efa37869ee1c4930b434a4c7b1548d46';
    const submitData = {
      classId: formClassId, // 工作交接子流程表单
      objAttrItemList: subFormData,
      tableList: [
        {
          tableName: 'TB_EXIT_KG_CONFIRM_ENTRY',
          rowList: babyTableData,
        },
      ],
    };

    console.log('提交数据======', submitData);
    request({
      ...commonApis.flowFormSubmit,
      data: submitData,
      // 婴幼儿交接
      params: {
        formClassId,
      },
      headers: {
        'x-app-id': formAppId,
      },
      onSuccess: res => {
        console.log('提交交接成功======', res);
        const processId = res.content?.id;
        console.log('processId======', processId);
        if (processId) {
          message.success('提交交接成功');
          setFlowStatus('ADUIT');
          setFlowStatusName('审批中');
        } else {
          message.error('提交审批失败');
        }
      },
      onError: err => {
        console.log('提交审批失败======', err);
      },
    });
  };
  // 获取流程

  // 如果是只读模式，不显示操作按钮
  if (readonly) {
    return null;
  }

  // 如果模块已经成功提交，显示已完成状态
  if (moduleStatuses && moduleStatuses[data.systemName] === 'success') {
    return (
      <Space>
        <Button type="primary" disabled>
          已完成
        </Button>
      </Space>
    );
  }

  const isDisabled = !!flowStatus;
  return (
    <Space>
      <Button type="primary" onClick={handleSubmitApproval} disabled={isDisabled}>
        提交审批
      </Button>
      {flowStatusName && (
        <span className={classnames('text-12 mx-4', flowStatus === 'FINISH' ? 'text-success' : 'text-danger')}>
          {flowStatusName}
        </span>
      )}
    </Space>
  );
};

export default WorkHandoverOperations;
