import React from 'react';
import { Button, Space, message, Modal } from 'antd';
import { XiooTableActions } from '@cvte/xioo-ag-table';
import leaveApis from '@apis/leave';
import { request } from '@utils/http';
import EmpSelectModal from '../../EmpSelectModal';

interface EAMOperationsProps {
  data: any;
  context: any;
}

/**
 * 资产交接模块用于处理员工持有的公司资产
 *
 * 功能:
 * - 交接方式选择: 选择"归还入库"或"交接"
 * - 设置交接人: 为选中的资产设置交接人
 * - 归还入库: 批量将资产归还入库
 * - 发起交接: 提交资产交接申请
 * - 提交变更: 修改已提交的交接信息
 */
const EAMOperations: React.FC<EAMOperationsProps> = ({ data, context }) => {
  const { form, readonly, moduleStatuses, updateModuleStatus, tzConfigs, tzContext, onUpdateHandoverData } = context;

  // 归还入库
  const handleReturnToWarehouse = () => {
    Modal.confirm({
      title: '确认归还入库',
      content: '归还入库后，将自动清空交接人字段。是否继续？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        message.success('资产已归还入库');
      },
    });
  };

  // 设置交接人
  const handleSetTransferee = () => {
    const modal = EmpSelectModal.render({
      title: '选择负责人',
      placeholder: '请搜索并选择负责人',
      onOk: (value: string, option: any) => {
        console.log('选择的人员ID:', value);
        console.log('选择的人员信息:', option);
        const selectedRows = XiooTableActions.getTable(`EAM_TABLE`)?.getSelectedRows();
        console.log('selectedRows===========', selectedRows);
        if (selectedRows.length > 0) {
          selectedRows.forEach(row => {
            row.handoverEmp = JSON.stringify({
              empId: option.empId,
              domainCode: option.empAccount,
              othername: option.empName,
            });
            row.return_type = 'HANDOVER';
          });
          XiooTableActions.getTable(`EAM_TABLE`)?.onUpdateItems(selectedRows);
        }
      },
      onCancel: () => {
        console.log('取消选择');
      },
    });
  };

  // 发起交接
  const handleInitiateTransfer = () => {
    const mainFormValues = form.getFieldsValue();
    const eamTable = XiooTableActions.getTable(`EAM_TABLE`);
    const eamData = eamTable.getAllData();
    console.log('eamData===========', eamData);
    const excludeFields = ['__canEdit__', 'key', '__isClickSelectAll__', '__selected__'];
    const data = {
      systemName: 'EAM',
      msg: eamData.map(row => [
        Object.keys(row)
          .filter(key => !excludeFields.includes(key))
          .map(key => ({
            fileId: key,
            value: row[key],
          })),
      ]),
    };
    console.log('data===========', data);
    const params = {
      applicantId: mainFormValues['C_EMP_ID'],
      empTypeCategory: mainFormValues['C_EMP_TYPE_CATEGORY'],
      exitDate: mainFormValues['C_EXIT_DATE'],
      applyId: tzContext.config.id,
    };
    console.log('params===========', params);
    request({
      ...leaveApis.handoverEamHandover,
      data,
      params,
      onSuccess: res => {
        console.log('资产交接申请已提交res===========', res);
        message.success('资产交接申请已提交');
      },
      onError: err => {
        message.error('资产交接申请失败', err);
      },
    });

    // leaveApis.handoverEamHandover.fetch({
    //   data,
    //   params,
    //   sucCallback: res => {
    //     console.log('资产交接申请已提交res===========', res);
    //     message.success('资产交接申请已提交');
    //   },
    //   errCallback: res => {
    //     message.error('资产交接申请失败');
    //   },
    // });
  };

  // 提交变更
  const handleSubmitChanges = () => {
    // 这里应该调用API提交资产交接变更
    message.success('资产交接变更已提交');
  };

  // 如果是只读模式，不显示操作按钮
  if (readonly) {
    return null;
  }

  // 如果模块已经成功提交，显示已完成状态
  if (moduleStatuses && moduleStatuses[data.systemName] === 'success') {
    return (
      <Space>
        <Button type="primary" disabled>
          已完成
        </Button>
      </Space>
    );
  }

  return (
    <Space>
      {/* <Button type="primary" onClick={handleReturnToWarehouse}>
        归还入库
      </Button> */}
      <Button onClick={handleSetTransferee}>设置交接人</Button>
      <Button onClick={handleInitiateTransfer}>发起交接</Button>
      <Button onClick={handleSubmitChanges}>提交变更</Button>
    </Space>
  );
};

export default EAMOperations;
