import React, { useEffect, useState } from 'react';
import { Button, message, Space } from 'antd';
import { WULIFormActions } from '@cvte/wuli-antd';
import { XiooTableActions } from '@cvte/xioo-ag-table';
import { toDateFormat } from '@utils/tools';
import { useFormInitializer } from '../../../hooks/useFormInitializer';
import { request } from '@utils/http';
import classnames from 'classnames';
import commonApis from '@apis/common';

interface WorkHandoverOperationsProps {
  data: any;
  context: any;
}

/**
 * 工作交接是离职交接中的核心模块
 *
 * 功能:
 * - 工作交接单上传: 上传工作交接文档
 * - 设置交接人: 选择工作交接人
 * - 提交审批: 发起工作交接流程
 */
const WorkHandoverOperations: React.FC<WorkHandoverOperationsProps> = ({ data, context }) => {
  const { readonly, moduleStatuses, form } = context;
  const [flowStatus, setFlowStatus] = useState<string>('');
  const [flowStatusName, setFlowStatusName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  // 使用表单初始化 Hook
  const { initializeForm, isFormInitialized } = useFormInitializer();

  // 只在组件挂载时初始化工作交接表单
  useEffect(() => {
    if (!isFormInitialized(data.systemName) && data.contentType === 'form') {
      const msg = data.msg[0];
      console.log('msg======', msg);
      const flowStatus = msg?.find(item => item.fileId === 'wfStatus')?.value;
      const flowStatusName = msg?.find(item => item.fileId === 'wfAuditStatus')?.value;
      console.log('开始初始化工作交接表单:', data, data.systemName);
      console.log('flowStatus======', flowStatus, flowStatusName);
      setFlowStatus(flowStatus);
      setFlowStatusName(flowStatusName);

      initializeForm(data.systemName, data, 200);
    }
  }, [data.systemName, data.contentType, initializeForm, isFormInitialized]);

  // 获取导师数据
  const genTutorData = () => {
    const tutorTable = XiooTableActions.getTable(`TUTOR_INFO_TABLE`);
    const tutorData = tutorTable?.getAllData() || [];
    return tutorData.map(row => ({
      dataList: [
        {
          apiName: 'C_EMP_NAME', // 姓名
          attrValue: row.name || '',
        },
        {
          apiName: 'C_EMP_ACCOUNT', // 域账号
          attrValue: row.account || '',
        },
        {
          apiName: 'C_EMP_TYPE_NAME', // 新人类型
          attrValue: row.type || '',
        },
        {
          apiName: 'C_TUTOR_NAME', // 导师
          attrValue: row.tutorNames || '',
        },
        {
          apiName: 'C_HANDOVER_EMP', // 离职交接人
          attrValue: row.handoverEmp || '',
        },
      ],
    }));
  };
  // 获取外包数据
  const genOdData = () => {
    const odTable = XiooTableActions.getTable('EXT_LEAD_TABLE');
    const odData = odTable?.getAllData() || [];
    return odData.map(row => ({
      dataList: [
        {
          apiName: 'C_EMP_NAME', // 姓名
          attrValue: row.name || '',
        },
        {
          apiName: 'C_EMP_ACCOUNT', // 域账号
          attrValue: row.account || '',
        },
        {
          apiName: 'C_EMP_TYPE', // 人员类型
          attrValue: row.extEmpType || '',
        },
        {
          apiName: 'C_PRINCIPAL', // 负责人
          attrValue: row.leaderName || '',
        },
        // {
        //   apiName: 'C_INFO_PRINCIPAL', // 信息维护人
        //   attrValue: row.infoPrincipal || '',
        // },
        {
          apiName: 'C_HANDOVER_EMP', // 离职交接人
          attrValue: row.handoverEmp || '',
        },
      ],
    }));
  };

  const genSubFormData = (mainFormValues: any, workHandoverFormValues: any) => {
    const C_HANDOVER_FILE = workHandoverFormValues['fileId']
      ? JSON.parse(workHandoverFormValues['fileId'])?.fileId
      : '';
    const handoverEmp = workHandoverFormValues['handoverEmp'] ? JSON.parse(workHandoverFormValues['handoverEmp']) : '';

    console.log('C_HANDOVER_FILE======', C_HANDOVER_FILE);
    const wfName = `部门工作交接_${mainFormValues['C_EMP_NAME']}_${toDateFormat(new Date(), 'YYYY_MM_DD')}`;
    return [
      {
        apiName: 'C_WF_NAME', // 离职流程
        attrValue: wfName,
      },
      {
        apiName: 'DESCRIPTION', // 流程标题_系统用
        attrValue: wfName,
      },
      {
        apiName: 'C_EXIT_APPLY_ID', // 离职流程
        attrValue: context.tzContext.config.id,
      },
      {
        apiName: 'C_EMP_ID', // 员工
        attrValue: mainFormValues['C_EMP_ID'],
      },
      {
        apiName: 'C_EMP_POSITION', // 员工职位
        attrValue: mainFormValues['C_JOB_HID'],
      },
      {
        apiName: 'C_EMP_DEPT', // 员工部门
        attrValue: mainFormValues['C_DEPT_HID'],
      },
      {
        apiName: 'C_EXIT_DATE', // 离职日期
        attrValue: mainFormValues['C_EXIT_DATE'],
      },
      {
        apiName: 'C_EMP_TYPE_CATEGORY', // 员工类型大类
        attrValue: mainFormValues['C_EMP_TYPE_CATEGORY'],
      },
      {
        apiName: 'C_HANDOVER_FILE', // 工作交接单
        attrValue: C_HANDOVER_FILE,
      },
      // {
      //   apiName: 'C_UNIT_MANAGER', // 部门主管
      //   attrValue: mainFormValues.unitManager,
      // },
      // {
      //   apiName: 'C_HRMO', // HRMO
      //   attrValue: mainFormValues.hrmo,
      // },
      // {
      //   apiName: 'C_HRD', // HRD
      //   attrValue: mainFormValues.hrd,
      // },
      {
        apiName: 'C_HANDOVER_EMP', // 离职交接人
        attrValue: handoverEmp.empId,
      },
      {
        apiName: 'C_HANDOVER_EMP_NAME', // 交接人姓名
        attrValue: handoverEmp.othername,
      },
      {
        apiName: 'C_HANDOVER_EMAIL', // 交接人邮箱
        attrValue: workHandoverFormValues.handoverEmpEmail,
      },
      {
        apiName: 'C_HANDOVER_ACCOUNT', // 交接人域账号
        attrValue: handoverEmp.account,
      },
      {
        apiName: 'C_HELD_ASSETS', // 是否持有以下公司资产
        attrValue: workHandoverFormValues.holdingCompanyAssets,
      },
      {
        apiName: 'C_ASSETS_DETAIL', // 请填写具体资产信息
        attrValue: workHandoverFormValues.assetsDetails,
      },
      {
        apiName: 'C_ALREADY_RETURN', // 请确认公司资产是否已经归还
        attrValue: workHandoverFormValues.alreadyReturn,
      },
      {
        apiName: 'C_LEAVE_SUBMIT', // 请假是否已提交系统
        attrValue: workHandoverFormValues.isLeaveSubmit,
      },
      {
        apiName: 'C_TRANSPORT_SUBMIT', // 费用补贴申请是否已提交
        attrValue: workHandoverFormValues.transportSubmit,
      },
    ];
  };

  // 提交审批
  const handleSubmitApproval = () => {
    const formKey = `${data.systemName}_FORM`;
    const workHandoverForm = WULIFormActions.get(formKey);

    // const tutorTable = XiooTableActions.getTable(`TUTOR_INFO_TABLE`);
    // const odTable = XiooTableActions.getTable('EXT_LEAD_TABLE');
    // const tutorData = tutorTable.getAllData();
    // const odData = odTable.getAllData();

    // console.log('导师徒弟关系明细表数据======', tutorData);
    // console.log('外包信息表数据======', odData);
    if (!workHandoverForm) return;

    const workHandoverFormValues = workHandoverForm.getValue();
    console.log('工作交接审批数据======', workHandoverFormValues);
    const mainFormValues = form.getFieldsValue();
    console.log('主表数据======', mainFormValues);
    const subFormData = genSubFormData(mainFormValues, workHandoverFormValues);
    const tutorData = genTutorData();
    const odData = genOdData();
    console.log('导师徒弟关系明细表数据======', tutorData);
    console.log('外包信息表数据======', odData);
    const submitData = {
      classId: '06d878e580c04797925123d0452bc85d', // 工作交接子流程表单
      objAttrItemList: subFormData,
      tableList: [
        {
          tableName: 'TB_EXIT_DH_TUTOR_ENTRY', // 导师徒弟关系明细表
          rowList: tutorData || [],
        },
        {
          tableName: 'TB_EXIT_DH_OD_ENTRY', // 外包信息表
          rowList: odData || [],
        },
      ],
    };

    console.log('提交数据======', submitData);
    setLoading(true);
    request({
      ...commonApis.flowFormSubmit,
      data: submitData,
      // 部门工作交接确认流程
      params: {
        formClassId: '06d878e580c04797925123d0452bc85d',
      },
      headers: {
        'x-app-id': 'efa37869ee1c4930b434a4c7b1548d46',
      },
      onSuccess: res => {
        console.log('提交审批成功======', res);
        const processId = res.content?.id;
        console.log('processId======', processId);
        if (processId) {
          message.success('提交审批成功');
          setFlowStatus('ADUIT');
          setFlowStatusName('审批中');
        } else {
          message.error('提交审批失败');
        }
        setLoading(false);
      },
      onError: err => {
        console.log('提交审批失败======', err);
        setLoading(false);
      },
    });
  };
  // 获取流程

  // 如果是只读模式，不显示操作按钮
  if (readonly) {
    return null;
  }

  // 如果模块已经成功提交，显示已完成状态
  if (moduleStatuses && moduleStatuses[data.systemName] === 'success') {
    return (
      <Space>
        <Button type="primary" disabled>
          已完成
        </Button>
      </Space>
    );
  }

  const isDisabled = !!flowStatus;
  return (
    <Space>
      <Button type="primary" onClick={handleSubmitApproval} disabled={isDisabled} loading={loading}>
        提交审批
      </Button>
      {flowStatusName && (
        <span className={classnames('text-12 mx-4', flowStatus === 'FINISH' ? 'text-success' : 'text-danger')}>
          {flowStatusName}
        </span>
      )}
    </Space>
  );
};

export default WorkHandoverOperations;
