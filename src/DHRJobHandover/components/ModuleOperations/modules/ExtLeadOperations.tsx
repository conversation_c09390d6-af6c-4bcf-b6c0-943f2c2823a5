import React from 'react';
import { Button, Space, message, Modal } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import XiooTableActions from '@cvte/xioo-ag-table/es/XiooTableActions';
import EmpSelectModal from '../../EmpSelectModal';

interface ExtLeadOperationsProps {
  data: any;
  context: any;
}

const TABLE_ID = 'EXT_LEAD_TABLE';

/**
 * 外协交接模块用于处理离职员工负责的外协人员交接
 *
 * 功能:
 * - 设置交接人: 为每个外协人员选择新负责人
 */
const ExtLeadOperations: React.FC<ExtLeadOperationsProps> = ({ data, context }) => {
  const { readonly, moduleStatuses, updateModuleStatus } = context;

  // 设置交接人
  const handleSetTransferee = () => {
    console.log('设置交接人');
    const selectedRows = XiooTableActions.getTable(TABLE_ID)?.getSelectedRows();
    if (selectedRows.length === 0) {
      message.warning('请选择数据设置交接人');
      return;
    }
    EmpSelectModal.render({
      title: '选择交接人',
      placeholder: '请搜索并选择交接人',
      onOk: (value: string, option: any) => {
        selectedRows.forEach(row => {
          row.handoverEmp = JSON.stringify({
            empId: option.empId,
            domainCode: option.empAccount,
            othername: option.empName,
          });
        });
        XiooTableActions.getTable(TABLE_ID)?.onUpdateItems(selectedRows);
      },
    });
  };

  // 如果是只读模式，不显示操作按钮
  if (readonly) {
    return null;
  }

  // 如果模块已经成功提交，显示已完成状态
  if (moduleStatuses && moduleStatuses[data.systemName] === 'success') {
    return (
      <Space>
        <Button type="primary" disabled>
          已完成
        </Button>
      </Space>
    );
  }

  // 检查是否有外协人员数据
  const hasExtStaff = data.msg && data.msg.length > 0;

  return (
    <Space>
      <Button type="primary" onClick={handleSetTransferee} disabled={!hasExtStaff}>
        设置交接人
      </Button>
    </Space>
  );
};

export default ExtLeadOperations;
