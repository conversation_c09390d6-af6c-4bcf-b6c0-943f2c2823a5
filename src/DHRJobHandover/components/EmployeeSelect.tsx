import React, { useState, useRef, useCallback } from 'react';
import { Select, Spin, Empty } from 'antd';
import axios from 'axios';
import { request } from '@utils/http';
import useDebounce from '@hooks/useDebounce';
import { customTemplate } from '@utils/tools';

const DEFAULT_OPTION_KEY = 'empId';
const DEFAULT_OPTION_LABEL = 'data.empName-data.domainAccount';

interface EmployeeSelectProps {
  value?: string;
  onChange?: (value: string, option: any) => void;
  disabled?: boolean;
  placeholder?: string;
  style?: React.CSSProperties;
  optionKey?: string;
  optionLabel?: string;
  onSelect?: (value: string, option: any) => void;
  extraParams?: Record<string, any>;
}

const EmployeeSelect: React.FC<EmployeeSelectProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder = '请选择',
  style,
  optionKey = DEFAULT_OPTION_KEY,
  optionLabel = DEFAULT_OPTION_LABEL,
  onSelect,
  extraParams = {},
}) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const cancelTokenSourceRef = useRef<any>(null);

  // 生成标签
  const genLabel = useCallback((template: string, optionData: any) => {
    return template ? customTemplate(template, optionData) : optionData.empName;
  }, []);

  // 搜索员工
  const handleSearch = useDebounce((searchValue?: string) => {
    if (cancelTokenSourceRef.current) {
      cancelTokenSourceRef.current.cancel('取消上次请求');
      cancelTokenSourceRef.current = null;
    }
    cancelTokenSourceRef.current = axios.CancelToken.source();
    setLoading(true);

    request({
      method: 'get',
      url: '/admin/v1/buzrole/manage_employee',
      baseURL: '/apis/common/proxy/lcpGw/tz_api/dhr_api/hcm/dhr-lcp-hcm',
      params: {
        objectName: searchValue,
        pageSize: 20,
        pageNum: 1,
        ...extraParams,
      },
      cancelToken: cancelTokenSourceRef.current.token,
    })
      .then(res => {
        cancelTokenSourceRef.current = null;
        const list = res.list || [];
        if (!Array.isArray(list)) {
          console.error('数据格式错误');
          return;
        }
        const _options = list.map(item => ({
          ...item,
          key: item[optionKey],
          value: item[optionKey],
          label: genLabel(optionLabel, item),
        }));
        setOptions(_options);
      })
      .finally(() => {
        setLoading(false);
      })
      .catch(err => {
        console.log('搜索员工出错', err);
      });
  }, 300);

  // 初始化搜索翻译
  const fetchEmployeeById = (employeeId: string) => {
    // 如果已存在值 - 则无需再请求
    const isExist = options.some(optionItem => optionItem[optionKey] === employeeId);
    if (!isExist && employeeId) {
      setLoading(true);
      request({
        method: 'get',
        url: '/admin/v1/buzrole/manage_employee',
        baseURL: '/apis/common/proxy/lcpGw/tz_api/dhr_api/hcm/dhr-lcp-hcm',
        params: {
          objectId: employeeId,
          ...extraParams,
        },
      })
        .then(res => {
          const list = res.list || [];
          if (!Array.isArray(list)) {
            console.error('数据格式错误');
            return;
          }

          const _options = list.map(item => ({
            ...item,
            key: item[optionKey],
            value: item[optionKey],
            label: genLabel(optionLabel, item),
          }));
          setOptions(_options);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  // 当值变化时，如果是新值，则获取员工信息
  React.useEffect(() => {
    if (value) {
      fetchEmployeeById(value);
    }
  }, [value]);

  const handleChange = (newValue: string, option: any) => {
    onChange?.(newValue, option);
  };

  const handleSelect = (newValue: string, option: any) => {
    onSelect?.(newValue, option);
  };

  return (
    <Select
      showSearch
      allowClear
      value={value}
      options={options}
      loading={loading}
      onSearch={handleSearch}
      onChange={handleChange}
      onSelect={handleSelect}
      filterOption={false} // 禁用过滤
      style={{ width: '100%', ...style }}
      disabled={disabled}
      placeholder={placeholder}
      notFoundContent={loading ? <Spin size="small" /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
    />
  );
};

export default EmployeeSelect;
