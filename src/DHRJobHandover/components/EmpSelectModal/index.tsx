import React, { useState, useEffect } from 'react';
import { Modal, Button, Space } from 'antd';
import * as ReactDOM from 'react-dom';
import EmployeeSelect from '../EmployeeSelect';

interface EmpSelectModalProps {
  title?: string;
  placeholder?: string;
  defaultValue?: string;
  disabled?: boolean;
  optionKey?: string;
  optionLabel?: string;
  extraParams?: Record<string, any>;
  onOk?: (value: string, option: any) => void;
  onCancel?: () => void;
  visible?: boolean;
  onClose?: () => void;
}

const EmpSelectModal: React.FC<EmpSelectModalProps> = ({
  title = '选择人员',
  placeholder = '请搜索并选择人员',
  defaultValue,
  disabled = false,
  optionKey,
  optionLabel,
  extraParams,
  onOk,
  onCancel,
  visible = true,
  onClose,
}) => {
  const [selectedValue, setSelectedValue] = useState<string | undefined>(defaultValue);
  const [selectedOption, setSelectedOption] = useState<any>(null);
  const [open, setOpen] = useState(visible);

  useEffect(() => {
    setOpen(visible);
  }, [visible]);

  const handleClose = () => {
    setOpen(false);
    if (onClose) {
      onClose();
    }
  };

  const handleEmployeeChange = (value: string, option: any) => {
    setSelectedValue(value);
    setSelectedOption(option);
  };

  const handleOk = () => {
    if (onOk && selectedValue) {
      onOk(selectedValue, selectedOption);
    }
    handleClose();
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    handleClose();
  };

  return (
    <Modal
      title={title}
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
      destroyOnClose
      footer={
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleOk} disabled={!selectedValue}>
            确定
          </Button>
        </Space>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <EmployeeSelect
          value={selectedValue}
          onChange={handleEmployeeChange}
          placeholder={placeholder}
          disabled={disabled}
          optionKey={optionKey}
          optionLabel={optionLabel || 'data.empName-data.empAccount'}
          extraParams={extraParams}
        />
      </div>
    </Modal>
  );
};

// 为组件添加静态方法类型
interface EmpSelectModalComponent extends React.FC<EmpSelectModalProps> {
  render: (props: Omit<EmpSelectModalProps, 'visible' | 'onClose'>) => { destroy: () => void };
}

// 静态方法，用于通过函数调用的方式打开弹窗
(EmpSelectModal as EmpSelectModalComponent).render = (props: Omit<EmpSelectModalProps, 'visible' | 'onClose'>) => {
  const div = document.createElement('div');
  document.body.appendChild(div);

  const destroy = () => {
    ReactDOM.unmountComponentAtNode(div);
    if (div.parentNode) {
      div.parentNode.removeChild(div);
    }
  };

  const modalProps: EmpSelectModalProps = {
    ...props,
    visible: true,
    onClose: destroy,
    onOk: (value: string, option: any) => {
      if (props.onOk) {
        props.onOk(value, option);
      }
      destroy();
    },
    onCancel: () => {
      if (props.onCancel) {
        props.onCancel();
      }
      destroy();
    },
  };

  ReactDOM.render(<EmpSelectModal {...modalProps} />, div);

  return {
    destroy,
  };
};

export default EmpSelectModal as EmpSelectModalComponent;
