import React from 'react';
import { useLeaveTransfer } from '../../context';
// import { useModuleRegistry } from '../../hooks/useModuleRegistry';
// import { DefaultSystemBlock } from '@src/DHRJobHandover/module';

// 特殊工作交接组渲染器
export const ModuleRenderer: React.FC<{
  data: Record<string, any>;
  module: Record<string, any>;
}> = ({ data, module }) => {
  const context = useLeaveTransfer();
  // const { getModule } = useModuleRegistry();
  // 处理单个模块
  if (module) {
    // const { systemName } = module;
    // const moduleConfig = getModule(systemName);
    // console.log('moduleConfig=====', systemName, moduleConfig);
    // if (!moduleConfig) {
    //   // 如果没有找到对应的模块配置，使用默认模块
    //   return <DefaultSystemBlock data={data} module={module} context={context} />;
    // }
    // 检查条件渲染 或者 没有组件
    if ((module.condition && !module.condition(context)) || !module.component) {
      return null;
    }

    const ModuleComponent = module.component;

    return <ModuleComponent data={data} module={module} context={context} />;
  }

  return null;
};
