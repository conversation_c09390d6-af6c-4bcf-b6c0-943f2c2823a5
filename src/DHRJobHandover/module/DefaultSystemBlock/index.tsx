import ModuleOperations from '../../components/ModuleOperations';
import { useLeaveTransfer, IHandoverData } from '../../context';
import { columnAdapter } from '../../utils/columnAdapter';
import { Alert } from 'antd';
import { SingleTable } from '@cvte/xioo-ag-table';
import type { ITableInfo } from '@cvte/xioo-ag-table/es/SingleTable/type';
import { convertToWULIFormItems } from '@src/DHRJobHandover/utils/formAdapter';
import { WULIForm } from '@cvte/wuli-antd';
// import HandoverBlock from '../../components/HandoverBlock';
import Block from '@components/Block';

import React from 'react';

export type TipsStatus = 'success' | 'error';

// const formatTipsStatus = item => {
//   const { tips } = item;
//   const tipsStatus = tips ? 'success' : 'error';
//   return tipsStatus;
// };

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const formatTipsStatus = (block, leaverIsOwn): TipsStatus => {
  // emptyList， requiredForm， 暂时只要把block的表单全部填了就可以success
  const { condition, msg } = block;
  if (!msg || msg.length === 0) {
    // if (systemName === 'EAM') globalEntity.setSubprocess('EAM', true);
    // if (systemName === 'BABY') globalEntity.setSubprocess('BABY', true);
    return 'success';
  }
  if (condition === 'emptyList') {
    return !msg || msg.length === 0 ? 'success' : 'error';
  }

  if (condition === 'auditPass') {
    const isSuccess = msg[0].find(fileItem => {
      return fileItem.fileId === 'wfStatus';
    });
    // if (isSuccess?.value) {
    //   if (systemName === 'WORK_HANDOVER') globalEntity.setSubprocess('WORK', true);
    //   if (systemName === 'BABY') globalEntity.setSubprocess('BABY', true);
    // }
    return isSuccess?.value === 'FINISH' ? 'success' : 'error';
  }

  // 判断是否存在属于当前block的表单字段，如果有，判断是否为空。
  // const blockField = Object.keys(fieldValues).every(field => {
  //   const index = field.indexOf(`${systemName}&`);
  //   return index !== 0 || !!fieldValues[field];
  // });

  // 如果当前登录人不是是离职人本身, 离职人也可以填的部分， EAM其实也需要过滤，因为他可能是交还入库。但EAM是emptyList类型的。(加上保命)
  // if (leaverIsOwn || ['EAM', 'DMS'].includes(systemName)) {
  //   return blockField ? 'success' : 'error';
  // }
  // 如果当前登录人是是离职人本身，则他们没有权限看到必填的表单字段，但是对于 requiredForm，依然要判断是否已经存在value

  // 只对OA类型进行特殊处理，如果有数据要判断是否已经存在交接人，
  // if (contentType === 'table' && this.isEmptyHandoverEmp(msg)) {
  //   return 'error';
  // }
  return 'success';
};

/* 计算block的tips状态 */

// 基础模块组件
export const BaseSystemBlock: React.FC<{
  title: string;
  children: React.ReactNode;
  item: IHandoverData;
  config?: {
    hideMsg?: boolean;
    noTitle?: boolean;
    leaverIsOwn?: boolean;
    [key: string]: any;
  };
}> = ({ title, children, item, config = {} }) => {
  const { hideMsg, noTitle, leaverIsOwn = false, tipsStatus } = config;
  const { tips, errorMsg } = item;

  return (
    <Block title={noTitle ? '' : title}>
      {!hideMsg && errorMsg && <Alert showIcon type="error" message={errorMsg} className="mb-10" />}
      {!hideMsg && !errorMsg && tips && <Alert showIcon type={tipsStatus} message={tips} className="mb-10" />}
      {children}
    </Block>
  );
};

// 默认模块处理器
export const DefaultSystemBlock: React.FC<{
  data: IHandoverData;
  config?: {
    hideMsg?: boolean;
    noTitle?: boolean;
    tipsStatus?: TipsStatus;
    [key: string]: any;
  };
  [key: string]: any;
}> = ({ data, config = {} }) => {
  const context = useLeaveTransfer();
  const { readonly } = context;
  const { hideMsg, noTitle, tipsStatus } = config;

  if (!data) {
    return <span className="text-12 text-gray text-center">模块信息为空</span>;
  }

  // 根据内容类型渲染不同的表单
  const renderContent = () => {
    const { contentType, msg } = data;

    if (!msg || msg.length === 0) {
      return <span className="text-12 text-gray text-center">暂无交接信息</span>;
    }

    // 表单类型
    if (contentType === 'form') {
      // 检查是否为工作交接表单，并获取flowStatus
      let isFormDisabled = readonly;
      if (data.systemName === 'WORK_HANDOVER' && msg && msg[0]) {
        const flowStatus = msg[0].find(item => item.fileId === 'wfStatus')?.value;
        if (flowStatus) {
          isFormDisabled = true; // 如果flowStatus有值，则禁用表单
        }
      }

      // 扩展context，添加表单禁用状态
      const extendedFormContext = {
        ...context,
        readonly: isFormDisabled, // 使用扩展的disabled状态
      };

      // 使用formAdapter将数据转换为WULIForm所需的表单项配置
      const formItems = convertToWULIFormItems(data, extendedFormContext);
      const formKey = `${data.systemName}_FORM`;
      return (
        <div className="handover-form-container">
          <WULIForm
            formKey={formKey}
            formItems={formItems}
            wuliMode={isFormDisabled ? 'view' : 'edit'}
            defaultLayout={
              config.defaultLayout || {
                col: 8,
                labelCol: 6,
                wrapperCol: 16,
              }
            }
          />
        </div>
      );
    }
    // 表格类型
    if (contentType === 'table') {
      // 生成表格唯一ID
      const tableId = `${data?.systemName}_TABLE`;
      // 动态生成列
      // 根据msg中的数据结构生成列
      const antdColumns = columnAdapter(data, {
        ...context,
        tableId,
      });

      // 重组数据
      const dataSource = (msg || []).map((item, index) => {
        return item.reduce(
          (pre, cur) => {
            if (cur.type === 'datePicker' && cur.value) {
              pre[cur.fileId] = +cur.value;
            } else {
              pre[cur.fileId] = cur.value;
            }
            return pre;
          },
          { key: 'key' + index, systemName: data.systemName }
        );
      });
      // 创建SingleTable的tableInfo配置
      const tableInfo: ITableInfo = {
        dvProgNo: tableId,
        selectType: 'checkbox',
        columnsList: antdColumns,
        initialData: dataSource,
        canEdit: true,
        showSettingColumn: false,
        rowKey: 'key',
      };

      // 行操作配置
      const cellSetting = {
        width: 0, // 不显示操作列
        pinned: 'right' as const,
        actionList: [],
        actionEvent: {}, // 必须提供空的actionEvent对象
      };

      // 使用SingleTable组件
      return (
        <SingleTable
          tableInfo={tableInfo}
          hasPagination={false}
          cellSetting={cellSetting}
          getRowId={row => row.data.key}
          toolExtraContent={{
            left: null,
            right: null,
          }}
        />
      );
    }
  };

  const tableId = `${data?.systemName}_TABLE`;
  // 扩展context，添加tableId
  const extendedContext = {
    ...context,
    tableId,
  };

  return (
    <BaseSystemBlock
      title={data?.registerTitle || '交接模块'}
      item={data}
      config={{
        hideMsg,
        noTitle,
        leaverIsOwn: false, // 提供默认值
        tipsStatus,
      }}
    >
      {/* 操作区 */}
      <div className="operation-area mb-4">
        {data.msg && data.msg.length > 0 && (
          <ModuleOperations moduleType={data.systemName} data={data} context={extendedContext} />
        )}
      </div>
      {/* 内容区 */}
      <div>{renderContent()}</div>
    </BaseSystemBlock>
  );
};
