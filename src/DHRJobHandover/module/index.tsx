import React from 'react';
import { Form, Input, Button, Table } from 'antd';
import { useLeaveTransfer } from '../context';

import '../style.less';
import { BaseSystemBlock, DefaultSystemBlock } from './DefaultSystemBlock';

// 工作交接模块
export { WorkSystemBlock } from './WorkSystemBlock';

export { DefaultSystemBlock } from './DefaultSystemBlock';

// 资产交接模块
export const AssetSystemBlock: React.FC<{
  item: any;
  [key: string]: any;
}> = ({ item, ...rest }) => {
  // 这里可以引用原有的资产交接组件或实现新的
  return (
    <BaseSystemBlock title={item.registerTitle || '资产交接'} item={item}>
      <div>
        {/* 资产交接内容 */}
        {item.msg && item.msg.length > 0 ? (
          <Table
            dataSource={item.msg}
            rowKey="id"
            pagination={false}
            columns={[
              { title: '资产编号', dataIndex: 'assetCode', key: 'assetCode' },
              { title: '资产名称', dataIndex: 'assetName', key: 'assetName' },
              { title: '状态', dataIndex: 'status', key: 'status' },
            ]}
          />
        ) : (
          <div>暂无资产交接信息</div>
        )}
      </div>
    </BaseSystemBlock>
  );
};

// 行政交接模块
export const AdminSystemBlock: React.FC<{
  item: any;
  [key: string]: any;
}> = ({ item, ...rest }) => {
  const { form, readonly } = useLeaveTransfer();

  return (
    <BaseSystemBlock title={item.registerTitle || '行政交接'} item={item}>
      <div>
        {/* 行政交接内容 */}
        {item.msg && item.msg.length > 0 ? (
          <div>
            {item.msg.map((msg, index) => (
              <div key={index}>
                <Form.Item label={msg.label}>
                  {form.getFieldDecorator(`${item.systemName}&${msg.key}`, {
                    initialValue: msg.value,
                    rules: [{ required: msg.required, message: `请${msg.label}` }],
                  })(<Input disabled={readonly} />)}
                </Form.Item>
              </div>
            ))}
          </div>
        ) : (
          <div>暂无行政交接信息</div>
        )}
      </div>
    </BaseSystemBlock>
  );
};

// OA系统交接模块
export const OaSystemBlock: React.FC<{
  item: any;
  [key: string]: any;
}> = ({ item, ...rest }) => {
  const { form, readonly } = useLeaveTransfer();

  return (
    <BaseSystemBlock title={item.registerTitle || 'OA系统交接'} item={item}>
      <div>
        {/* OA系统交接内容 */}
        {item.msg && item.msg.length > 0 ? (
          <Table
            dataSource={item.msg}
            rowKey="id"
            pagination={false}
            columns={[
              { title: '系统名称', dataIndex: 'systemName', key: 'systemName' },
              { title: '状态', dataIndex: 'status', key: 'status' },
              {
                title: '操作',
                key: 'action',
                render: (_, record) => (
                  <Button type="link" disabled={readonly} onClick={() => window.open(record.url)}>
                    前往处理
                  </Button>
                ),
              },
            ]}
          />
        ) : (
          <div>暂无OA系统交接信息</div>
        )}
      </div>
    </BaseSystemBlock>
  );
};

// 费控交接模块
export const FeeSystemBlock: React.FC<{
  item: any;
  [key: string]: any;
}> = ({ item, ...rest }) => {
  return (
    <BaseSystemBlock title={item.registerTitle || '费控交接'} item={item}>
      <div>
        {/* 费控交接内容 */}
        {item.msg && item.msg.length > 0 ? (
          <Table
            dataSource={item.msg}
            rowKey="id"
            pagination={false}
            columns={[
              { title: '单据编号', dataIndex: 'billNo', key: 'billNo' },
              { title: '单据类型', dataIndex: 'billType', key: 'billType' },
              { title: '金额', dataIndex: 'amount', key: 'amount' },
              { title: '状态', dataIndex: 'status', key: 'status' },
            ]}
          />
        ) : (
          <div>暂无费控交接信息</div>
        )}
      </div>
    </BaseSystemBlock>
  );
};

// 幼早教信息模块
export const BabySystemBlock: React.FC<{
  data: Record<string, any>;
  module: Record<string, any>;
  [key: string]: any;
}> = ({ data, module, ...rest }) => {
  const { form, readonly } = useLeaveTransfer();

  return (
    <BaseSystemBlock title={module.registerTitle || '幼早教信息'} item={data}>
      <div>
        {/* 幼早教信息内容 */}
        {data.msg && data.msg.length > 0 ? (
          <div>
            {data.msg.map((msg, index) => (
              <div key={index}>
                <Form.Item label={msg.label}>
                  {form.getFieldDecorator(`${module.systemName}&${msg.key}`, {
                    initialValue: msg.value,
                    rules: [{ required: msg.required, message: `请${msg.label}` }],
                  })(<Input disabled={readonly} />)}
                </Form.Item>
              </div>
            ))}
          </div>
        ) : (
          <div>暂无幼早教信息</div>
        )}
      </div>
    </BaseSystemBlock>
  );
};
