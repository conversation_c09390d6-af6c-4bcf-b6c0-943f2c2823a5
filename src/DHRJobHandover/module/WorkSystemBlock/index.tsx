import React from 'react';
import { useLeaveTransfer } from '@src/DHRJobHandover/context';
import { IHandoverData } from '@src/DHRJobHandover/index.d';
import { DefaultSystemBlock } from '../DefaultSystemBlock';
import { ModuleConfig } from '@src/DHRJobHandover/hooks/useModuleRegistry';
import Block from '@components/Block';
import './style.less';
// 工作交接模块
export const WorkSystemBlock: React.FC<{
  data: IHandoverData;
  module: ModuleConfig;
}> = props => {
  const { handoverData } = useLeaveTransfer();
  const { module } = props;
  const { group } = module;

  const filterList = handoverData.filter(item => group.includes(item.systemName));
  return (
    <Block>
      <div className="work-system-block">
        {filterList.map(item => (
          <DefaultSystemBlock
            key={item.systemName}
            data={item}
            config={{
              hideMsg: item.systemName !== 'WORK_HANDOVER',
              noTitle: item.systemName === 'WORK_HANDOVER',
              defaultLayout: {
                col: 8,
                labelCol: 10,
                wrapperCol: 14,
              },
            }}
          />
        ))}
      </div>
    </Block>
  );
};
