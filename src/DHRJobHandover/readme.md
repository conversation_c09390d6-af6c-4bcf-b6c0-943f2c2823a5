## 功能点

## 工作交接已完成项目自动折叠，并标示已完成


## 离职交接说明

离职交接模块是员工离职流程中的重要环节，用于处理员工离职前的各种交接事项，包括工作交接、资产交接、费控交接等。


### 特殊处理模块

以下三个交接模块需要特殊处理，它们会被合并在一起，显示为"工作交接详情"：

- **WORK_HANDOVER** - 工作交接
- **TUTOR_INFO** - 导师信息
- **EXT_LEAD** - 外协交接

这三个模块通过 `WorkSystemBlock` 组件进行统一渲染，而其他模块则通过 `WrapperSystemBlock` 组件单独渲染。

### 工作交接 (WORK_HANDOVER)  -- 调用天舟云流程发起接口

工作交接是离职交接中的核心模块，包含以下特殊功能：

| 功能 | 说明 | 特殊处理 |
| --- | --- | --- |
| 工作交接单上传 | 上传工作交接文档 | 提供模板下载链接，支持多种文件格式（图片、pdf、word、excel） |
| 设置交接人 | 选择工作交接人 | 自动填充交接人邮箱，禁止选择离职人本人作为交接人 |
| 资产持有情况 | 选择是否持有公司资产 | 选择"其他"时显示资产详情输入框；选择"无"时禁用多选并清除其他选项 |
| 资产回收 | 选择是否回收公司资产 | 选择"否"时显示原因输入框 |
| 请假情况 | 确认是否有未提交的请假 | 特指上月26日至今的请假，选择"否"时无法提交审批 |
| 提交审批 | 发起工作交接流程 | 验证所有必填字段，提交后显示下一步审核人 |

### 导师信息 (TUTOR_INFO)

导师信息模块用于处理离职员工作为导师的交接：

| 功能 | 说明 | 特殊处理 |
| --- | --- | --- |
| 设置交接人 | 为每个被指导人选择新导师 | 与工作交接共用同一个子流程 |
| 数据展示 | 显示被指导人信息 | 包括姓名、账号、新人类型、原导师等信息 |

### 外协交接 (EXT_LEAD)

外协交接模块用于处理离职员工负责的外协人员交接：

| 功能 | 说明 | 特殊处理 |
| --- | --- | --- |
| 设置交接人 | 为每个外协人员选择新负责人 | 与工作交接共用同一个子流程 |
| 数据展示 | 显示外协人员信息 | 包括姓名、账号、类型、负责人等信息 |

### 资产交接 (EAM) -- 调用dhr后端接口

资产交接模块用于处理员工持有的公司资产：

| 功能 | 说明 | 特殊处理 |
| --- | --- | --- |
| 交接方式选择 | 选择"归还入库"或"交接" | 选择"归还入库"时无需设置交接人 |
| 设置交接人 | 为选中的资产设置交接人 | 批量设置功能，弹窗选择 |
| 归还入库 | 批量将资产归还入库 | 自动清空交接人字段 |
| 发起交接 | 提交资产交接申请 | 需要验证离职日期和交接信息 |
| 提交变更 | 修改已提交的交接信息 | 只能修改未审核的数据 |

### 待办流程交接 (OA)

OA模块用于处理员工在OA系统中的待办事项：

| 功能 | 说明 | 特殊处理 |
| --- | --- | --- |
| 设置交接人 | 为待办事项设置交接人 | 批量设置功能 |
| 发起交接 | 提交OA待办交接申请 | 过滤掉包含"离职"的待办事项 |

### 费控交接 (FEE)

费控模块用于处理员工在费控系统中的未结单据：

| 操作 | 作用说明 | 备注 |
| --- | --- | --- |
| 设置交接人 | 弹出选人弹窗，选择人，然后确认之后就把人都设置进table form中的交接人字段 | 弹窗使用资源组件进行人员选择 |
| 更新交接人 | 选择好费控交接人后，调用费控更新接口。更新完毕，调用获取费控未结单据接口获取最新列表 | 初始进来是从handover接口的FEE模块拿msg信息作为费控交接信息。后面更新交接人之后才调用费控未结单据接口获取最新列表 |

### 幼早教信息 (BABY)

幼早教模块用于处理员工的婴幼儿相关信息：

| 功能 | 说明 | 特殊处理 |
| --- | --- | --- |
| 配偶在职状态 | 选择配偶是否在公司任职 | 选择"否"时自动设置"是否退园"为"是"并禁用修改 |
| 退园信息 | 设置是否退园及退园时间 | 选择"是"时需要填写退园时间 |
| 发起交接 | 提交幼早教信息确认 | 验证所有必填字段 |

### 权限控制

离职交接模块根据不同的离职类型和节点有不同的权限控制：

1. **主动离职和被动离职**：
   - 只有在"员工处理离职待办"节点，且当前用户是离职人本人时，才能操作交接功能
   - 其他节点只能查看

2. **特殊离职**：
   - 除了薪酬HR外，所有节点都可以操作交接功能
   - 薪酬HR无法看到离职交接模块

### 状态管理

离职交接模块使用全局状态管理交接状态：

- 使用 `globalEntity.disableSubprocess` 记录各子流程的完成状态
- 使用 `globalEntity.handover` 记录已发起交接的资产
- 交接完成后，相应模块会显示成功状态并禁用编辑

### 表单验证

所有交接模块都有严格的表单验证：

- 必填字段验证
- 特殊字段格式验证
- 文件上传格式和大小验证（最大5MB）
- 离职日期必须选择













