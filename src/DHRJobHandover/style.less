.dhr-job-handover-container {
  .baseBlocks {
    margin-top: 0;
  }
  .baseBlocksContent {
    padding: 4px;
  }
  .dhr-job-handover-sample-render {
    // padding: px;
    height: 100px;
    width: 100%;
    line-height: 100px;
    
    background-color: #f5f5f5;
    border-radius: 4px;
    text-align: center;
  }
  // 容器样式
}

// 操作区域样式
.operation-area {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 4px;
}

// 表格样式
.ag-theme-balham {
  width: 100%;
  height: 100%;

  .ag-header-cell {
    background-color: #f5f5f5;
    font-weight: bold;
  }

  .ag-row-even {
    background-color: #ffffff;
  }

  .ag-row-odd {
    background-color: #f9f9f9;
  }
}

// 表单样式
.ant-form-item {
  margin-bottom: 16px;
}

// 交接表单容器样式
.handover-form-container {
  width: 100%;
  padding: 4px 0;

  .wuli-form {
    width: 100%;
  }
}

// 提示信息样式
.mb-10 {
  margin-bottom: 10px;
}
