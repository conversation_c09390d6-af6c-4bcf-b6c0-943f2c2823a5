import { onMapFormItem } from './renderFormItem';
import { IHandoverData, IMsgItem } from '../index.d';
import { IColumn } from '@cvte/xioo-ag-table/es/SingleTable/type';
import { WHETHER_OPTIONS } from '@constants/common';

// 表格列处理
export const columnAdapter = (data: IHandoverData, context: Record<string, any>) => {
  console.log('data===========222', data);
  const { msg } = data;
  const { systemName } = data;
  const { isHandler, onUpdateHandoverData, tableId } = context;
  const columns: IColumn[] = [];
  (msg?.[0] || []).forEach((item: IMsgItem) => {
    const { type, fileId } = item;

    if (type === 'hide') {
      return;
    }

    // 根据类型来确定列的类型
    const typeMap = {
      readonly: 'Input',
      selectWhether: 'Select',
      // radio: 'Radio',
      datePicker: 'DatePicker',
    };

    const isReadonly = type === 'readonly';
    if (Object.keys(typeMap).includes(type)) {
      const baseColumn: IColumn = {
        columnName: item.fileName,
        columnNo: item.fileId,
        columnType: typeMap[type],
        isEdit: !isReadonly,
        flex: 1,
      };
      if (type === 'selectWhether') {
        baseColumn.options = WHETHER_OPTIONS;
      }

      columns.push(baseColumn);
      return;
    }
    // 如果存在离职交接人，则说明需要框框
    // 只有离职人员处理待办的时候才会出现交互人 且是处于当前离职人
    // 分两种情况
    // 一、主动和被动离职时，只需要在员工待办节点可操作
    // 二、特殊离职情况下，除了薪酬HR，都可以操作。但薪酬HR看不到离职交接，所以不用管
    // console.log('item===========', item);
    columns.push({
      columnName: item.fileName,
      columnNo: item.fileId,
      // width: systemName === 'EAM' && item.fileId === 'return_type' ? 160 : undefined,
      render: ({ value, data }) => {
        // 只有员工待办的时候需要展示出表单 且是处于当前离职人
        // if (isHandler) return value;
        // 如果是表单字段的话，需要转换 - 如果不是，就直接展示文本
        // 例如资产详情的 资产交接人
        return onMapFormItem(
          data,
          {
            // fileName: item.fileName,
            // type: item.type,
            ...item,
            systemName,
            rowKey: data.key,
            value: data[fileId],
            fieldKey: `${systemName}&${data.key}&${fileId}`, // 系统名-序号-字段
            onSelect: (newValue, config) => {
              const newConfig = {
                ...config,
                contentType: 'table',
                tableId,
              };
              console.log('onSelect====', newValue, newConfig);
              onUpdateHandoverData(newValue, newConfig);
            },
            onChange: (newValue, config) => {
              const newConfig = {
                ...config,
                contentType: 'table',
                tableId,
              };
              console.log('onChange====', newValue?.target?.value || newValue, newConfig);
              onUpdateHandoverData(newValue?.target?.value || newValue, newConfig);
            },
            // rules: systemName === 'BABY' &&
            //   item.type === 'input' && [{ required: true, message: '只能输入"是"和"否"', pattern: /\是|否/g }],
          },
          context
        );
      },
    });
  });
  return columns;
};
