import React from 'react';
import { onMapFormItem } from './renderFormItem';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

/**
 * 将系统数据转换为WULIForm所需的表单项配置
 */
export const convertToWULIFormItems = (data: any, context: any): IFormItem[] => {
  const { msg = [], systemName } = data;
  const { onUpdateHandoverData } = context;

  const formItems: IFormItem[] = [];
  if (!msg.length) {
    return formItems;
  }
  msg?.[0]
    ?.filter(field => field.type !== 'hide')
    .forEach(field => {
      const { fileId, fileName, value, required } = field;

      const baseFormItem: IFormItem = {
        key: fileId,
        label: fileName,
        type: 'custom', // 使用自定义渲染
        required: !!required,
        configs: {
          // options: type === 'selectWhether' ? WHETHER_OPTIONS : [],
        },
      };
      baseFormItem.render = formData => {
        return onMapFormItem(
          {
            ...field,
            value: formData.value || value, // 优先使用表单中的值
          },
          {
            ...field,
            systemName,
            onChange: (newValue, config) => {
              const newConfig = {
                ...config,
                contentType: 'form',
              };
              onUpdateHandoverData(newValue, newConfig);
            },
          },
          context
        );
      };
      formItems.push(baseFormItem);
    });
  return formItems;
};
