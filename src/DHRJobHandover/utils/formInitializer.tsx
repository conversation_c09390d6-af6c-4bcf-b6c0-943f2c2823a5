import { WULIFormActions } from '@cvte/wuli-antd';
import { IHandoverData } from '../context';

/**
 * 表单初始化工具函数
 * 用于统一处理 DHRJobHandover 模块中各个表单的初始值设置
 */

/**
 * 从系统数据中提取表单初始值
 * @param systemData 系统数据
 * @returns 表单初始值对象
 */
export const extractFormInitialValues = (systemData: IHandoverData) => {
  const initialValues: Record<string, any> = {};

  if (!systemData.msg || !systemData.msg[0]) {
    return initialValues;
  }

  // 提取表单字段的初始值
  systemData.msg[0].forEach(field => {
    if (field.value !== undefined && field.value !== null && field.value !== '') {
      initialValues[field.fileId] = field.value;
    }
  });

  return initialValues;
};

/**
 * 设置单个表单的初始值
 * @param systemName 系统名称
 * @param systemData 系统数据
 * @param timeout 延迟时间（毫秒），用于确保表单实例已创建
 */
export const setFormInitialValues = (systemName: string, systemData: IHandoverData, timeout = 100) => {
  setTimeout(() => {
    const formKey = `${systemName}_FORM`;
    const workHandoverForm = WULIFormActions.get(formKey);
    console.log('workHandoverForm===========', workHandoverForm);

    if (workHandoverForm) {
      const initialValues = extractFormInitialValues(systemData);

      if (Object.keys(initialValues).length > 0) {
        console.log(`设置表单 ${formKey} 初始值:`, initialValues);
        workHandoverForm.setValue(initialValues);
      }
    } else {
      console.warn(`表单实例 ${formKey} 未找到，跳过初始化`);
    }
  }, timeout);
};

/**
 * 批量初始化多个表单
 * @param handoverDataList 交接数据列表
 * @param timeout 延迟时间（毫秒）
 */
export const initializeAllForms = (handoverDataList: IHandoverData[], timeout = 100) => {
  handoverDataList.forEach(systemData => {
    if (systemData.contentType === 'form') {
      setFormInitialValues(systemData.systemName, systemData, timeout);
    }
  });
};

/**
 * 检查表单是否需要初始化
 * @param systemName 系统名称
 * @returns 是否需要初始化
 */
export const shouldInitializeForm = (systemName: string): boolean => {
  const formKey = `${systemName}_FORM`;
  const workHandoverForm = WULIFormActions.get(formKey);

  if (!workHandoverForm) {
    return false;
  }

  // 检查表单是否已经有值
  const currentValues = workHandoverForm.getValue();
  return Object.keys(currentValues).length === 0;
};

/**
 * 重新初始化指定表单
 * @param systemName 系统名称
 * @param systemData 系统数据
 */
export const reinitializeForm = (systemName: string, systemData: IHandoverData) => {
  const formKey = `${systemName}_FORM`;
  const workHandoverForm = WULIFormActions.get(formKey);

  if (workHandoverForm) {
    // 先重置表单
    workHandoverForm.reset?.();

    // 再设置初始值
    setTimeout(() => {
      setFormInitialValues(systemName, systemData, 0);
    }, 50);
  }
};

/**
 * 便捷的初始化函数 - 可直接在组件中调用
 * 检查表单是否存在且未初始化，如果满足条件则进行初始化
 * @param systemName 系统名称
 * @param systemData 系统数据
 * @param options 配置选项
 */
export const initializeFormIfNeeded = (
  systemName: string,
  systemData: IHandoverData,
  options: {
    timeout?: number;
    forceInit?: boolean; // 是否强制初始化
    skipCheck?: boolean; // 是否跳过已有值检查
  } = {}
) => {
  const { timeout = 100, forceInit = false, skipCheck = false } = options;

  const formKey = `${systemName}_FORM`;
  const workHandoverForm = WULIFormActions.get(formKey);

  if (!workHandoverForm) {
    console.warn(`表单实例 ${formKey} 未找到，跳过初始化`);
    return false;
  }

  // 如果不跳过检查，且不强制初始化，则检查表单是否已有值
  if (!skipCheck && !forceInit) {
    const currentValues = workHandoverForm.getValue();
    if (Object.keys(currentValues).length > 0) {
      console.log(`表单 ${formKey} 已有值，跳过初始化`);
      return false;
    }
  }

  // 执行初始化
  setFormInitialValues(systemName, systemData, timeout);
  console.log(`成功初始化表单: ${formKey}`);
  return true;
};
