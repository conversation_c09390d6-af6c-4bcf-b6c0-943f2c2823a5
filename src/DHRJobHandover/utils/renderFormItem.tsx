import React from 'react';
import '../style.less';
import { renderMethods } from './renderMethods';
import { transformDictOptions } from '@utils/tools';

// 常量定义
export const EXIT_TYPE = {
  SELF: 'SELF', // 自主离职
  SPECIAL: 'FAST', // 快速离职
  HR_SUBMIT: 'HR_SUBMIT', // 人事HR复审
};

export const EAM_RETURN_TYPE = [
  { value: 'RETURN', label: '归还入库' },
  { value: 'HANDOVER', label: '交接' },
];
// 创建表单项渲染器工厂
export const createFormItemRenderer = (context: IObject): IObject => {
  const { dicts } = context;
  const getRenderMethods = () => {
    return renderMethods.EMP;
  };
  const _renderMethods = getRenderMethods();

  return {
    // EAM系统表单项渲染--EAM是表格数据
    renderEAMFormItem: (data: IObject, config: IObject = {}) => {
      const { fileId } = config;
      if (fileId === 'return_type') {
        return _renderMethods.renderFormItemByType(
          data,
          {
            ...config,
            options: EAM_RETURN_TYPE,
          },
          context
        );
      }

      if (fileId === 'handoverEmp') {
        return _renderMethods.renderFormItemByType(
          data,
          {
            ...config,
          },
          context
        );
      }

      // 其他EAM字段使用通用渲染
      return _renderMethods.renderFormItemByType(data, config, context);
    },
    // 费用交接表单项渲染
    renderFEEFormItem: (data: IObject, config: IObject = {}) => {
      const { fileId } = config;
      // 来源地址
      if (fileId === 'sourceOrderUrl') {
        return (
          <a href={data.sourceOrderUrl} target="_blank" rel="noreferrer">
            点我跳转
          </a>
        );
      }
      // 交接人
      if (fileId === 'handover') {
        if (data.handover === '无需交接') {
          return data.handover;
        }
        return _renderMethods.renderFormItemByType(
          data,
          {
            ...config,
            type: 'employee',
          },
          context
        );
      }
      return _renderMethods.renderFormItemByType(data, config, context);
    },
    // 工作交接表单项渲染
    renderWorkHandoverFormItem: (data: IObject, config: IObject = {}) => {
      const { fileId } = config;
      // 自定义渲染交接
      if (fileId === 'fileId') {
        return _renderMethods.renderWorkHandoverFormItem(data, config, context);
      }
      if (fileId === 'holdingCompanyAssets') {
        return _renderMethods.renderFormItemByType(
          data,
          {
            ...config,
            options: transformDictOptions(dicts['DHR_EXIT_HELD_ASSET'] || []),
            mode: 'multiple',
          },
          context
        );
      }
      return _renderMethods.renderFormItemByType(data, config, context);
    },
    // 根据表单项类型渲染
    renderFormItemByType: (data: IObject, config: IObject = {}) => {
      return _renderMethods.renderFormItemByType(data, config, context);
    },
  };
};

// 主方法：映射表单项
export const onMapFormItem = (data: IObject, config: IObject = {}, context: IObject): React.ReactNode => {
  // 创建表单项渲染器
  const formItemRenderer = createFormItemRenderer(context);
  const { systemName } = config;
  // 根据系统类型选择不同的渲染策略
  if (systemName === 'EAM') {
    return formItemRenderer.renderEAMFormItem(data, config);
  }
  if (systemName === 'FEE') {
    return formItemRenderer.renderFEEFormItem(data, config);
  }
  if (systemName === 'WORK_HANDOVER') {
    return formItemRenderer.renderWorkHandoverFormItem(data, config);
  }
  // 根据表单项类型渲染
  return formItemRenderer.renderFormItemByType(data, config);
};
