import React from 'react';
import { Input, DatePicker, Radio, Select } from 'antd';
import '../style.less';
import { WHETHER_OPTIONS } from '@constants/common';
import EmployeeSelect from '../components/EmployeeSelect';
import DHRFileSign from '../../DHRFileSign';

// 定义类型
interface IObject {
  [key: string]: any;
}

// 渲染基础类型
const renderBaseTypes = {
  EMP: {
    // 渲染默认字段（未知类型）
    renderDefaultField: (data: Record<string, any>, config: Record<string, any> = {}, context: Record<string, any>) => {
      const { ...restConfig } = config;
      return <Input disabled={true} {...restConfig} />;
    },

    // 渲染日期选择器字段
    renderDatePickerField: (
      data: Record<string, any>,
      config: Record<string, any> = {},
      context: Record<string, any>
    ) => {
      const { fieldKey } = data;
      const { readonly } = context;
      return (
        <DatePicker
          key={fieldKey}
          {...config}
          disabled={readonly || config.disabled}
          popupClassName="date-picker-popup"
          placeholder="请选择"
        />
        // </FormItem>
      );
    },

    // 渲染员工搜索字段
    renderEmployeeField: (
      data: Record<string, any>,
      config: Record<string, any> = {},
      context: Record<string, any>
    ) => {
      const { fieldKey, value } = config;
      const { readonly } = context;
      console.log('renderEmployeeField===========', data, config, context);

      // 解析初始值 - 处理JSON字符串格式的员工数据
      let empId = '';
      if (value) {
        console.log('解析员工初始值===========', value);
        try {
          // 尝试解析JSON字符串
          const employeeData = typeof value === 'string' ? JSON.parse(value) : value;

          console.log('employeeData===========', employeeData);
          // 提取员工ID
          if (employeeData && employeeData.empId) {
            empId = employeeData.empId;
          }
        } catch (error) {
          // 如果解析失败，可能value本身就是empId
          empId = value;
          console.error('解析员工初始值出错', error);
        }
      }

      // 处理选择事件
      const handleSelect = (selectedValue: string, option: Record<string, any>) => {
        const employeeData = option
          ? {
            empId: option.empId,
            domainCode: option.empAccount,
            othername: option.empOtherName || option.empName,
          }
          : {};

        // 调用原有的onSelect
        if (config.onChange) {
          config.onChange(JSON.stringify(employeeData), config);
        }
      };

      console.log('empId===========', empId);
      return (
        <EmployeeSelect
          key={fieldKey}
          value={empId}
          disabled={readonly || config.disabled}
          optionKey="empId"
          optionLabel="data.empOtherName-data.empDeptFullPathName"
          placeholder="请选择员工"
          // onSelect={handleSelect}
          onChange={handleSelect}
          extraParams={{
            modelType: config.modelType || undefined,
            isNeedPermission: config.isNeedPermission || undefined,
          }}
        />
      );
    },

    // 渲染输入框字段
    renderInputField: (data: Record<string, any>, config: Record<string, any> = {}, context: Record<string, any>) => {
      const { readonly } = context;
      return (
        <Input
          placeholder="请填写"
          // key={fieldKey}
          disabled={readonly || config.disabled}
          maxLength={31}
          {...config}
          onChange={e => config.onChange(e.target.value, config)}
        />
      );
    },

    // 渲染文本区域字段
    renderTextAreaField: (
      data: Record<string, any>,
      config: Record<string, any> = {},
      context: Record<string, any>
    ) => {
      const { readonly } = context;
      return (
        <Input.TextArea
          maxLength={501}
          {...config}
          disabled={readonly || config.disabled}
          onChange={e => config.onChange(e.target.value, config)}
        />
      );
    },

    // 渲染文件上传字段
    renderFileField: (data: Record<string, any>, config: Record<string, any> = {}, context: Record<string, any>) => {
      return <div>文件上传</div>;
    },

    // 渲染单选框
    renderSelectWhetherField: (
      data: Record<string, any>,
      config: Record<string, any> = {},
      context: Record<string, any>
    ) => {
      const { readonly } = context;
      const { options = WHETHER_OPTIONS, onChange, disabled, ...restConfig } = config;
      return (
        <Radio.Group
          disabled={readonly || disabled}
          options={options || WHETHER_OPTIONS}
          {...restConfig}
          onChange={value => onChange(value, config)}
        />
      );
    },

    // 渲染选择框字段
    renderSelectField: (data: Record<string, any>, config: Record<string, any> = {}, context: Record<string, any>) => {
      const { readonly } = context;
      const { options = WHETHER_OPTIONS, onChange, disabled, ...restConfig } = config;
      return (
        <Select
          options={options || WHETHER_OPTIONS}
          disabled={readonly || disabled}
          {...restConfig}
          onChange={value => onChange(value, config)}
        />
      );
    },

    // 渲染工作交接选择框
    renderWorkHandoverSelectField: (
      data: Record<string, any>,
      config: Record<string, any> = {},
      context: Record<string, any>
    ) => {
      // 工作交接选择框的具体实现
      // 这里可以根据需要进一步拆分
      // ...

      // 为简化示例，这里省略具体实现
      return renderBaseTypes.EMP.renderSelectWhetherField(data, config, context);
    },

    // 渲染单选框字段
    renderRadioField: (data: Record<string, any>, config: Record<string, any> = {}, context: Record<string, any>) => {
      const { readonly } = context;
      const { options = WHETHER_OPTIONS, onChange, disabled, ...restConfig } = config;

      return (
        <Radio.Group
          disabled={readonly || disabled}
          options={options}
          {...restConfig}
          onChange={e => {
            console.log('renderRadioField===========', e);
            onChange(e.target.value, config);
          }}
        />
      );
    },
  },
};

export const renderMethods = {
  // 正式员工相关渲染方法
  EMP: {
    // 通用方法 - 根据类型渲染表单项
    renderFormItemByType: (data: IObject, config: IObject = {}, context: IObject = {}): React.ReactNode => {
      const { type } = config;
      // 根据类型选择渲染方法
      const renderMethodsMap = {
        datePicker: () => renderBaseTypes.EMP.renderDatePickerField(data, config, context),
        employee: () => renderBaseTypes.EMP.renderEmployeeField(data, config, context),
        input: () => renderBaseTypes.EMP.renderInputField(data, config, context),
        textArea: () => renderBaseTypes.EMP.renderTextAreaField(data, config, context),
        file: () => renderBaseTypes.EMP.renderFileField(data, config, context),
        selectWhether: () => renderBaseTypes.EMP.renderSelectField(data, config, context),
        radio: () => renderBaseTypes.EMP.renderRadioField(data, config, context),
        // select: () => renderBaseTypes.EMP.renderSelectField(data, config, context),
      };
      console.log('type===========', config.systemName, type, data, config);

      // 如果有对应的渲染方法
      if (renderMethodsMap[type]) {
        return renderMethodsMap[type]();
      }

      // 默认渲染
      return renderBaseTypes.EMP.renderDefaultField(data, config, context);
    },
    renderWorkHandoverFormItem: (data: IObject, config: IObject = {}, context: IObject = {}): React.ReactNode => {
      const { onChange } = config;
      return (
        <DHRFileSign
          // value={data.value}
          value={data.value}
          configs={{
            ...context.tzConfigs,
            config: {
              ...context.tzConfigs.config,
              baseConfig: {
                ...context.tzConfigs.config.baseConfig,
                dictConfig: {
                  ...context.tzConfigs.config.baseConfig.dictConfig,
                  buttonText: '交接签署',
                  type: 'CHECK_LIST',
                  typeName: '工作交接',
                  dataTpl: `
                        return {
                          name: data.C_EMP_NAME,
                          idNumber: data.C_CTF_ID,
                          date: moment().format('YYYY年MM月DD日'),
                        }
                  `,
                  validateFields: 'C_EMP_NAME,C_DEPT_FULL_NAME,C_POSITION_NAME',
                },
              },
            },
          }}
          onChange={value => {
            console.log('工作交接onChange===========', value);
            onChange(value, config);
          }}
        />
      );
    },
  },
  OUTSOURCE: {}, // 外包
  INTERN: {}, // 实习生
};
