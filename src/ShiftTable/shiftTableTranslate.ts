interface IAppProps {
  fields: any;
  apiConfigMap: any;
  baseConfigs: any;
}

const batchTranslate = (configs: IAppProps) => {
  const { apiConfigMap, fields, baseConfigs } = configs;
  console.log('apiConfigMap===', apiConfigMap, fields, baseConfigs);
  const dictConfig = baseConfigs?.dictConfig || {};
  const { tableKey, formConfig } = dictConfig;
  const formRelationConfig = formConfig || '{}';
  const tableList = fields[tableKey] || [];
  const formConfigParse = JSON.parse(formRelationConfig) || {};
  console.log('formConfigParse===', formConfigParse, tableList);

  return {
    fields: [],
    map: {},
  };
};

export default batchTranslate;
