import React, { useMemo } from 'react';
import { Button, Row, Col } from 'antd';
import { IOriginalProps } from '@src/types';
import './style.less';

interface IAppProps extends IOriginalProps {}
const BaseInfo: React.FC<IAppProps> = props => {
  console.log('props', props);
  const dictConfig = props.config?.config?.baseConfig?.dictConfig || {};
  const { headerNames } = dictConfig;
  const customTitleList = headerNames ? headerNames.split(/[,，]/g) : ['一', '二', '三', '四', '五', '六', '日'];
  const columns = useMemo(() => ['序号', ...customTitleList], [customTitleList]);
  return (
    <div className="shiftTableFormShowContainer">
      <Row className="titleContainer" style={{ marginBottom: 0 }}>
        {columns.map((colVal, index) => (
          <Col flex="1" key={`title_${index}`} className="shiftTableTitle">
            {colVal}
          </Col>
        ))}
      </Row>
      {/* <Row className="shiftTableContentContainer" style={{ marginBottom: 0 }}>
        <Col flex="1" key="number" className="shiftTableContent">
          1
        </Col>
        {customTitleList.map((_, index) => (
          <Col flex="1" key={`content_${index}`} className="shiftTableContent">
            <Button type="primary" ghost className="shiftTableItemAddBtn">
              添加
            </Button>
          </Col>
        ))}
      </Row> */}
    </div>
  );
};

export default BaseInfo;
