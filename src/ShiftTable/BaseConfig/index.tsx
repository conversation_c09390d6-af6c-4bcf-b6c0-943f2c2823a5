import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

import { SHIFT_TABLE_ADD_DATA_TYPE, SHIFT_TABLE_SELECT_TYPE } from '@constants/common';

const WHETHER_OPTIONS = [
  {
    label: '是',
    key: '1',
    value: '1',
  },
  {
    label: '否',
    key: '0',
    value: '0',
  },
];

const SELECT_TYPE_OPTIONS = [
  {
    label: '多选',
    value: SHIFT_TABLE_SELECT_TYPE.MULTIPLE,
  },
  {
    label: '单选',
    value: SHIFT_TABLE_SELECT_TYPE.RADIO,
  },
];

const requireConfig = {
  decoratorOptions: {
    rules: [
      {
        required: true,
        message: '请选择',
      },
    ],
  },
};

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

export const setBaseConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  formRef?.current?.setFormItem?.(key, value);
  console.log('key, value', key, value);
  ctx?.context?.onConfirm?.(key, value);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  // 表单/字典下拉列表缓存
  const formOptionsCacheMap = {};
  const dictOptionsCacheMap = {};

  const { formRef } = context || {};
  context?.dataEntity?.fetchFormTree?.();
  const dictOptions = context?.dataEntity?.dictNodeList || [];
  // 更新类型
  const handleUpdateFieldType = (key, type, otherHideKeys: string[] = [], otherConfig: any = {}) => {
    formRef?.current?.diffFormItem?.(
      [
        {
          key,
          type,
          ...otherConfig,
        },
      ],
      [...otherHideKeys]
    );
  };

  const handleGetOptions = async val => {
    let options = [];
    try {
      const detail = JSON.parse(val || '{}');
      const classId = detail?.id || '';
      const newOptions = formOptionsCacheMap[classId];
      if (!newOptions) {
        // 选择表单的具体的字段
        const list = await context?.dataEntity?.fetchAttrTreeFormat(classId);
        // const newlist = loopTreeDataConstruct([...list||[]],'children',() => {

        // })
        const children = list?.[0]?.children || [];
        const childrenDetail = children.find(({ attributeGroupType }) => attributeGroupType === 'base');
        options = (childrenDetail?.children || []).map(optionItem => ({
          ...optionItem,
          label: optionItem?.name || '',
        }));
        formOptionsCacheMap[classId] = [...(options || [])];
      } else {
        options = newOptions;
      }
    } catch (error) {
      console.log('获取options失败：', error);
    }

    return options;
  };

  const handleGetDictOptions = async dictId => {
    let options: any = [];
    try {
      const newOptions = dictOptionsCacheMap[dictId];
      if (!newOptions) {
        const list: any = (await context?.dataEntity?.fetchDict(dictId)) || [];
        options = (list || []).map(optionItem => ({
          ...optionItem,
          label: optionItem.name,
          value: optionItem.itemValue,
        }));

        dictOptionsCacheMap[dictId] = options;
      } else {
        options = newOptions;
      }
    } catch (error) {
      console.log('获取字典的options失败啦~~', error);
    }
    return options;
  };

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const {
      dictKey,
      tableKey,
      optionIds,
      selectType,
      colFieldKey,
      formConfig,
      rowFieldKey,
      headerNames,
      headerTitle,
      frequencyKey,
      isUseBuiltIn,
      modalDataType,
      headerTooltip,
      periodTypeKey,
      isUseTabStyle,
      searchParams,
      formCellFieldKey,
      cellSaveFieldKey,
      formCellSaveFieldKey,
    } = dictConfigByProps || {};
    const curFormData = formRef?.current?.getFormItem?.();
    const defFormData = formRef?.current?.getInitFormData?.();

    const isTypeFormList = modalDataType === SHIFT_TABLE_ADD_DATA_TYPE.FORM_LIST;
    const isTypeDict = modalDataType === SHIFT_TABLE_ADD_DATA_TYPE.DICT;

    // 有数据-则显示
    isTypeFormList && handleUpdateFieldType('dictConfigFormConfig', 'select', [], requireConfig);
    isTypeDict && handleUpdateFieldType('dictConfigDictList', 'treeSelect', [], requireConfig);
    // 数据集有选值 -> 更新可选值下拉列表
    if (dictKey && isTypeDict) {
      Promise.resolve(handleGetDictOptions(dictKey)).then(options => {
        handleUpdateFieldType('dictConfigOptionIds', 'select', [], {
          configs: {
            options,
          },
        });
      });
    }

    // 选择的表单列表并且关联表单列表有值
    if (isTypeFormList && formConfig) {
      Promise.resolve(handleGetOptions(formConfig)).then(options => {
        handleUpdateFieldType('dictConfigFormCellFieldKey', 'select', [], {
          ...requireConfig,
          configs: {
            options,
          },
        });
        handleUpdateFieldType('dictConfigFormCellSaveFieldKey', 'select', [], {
          configs: {
            options,
          },
        });
        handleUpdateFieldType('dictConfigSearchParams', 'input', []);
      });
    }

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigRowFieldKey',
      curFormData?.dictConfigRowFieldKey || defFormData?.dictConfigRowFieldKey || rowFieldKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigColFieldKey',
      curFormData?.dictConfigColFieldKey || defFormData?.dictConfigColFieldKey || colFieldKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFormCellFieldKey',
      curFormData?.dictConfigFormCellFieldKey || defFormData?.dictConfigFormCellFieldKey || formCellFieldKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFormCellSaveFieldKey',
      curFormData?.dictConfigFormCellSaveFieldKey || defFormData?.dictConfigFormCellSaveFieldKey || formCellSaveFieldKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigSearchParams',
      curFormData?.dictConfigSearchParams || defFormData?.dictConfigSearchParams || searchParams
    );
    formRef?.current?.setFormItem?.(
      'dictConfigCellSaveFieldKey',
      curFormData?.dictConfigCellSaveFieldKey || defFormData?.dictConfigCellSaveFieldKey || cellSaveFieldKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigHeaderNames',
      curFormData?.dictConfigHeaderNames || defFormData?.dictConfigHeaderNames || headerNames
    );
    formRef?.current?.setFormItem?.(
      'dictConfigModalDataType',
      curFormData?.dictConfigModalDataType || defFormData?.dictConfigModalDataType || modalDataType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFormConfig',
      curFormData?.dictConfigFormConfig || defFormData?.dictConfigFormConfig || formConfig
    );
    formRef?.current?.setFormItem?.(
      'dictConfigDictList',
      curFormData?.dictConfigDictList || defFormData?.dictConfigDictList || dictKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigPeriodTypeKey',
      curFormData?.dictConfigPeriodTypeKey || defFormData?.dictConfigPeriodTypeKey || periodTypeKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFrequencyKey',
      curFormData?.dictConfigFrequencyKey || defFormData?.dictConfigFrequencyKey || frequencyKey
    );

    formRef?.current?.setFormItem?.(
      'dictConfigIsUseBuiltIn',
      curFormData?.dictConfigIsUseBuiltIn || defFormData?.dictConfigIsUseBuiltIn || isUseBuiltIn
    );
    formRef?.current?.setFormItem?.(
      'dictConfigOptionIds',
      curFormData?.dictConfigOptionIds || defFormData?.dictConfigOptionIds || optionIds
    );

    formRef?.current?.setFormItem?.(
      'dictConfigHeaderTitle',
      curFormData?.dictConfigHeaderTitle || defFormData?.dictConfigHeaderTitle || headerTitle
    );

    formRef?.current?.setFormItem?.(
      'dictConfigHeaderTooltip',
      curFormData?.dictConfigHeaderTooltip || defFormData?.dictConfigHeaderTooltip || headerTooltip
    );
    formRef?.current?.setFormItem?.(
      'dictConfigIsUseTabStyle',
      curFormData?.dictConfigIsUseTabStyle || defFormData?.dictConfigIsUseTabStyle || isUseTabStyle
    );
    formRef?.current?.setFormItem?.(
      'dictConfigTableKey',
      curFormData?.dictConfigTableKey || defFormData?.dictConfigTableKey || tableKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigSelectType',
      curFormData?.dictConfigSelectType || defFormData?.dictConfigSelectType || selectType
    );
  });

  const list = context?.dataEntity?.treeData?.data || [];

  const formOptions = list.map(listItem => ({
    key: JSON.stringify(listItem),
    value: JSON.stringify(listItem),
    label: listItem.name,
  }));
  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    // 把字典的id塞这里
    // 渲染端会自动帮我们生成字典下拉
    {
      type: 'hidecomp',
      key: 'dictId',
    },
    {
      type: 'select',
      key: 'dictConfigModalDataType',
      label: '添加的数据类型',
      required: true,
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请选择',
          },
        ],
      },
      configs: {
        placeholder: '请选择',
        options: [
          {
            label: '数据集',
            key: SHIFT_TABLE_ADD_DATA_TYPE.DICT,
            value: SHIFT_TABLE_ADD_DATA_TYPE.DICT,
          },
          {
            label: '表单列表',
            key: SHIFT_TABLE_ADD_DATA_TYPE.FORM_LIST,
            value: SHIFT_TABLE_ADD_DATA_TYPE.FORM_LIST,
          },
        ],
        onSelect: val => {
          if (val === SHIFT_TABLE_ADD_DATA_TYPE.FORM_LIST) {
            val === SHIFT_TABLE_ADD_DATA_TYPE.FORM_LIST &&
              handleUpdateFieldType(
                'dictConfigFormConfig',
                'select',
                ['dictConfigDictList', 'dictConfigOptionIds'],
                requireConfig
              );
          }

          if (val === SHIFT_TABLE_ADD_DATA_TYPE.DICT) {
            handleUpdateFieldType(
              'dictConfigDictList',
              'treeSelect',
              [
                'dictConfigSelectType',
                'dictConfigFormConfig',
                'dictConfigSearchParams',
                'dictConfigFormCellFieldKey',
                'dictConfigFormCellSaveFieldKey',
              ],
              requireConfig
            );
          }

          context?.onConfirm?.('dictConfigModalDataType', val);
          setDictConfig(formRef, 'modalDataType', val, {
            context,
          });
        },
      },
    },
    {
      required: true,
      type: 'hidecomp',
      label: '关联表单列表',
      key: 'dictConfigFormConfig',
      configs: {
        placeholder: '请选择',
        options: formOptions,
        showSearch: true,
        optionFilterProp: 'label',
        onSelect: async val => {
          Promise.resolve(handleGetOptions(val)).then(options => {
            handleUpdateFieldType('dictConfigFormCellFieldKey', 'select', [], {
              ...requireConfig,
              configs: {
                options,
              },
            });
            handleUpdateFieldType('dictConfigFormCellSaveFieldKey', 'select', [], {
              configs: {
                options,
              },
            });
            handleUpdateFieldType('dictConfigSearchParams', 'input', []);
          });
          context?.onConfirm?.('dictConfigFormConfig', val);
          context?.onConfirm?.('dictConfigSearchParams', undefined);
          context?.onConfirm?.('dictConfigFormCellFieldKey', undefined);
          context?.onConfirm?.('dictConfigFormCellSaveFieldKey', undefined);
          setDictConfig(formRef, 'formConfig', val, {
            context,
          });
        },
      },
    },
    {
      label: '数据集',
      type: 'hidecomp',
      required: true,
      key: 'dictConfigDictList',
      configs: {
        showSearch: true,
        placeholder: '请选择',
        options: dictOptions,
        treeNodeFilterProp: 'label',
        onSelect: value => {
          const dictId = value || '';
          Promise.resolve(handleGetDictOptions(dictId)).then(options => {
            handleUpdateFieldType('dictConfigOptionIds', 'select', [], {
              configs: {
                options,
              },
            });
          });
          context?.onConfirm?.('dictConfigOptionIds', undefined);
          setBaseConfig(formRef, 'dictId', dictId, {
            context,
          });
          setDictConfig(formRef, 'dictKey', dictId, {
            context,
          });
        },
      },
    },
    {
      label: '可选值(默认为所有选项)',
      type: 'hidecomp',
      key: 'dictConfigOptionIds',
      configs: {
        options: [],
        mode: 'multiple',
        showSearch: true,
        allowClear: true,
        placeholder: '请选择',
        treeNodeFilterProp: 'label',
        onChange: value => {
          context?.onConfirm?.('dictConfigOptionIds', value);
          setDictConfig(formRef, 'optionIds', value, {
            context,
          });
        },
      },
    },
    // form的单元格显示字段
    {
      required: true,
      type: 'hidecomp',
      label: '单元格显示设置',
      labelTips: '表单列表展示的字段才有效',
      key: 'dictConfigFormCellFieldKey',
      configs: {
        showSearch: true,
        placeholder: '请选择',
        options: [],
        optionFilterProp: 'label',
        onSelect: value => {
          setDictConfig(formRef, 'formCellFieldKey', value, {
            context,
          });
        },
      },
    },
    {
      type: 'hidecomp',
      label: '实际存储的关联字段值',
      labelTips: '列表显示的字段才有效',
      key: 'dictConfigFormCellSaveFieldKey',
      configs: {
        options: [],
        showSearch: true,
        allowClear: true,
        placeholder: '默认使用主键',
        optionFilterProp: 'label',
        onChange: value => {
          setDictConfig(formRef, 'formCellSaveFieldKey', value, {
            context,
          });
        },
      },
    },
    {
      type: 'hidecomp',
      label: '默认搜索参数',
      labelTips: 'JSON格式',
      key: 'dictConfigSearchParams',
      configs: {
        allowClear: true,
        placeholder: '例：{"C_GEN_CYCLE": "1"}',
        onChange: value => {
          setDictConfig(formRef, 'searchParams', value, {
            context,
          });
        },
      },
    },
    {
      required: true,
      type: 'input',
      label: '单元格储存字段',
      key: 'dictConfigCellSaveFieldKey',
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请填写',
          },
        ],
      },
      configs: {
        placeholder: '请填写',
        onBlur: () => {
          const dictConfigCellSaveFieldKey = formRef?.current?.getFormItem?.('dictConfigCellSaveFieldKey');
          context?.onConfirm?.('dictConfigCellSaveFieldKey', dictConfigCellSaveFieldKey);
          setDictConfig(formRef, 'cellSaveFieldKey', dictConfigCellSaveFieldKey, {
            context,
          });
        },
      },
    },

    {
      type: 'select',
      label: '数据勾选设置',
      key: 'dictConfigSelectType',
      configs: {
        placeholder: '请选择，默认单选',
        options: SELECT_TYPE_OPTIONS,
        showSearch: true,
        optionFilterProp: 'label',
        onSelect: async val => {
          context?.onConfirm?.('dictConfigSelectType', val);
          setDictConfig(formRef, 'selectType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigTableKey',
      label: '明细表字段映射',
      required: true,
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请填写',
          },
        ],
      },
      configs: {
        placeholder: '请填写循环模式字段名',
        onBlur: () => {
          const dictConfigTableKey = formRef?.current?.getFormItem?.('dictConfigTableKey');
          context?.onConfirm?.('dictConfigTableKey', dictConfigTableKey);
          setDictConfig(formRef, 'tableKey', dictConfigTableKey, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigPeriodTypeKey',
      label: '循环模式字段',
      required: true,
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请填写',
          },
        ],
      },
      configs: {
        placeholder: '请填写循环模式字段名',
        onBlur: () => {
          const dictConfigPeriodTypeKey = formRef?.current?.getFormItem?.('dictConfigPeriodTypeKey');
          context?.onConfirm?.('dictConfigPeriodTypeKey', dictConfigPeriodTypeKey);
          setDictConfig(formRef, 'periodTypeKey', dictConfigPeriodTypeKey, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigFrequencyKey',
      label: '频率字段',
      required: true,
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请填写',
          },
        ],
      },
      configs: {
        placeholder: '请填写频率字段名',
        onBlur: () => {
          const dictConfigFrequencyKey = formRef?.current?.getFormItem?.('dictConfigFrequencyKey');
          context?.onConfirm?.('dictConfigFrequencyKey', dictConfigFrequencyKey);
          setDictConfig(formRef, 'frequencyKey', dictConfigFrequencyKey, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigRowFieldKey',
      label: '行索引字段',
      configs: {
        placeholder: '行索引字段,默认值为row',
        allowClear: true,
        onBlur: () => {
          const dictConfigRowFieldKey = formRef?.current?.getFormItem?.('dictConfigRowFieldKey');
          context?.onConfirm?.('dictConfigRowFieldKey', dictConfigRowFieldKey);
          setDictConfig(formRef, 'rowFieldKey', dictConfigRowFieldKey, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigColFieldKey',
      label: '列索引字段',
      configs: {
        placeholder: '列索引字段,默认值为col',
        allowClear: true,
        onBlur: () => {
          const dictConfigColFieldKey = formRef?.current?.getFormItem?.('dictConfigColFieldKey');
          context?.onConfirm?.('dictConfigColFieldKey', dictConfigColFieldKey);
          setDictConfig(formRef, 'colFieldKey', dictConfigColFieldKey, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigHeaderNames',
      label: '行头名称列表',
      configs: {
        placeholder: '以逗号隔开,默认值为一到日',
        allowClear: true,
        onBlur: () => {
          const dictConfigHeaderNames = formRef?.current?.getFormItem?.('dictConfigHeaderNames');
          context?.onConfirm?.('dictConfigHeaderNames', dictConfigHeaderNames);
          setDictConfig(formRef, 'headerNames', dictConfigHeaderNames, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigIsUseBuiltIn',
      label: '启用内置联动组件',
      configs: {
        placeholder: '请选择',
        allowClear: true,
        options: WHETHER_OPTIONS,
        onChange: val => {
          context?.onConfirm?.('dictConfigIsUseBuiltIn', val);
          setDictConfig(formRef, 'isUseBuiltIn', val, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigHeaderTitle',
      label: '标题',
      configs: {
        allowClear: true,
        placeholder: '请填写',
        onBlur: () => {
          const dictConfigHeaderTitle = formRef?.current?.getFormItem?.('dictConfigHeaderTitle');
          context?.onConfirm?.('dictConfigHeaderTitle', dictConfigHeaderTitle);
          setDictConfig(formRef, 'headerTitle', dictConfigHeaderTitle, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigHeaderTooltip',
      label: '内容提示',
      configs: {
        allowClear: true,
        placeholder: '请填写',
        onBlur: () => {
          const dictConfigHeaderTooltip = formRef?.current?.getFormItem?.('dictConfigHeaderTooltip');
          context?.onConfirm?.('dictConfigHeaderTooltip', dictConfigHeaderTooltip);
          setDictConfig(formRef, 'headerTooltip', dictConfigHeaderTooltip, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigIsUseTabStyle',
      label: '启用tab样式',
      configs: {
        allowClear: true,
        placeholder: '请选择',
        options: WHETHER_OPTIONS,
        onChange: val => {
          context?.onConfirm?.('dictConfigIsUseTabStyle', val);
          setDictConfig(formRef, 'isUseTabStyle', val, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
