import React, { useCallback, useMemo, useRef, useState, Fragment, useEffect } from 'react';
import { Row, Col, Form, Select, InputNumber, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import classnames from 'classnames';

import { IOriginalProps } from '@src/types';
import FormAction from './containers/FormAction';
import DataItemModal from './containers/DataItemModal';
import { SHIFT_TABLE_ADD_DATA_TYPE, SHIFT_TABLE_SELECT_TYPE } from '@constants/common';

import './style.less';

const CYCLIC_TYPE_MAP = {
  WEEK: 'WEEK',
  DAY: 'DAY',
};

const { Item: FormItem } = Form;
interface IAppProps extends IOriginalProps {
  value: any;
  onChange: (val: any) => void;
}
const ShiftTable: React.FC<IAppProps> = props => {
  const { value, onChange } = props;
  console.log('props', props);

  const configs = props?.configs;
  const config = configs?.config;
  const rulesConfig = config?.rulesConfig || [];
  const isRequired = rulesConfig.some(
    ({ isEnabled, type, value }) => isEnabled && type === 'required' && value === '1'
  );

  const context = configs?.context;
  const formData = context?.getFormData?.() || {};
  console.log('value===', value);
  console.log('props======数据', formData);

  const dictConfig = configs.config.baseConfig?.dictConfig || {};
  const {
    tableKey,
    optionIds,
    formConfig,
    colFieldKey,
    headerNames,
    rowFieldKey,
    headerTitle,
    frequencyKey,
    isUseBuiltIn,
    isUseTooltip,
    periodTypeKey,
    modalDataType,
    headerTooltip,
    isUseTabStyle,
    searchParams,
    cellSaveFieldKey,
    formCellFieldKey,
    formCellSaveFieldKey,
    selectType = SHIFT_TABLE_SELECT_TYPE.RADIO,
  } = dictConfig;

  const optionMap = context?.getDictionary?.() || {};
  const formConfigParse = formConfig ? JSON.parse(formConfig || '{}') : {};

  const useTabStyle = isUseTabStyle === '1';
  const initTableValue = formData[tableKey];

  // 周期的值
  const periodTypeVal = formData[periodTypeKey];
  // 频率
  const frequencyVal = Number(formData[frequencyKey]);

  const tableRef = useRef([]);
  const cyclicTypeRef = useRef(undefined);
  // 标识 是否已经执行过onChange
  const updateValRef = useRef(false);
  const [count, setCount] = useState<number>(1);
  const [tableList, setTableList] = useState<any[]>([]);
  const [cyclicType, setCyclicType] = useState<string>(undefined);

  const customTitleList = headerNames ? headerNames.split(/[,，]/g) : ['一', '二', '三', '四', '五', '六', '日'];
  const columns = useMemo(() => ['序号', ...customTitleList], [customTitleList]);
  // 一周的长度
  const weekLen = useMemo(() => customTitleList.length, [customTitleList]);
  // 数据选择是否多选
  const isMultiple = selectType === SHIFT_TABLE_SELECT_TYPE.MULTIPLE;

  // 字典下拉选项
  const dictOptions = useMemo(() => {
    let list = props?.data?.options || [];
    if ((optionIds || []).length > 0) {
      list = list.filter(({ itemValue }) => optionIds.includes(itemValue));
    }
    return list;
  }, [props, optionIds]);

  useEffect(() => {
    props.configs.context?.addListener?.({
      code: Math.random(),
      type: 'valueChange',
      handler: data => {
        const { attrCode, value } = data;
        attrCode === periodTypeKey && handleChangeCyclicType(value);
        attrCode === frequencyKey && handleChangeCyclicCount(value);
      },
    });
  }, []);

  useEffect(() => {
    setCyclicType(periodTypeVal);
    cyclicTypeRef.current = periodTypeVal;
  }, [periodTypeVal]);

  useEffect(() => {
    if (frequencyVal) {
      setCount(frequencyVal);
    }
  }, [frequencyVal]);

  useEffect(() => {
    onInitData();
  }, [
    isRequired,
    periodTypeVal,
    frequencyVal,
    weekLen,
    initTableValue,
    rowFieldKey,
    colFieldKey,
    optionMap,
    modalDataType,
  ]);

  // 翻译下拉选项的数据
  const handleFieldTranslate = useCallback(
    (tableItem: any) => {
      const keyVal = tableItem?.[cellSaveFieldKey] || '';
      const keysArr = keyVal.split(',');
      const tableFieldOptions = optionMap?.[`${tableKey}:${cellSaveFieldKey}`] || [];
      const selectRows = tableFieldOptions.filter(optionsItem =>
        keysArr.includes(optionsItem[formCellSaveFieldKey || 'objId'])
      );
      return selectRows;
    },
    [optionMap, tableKey, cellSaveFieldKey, formCellSaveFieldKey]
  );

  // 初始化回写到虚拟字段
  const handleInitUpdateChange = useCallback(
    list => {
      const isNotAllEdit = list.some(tableItem => !tableItem);
      if ((isRequired && !isNotAllEdit) || !isRequired) {
        // 过滤掉 undefined
        const newTableList = list
          .filter(tableItem => !!tableItem)
          .map(tableItem => {
            const newTableItem = {
              ...tableItem,
            };
            newTableItem.selectRows && delete newTableItem.selectRows;
            return newTableItem;
          });
        onChange(JSON.stringify(newTableList));
      }
      if (isRequired && isNotAllEdit) {
        onChange(undefined);
      }
    },
    [isRequired]
  );

  // 初始化数据
  const onInitData = () => {
    try {
      const isUpdateVal = updateValRef.current;
      if (!isUpdateVal) {
        // 存在周期&频率
        if (periodTypeVal && frequencyVal) {
          let tableLen = frequencyVal;
          periodTypeVal === CYCLIC_TYPE_MAP.WEEK && (tableLen = frequencyVal * weekLen);
          !initTableValue && handleUpdateTableList(tableLen);
          if (initTableValue) {
            // 生成固定长度的空数组
            const updateTableList = Array(tableLen).fill(undefined);
            try {
              let list = initTableValue || [];
              // 翻译 字段
              if (modalDataType === SHIFT_TABLE_ADD_DATA_TYPE.FORM_LIST) {
                list = list.map(listItem => ({
                  ...listItem,
                  selectRows: handleFieldTranslate(listItem),
                }));
              }
              //  存在行列的字段
              if (rowFieldKey && colFieldKey) {
                list.forEach(item => {
                  const row = Number(item[rowFieldKey] || 1) - 1;
                  const col = Number(item[colFieldKey] || 1) - 1;
                  const index = row * weekLen + col;
                  updateTableList[index] = { ...item };
                });
              } else {
                updateTableList.splice(0, list.length, ...list);
              }
              // 如果有数据需要回写回去
              handleInitUpdateChange(updateTableList);
            } catch (error) {
              console.log('特殊列表初始化失败啦~~~', error);
            }
            tableRef.current = updateTableList;
            setTableList(updateTableList);
          }
        }
      }
    } catch (error) {
      console.log('初始化失败啦～～', error);
    }
  };

  // 更新字段的值
  const handleChange = useCallback(() => {
    const tableList = [...(tableRef.current || [])];
    const isNotAllEdit = tableList.some(tableItem => !tableItem);
    if ((isRequired && !isNotAllEdit) || !isRequired) {
      // 过滤掉 undefined
      const newTableList = tableList
        .filter(tableItem => !!tableItem)
        .map(tableItem => {
          const newTableItem = {
            ...tableItem,
          };
          newTableItem.selectRows && delete newTableItem.selectRows;
          return newTableItem;
        });
      onChange(JSON.stringify(newTableList));
      context?.setFormData({
        [tableKey]: newTableList,
      });
    }
    if (isRequired && isNotAllEdit) {
      onChange(undefined);
      context?.setFormData({
        [tableKey]: undefined,
      });
    }
    updateValRef.current = true;
  }, [isRequired, tableKey]);

  // 更新tableList
  const handleUpdateTableList = useCallback((len, inInit = false) => {
    const oldTableList = tableRef.current || [];
    const newTableList = [...oldTableList];
    const tableLen = oldTableList.length;
    const fillLength = len - tableLen;
    fillLength < 0 && newTableList.splice(len, fillLength * -1);
    if (fillLength > 0) {
      const addArr = Array(fillLength).fill(undefined);
      newTableList.push(...addArr);
    }
    setTableList(newTableList);
    tableRef.current = newTableList;
    !inInit && handleChange();
  }, []);

  // 切换循环周期
  const handleChangeCyclicType = useCallback(
    val => {
      const oldTableList = tableRef.current || [];
      const tableLen = oldTableList.length;
      let newCount = tableLen;
      // 周期
      if (val === CYCLIC_TYPE_MAP.WEEK) {
        // tableLen 为0 则默认给一周
        const weekTotalCount = tableLen || weekLen;
        const weekCount = Math.ceil(weekTotalCount / weekLen);
        const newLen = weekCount * weekLen;
        newCount = weekCount;
        handleUpdateTableList(newLen, true);
      }
      // 更新隐藏字段
      context?.setFormData?.({
        [periodTypeKey]: val,
        [frequencyKey]: newCount,
      });

      setCount(newCount);
      setCyclicType(val);
      cyclicTypeRef.current = val;
    },
    [weekLen, periodTypeKey, frequencyKey]
  );

  // 切换频率
  const handleChangeCyclicCount = useCallback(
    val => {
      const cyclicType = cyclicTypeRef.current;
      const isInteger = Number.isInteger(val);
      // 更新数组
      if (isInteger) {
        const len = cyclicType === CYCLIC_TYPE_MAP.WEEK ? val * weekLen : val;
        handleUpdateTableList(len);
      }
      setCount(val);
      // 更新隐藏字段
      context?.setFormData?.({
        [frequencyKey]: val,
      });
    },
    [context, frequencyKey]
  );

  // 添加 - 打开列表选择
  const handleAddItem = useCallback(
    (row, col, index) => {
      const params = {
        width: 900,
        isMultiple,
        pageType: 'LCP_VIEW',
        title: formConfigParse.title,
        pageCode: formConfigParse.id,
        classId: formConfigParse.id,
        appId: formConfigParse.appId,
        apiName: formConfigParse.apiName,
        tenantId: formConfigParse.tenantId,
      };
      if (searchParams) {
        try {
          const searchData = JSON.parse(searchParams || '{}');
          const metaConfig = {
            compParams: {
              LCP_VIEW_TABLE_FLTER: {
                data: searchData,
              },
            },
          };
          params['metaConfig'] = metaConfig;
        } catch (error) {}
      }
      DataItemModal.add({
        ...params,
        onOk: selectRows => {
          const oldTableList = tableRef.current || [];
          const newTableList = [...oldTableList];
          const saveFieldValues = selectRows.map(selectItem => selectItem[formCellSaveFieldKey || 'objId']);
          const fieldValues = saveFieldValues.join(',');
          const updateParams = {
            ...(newTableList[index] || {}),
            [cellSaveFieldKey]: fieldValues,
            selectRows,
          };
          rowFieldKey && (updateParams[rowFieldKey] = `${row}`);
          colFieldKey && (updateParams[colFieldKey] = `${col}`);
          // 更新
          newTableList.splice(index, 1, updateParams);
          setTableList(newTableList);
          tableRef.current = newTableList;
          handleChange();
        },
      });
    },
    [rowFieldKey, colFieldKey, formCellSaveFieldKey, formCellFieldKey, isMultiple, searchParams]
  );

  // 删除一个
  const handleDelItem = useCallback((row, col, index) => {
    const oldTableList = tableRef.current || [];
    const newTableList = [...oldTableList];
    // 置为undefined 占坑
    newTableList.splice(index, 1, undefined);
    setTableList(newTableList);
    tableRef.current = newTableList;
    handleChange();
  }, []);

  // 切换字典下拉
  const handleChangeDict = useCallback(
    (value, row, col, index) => {
      const oldTableList = tableRef.current || [];
      const newTableList = [...oldTableList];
      const updateParams = {
        ...(newTableList[index] || {}),
        [cellSaveFieldKey]: value,
      };
      rowFieldKey && (updateParams[rowFieldKey] = `${row}`);
      colFieldKey && (updateParams[colFieldKey] = `${col}`);
      // 更新
      newTableList.splice(index, 1, updateParams);
      setTableList(newTableList);
      tableRef.current = newTableList;
      handleChange();
    },
    [rowFieldKey, colFieldKey, cellSaveFieldKey]
  );

  const isShowTooltip = isUseTooltip === '1';
  const isShowBuiltIn = isUseBuiltIn === '1';
  const colWidth = useMemo(() => Math.floor((94 / weekLen) * 100) / 100, [weekLen]);

  return (
    <div className="shiftTableFormShowContainer">
      {headerTitle && (
        <div className={classnames('shiftTableHeaderTitle', { shiftTableHeaderTabStyle: useTabStyle })}>
          <span>{headerTitle}</span>
          {headerTooltip && (
            <Tooltip title={headerTooltip}>
              <QuestionCircleOutlined style={{ cursor: 'pointer', marginLeft: '4px' }} />
            </Tooltip>
          )}
        </div>
      )}
      {isShowBuiltIn && (
        <Row className="cyclicConfigs">
          <Col span={4}>
            <FormItem label="循环周期">
              <Select
                options={[
                  {
                    label: '按周',
                    value: CYCLIC_TYPE_MAP.WEEK,
                  },
                  {
                    label: '按天',
                    value: CYCLIC_TYPE_MAP.DAY,
                  },
                ]}
                value={cyclicType}
                placeholder="请选择"
                onChange={handleChangeCyclicType}
              />
            </FormItem>
          </Col>
          {cyclicType && (
            <Col span={4} offset={1}>
              <FormItem label="频率">
                <InputNumber
                  min={1}
                  value={count}
                  precision={0}
                  placeholder="请填写"
                  onChange={handleChangeCyclicCount}
                />
              </FormItem>
            </Col>
          )}
        </Row>
      )}
      <Row className="titleContainer" style={{ marginBottom: 0 }}>
        {columns.map((colVal, index) => (
          <Col key={`title_${index}`} className="shiftTableTitle" style={{ width: `${index === 0 ? '6' : colWidth}%` }}>
            {colVal}
          </Col>
        ))}
        {tableList.map((tableItem, index) => {
          const row = Math.floor(index / weekLen) + 1;
          const col = (index % weekLen) + 1;
          const value = tableItem?.[cellSaveFieldKey];
          return (
            <Fragment key={`${row}${col}`}>
              {index % weekLen === 0 && (
                <Col
                  style={{ width: '6%' }}
                  key={`number_${index}`}
                  className="shiftTableContent rightSidebar bottomSidebar"
                >
                  {index / weekLen + 1}
                </Col>
              )}
              <Col
                key={`content_${index}`}
                style={{ width: `${colWidth}%` }}
                className={classnames('shiftTableContent bottomSidebar', {
                  rightSidebar: (index + 1) % weekLen !== 0,
                })}
              >
                {modalDataType === SHIFT_TABLE_ADD_DATA_TYPE.FORM_LIST && (
                  <FormAction
                    row={row}
                    col={col}
                    index={index}
                    key={`${row}${col}`}
                    initValues={tableItem}
                    isTooltip={isShowTooltip}
                    onAddItem={handleAddItem}
                    formConfig={formConfigParse}
                    cellFieldKey={formCellFieldKey}
                    onDelItem={(row, col, index) => handleDelItem(row, col, index)}
                  />
                )}
                {modalDataType === SHIFT_TABLE_ADD_DATA_TYPE.DICT && (
                  <Select
                    allowClear
                    showSearch
                    value={value}
                    placeholder="请选择"
                    options={dictOptions}
                    optionFilterProp="label"
                    mode={isMultiple ? 'multiple' : undefined}
                    onChange={val => handleChangeDict(val, row, col, index)}
                  />
                )}
              </Col>
            </Fragment>
          );
        })}
      </Row>
    </div>
  );
};

export default ShiftTable;
