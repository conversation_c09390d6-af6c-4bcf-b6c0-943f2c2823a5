.shiftTableFormShowContainer,
.shiftTableFormRenderContainer {
  .shiftTableHeaderTitle {
    line-height: 1.5715;
    font-variant: tabular-nums;
    font-feature-settings: 'tnum';
    padding: var(--small-space) 0;
    font-size: var(--large-font);
    margin: 0 0 var(--small-space);
  }

  .shiftTableHeaderTabStyle {
    color: var(--antd-dynamic-primary-color);
    text-shadow: 0 0 0.25px currentcolor;
    position: relative;
    &::before {
      content: '';
      left: 0;
      right: 0;
      bottom: 0;
      position: absolute;
      border-bottom: 1px solid #f0f0f0;
    }
    &::after {
      content: '';
      left: 0;
      bottom: 0;
      width: 70px;
      height: 2px;
      position: absolute;
      background-color: var(--antd-dynamic-primary-color);
    }
  }
  .cyclicConfigs {
    & > .ant-col {
      & > .ant-form-item {
        margin-bottom: 0;
        & > .ant-form-item-row {
          margin-bottom: 0;
        }
      }
    }
  }
  .titleContainer {
    border-radius: 2px;
    background-color: #fafafa99;
    border: 1px solid #0000000f;
    .shiftTableTitle {
      padding-top: 6px;
      padding-bottom: 6px;
      line-height: 20px;
      padding-left: 6px;
      position: relative;
      border-bottom: 1px solid #0000000f;
      background-color: var(--head-color);
      &:not(:last-of-type) {
        &::after {
          content: '';
          width: 1px;
          position: absolute;
          top: 0;
          bottom: 0;
          right: 0;
          background-color: #0000000f;
        }
      }
    }
  }

  .bottomSidebar {
    &::before {
      content: '';
      height: 1px;
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      background-color: #0000000f;
    }
  }

  .rightSidebar {
    &::after {
      content: '';
      width: 1px;
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      background-color: #0000000f;
    }
  }

  .shiftTableContent {
    padding: 8px 6px;
    position: relative;
  }
}
