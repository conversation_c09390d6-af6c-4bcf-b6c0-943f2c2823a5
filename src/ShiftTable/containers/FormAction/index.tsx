import { EyeOutlined, EllipsisOutlined } from '@ant-design/icons';
import React, { useCallback, useMemo, useRef } from 'react';
import { Button, Tooltip, Dropdown, Popover } from 'antd';
import type { MenuProps } from 'antd';

import PopoverContent from './containers/PopoverContent';

import './style.less';

export interface IAppProps {
  initValues: any;
  row: number;
  col: number;
  index: number;
  isTooltip: boolean;
  cellFieldKey: string;
  formConfig: {
    appId: string;
    id: string;
    tenantId: string;
  };
  onAddItem: (row: number, col: number, index: number) => void;
  onDelItem: (row: number, col: number, index: number) => void;
}
const FormAction: React.FC<IAppProps> = ({
  isTooltip,
  cellFieldKey,
  initValues,
  row,
  col,
  index,
  formConfig,
  onAddItem,
  onDelItem,
}) => {
  const items: MenuProps['items'] = useMemo(
    () => [
      {
        key: 'add',
        label: '重新选择',
      },
      {
        key: 'del',
        label: '删除',
      },
    ],
    []
  );

  const handleSelectMenuItem: MenuProps['onClick'] = useCallback(e => {
    const key = e.key;

    const fnMap = {
      add: () => onAddItem(row, col, index),
      del: () => onDelItem(row, col, index),
    };
    fnMap[key]?.();
  }, []);

  const selectRows = initValues?.['selectRows'] || [];
  const cellContents = selectRows.map(selectItem => selectItem[cellFieldKey]).join('、');
  return (
    <div className="shiftTableFormActionContainer">
      {!initValues && (
        <Button ghost type="primary" className="shiftTableItemAddBtn" onClick={() => onAddItem(row, col, index)}>
          添加
        </Button>
      )}
      {initValues && (
        <div className="shiftTableFormShowContent">
          <div className="contentIconAction">
            <Dropdown
              menu={{
                items,
                onClick: handleSelectMenuItem,
              }}
            >
              <EllipsisOutlined className="ellipsIcon" />
            </Dropdown>
            <Popover
              className="eyeIcon"
              content={
                <PopoverContent
                  list={selectRows}
                  classId={formConfig?.id}
                  appId={formConfig?.appId}
                  tenantId={formConfig?.tenantId}
                  metaConfig={{
                    contextData: {
                      viewType: 'read',
                    },
                  }}
                />
              }
            >
              <EyeOutlined />
            </Popover>
          </div>
          <div className="selectShowContent">
            <Tooltip title={cellContents}>
              <span className="showDetail">{cellContents}</span>
            </Tooltip>
          </div>
        </div>
      )}
    </div>
  );
};

export default FormAction;
