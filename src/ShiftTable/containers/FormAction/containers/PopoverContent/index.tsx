import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Spin } from 'antd';

import { LCPDetailTemplate } from '@components/LcpTemplate';

import './style.less';

export interface IAppProps {
  list: any[];
  appId: string;
  classId: string;
  tenantId: string;
  metaConfig: any;
}
const PopoverContent: React.FC<IAppProps> = forwardRef((props, ref: any) => {
  const { list, ...othProps } = props;

  const loadingRef = useRef({});
  // 单纯控制渲染
  const [load, setLoad] = useState<boolean>(false);

  useEffect(() => {
    list.forEach(({ objId }) => {
      loadingRef.current = {
        ...loadingRef.current,
        [objId]: true,
      };
      setLoad(!load);
    });
  }, [list]);

  const handleLoadComplete = useCallback(
    (objId: string) => {
      if (objId) {
        loadingRef.current = {
          ...loadingRef.current,
          [objId]: false,
        };
        setLoad(!load);
      }
    },
    [load]
  );

  const pageLoading: boolean = useMemo(() => {
    const newLoading = Object.values(loadingRef.current).some(loading => loading);
    return newLoading;
  }, [load]);

  return (
    <Spin spinning={pageLoading}>
      <div className="shiftTablePopoverContent">
        {list.map(listItem => {
          const objId = listItem?.objId;
          return (
            <LCPDetailTemplate {...othProps} ref={ref} pageId={objId} onInitData={() => handleLoadComplete(objId)} />
          );
        })}
      </div>
    </Spin>
  );
});

export default PopoverContent;
