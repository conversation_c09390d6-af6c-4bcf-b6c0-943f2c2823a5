import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useEffect, useRef, useState } from 'react';
import { Modal, message } from 'antd';

import { LCPPageTemplate } from '@components/LcpTemplate';

import './style.less';

export interface ModalCommonProps {
  title: string;
  classId: string;
  pageCode: string;
  pageType: string;
  tenantId: string;
  apiName: string;
  appId: string;
  width?: number;
  isMultiple: boolean;
  onOk?: (selects: any[]) => void;
}

export interface IAppProps extends ModalCommonProps {
  visible: boolean;
  onDestroy: () => void;
}
const DataItemModal: React.FC<IAppProps> = ({ width, visible, title, isMultiple, onOk, onDestroy, ...otherProps }) => {
  const tableRef = useRef(null);
  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    setOpen(visible);
  }, [visible]);

  const handleOk = () => {
    const context = tableRef.current?.getRef()?.RenderRef?.current?.getContext?.();
    const selectedRows = context?.getCompRef?.({ code: 'VIEW_AREA' })?.selectedRows || [];
    if (selectedRows.length > 0) {
      if (!isMultiple && selectedRows.length > 1) {
        return message.warning('请勾选一条数据哦～');
      }
      onOk(selectedRows);
      return setOpen(false);
    }
    message.warning('请勾选数据');
  };

  return (
    <Modal
      open={open}
      width={width}
      title={title}
      onOk={handleOk}
      afterClose={onDestroy}
      className="dataItemModal"
      onCancel={() => setOpen(false)}
    >
      <div className="dataItemModalContainer">
        <LCPPageTemplate {...otherProps} ref={tableRef} />
      </div>
    </Modal>
  );
};

function withParams(params: ModalCommonProps): IAppProps {
  return {
    visible: true,
    onDestroy: () => {},
    ...params,
  };
}

function add(config: IAppProps) {
  const containers = document.createDocumentFragment();
  const destroy = () => {
    reactUnmount(containers);
  };
  reactRender(<DataItemModal {...config} onDestroy={destroy} />, containers);
}

export type ModalStaticFunctions = Record<string, any>;

type ModalType = typeof DataItemModal & ModalStaticFunctions;

const AddModal = DataItemModal as ModalType;

AddModal.add = function addFn<T extends ModalCommonProps>(props: T) {
  return add(withParams(props));
};

export default AddModal;
