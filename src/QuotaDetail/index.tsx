import React, { useCallback, useMemo } from 'react';
import { Spin, Modal } from 'antd';

import useFetch from '@hooks/useFetch';
import useDictCode from '@hooks/useDictCode';
import salarySlipApis from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';
import excelTask from '@components/ExportLargeExcel';
import { exportMultiExcel, IExport } from '@utils/excel';
import { defaultObj, showSucNotification, toDateFormat } from '@utils/tools';

import QuotaAddModal from './containers/QuotaAddModal';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

import { DICT_CODE_MAP_ID } from '@constants/common';

import { IOriginalProps } from '../types/index';

const filterFormKey = 'quotaDetailFormKey';

export interface IAppProps extends IOriginalProps {
  channel: string;
  processId: string;
}

const QuotaDetail: React.FC<IAppProps> = ({ processId, channel }) => {
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);

  const { DHR_NOTIFICATION_STATUS = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_NOTIFICATION_STATUS]);

  /** 请求过程 */
  const {
    Data: payroll,
    Loading: payrollListLoading,
    runAction: runActionFetchNotofyList,
  } = useFetch({
    ...salarySlipApis.salaryslipNotifyList,
    params: {
      channel,
      processId,
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  /** 请求过程 */
  const onFetchNotifyList = (data: Record<string, any> = {}) => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    runActionFetchNotofyList({
      params: {
        channel,
        processId,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...formData,
        ...data,
      },
    });
  };

  const columns = useMemo(
    () => [
      {
        title: '工号',
        dataIndex: 'receiverCode',
        key: 'receiverCode',
      },
      {
        title: '别名',
        dataIndex: 'channelTypeName',
        key: 'channelTypeName',
      },
      {
        title: '域账号',
        dataIndex: 'statusName',
        key: 'statusName',
      },
      {
        title: '所属公司',
        dataIndex: 'typeName',
        key: 'typeName',
      },
      {
        title: '部门全路径',
        dataIndex: 'sendCount',
        key: 'sendCount',
      },
      {
        title: '职位',
        dataIndex: 'definedSendTime',
        key: 'definedSendTime',
      },
      {
        title: '岗位',
        dataIndex: 'sendTime',
        key: 'sendTime',
      },
      {
        title: '定额项目',
        dataIndex: 'sendTime',
        key: 'sendTime',
      },
      {
        title: '来源',
        dataIndex: 'sendTime',
        key: 'sendTime',
      },
      {
        title: '单位',
        dataIndex: 'sendTime',
        key: 'sendTime',
      },
      {
        title: '享有时长',
        dataIndex: 'sendTime',
        key: 'sendTime',
      },
      {
        title: '可用时长',
        dataIndex: 'crtTime',
        key: 'crtTime',
      },
      {
        title: '已用时长',
        dataIndex: 'crtTime1',
        key: 'crtTime1',
      },
      {
        title: '结账时长',
        dataIndex: 'crtTime2',
        key: 'crtTime2',
      },
      {
        title: '失效时长',
        dataIndex: 'crtTime3',
        key: 'crtTime3',
      },
      {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
        render: ({ data: { crtTime } }) => toDateFormat(crtTime),
      },
      {
        title: '结束时间',
        dataIndex: 'endTime',
        key: 'endTime',
        render: ({ data: { crtTime } }) => toDateFormat(crtTime),
      },
      {
        title: '使用开始时间',
        dataIndex: 'startTime234',
        key: 'startTime234',
        render: ({ data: { crtTime } }) => toDateFormat(crtTime),
      },
      {
        title: '使用结束时间',
        dataIndex: 'endTime234',
        key: 'endTime234',
        render: ({ data: { crtTime } }) => toDateFormat(crtTime),
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
      },
      {
        title: '编码',
        dataIndex: 'code',
        key: 'code',
      },
    ],
    []
  );

  const filterFormItems: any[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'receiverCode',
        label: '组织',
        configs: {
          allowClear: true,
          placeholder: '请输入',
        },
        col: 8,
      },
      {
        type: 'input',
        key: 'name',
        label: '定额名称',
        configs: {
          allowClear: true,
          placeholder: '请输入',
        },
        col: 8,
      },
      {
        type: 'select',
        key: 'status',
        label: '生成年份',
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: (DHR_NOTIFICATION_STATUS || []).map(codeItem => ({
            ...codeItem,
            label: codeItem.name,
            value: codeItem.itemValue,
            key: codeItem.itemValue,
          })),
        },
        col: 8,
      },
    ],
    [DHR_NOTIFICATION_STATUS]
  );

  // 新增
  const handleAdd = useCallback(() => {
    QuotaAddModal.add({});
  }, []);

  // 废弃
  const handleAbandon = useCallback(() => {
    Modal.confirm({
      title: '请确认是否废弃？',
      onOk: () =>
        new Promise((resolve, reject) => {
          resolve(true);
        }),
    });
  }, [selectedRowKeys]);

  // 导出
  const handleDerive = useCallback(() => {
    if (selectedRowKeys.length > 0) {
      const exportArr: IExport[] = [
        {
          columns,
          data: selectedRowKeys,
          sheetName: 'Sheet1',
        },
      ];
      return exportMultiExcel(exportArr, '定额明细.xlsx');
    }
    // const formValues = WULIFormActions.get(filterFormKey).getValue();
    // const params = {
    //   fetchParams: {
    //     ...cnbApis.structureEmpList,
    //     params: {
    //       ...formValues,
    //     },
    //   },
    //   xlsxName: '人员固定薪资结构管理列表',
    //   configs: {
    //     columns,
    //     extraHandle: handleAssembletableList,
    //   },
    // };
    // excelTask.add(params);
  }, []);

  const actionBtnItems: any[] = useMemo(
    () => [
      {
        key: 'add',
        content: '新增',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleAdd,
        },
      },
      {
        key: 'abandon',
        content: '废弃',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleAbandon,
          disabled: selectedRowKeys.length === 0,
        },
      },
      {
        key: 'export',
        content: '导出',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleDerive,
          disabled: selectedRowKeys.length === 0,
        },
      },
    ],
    [selectedRowKeys]
  );

  const { list: payrollList = [], pagination = {} } = defaultObj(payroll);
  return (
    <Spin spinning={payrollListLoading}>
      <div className="CnbPayProcessWrap">
        <WULIWholeTable
          pagination
          canSelect
          multiSelect
          rowKey="id"
          columns={columns}
          data={payrollList}
          paginationConfig={{
            current: pagination.pageNum,
            pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (pageNum, pageSize) => onFetchNotifyList({ pageNum, pageSize }),
          }}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            onSearch: () => onFetchNotifyList(),
            onClear: () => {
              WULIFormActions.get(filterFormKey)?.reset();
              onFetchNotifyList();
            },
            defaultLayout: {
              col: 6,
              labelCol: 6,
              wrapperCol: 18,
            },
          }}
          action={actionBtnItems}
          onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => {
            setSelectedRowKeys(selectKeys);
            setSelectedRows(_selectedRows);
          }}
          selectedRows={selectedRows}
          showSelectedDetail
          rowNameKey="receiverCode"
          afterCancelSelect={newRows => {
            setSelectedRows(newRows);
            setSelectedRowKeys(newRows.map(k => k.id));
          }}
        />
      </div>
    </Spin>
  );
};
export default QuotaDetail;
