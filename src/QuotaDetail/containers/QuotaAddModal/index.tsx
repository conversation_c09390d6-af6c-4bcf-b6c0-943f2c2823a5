import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Switch } from 'antd';

import { request as fetchApi } from '@utils/http';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';

import useDebounce from '@hooks/useDebounce';

const formKey = 'quotaFormKey';

const formLayout = {
  col: 12,
  labelCol: 10,
  wrapperCol: 14,
};

export interface ModalProps {}

export interface IAppProps extends ModalProps {
  open: boolean;
  title: string;
  onAfterClose: any;
}
const AddModal: React.FC<IAppProps> = props => {
  const { open, title, onAfterClose } = props;

  const [options, setOptions] = useState<any[]>([]);
  const [visible, setVisible] = useState<boolean>(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleSearch = useDebounce((newValue: string) => {
    if (!newValue) return;
    return fetchApi({
      params: {},
      onSuccess: res => {
        setOptions(res.list || []);
      },
    });
  }, 300);

  const formItems: IFormItem[] = useMemo(
    () => [
      {
        labelCol: 8,
        label: '人员',
        type: 'select',
        required: true,
        key: 'employeeId',
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          showSearch: true,
          options: options,
          filterOption: false,
          placeholder: '请选择',
          notFoundContent: null,
          onSearch: handleSearch,
          defaultActiveFirstOption: false,
        },
      },
      {
        type: 'select',
        key: 'type',
        label: '定额类型',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          showSearch: true,
          options: [],
          placeholder: '请选择',
          optionFilterProp: 'label',
        },
      },
      {
        type: 'select',
        key: 'count',
        labelCol: 8,
        label: '定额数量',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          showSearch: true,
          options: options,
          placeholder: '请选择',
          optionFilterProp: 'label',
        },
      },
      {
        type: 'input',
        key: 'unit',
        label: '单位',
        disabled: true,
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'select',
        key: 'year',
        labelCol: 8,
        label: '生成年份',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: [],
        },
      },
      {
        type: 'datePicker',
        key: 'startTime',
        label: '使用开始日期',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: [],
        },
      },
      {
        type: 'datePicker',
        key: 'endTime',
        labelCol: 8,
        label: '使用结束日期',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        configs: {
          options: [],
          allowClear: true,
          placeholder: '请选择',
        },
      },
      {
        type: 'custom',
        key: 'isConvert',
        label: '以调整后的结果参与离职折算',
        required: true,
        col: 24,
        labelCol: 7,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择',
            },
          ],
        },
        render: data => {
          return <Switch checkedChildren="1" unCheckedChildren="0" checked={data.value} onChange={data.onChange} />;
        },
      },
    ],
    [options]
  );
  const handleOk = () => {
    WULIFormActions.get(formKey).validate((result, data) => {
      console.log('daaa===', result, data);
      if (result === 'error') {
        return;
      }
    });
  };

  return (
    <Modal
      width={800}
      title={title}
      open={visible}
      onOk={handleOk}
      afterClose={onAfterClose}
      onCancel={() => setVisible(false)}
    >
      <WULIForm formKey={formKey} formItems={formItems} defaultLayout={formLayout} />
    </Modal>
  );
};

const withAddParams = (params: ModalProps): IAppProps => {
  return {
    ...params,
    title: '新增定额',
    open: true,
    onAfterClose: () => {},
  };
};

const modalFn = (params: any) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
  };
  reactRender(<AddModal {...params} onAfterClose={onAfterClose} />, container);
};

const modal = AddModal as any;

modal.add = function addFn(props: ModalProps) {
  return modalFn(withAddParams(props));
};

export default modal;
