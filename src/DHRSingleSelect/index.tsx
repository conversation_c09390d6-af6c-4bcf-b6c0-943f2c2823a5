import React, { useEffect, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { WULI_MODE } from '@constants/common';
import { request } from '@utils/http';
import { IOriginalProps } from '@src/types';

export interface IAppProps extends IOriginalProps {
  onChange: (hid: string) => void;
  value: string;
  data: any;
}

const SelectTree: React.FC<IAppProps> = props => {
  const [options, setOptions] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const { configs, onChange, value, data } = props;
  const { wuliMode } = configs;
  const isView = wuliMode === WULI_MODE.VIEW;
  const { dictConfig = {}, isEnabled } = configs.config?.baseConfig || {};
  const { searchUrl, searchQueryParams, extraSetValue } = dictConfig;
  const detailData = configs.context.globalContext.pageTools.getDetailEngineProps();
  const conetxtData = detailData?.metaConfig?.contextData || {};
  const extraSetValueMap = useMemo(() => {
    return extraSetValue?.split(';')?.reduce((acc, cur) => {
      const [field, key] = cur.split('=');
      acc[field] = key;
      return acc;
    }, {});
  }, [extraSetValue]);
  const genSearchParams = () => {
    return searchQueryParams?.split(',').reduce((acc, cur) => {
      acc[cur] = conetxtData[cur] || detailData[cur];
      return acc;
    }, {});
  };

  /** 拉取下拉列表 */
  const onFetcList = () => {
    setLoading(true);
    request({
      method: 'get',
      url: searchUrl,
      baseURL: '/apis/common/proxy/lcpGw/tz_api/dhr_api/hcm/dhr-lcp-hcm',
      params: {
        ...genSearchParams(),
      },
    })
      .then(list => {
        const _options = list.map(item => ({
          ...item,
          value: item.code,
          label: item.name,
        }));
        setOptions(_options);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**  */

  const onSelect = (value, option) => {
    const target = options.find(item => item.code === value);
    if (!target) return;

    if (extraSetValueMap) {
      const additionnalInfo = Object.keys(extraSetValueMap).reduce((pre, cur) => {
        // key 为目标表单字段，value 为来源字段
        const _value = target[extraSetValueMap[cur]];
        pre[cur] = _value;
        return pre;
      }, {});
      configs.context?.setFormData(additionnalInfo);
      onChange(value);
    }
  };

  useEffect(() => {
    !isView && onFetcList();
  }, [isView]);

  return (
    <Spin spinning={loading}>
      <Select value={value} style={{ minWidth: '100%' }} allowClear onSelect={onSelect} disabled={isEnabled === '0'}>
        {(options || []).map(item => (
          <Select.Option key={item.code} value={item.code}>
            {item.name}
          </Select.Option>
        ))}
      </Select>
    </Spin>
  );
};

export default SelectTree;
