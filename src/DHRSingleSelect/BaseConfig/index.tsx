import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { searchUrl, searchQueryParams, extraSetValue } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigSearchUrl',
      curFormData?.dictConfigSearchUrl || defFormData?.dictConfigSearchUrl || searchUrl
    );
    formRef?.current?.setFormItem?.(
      'dictConfigSearchQueryParams',
      curFormData?.dictConfigSearchQueryParams || defFormData?.dictConfigSearchQueryParams || searchQueryParams
    );
    formRef?.current?.setFormItem?.(
      'dictConfigExtraSetValue',
      curFormData?.dictConfigExtraSetValue || defFormData?.dictConfigExtraSetValue || extraSetValue
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigSearchUrl',
      label: '请求地址',
      configs: {
        placeholder: '下拉列表的请求地址',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigSearchUrl');
          context?.onConfirm?.('dictConfigSearchUrl', value);
          setDictConfig(formRef, 'searchUrl', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigSearchQueryParams',
      label: '接口参数Key',
      configs: {
        placeholder: '多个用,分割，参数格式：a,b,c',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigSearchQueryParams');
          context?.onConfirm?.('dictConfigSearchQueryParams', value);
          setDictConfig(formRef, 'searchQueryParams', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigExtraSetValue',
      label: '额外赋值【选择项赋值表单字段】',
      configs: {
        placeholder: 'formField1=valueKey1,formField2=valueKey2',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigExtraSetValue');
          context?.onConfirm?.('dictConfigExtraSetValue', value);
          setDictConfig(formRef, 'extraSetValue', value, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
