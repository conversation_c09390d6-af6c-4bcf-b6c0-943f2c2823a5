import React, { useCallback, useEffect, useMemo, useState } from 'react';

import commonApis from '@apis/common';
import { request as fetchApi } from '@utils/http';

import './style.less';

export interface IAppProps {
  pageId: string;
}
const EntryApplication: React.FC<IAppProps> = props => {
  console.log('props===222', props);
  const { pageId } = props;

  const [initValues, setInitValues] = useState<Record<string, any>>({});

  const onFetchFormData = useCallback(values => {
    const params = {
      onlyMain: true,
      keyType: 'CAMEL',
      appId: 'efa37869ee1c4930b434a4c7b1548d46',
      formClassId: '9d550cedeb874a9293a6c6fe6533ac6f',
      ...values,
    };
    fetchApi({
      ...commonApis.formDetailInfo,
      params,
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const newInitValues = res?.mainData || {};
        setInitValues(newInitValues);
      },
    });
  }, []);

  useEffect(() => {
    pageId &&
      onFetchFormData({
        id: pageId,
      });
  }, [pageId]);

  const columns = useMemo(
    () => [
      {
        title: '内部关联关系申报地址',
        key: 'cInternRelationDeclLink',
      },
      {
        title: '外部利益冲突申报地址',
        key: 'cExtConflictDeclLink',
      },
      {
        title: '员工商业行为准则地址',
        key: 'cBusinessConductLink',
      },
    ],
    []
  );
  return (
    <div className="dhr-entry-application-wrap">
      {columns.map(columnItem => (
        <div key={columnItem.key} style={{ marginBottom: 10 }}>
          <label>{columnItem.title}：</label>
          <span>
            {initValues[columnItem.key] ? (
              <a href={initValues[columnItem.key]} target="_blank" rel="noreferrer">
                {initValues[columnItem.key]}
              </a>
            ) : (
              '-'
            )}
          </span>
        </div>
      ))}
    </div>
  );
};

export default EntryApplication;
