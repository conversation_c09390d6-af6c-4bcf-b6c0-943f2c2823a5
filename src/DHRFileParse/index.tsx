import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Upload, Button, message, Spin } from 'antd';
import { useEventAsync } from '@cvte/kylin-hooks';
import { PDFDocument } from 'pdf-lib';
import classnames from 'classnames';
import * as R from 'ramda';
import axios from 'axios';
import Big from 'big.js';

import attachmentApis from '@apis/attachment';
import useAttachment from '@hooks/useAttachment';
import { request as fetchApi } from '@utils/http';
import { isString, fileDimensionMap, isStandardSizePage } from '@utils/tools';

import { UPLOAD_LIST_TYPE, FILE_PARSE_TYPE } from '@constants/common';

import './style.less';

// A4页面尺寸
const pageWidth = 595;
const pageHeight = 842;

export interface IAppProps {
  value: any;
  onChange: (val) => void;
  configs: {
    code: string;
    context?: {
      apiConfigMap?: any;
      getFormData: () => Record<string, any>;
      setFormData: (values: Record<string, any>) => void;
    };
    wuliMode: 'view' | 'edit';
    containerType: 'form' | 'table';
    config: {
      baseConfig: {
        dictConfig: {
          /** 解析类型 */
          parseType: string;
          /** 文件展示形式 - 默认text */
          listType: string;
          /** 是否允许多选文件 - 默认否 */
          multiple: boolean;
          /** 上传控件类型 - 默认按钮 */
          controlType: string;
          /** 文件格式限制列表 - 默认不限制 */
          limitFileTypes: string[];
          /** 文件上限数量 - 默认不限制 */
          maxCount?: number;
          /** 文件大小 - 默认4M */
          fileSize?: number;
          /** 解析内容映射 */
          contentMapFieldKeys?: string;
          /** 附件Id映射 */
          fieldMapKey?: string;
          // pdf保存尺寸 - 默认无
          savePdfDimension: string | undefined;
          /** 图片是否保存为PDF - 默认否 */
          imgSaveFileType: string | undefined;
        };
      };
    };
  };
  data: {
    record: any;
    value: string | undefined;
    options: any[] | undefined;
    optionsMap: Record<string, any>;
  };
  events: {
    onEvent: (params) => any;
  };
  utils: {
    getSubjectEventBus: () => any;
    extra: {
      getValue: (key: string) => any;
      setValue: (key: string, value: Record<string, any>) => void;
    };
  };
}
const DHRFileParse: React.FC<IAppProps> = props => {
  const { configs, onChange, data, events, utils } = props;
  const wuliMode = configs.wuliMode;
  const isView = wuliMode === 'view';
  const { onPreview, onUploadAttachment, onUpdateApiCongiMap } = useAttachment({ manual: true });
  const subjectEventBus = useRef(null);

  const [files, setFiles] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const fileValues = useRef<any[]>([]);
  const eventIds = useRef<Record<string, any>>(null);
  // 标志已经加载过的附件
  const getFilesFlag = useRef<Record<string, any>>({});

  const { onEvent } = events || {};
  const { config, code, context } = configs || {};
  const apiCongiMap = context?.apiConfigMap || {};
  const { getValue, setValue } = utils?.extra || {};
  const { value, options, optionsMap, record } = data || {};

  const dictConfig: any = config?.baseConfig?.dictConfig || {};
  const {
    maxCount,
    fileSize,
    parseType,
    contentMapFieldKeys,
    fieldMapKey,
    idCardKey,
    savePdfDimension,
    imgSaveFileType,
  } = dictConfig;

  const multiple = !!dictConfig.multiple;
  const listType = dictConfig.listType || 'text';
  // 图片是否保存为PDF
  const isImgSaveFileType = imgSaveFileType === '1';
  const limitFileTypes = dictConfig.limitFileTypes || [];
  const controlType = dictConfig.fileControlType || 'button';

  // 更新apiCongiMap 配置
  const apiCongiMapJson = JSON.stringify(apiCongiMap);
  useEffect(() => {
    onUpdateApiCongiMap(apiCongiMap);
  }, [apiCongiMapJson]);

  useEffect(() => {
    // 身份证才需要发布订阅 同步loading
    if (parseType === FILE_PARSE_TYPE.ID_CTF) {
      subjectEventBus.current = utils?.getSubjectEventBus?.();
    }
  }, [parseType]);

  useEffect(() => {
    return () => {
      /** 组件卸载 删除发布订阅 */
      if (eventIds.current) {
        subjectEventBus.current?.removeEvents({
          eventIds: [...Object.values(eventIds.current || {})],
          owner: FILE_PARSE_TYPE.ID_CTF,
        });
      }
    };
  }, []);

  useEffect(() => {
    if (idCardKey && subjectEventBus.current) {
      /** 监听关联字段的变化 */
      eventIds.current = subjectEventBus.current.subscribeEvent({
        owner: FILE_PARSE_TYPE.ID_CTF,
        eventName: idCardKey,
        callback: res => {
          const newLoading = res?.loading;
          setLoading(newLoading);
        },
      });
    }
  }, [idCardKey, parseType]);

  const fieldKeysMap = useMemo(() => {
    const result = {};
    if (contentMapFieldKeys) {
      try {
        const fieldKeyMaps = contentMapFieldKeys.split(/[,，]/g);
        fieldKeyMaps.forEach(fieldKeyMap => {
          const [updateFieldKey, contentFieldKey] = fieldKeyMap.split('=');
          result[(updateFieldKey || '').trim()] = (contentFieldKey || '').trim();
        });
      } catch (error) {
        console.log(contentMapFieldKeys, ':', '内容字段映射解析失败：', error);
      }
    }
    return result;
  }, [contentMapFieldKeys]);

  const formatValue = useCallback(value => {
    let _value;
    if (isString(value)) {
      if (value === '') {
        _value = [];
      } else {
        _value = (value as string).split(',');
      }
    } else {
      _value = (value || []).concat();
    }
    // 去除重复
    return Array.from(new Set(_value)) as string[];
  }, []);

  const updateFilesByValue = useEventAsync(async () => {
    // 如果相同则不往下执行 - 则无需翻译
    const isValueEquals = R.equals(fileValues.current, formatValue(value));
    if (isValueEquals) {
      return;
    }
    const { files: _files, getFilesFlag: _getFilesFlag } = (await onEvent?.({
      type: 'getCache',
      attrCode: code,
      record,
    })) ?? {
      files,
      getFilesFlag: getFilesFlag.current,
    };

    fileValues.current = formatValue(value);
    const getOpt = (_optionsMap: Record<string, any>) => {
      return fileValues.current.reduce(
        (sum, cur) => {
          const item = _optionsMap?.[cur as string];
          if (isString(cur) && item) {
            sum.opt.push(item);
            sum.map[cur as string] = item;
            return sum;
          }
          return sum;
        },
        { opt: [], map: {} }
      );
    };

    if (!_files?.length) {
      const { opt: _opt, map: _map } = getOpt(optionsMap);
      await onEvent?.({ type: 'setCache', data: { files: _opt, getFilesFlag: _map }, attrCode: code, record });
      getFilesFlag.current = _map;
      setFiles(_opt);
      return;
    }
    const __getFilesFlag = { ...(_getFilesFlag || {}), ...(optionsMap || {}) };
    const { opt: _opt, map } = getOpt(__getFilesFlag);

    await onEvent?.({ type: 'setCache', data: { files: _opt, getFilesFlag: map }, attrCode: code, record });
    getFilesFlag.current = map;
    setFiles(_opt);
  });

  useEffect(() => {
    updateFilesByValue();
  }, [value, options, optionsMap]);

  const UploadComponent = useCallback(() => {
    if (listType === UPLOAD_LIST_TYPE.PICTURE_CARD) {
      return (
        <Spin spinning={loading} tip="解析中..." size="small">
          <div>
            <PlusOutlined />
            <div style={{ marginTop: 8, fontSize: '12px' }}>拖拽或点击上传</div>
          </div>
        </Spin>
      );
    }

    if (controlType === 'button') {
      return (
        <Spin spinning={loading} tip="解析中..." size="small">
          <Button
            size="middle"
            style={{ width: '100%' }}
            icon={!loading && <UploadOutlined style={{ fontSize: '14px' }} />}
          >
            拖拽或点击上传
          </Button>
        </Spin>
      );
    }
    return (
      <Spin spinning={loading} tip="解析中..." size="small">
        <div className="dhr-custom-upload-card">
          <div>
            <PlusOutlined />
            <div className="lcp-margin-top-8" style={{ fontSize: '12px' }}>
              拖拽或点击上传
            </div>
          </div>
        </div>
      </Spin>
    );
  }, [listType, controlType, loading]);

  //
  const fileLimitConfig = useMemo(() => {
    if (limitFileTypes.length > 0) {
      const fileRegStr = limitFileTypes.join('|');
      const fileReg = new RegExp(`\\.(${fileRegStr})$`, 'i');
      return {
        reg: fileReg,
        tip: fileRegStr,
      };
    }

    return {
      reg: undefined,
      tip: undefined,
    };
  }, [limitFileTypes.length]);

  // 计算文件字节大小
  const formatFileSize = useCallback(bytes => {
    const b = new Big(bytes);
    let result;
    if (b.lt(1024)) {
      result = b.toString() + 'B'; // 字节
    } else if (b.lt(1024 ** 2)) {
      result = b.div(1024).toString() + ' KB'; // 千字节
    } else if (b.lt(1024 ** 3)) {
      result = b.div(1024 ** 2).toString() + 'MB'; // 兆字节
    } else {
      result = b.div(1024 ** 3).toString() + 'GB'; // 吉字节
    }
    // 如果结果是整数，则移除小数点
    return result.replace(/(\.0+)(?!\S)/, '');
  }, []);

  // 上传前
  const handleBeforeUpload = useCallback(
    async file => {
      // 文件格式校验
      if (fileLimitConfig.reg && !fileLimitConfig.reg?.test(file.name)) {
        message.error(`仅支持${fileLimitConfig.tip}格式的文件`);
        file.status = 'error';
        return Promise.reject(false);
      }

      // 文件大小校验
      const limitSize = fileSize ? fileSize : 4 * 1024 * 1024;
      if (file.size > limitSize) {
        const limitSizeText = formatFileSize(limitSize);
        message.error(`文件大小不可超过${limitSizeText}`);
        file.status = 'error';
        return Promise.reject(false);
      }
      // 如果是图片类型 需要将图片转为pdf
      if (file.type.includes('image') && isImgSaveFileType) {
        try {
          // 设置标准尺寸，默认为A4
          const standard = fileDimensionMap[savePdfDimension] || fileDimensionMap.A4;
          const { width: SAVE_WIDTH, height: SAVE_HEIGHT } = standard;

          const arrayBuffer = await file.arrayBuffer();
          const pdfDoc = await PDFDocument.create();
          const page = pdfDoc.addPage([SAVE_WIDTH, SAVE_HEIGHT]);

          // 根据图片类型嵌入
          let image;
          const extension = file.type.split('/').pop().toLowerCase();
          if (['jpg', 'jpeg'].includes(extension)) {
            image = await pdfDoc.embedJpg(arrayBuffer);
          } else if (['png'].includes(extension)) {
            image = await pdfDoc.embedPng(arrayBuffer);
          } else {
            message.error(`不支持的图像格式: ${extension}，仅支持jpg、jpeg和png`);
            return Promise.reject(false);
          }

          // 计算保持图片原始比例的尺寸
          const imgWidth = image.width;
          const imgHeight = image.height;

          // 计算缩放比例以使图像适合页面（减去边距）
          const margin = 40; // 页边距
          const maxWidth = SAVE_WIDTH - margin * 2;
          const maxHeight = SAVE_HEIGHT - margin * 2;

          let scale = 1;
          let scaledWidth = imgWidth;
          let scaledHeight = imgHeight;

          // 判断图片是否需要缩放
          if (imgWidth > maxWidth || imgHeight > maxHeight) {
            // 图片太大，需要缩小
            scale = Math.min(maxWidth / imgWidth, maxHeight / imgHeight);
            scaledWidth = imgWidth * scale;
            scaledHeight = imgHeight * scale;
          } else if (imgWidth < maxWidth / 4 && imgHeight < maxHeight / 4) {
            // 图片太小（小于页面的1/4），适当放大但不超过内容区域的一半
            scale = Math.min(maxWidth / 2 / imgWidth, maxHeight / 2 / imgHeight);
            scaledWidth = imgWidth * scale;
            scaledHeight = imgHeight * scale;
          }

          // 在页面中心绘制图像
          page.drawImage(image, {
            x: (SAVE_WIDTH - scaledWidth) / 2,
            y: (SAVE_HEIGHT - scaledHeight) / 2,
            width: scaledWidth,
            height: scaledHeight,
          });

          const pdfBytes = await pdfDoc.save();
          const pdfFile: any = new File([pdfBytes], `${file.name.split('.')[0]}.pdf`, { type: 'application/pdf' });
          pdfFile.uid = file.uid; // 保留原始 uid

          return Promise.resolve(pdfFile);
        } catch (error) {
          console.error('图片转PDF失败:', error);
          message.error('图片转PDF失败，请重新上传');
          return Promise.reject(false);
        }
      }

      if (file.type.includes('pdf') && !!savePdfDimension) {
        try {
          // 提前检查配置和文件有效性
          const standard = fileDimensionMap[savePdfDimension];
          if (!standard) {
            return Promise.resolve(file); // 如果没有配置标准尺寸，直接返回原文件
          }

          const { width: SAVE_WIDTH, height: SAVE_HEIGHT } = standard;

          // 使用ArrayBuffer只读取一次文件内容
          const arrayBuffer = await file.arrayBuffer();
          const pdfDoc = await PDFDocument.load(arrayBuffer);
          const pages = pdfDoc.getPages();

          if (pages.length === 0) {
            return Promise.resolve(file); // 空PDF直接返回
          }

          // 快速判断：检查PDF页面尺寸是否需要调整
          let needsResize = false;
          for (let i = 0; i < pages.length; i++) {
            const { width, height } = pages[i].getSize();
            if (!isStandardSizePage(width, height, savePdfDimension)) {
              needsResize = true;
              break; // 只要有一页不匹配就需要调整，立即退出循环
            }
          }

          // 如果所有页面都符合尺寸要求，直接返回原文件
          if (!needsResize) {
            return Promise.resolve(file);
          }

          // 仅在需要调整尺寸时创建新的PDF文档
          const newPdfDoc = await PDFDocument.create();

          // 复用变量减少内存分配
          let embeddedPage, scale, scaledWidth, scaledHeight;

          for (const page of pages) {
            const { width, height } = page.getSize();
            // 创建标准尺寸的页面
            const newPage = newPdfDoc.addPage([SAVE_WIDTH, SAVE_HEIGHT]);

            // 判断是否需要缩放
            if (width > SAVE_WIDTH || height > SAVE_HEIGHT) {
              // 计算缩放比例 - 取最小缩放比例以确保内容完全可见
              scale = Math.min(SAVE_WIDTH / width, SAVE_HEIGHT / height);
              scaledWidth = Number((width * scale).toFixed(3));
              scaledHeight = Number((height * scale).toFixed(3));

              // 嵌入并居中绘制页面
              embeddedPage = await newPdfDoc.embedPage(page);
              newPage.drawPage(embeddedPage, {
                x: (SAVE_WIDTH - scaledWidth) / 2,
                y: (SAVE_HEIGHT - scaledHeight) / 2,
                width: scaledWidth,
                height: scaledHeight,
              });
            } else {
              // 内容小于目标尺寸，只需居中绘制
              embeddedPage = await newPdfDoc.embedPage(page);
              newPage.drawPage(embeddedPage, {
                x: (SAVE_WIDTH - width) / 2,
                y: (SAVE_HEIGHT - height) / 2,
              });
            }
          }

          // 优化保存逻辑
          const newPdfBytes = await newPdfDoc.save();
          const pdfFile: any = new File([newPdfBytes], file.name, { type: 'application/pdf' });
          pdfFile.uid = file.uid; // 保留原始 uid
          return Promise.resolve(pdfFile);
        } catch (error) {
          console.error('PDF处理失败:', error);
          message.error('PDF处理失败，请重新上传');
          return Promise.reject(false);
        }
      }

      return Promise.resolve(file);
    },
    [fileLimitConfig.reg, fileSize, savePdfDimension, isImgSaveFileType]
  );

  // 预览附件
  const handlePreview = useCallback(
    file => {
      onPreview({
        fileId: file?.fileId,
        fileName: file.name,
        fileType: file.type,
      });
    },
    [apiCongiMap]
  );

  // 下载附件url prefix
  const downUrlPrefix = useMemo(() => {
    if (apiCongiMap.download) {
      return apiCongiMap.download?.url;
    }
    return '';
  }, [apiCongiMap.download]);

  /** 解析附件内容 */
  const onFetchParseContent = useCallback(
    data =>
      new Promise((resolve, reject) => {
        setLoading(true);
        fetchApi({
          ...attachmentApis.attachmentAnalysis,
          data,
          onSuccess: res => {
            resolve(res);
          },
          onError: reject,
        }).finally(() => {
          setLoading(false);
          if (subjectEventBus.current) {
            subjectEventBus.current?.publishEvent?.({
              owner: FILE_PARSE_TYPE.ID_CTF,
              eventName: code,
              data: {
                loading: false,
              },
            });
          }
        });
      }),
    []
  );

  const isImageFile = useCallback(file => {
    return file.type.includes('image');
  }, []);

  /** 更新formValue的字段 */
  const handleUpdateFormData = useCallback(
    ({ data, fileId }) => {
      if (Object.keys(fieldKeysMap).length > 0) {
        const _data = data || {};
        const updateFormValues = {};
        Object.keys(fieldKeysMap).forEach(key => {
          updateFormValues[key] = _data[fieldKeysMap[key]];
        });
        if (fieldMapKey) {
          updateFormValues[fieldMapKey] = fileId;
        }
        context?.setFormData(updateFormValues);
      }
    },
    [fieldKeysMap, fieldMapKey]
  );

  /** 身份证解析 */
  const handleIdCtf = useCallback(
    async newFiles => {
      /** 上传正反面进行解析 */
      const [file] = newFiles;
      const formValues = context?.getFormData();
      const associatedField = formValues[idCardKey];
      const { fileId, name, type } = file;
      if (!associatedField) {
        // 缓存关联字段最新的附件信息
        return setValue(code, { fileId, name, type });
      }

      try {
        subjectEventBus.current?.publishEvent?.({
          owner: FILE_PARSE_TYPE.ID_CTF,
          eventName: code,
          data: {
            loading: true,
          },
        });
        setLoading(true);
        const pdfDoc = await PDFDocument.create();
        const downUrl = `${apiCongiMap?.download?.url}/${associatedField}`;
        const response = await axios.get(downUrl, { responseType: 'blob' });
        const associatedDetail = getValue(idCardKey);
        const blob = response?.data;
        const associatedFile = new File([blob], associatedDetail.name, { type: associatedDetail.type });
        const newFiles = [file, associatedFile];
        // 收集所有要渲染的内容
        const contentsToDraw = [];

        for (const fileItem of newFiles) {
          const file = fileItem?.originFileObj || fileItem;
          const fileBytes = await file.arrayBuffer();
          if (isImageFile(file)) {
            let img: any;
            const extension = file.type.split('/').pop().toLowerCase();
            if (['jpg', 'jpeg'].includes(extension)) {
              img = await pdfDoc.embedJpg(fileBytes);
            } else if (['png'].includes(extension)) {
              img = await pdfDoc.embedPng(fileBytes);
            } else {
              message.error(`不支持的图像格式: ${extension}`);
              return;
            }
            contentsToDraw.push({ type: 'image', content: img });
          } else {
            // 对于PDF文件，提取第一页作为图像
            try {
              const existingPdfDoc = await PDFDocument.load(fileBytes);
              if (existingPdfDoc.getPageCount() > 0) {
                // 通过SVG渲染或其他方式，我们可以将PDF页面转为图像
                // 这里我们简单地记录PDF页面信息，稍后会处理
                contentsToDraw.push({
                  type: 'pdf',
                  content: {
                    doc: existingPdfDoc,
                    pageIndices: existingPdfDoc.getPageIndices(),
                  },
                });
              }
            } catch (error) {
              message.error(`PDF处理错误: ${error.message}`);
              return;
            }
          }
        }

        // 计算布局参数
        const padding = 20;
        const margin = 50; // 页边距
        const contentWidth = pageWidth - 2 * margin;
        // 缩小比例 (80% = 原尺寸的80%)
        const scaleFactor = 0.8;
        // 当前页面和位置
        let currentPage = pdfDoc.addPage([pageWidth, pageHeight]);
        // 当前绘制位置
        let yOffset = pageHeight - margin;

        // 添加新页面并重置位置的函数
        const addNewPage = () => {
          currentPage = pdfDoc.addPage([pageWidth, pageHeight]);
          yOffset = pageHeight - margin;
          return currentPage;
        };

        for (const item of contentsToDraw) {
          if (item.type === 'image') {
            const img = item.content;

            // 计算图像在页面上的尺寸，确保不超过页面宽度
            let imgWidth = img.width;
            let imgHeight = img.height;

            if (imgWidth > contentWidth) {
              const additionalScale = contentWidth / imgWidth;
              imgWidth = contentWidth;
              imgHeight = imgHeight * additionalScale;
            }

            // 检查是否需要分页
            if (yOffset - imgHeight < margin) {
              // 如果当前页放不下，创建新页面
              currentPage = addNewPage();
            }

            // 绘制图像
            yOffset -= imgHeight;
            currentPage.drawImage(img, {
              x: margin,
              y: yOffset,
              width: imgWidth,
              height: imgHeight,
            });

            yOffset -= padding;
          } else if (item.type === 'pdf') {
            // 对于PDF文件
            try {
              // 从源PDF复制页面
              const { doc, pageIndices, fileName } = item.content;

              for (const pageIndex of pageIndices) {
                // 获取源页面尺寸
                const sourcePage = doc.getPage(pageIndex);
                const { width: srcWidth, height: srcHeight } = sourcePage.getSize();

                // 计算缩放后的尺寸 (应用80%缩放因子)
                let displayWidth = srcWidth * scaleFactor;
                let displayHeight = srcHeight * scaleFactor;

                // 如果缩放后的宽度仍然超过内容区域，进一步缩小
                if (displayWidth > contentWidth) {
                  const additionalScale = contentWidth / displayWidth;
                  displayWidth = contentWidth;
                  displayHeight = displayHeight * additionalScale;
                }

                // 检查是否需要分页
                if (yOffset - displayHeight < margin) {
                  // 如果当前页放不下，创建新页面
                  currentPage = addNewPage();
                }

                // 复制页面到新文档
                const [embeddedPage] = await pdfDoc.copyPages(doc, [pageIndex]);

                // 尝试将其作为表单XObject嵌入
                let form;
                try {
                  form = await pdfDoc.embedPage(embeddedPage);
                } catch (embedError) {
                  // 如果嵌入失败，直接终止并显示具体错误
                  message.error(`PDF嵌入失败 (${fileName}): ${embedError.message}`);
                  console.error(`PDF嵌入失败 (${fileName}):`, embedError);
                  return;
                }
                // 计算绘制位置
                yOffset -= displayHeight;

                // 绘制嵌入的页面
                currentPage.drawPage(form, {
                  x: margin,
                  y: yOffset,
                  width: displayWidth,
                  height: displayHeight,
                });

                yOffset -= padding;
              }
            } catch (error) {
              // 处理整体PDF处理错误，直接终止
              message.error(`PDF处理失败 (${item.content.fileName}): ${error.message}`);
              console.error(`PDF处理失败 (${item.content.fileName}):`, error);
              return;
            }
          }
        }
        const mergedPdfBytes = await pdfDoc.save();
        const pdfFile = new File([mergedPdfBytes], '身份证附件.pdf', { type: 'application/pdf' });
        const uploadData: any = await onUploadAttachment(pdfFile, { configs });
        const fieldId = uploadData?.fileIds?.[0] || '';
        onFetchParseContent({ fileId: fieldId, fileType: FILE_PARSE_TYPE.ID_CTF }).then(res => {
          handleUpdateFormData({ data: res, fileId: fieldId });
        });
      } catch (error) {
        setLoading(false);
        subjectEventBus.current?.publishEvent?.({
          owner: FILE_PARSE_TYPE.ID_CTF,
          eventName: code,
          data: {
            loading: false,
          },
        });
        message.warning('身份证上传解析失败，请重新上传');
        console.log('身份证合并/解析失败：', error);
      }
    },
    [parseType, fieldKeysMap, fieldMapKey, apiCongiMap, configs]
  );

  /** 离职证明 */
  const handleExitCtf = useCallback(
    newFiles => {
      const [{ fileId }] = newFiles;
      onFetchParseContent({ fileId, fileType: FILE_PARSE_TYPE.EXIT_CTF }).then(res => {
        handleUpdateFormData({ data: res, fileId });
      });
    },
    [parseType, fieldKeysMap, fieldMapKey]
  );
  /** 学历 */
  const handleEduCtf = useCallback(
    newFiles => {
      const [{ fileId }] = newFiles;
      onFetchParseContent({ fileId, fileType: FILE_PARSE_TYPE.EDU_CTF }).then(res => {
        handleUpdateFormData({ data: res, fileId });
      });
    },
    [parseType, fieldKeysMap, fieldMapKey]
  );

  /** 上传结束开始解析内容 */
  const handleParseContent = useCallback(
    fileList => {
      const parseTypeFnMap = {
        [FILE_PARSE_TYPE.ID_CTF]: handleIdCtf,
        [FILE_PARSE_TYPE.EXIT_CTF]: handleExitCtf,
        [FILE_PARSE_TYPE.EDU_CTF]: handleEduCtf,
      };
      parseTypeFnMap[parseType](fileList);
    },
    [parseType, fieldKeysMap, fieldMapKey, idCardKey, apiCongiMap, configs]
  );

  const handleChange = useCallback(
    info => {
      const { status } = info.file;
      if (status !== 'uploading') {
        const fileIds = [];
        info.fileList.map(file => {
          if (file.response) {
            if (file.response.status !== '0') file.status = 'error';
            const fileId = file.response?.data?.result?.fileIds?.[0];
            file.fileId = fileId;
            if (downUrlPrefix) {
              file.url = `${downUrlPrefix}/${fileId}`;
            }
            getFilesFlag.current[fileId] = file;
          }
          file.fileId && fileIds.push(file.fileId);
          return file;
        });
        const updateIds = fileIds.length > 0 ? fileIds.join(',') : undefined;
        onEvent?.({
          record,
          type: 'setCache',
          attrCode: code,
          data: { files: info.fileList, getFilesFlag: getFilesFlag.current },
        });
        fileValues.current = fileIds;
        onChange(updateIds);
      }
      if (status === 'done') {
        /** 回调 */
        handleParseContent(info.fileList.slice());
      }
      setFiles(info.fileList);
    },
    [downUrlPrefix, fieldKeysMap, fieldMapKey, apiCongiMap, configs]
  );

  /** 自定义上传 */
  const handleCustomRequest = useCallback(
    async ({ file, onSuccess, onError, onProgress }) => {
      try {
        const resp = await onUploadAttachment(
          file,
          { configs },
          {
            onUploadProgress: progressEvent => {
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress({ percent });
            },
          }
        );
        onSuccess({
          data: {
            result: resp,
          },
          status: '0',
          message: 'success',
        });
      } catch (error) {
        onError(error);
      }
    },
    [configs]
  );

  return (
    <div className="dhr-custom-upload">
      <Upload
        name="files"
        fileList={files}
        multiple={multiple}
        listType={listType}
        maxCount={maxCount}
        onChange={handleChange}
        onPreview={handlePreview}
        disabled={loading || isView}
        // action={apiCongiMap?.upload?.url}
        beforeUpload={handleBeforeUpload}
        customRequest={handleCustomRequest}
        showUploadList={{
          showPreviewIcon: true,
          showDownloadIcon: true,
          showRemoveIcon: !isView && !loading,
        }}
        className={classnames({
          'dhr-custom-button-upload': controlType === 'button' && listType !== UPLOAD_LIST_TYPE.PICTURE_CARD,
        })}
        data={{
          categoryId: `/dhr/common/${apiCongiMap?.upload?.params?.categoryId || ''}`,
        }}
      >
        {!isView && <UploadComponent />}
      </Upload>
    </div>
  );
};

export default DHRFileParse;
