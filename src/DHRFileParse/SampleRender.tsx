import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import React, { useCallback } from 'react';
import { Button } from 'antd';

import { UPLOAD_LIST_TYPE } from '@constants/common';

import { IOriginalProps } from '../types';

import './style.less';

interface IAppProps extends IOriginalProps {
  value: any;
  onChange: (val) => void;
  configs: {
    wuliMode: 'view' | 'edit';
    containerType: 'form' | 'table';
    config: {
      baseConfig: {
        dictConfig: {
          /** 上传控件类型 - 默认按钮 */
          controlType: string;
          /** 文件展示形式 - 默认text */
          listType: string;
        };
      };
    };
  };
}
const BaseInfo: React.FC<IAppProps> = props => {
  const { configs } = props;
  const dictConfig: any = configs?.config?.baseConfig?.dictConfig || {};
  const controlType = dictConfig.fileControlType || 'button';
  const listType = dictConfig.listType || 'text';

  const UploadComponent = useCallback(() => {
    if (listType === UPLOAD_LIST_TYPE.PICTURE_CARD) {
      return (
        <div className="dhr-custom-upload-sample-render">
          <div>
            <PlusOutlined />
            <div>上传</div>
          </div>
        </div>
      );
    }

    if (controlType === 'button') {
      return (
        <Button style={{ width: '100%' }} icon={<UploadOutlined />}>
          拖拽或点击上传
        </Button>
      );
    }
    return (
      <div className="dhr-custom-upload-sample-render">
        <div>
          <PlusOutlined />
          <div>上传</div>
        </div>
      </div>
    );
  }, [listType, controlType]);

  return <UploadComponent />;
};

export default BaseInfo;
