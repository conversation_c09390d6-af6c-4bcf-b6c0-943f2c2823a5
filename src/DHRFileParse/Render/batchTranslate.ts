import { Obj2Arr, map2Obj, formatFile2Image } from './util';
import { request } from '@utils/http';

const batchTranslate = async configs => {
  const { apiConfigMap: oldApiConfigMap, fields, configs: _configs, utils } = configs;

  const { value, apiConfigMap: _apiConfigMap } = _configs || {};
  const apiConfigMap = _apiConfigMap || oldApiConfigMap;
  const { fetch } = utils || {};
  const ids = Obj2Arr(value || fields);
  if (!ids?.length) {
    return {
      fields: {},
    };
  }
  const { success, data = [] } = await (fetch || request)({
    ...apiConfigMap.getInfo,
    data: { id: ids },
  });
  if (success) {
    const map =
      data?.reduce((sum, cur) => {
        const { id, fileSize } = cur;
        sum[id] = formatFile2Image({ ...cur, uid: id, size: fileSize }, apiConfigMap);
        return sum;
      }, {}) ?? {};
    const { options, map: fieldsMap } = map2Obj(value || fields, map);
    if (_configs && utils) {
      return {
        options,
        optionsMap: fieldsMap,
      };
    }
    return {
      fields: options,
      map: fieldsMap,
    };
  }
  return {
    fields: {},
  };
};

export default batchTranslate;
