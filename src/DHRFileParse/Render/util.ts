const hasValueOfObject = (obj: any): obj is Record<string, any> => {
  const protoStr = Object.prototype.toString.call(obj) as string;
  if (protoStr === '[object Object]' && Object.keys(obj)?.length) {
    return true;
  }
  return false;
};

const isEmpty = (value: any) => {
  if (value === undefined || value === null) {
    return true;
  }
  if (typeof value === 'string') {
    return !value;
  }
  if (Array.isArray(value)) {
    return !value?.length;
  }
  if (typeof value === 'object') {
    return !hasValueOfObject(value);
  }
  return false;
};

export const Obj2Arr = obj => {
  return Object.keys(obj || {}).reduce((sum, cur) => {
    const content = obj[cur];
    if (isEmpty(content)) {
      return sum;
    }
    if (Array.isArray(content)) {
      sum.push(
        ...content
          .filter(item => !isEmpty(item))
          .reduce((_sum, _cur) => {
            if (_cur.includes(',')) {
              _sum.push(..._cur.split(','));
              return _sum;
            }
            Array.isArray(_cur) ? _sum.push(..._cur) : _sum.push(_cur);
            return _sum;
          }, [])
      );
      return sum;
    }
    if (typeof content === 'string') {
      if (content.includes(',')) {
        sum.push(...content.split(','));
        return sum;
      }
      sum.push(content);
      return sum;
    }
    sum.push(...Obj2Arr(content));
    return sum;
  }, []);
};

/**
 * 把map对应的值放入对象中
 */
export const map2Obj = (obj, map) => {
  return Object.keys(obj || {}).reduce(
    (sum, cur) => {
      const content = obj[cur];
      sum.options[cur] = [];
      sum.map[cur] = {};
      if (isEmpty(content)) {
        return sum;
      }
      if (Array.isArray(content)) {
        content
          .filter(item => !isEmpty(item))
          .reduce((_sum, _cur) => {
            if (_cur.includes(',')) {
              _sum.push(..._cur.split(','));
              return _sum;
            }
            Array.isArray(_cur) ? _sum.push(..._cur) : _sum.push(_cur);
            return _sum;
          }, [])
          .filter(key => map[key])
          .forEach(key => {
            sum.options[cur].push(map[key]);
            sum.map[cur][key] = map[key];
          });
        return sum;
      }
      if (typeof content === 'string') {
        if (content.includes(',')) {
          const _map = {};
          const options = content
            .split(',')
            .filter(code => map[code])
            .map(code => {
              _map[code] = map[code];
              return map[code];
            });
          sum.options[cur] = options;
          sum.map[cur] = _map;
          // sum.push(...content.split(','));
          return sum;
        }
        const option = map[content];
        sum.options[cur] = option ? [option] : [];
        sum.map[cur] = option ? { [content]: option } : {};
        return sum;
      }
      const { options, map: _map } = map2Obj(content, map);
      sum.options[cur] = options;
      sum.map[cur] = _map;
      return sum;
    },
    { options: {}, map: {} }
  );
};

const formatUrl = (url: string, file) => {
  return `${url}/${file.uid || file.id}`;
};

export const PICTURES_MIME = [
  'xbm',
  'tif',
  'pjp',
  'svgz',
  'jpg',
  'jpeg',
  'ico',
  'tiff',
  'gif',
  'svg',
  'jfif',
  'webp',
  'png',
  'bmp',
  'pjpeg',
  'avif',
];

export const formatFile2Image = (file, apiConfigMap, _formatUrl = formatUrl) => {
  const { name, type, id, fileSize } = file;
  const url = apiConfigMap.download?.url ?? '';
  let thumbUrl;
  if (PICTURES_MIME.includes(`${type}`.toLocaleLowerCase())) {
    thumbUrl = _formatUrl(url, file);
  }
  const result = {
    uid: id as string,
    size: fileSize as number,
    name,
    type,
    url: _formatUrl(url, file),
    status: 'done',
    thumbUrl,
    key: id,
    value: id,
    fileId: id,
  };
  return result;
};
