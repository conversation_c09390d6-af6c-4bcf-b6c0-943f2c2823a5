import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { UPLOAD_LIST_TYPE, UPLOAD_LIMIT_FILE_TYPE, FILE_PARSE_TYPE, FILE_PARSE_TYPE_NAME } from '@constants/common';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

const fileTypeOptions = Object.keys(UPLOAD_LIMIT_FILE_TYPE).map(key => ({
  label: key,
  value: UPLOAD_LIMIT_FILE_TYPE[key],
  key: UPLOAD_LIMIT_FILE_TYPE[key],
}));

const parseOptions = Object.keys(FILE_PARSE_TYPE).map(key => ({
  label: FILE_PARSE_TYPE_NAME[key],
  value: FILE_PARSE_TYPE[key],
  key: FILE_PARSE_TYPE[key],
}));

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  // 更新类型
  const handleUpdateFieldType = (key, type, otherHideKeys: string[] = [], otherConfig: any = {}) => {
    formRef?.current?.diffFormItem?.(
      key
        ? [
            {
              key,
              type,
              ...otherConfig,
            },
          ]
        : [],
      [...otherHideKeys]
    );
  };

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const {
      maxCount,
      listType,
      fileSize,
      multiple,
      parseType,
      idCardKey,
      fieldMapKey,
      limitFileTypes,
      fileControlType,
      savePdfDimension,
      imgSaveFileType,
      contentMapFieldKeys,
    } = dictConfigByProps || {};

    const curFormData = formRef?.current?.getFormItem?.();
    const defFormData = formRef?.current?.getInitFormData?.();

    const idCardKeyVal =
      (curFormData?.dictConfigParseType || defFormData?.dictConfigParseType || parseType) === FILE_PARSE_TYPE.ID_CTF;

    idCardKeyVal
      ? handleUpdateFieldType('dictConfigIDCardKey', 'input', [])
      : handleUpdateFieldType(undefined, undefined, ['dictConfigIDCardKey']);
    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigParseType',
      curFormData?.dictConfigParseType || defFormData?.dictConfigParseType || parseType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFileControlType',
      curFormData?.dictConfigFileControlType || defFormData?.dictConfigFileControlType || fileControlType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigListType',
      curFormData?.dictConfigListType || defFormData?.dictConfigListType || listType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigMaxCount',
      curFormData?.dictConfigMaxCount || defFormData?.dictConfigMaxCount || maxCount
    );
    formRef?.current?.setFormItem?.(
      'dictConfigMultiple',
      curFormData?.dictConfigMultiple || defFormData?.dictConfigMultiple || multiple
    );
    formRef?.current?.setFormItem?.(
      'dictConfigLimitFileTypes',
      curFormData?.dictConfigLimitFileTypes || defFormData?.dictConfigLimitFileTypes || limitFileTypes
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFileSize',
      curFormData?.dictConfigFileSize || defFormData?.dictConfigFileSize || fileSize
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFieldMapKey',
      curFormData?.dictConfigFieldMapKey || defFormData?.dictConfigFieldMapKey || fieldMapKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigContentMapFieldKeys',
      curFormData?.dictConfigContentMapFieldKeys || defFormData?.dictConfigContentMapFieldKeys || contentMapFieldKeys
    );
    formRef?.current?.setFormItem?.(
      'dictConfigIDCardKey',
      curFormData?.dictConfigIDCardKey || defFormData?.dictConfigIDCardKey || idCardKey
    );
    formRef?.current?.setFormItem?.(
      'dictConfigSavePdfDimension',
      curFormData?.dictConfigSavePdfDimension || defFormData?.dictConfigSavePdfDimension || savePdfDimension
    );
    formRef?.current?.setFormItem?.(
      'dictConfigImgSaveFileType',
      curFormData?.dictConfigImgSaveFileType || defFormData?.dictConfigImgSaveFileType || imgSaveFileType
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'select',
      key: 'dictConfigLimitFileTypes',
      label: '上传文件格式',
      required: true,
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请填写',
          },
        ],
      },
      configs: {
        mode: 'multiple',
        allowClear: true,
        placeholder: '请选择',
        options: fileTypeOptions,
        onChange: val => {
          context?.onConfirm?.('dictConfigLimitFileTypes', val);
          setDictConfig(formRef, 'limitFileTypes', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigParseType',
      label: '解析类型',
      required: true,
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请填写',
          },
        ],
      },
      configs: {
        allowClear: true,
        placeholder: '请选择',
        options: parseOptions,
        onChange: val => {
          if (val === FILE_PARSE_TYPE.ID_CTF) {
            handleUpdateFieldType('dictConfigIDCardKey', 'input', []);
          } else {
            handleUpdateFieldType(undefined, undefined, ['dictConfigIDCardKey']);
          }
          context?.onConfirm?.('dictConfigParseType', val);
          setDictConfig(formRef, 'parseType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'hidecomp',
      key: 'dictConfigIDCardKey',
      label: '身份证关联字段',
      configs: {
        placeholder: '请输入身份证关联字段',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigIDCardKey');
          context?.onConfirm?.('dictConfigIDCardKey', value);
          setDictConfig(formRef, 'idCardKey', value, {
            context,
          });
        },
      },
    },
    {
      type: 'textarea',
      key: 'dictConfigContentMapFieldKeys',
      label: '解析内容映射字段',
      configs: {
        autoSize: {
          minRows: 5,
          maxRows: 8,
        },
        placeholder: '格式：TEXT_47B=a,DHR_PERSON_4E=b...',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigContentMapFieldKeys');
          context?.onConfirm?.('dictConfigContentMapFieldKeys', value);
          setDictConfig(formRef, 'contentMapFieldKeys', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigFieldMapKey',
      label: '附件ID映射',
      configs: {
        placeholder: '请输入更新字段',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigFieldMapKey');
          context?.onConfirm?.('dictConfigFieldMapKey', value);
          setDictConfig(formRef, 'fieldMapKey', value, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigImgSaveFileType',
      label: '图片是否保存为PDF',
      configs: {
        allowClear: true,
        placeholder: '请选择，默认为否',
        options: [
          {
            label: '是',
            key: '1',
          },
          {
            label: '否',
            key: '0',
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigImgSaveFileType', val);
          setDictConfig(formRef, 'imgSaveFileType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigSavePdfDimension',
      label: '保存PDF尺寸',
      configs: {
        allowClear: true,
        placeholder: '请选择',
        options: [
          {
            label: 'A4',
            key: 'A4',
          },
          {
            label: 'A3',
            key: 'A3',
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigSavePdfDimension', val);
          setDictConfig(formRef, 'savePdfDimension', val, {
            context,
          });
        },
      },
    },
    {
      type: 'inputNumber',
      key: 'dictConfigFileSize',
      label: '文件大小',
      configs: {
        placeholder: '请输入限制文件上传大小',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigFileSize');
          context?.onConfirm?.('dictConfigFileSize', value);
          setDictConfig(formRef, 'fileSize', value, {
            context,
          });
        },
      },
    },
    {
      type: 'inputNumber',
      key: 'dictConfigMaxCount',
      label: '文件数量',
      configs: {
        placeholder: '请输入限制上传数量',
        onBlur: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigMaxCount');
          context?.onConfirm?.('dictConfigMaxCount', value);
          setDictConfig(formRef, 'maxCount', value, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigMultiple',
      label: '是否多选文件',
      configs: {
        allowClear: true,
        placeholder: '请选择, 默认否',
        options: [
          {
            label: '是',
            key: true,
            value: true,
          },
          {
            label: '否',
            key: false,
            value: false,
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigMultiple', val);
          setDictConfig(formRef, 'multiple', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigFileControlType',
      label: '上传控件类型',
      configs: {
        allowClear: true,
        placeholder: '请选择，默认按钮',
        options: [
          {
            label: '按钮',
            key: 'button',
            value: 'button',
          },
          {
            label: '卡片',
            key: 'card',
            value: 'card',
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigFileControlType', val);
          setDictConfig(formRef, 'fileControlType', val, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigListType',
      label: '文件展示形式',
      configs: {
        allowClear: true,
        placeholder: '请选择文件，默认文本形式',
        options: [
          {
            label: '文本',
            key: UPLOAD_LIST_TYPE.TEXT,
            value: UPLOAD_LIST_TYPE.TEXT,
          },
          {
            label: '图形',
            key: UPLOAD_LIST_TYPE.PICTURE,
            value: UPLOAD_LIST_TYPE.PICTURE,
          },
          {
            label: '图形卡片',
            key: UPLOAD_LIST_TYPE.PICTURE_CARD,
            value: UPLOAD_LIST_TYPE.PICTURE_CARD,
          },
        ],
        onChange: val => {
          context?.onConfirm?.('dictConfigListType', val);
          setDictConfig(formRef, 'listType', val, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
