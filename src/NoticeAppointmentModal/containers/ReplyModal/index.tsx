import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import { Modal, ConfigProvider } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import * as React from 'react';
import axios from 'axios';

import hcmApis from '@apis/hcm';
import employeeApis from '@apis/employee';
import { request as fetchApi } from '@utils/http';
import useDebounce from '@hooks/useDebounce';
import { showSucNotification } from '@utils/tools';

const REPLY_MODAL_FORM_KEY = 'replyModalFormKey';

import '../../style.less';

export interface IAppProps extends ModalProps {
  type: 'appoint' | 'reappoint';
  defenceProcessIds: string[];
}

const ReplyModal: React.FC<IAppProps> = props => {
  const { type, defenceProcessIds, ...restProps } = props;
  const [loading, setLoading] = React.useState(false);
  const [empList, setEmpList] = React.useState([]);
  const cancelTokenSourceRef = React.useRef(null);
  const [visible, setVisible] = React.useState<boolean>(false);

  React.useEffect(() => {
    setVisible(props.open);
  }, [props.open]);

  const handleClose = () => {
    setVisible(false);
  };

  const handleSearchEmp = useDebounce(
    keyword => {
      if (cancelTokenSourceRef.current) {
        cancelTokenSourceRef.current.cancel('取消上次请求');
        cancelTokenSourceRef.current = null;
      }
      cancelTokenSourceRef.current = axios.CancelToken.source();
      fetchApi({
        ...hcmApis.empSearch,
        params: {
          pageNum: 1,
          pageSize: 20,
          objectName: keyword,
        },
        cancelToken: cancelTokenSourceRef.current.token,
        onSuccess: res => {
          const newEmpList = (res?.list || []).map(item => ({
            ...item,
            key: item.empAccount,
            value: item.empAccount,
            label: `${item.empName}-${item.empCompanyName}`,
          }));
          setEmpList(newEmpList);
        },
      });
    },
    300,
    []
  );

  const fields: IFormItem[] = React.useMemo(
    () => [
      {
        type: 'select',
        key: 'defenceOfficerAccount',
        label: '定岗沟通HR',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '必填',
            },
          ],
        },
        configs: {
          mode: 'multiple',
          options: empList,
          onShowSearch: true,
          filterOption: false,
          onSearch: handleSearchEmp,
          placeholder: '请搜索并选择HR人员',
          getPopupContainer: () => document.body,
        },
      },
    ],
    [empList]
  );
  const handleOk = () => {
    WULIFormActions.get(REPLY_MODAL_FORM_KEY).validate((result, values) => {
      if (result === 'error') {
        return;
      }
      setLoading(true);
      const fetchApiParams =
        type === 'appoint' ? employeeApis.appointDefenceOfficer : employeeApis.reAppointDefenceOfficer;
      fetchApi({
        ...fetchApiParams,
        data: {
          defenceProcessIds,
          defenceOfficerAccount: values.defenceOfficerAccount.join(','),
        },
        onSuccess: () => {
          handleClose();
          showSucNotification('操作成功');
        },
      }).finally(() => {
        setLoading(false);
      });
    });
  };

  return (
    <ConfigProvider locale={zhCN}>
      <Modal
        {...restProps}
        centered
        open={visible}
        onOk={handleOk}
        maskClosable={false}
        onCancel={handleClose}
        confirmLoading={loading}
        wrapClassName="dhr-reply-modal"
        getContainer={() => document.querySelector('.lcp-portal-frame-content-tabs-item')}
      >
        <WULIForm
          formItems={fields}
          formKey={REPLY_MODAL_FORM_KEY}
          defaultLayout={{
            col: 24,
            labelCol: 7,
            wrapperCol: 18,
          }}
        />
      </Modal>
    </ConfigProvider>
  );
};

ReplyModal.defaultProps = {
  title: '指派定岗沟通HR',
  maskClosable: false,
};

const withParams = (params: IAppProps): IAppProps => {
  return {
    ...params,
    open: true,
  };
};

const addModal = (params: IAppProps) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
    params.afterClose && params.afterClose();
  };
  reactRender(<ReplyModal {...params} afterClose={onAfterClose} />, container);
};

const modal = ReplyModal as any;

modal.add = function addFn(props: IAppProps) {
  return addModal(withParams(props));
};

export default modal as {
  (props: IAppProps): JSX.Element;
  defaultProps: typeof ReplyModal.defaultProps;
  add: (props: IAppProps) => void;
};
