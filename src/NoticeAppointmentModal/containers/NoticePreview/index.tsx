import { unmountComponentAtNode as reactUnmount, render as reactRender } from 'react-dom';
import React, { useEffect, useState, memo, useCallback } from 'react';
import { Row, Col, Modal } from 'antd';

import '../../style.less';

export interface ModalConfig {
  names: string;
  orgName: string;
  defineEmpOrgName: string;
  templateContent: string;
}

export interface IAppProps extends ModalConfig {
  open: boolean;
  onAfterClose: () => void;
}

const NoticePreview: React.FC<IAppProps> = memo(props => {
  const { names, open, orgName, defineEmpOrgName, templateContent, onAfterClose } = props;
  const [visible, setVisible] = useState<boolean>(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  return (
    <Modal
      centered
      width={700}
      mask={false}
      footer={null}
      open={visible}
      destroyOnClose
      title="预览定岗通知"
      maskClosable={false}
      onCancel={handleCancel}
      afterClose={onAfterClose}
      wrapClassName="dhr-notice-preview-modal"
      getContainer={() => document.querySelector('.lcp-portal-frame-content-tabs-item')}
    >
      <div style={{ minHeight: 300 }}>
        <Row gutter={[8, 8]} style={{ paddingBottom: 10 }}>
          <Col>
            <span>定岗人员所属BG：</span>
            <span>{defineEmpOrgName}</span>
          </Col>
          <Col>
            <span>通知对象所属BG：</span>
            <span>{orgName}</span>
          </Col>
          <Col span={24}>
            <span>其他通知对象：</span>
            <span>{names || '-'}</span>
          </Col>
        </Row>
        {templateContent && (
          <Row>
            <Col span={24}>
              <div
                dangerouslySetInnerHTML={{ __html: templateContent }}
                style={{ border: '1px solid #b1abab', paddingLeft: 10, paddingRight: 10 }}
              ></div>
            </Col>
          </Row>
        )}
      </div>
    </Modal>
  );
});

NoticePreview.displayName = 'NoticePreview';

/**
 * 为配置添加默认属性，创建完整的Modal属性对象
 * @param config 基础配置信息
 * @returns 完整的Modal属性对象
 */
function withParams(config: ModalConfig): IAppProps {
  return {
    open: true,
    onAfterClose: () => {},
    ...config,
  };
}

/**
 * 在DOM中创建并渲染一个临时Modal组件
 * @param props Modal配置信息
 */
function preview(props: ModalConfig): void {
  // 使用文档片段避免实际DOM操作
  const container = document.createDocumentFragment();

  // 关闭后直接清理DOM引用，避免内存泄漏
  const handleAfterClose = (): void => {
    // 直接卸载组件
    reactUnmount(container);
  };

  // 创建完整的属性集
  const fullProps: IAppProps = {
    ...withParams(props),
    onAfterClose: handleAfterClose,
  };

  // 渲染组件到DOM
  reactRender(<NoticePreview {...fullProps} />, container);
}

// 扩展组件类型，添加静态方法
interface NoticePreviewType extends React.FC<IAppProps> {
  preview: (props: ModalConfig) => void;
}

// 使用更精确的类型转换
const modal = NoticePreview as NoticePreviewType;

/**
 * 预览方法 - 显示通知预览Modal
 */
modal.preview = function (props: ModalConfig): void {
  preview(props);
};

export default modal;
