import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Form, Modal, Button, ConfigProvider } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import zhCN from 'antd/lib/locale/zh_CN';
import * as R from 'ramda';
import axios from 'axios';

import hcmApis from '@apis/hcm';
import cnbApis from '@apis/cnb';
import employeeApis from '@apis/employee';
import useDebounce from '@hooks/useDebounce';
import { getDictionaryName, showSucNotification } from '@utils/tools';
import { request as fetchApi } from '@utils/http';

import NoticePreview from './containers/NoticePreview';
import { WULIFormActions, WULIForm } from '@cvte/wuli-antd';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

import './style.less';

const formKey = 'noticeAppointmentModalFormKey';

const formLayout = {
  col: 24,
  labelCol: 7,
  wrapperCol: 16,
};

export interface IAppProps extends ModalProps {
  open: boolean;
}
const NoticeAppointmentModal: React.FC<IAppProps> = ({
  open,
  getContainer = () => document.querySelector('.lcp-portal-frame-content-tabs-item'),
  ...otherProps
}) => {
  const [form] = Form.useForm();
  const otherNamesRef = useRef(null);
  const cancelTokenSourceRef = useRef(null);

  const [orgList, setOrgList] = useState([]);
  const [empList, setEmpList] = useState([]);
  const [sendLoading, setSendLoading] = useState(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [previewLoading, setPreviewLoading] = useState(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  // 获取组织列表
  const onFetchOrgList = () => {
    fetchApi({
      ...cnbApis.dimList,
      params: {
        categories: 'BG',
      },
      onSuccess: list => {
        setOrgList(list || []);
      },
    });
  };

  useEffect(() => {
    onFetchOrgList();
  }, []);

  const handleChangeEmp = useCallback((val, options) => {
    const newNames = (options || [])
      .map(({ empAccount, empName }) => (empAccount ? `${empName}(${empAccount})` : empName))
      .join('、');
    otherNamesRef.current = newNames;
  }, []);

  const handleSearchEmp = useDebounce(
    keyword => {
      if (cancelTokenSourceRef.current) {
        cancelTokenSourceRef.current.cancel('取消上次请求');
        cancelTokenSourceRef.current = null;
      }
      cancelTokenSourceRef.current = axios.CancelToken.source();
      fetchApi({
        ...hcmApis.empSearch,
        params: {
          pageNum: 1,
          pageSize: 20,
          objectName: keyword,
        },
        cancelToken: cancelTokenSourceRef.current.token,
        onSuccess: res => {
          const newEmpList = (res?.list || []).map(item => ({
            ...item,
            key: item.empId,
            value: item.empId,
            label: `${item.empName}-${item.empCompanyName}`,
          }));
          setEmpList(newEmpList);
        },
      });
    },
    300,
    []
  );

  const formItems: IFormItem[] = useMemo(
    () => [
      {
        type: 'datePicker',
        label: '人员定岗时间',
        key: 'defineDate',
        required: true,
        decoratorOptions: {
          rules: [{ required: true, message: '请选择' }],
        },
        configs: {
          placeholder: '请选择',
        },
      },
      {
        type: 'select',
        label: '定岗人员所属BG',
        key: 'defineEmpOrgHid',
        required: true,
        decoratorOptions: {
          rules: [{ required: true, message: '请选择' }],
        },
        configs: {
          showSearch: true,
          options: orgList,
          optionFilterProp: 'name',
          placeholder: '请按BG选择定岗人员',
          getPopupContainer: () => document.body,
          fieldNames: {
            label: 'name',
            value: 'hid',
          },
        },
      },
      {
        type: 'select',
        label: '通知对象所属BG',
        key: 'orgHid',
        required: true,
        decoratorOptions: {
          rules: [{ required: true, message: '请选择' }],
        },
        configs: {
          showSearch: true,
          options: orgList,
          optionFilterProp: 'name',
          placeholder: '请按BG选择被通知对象',
          getPopupContainer: () => document.body,
          fieldNames: {
            label: 'name',
            value: 'hid',
          },
        },
        extra: '所选组织的全员都会收到该组织的定岗人员的通知',
      },
      {
        type: 'select',
        label: '其他通知对象',
        key: 'empIds',
        configs: {
          mode: 'multiple',
          options: empList,
          onShowSearch: true,
          filterOption: false,
          onSearch: handleSearchEmp,
          onChange: handleChangeEmp,
          getPopupContainer: () => document.body,
          placeholder: '搜索，请选择非该组织的其他人员',
        },
      },
    ],
    [orgList.length, empList]
  );

  const handleClose = useCallback(() => {
    setVisible(false);
  }, []);

  const handleAssembleParams = useCallback(
    values => {
      return {
        ...values,
        orgName: getDictionaryName(values.orgHid, orgList, 'hid', 'name'),
        defineEmpOrgName: getDictionaryName(values.defineEmpOrgHid, orgList, 'hid', 'name'),
      };
    },
    [orgList]
  );
  const handleOk = () => {
    WULIFormActions.get(formKey).validate((result, values) => {
      if (result === 'error') {
        return;
      }
      setSendLoading(true);
      const params = handleAssembleParams(values);
      fetchApi({
        ...employeeApis.notificationDefineSend,
        data: {
          ...params,
        },
        onSuccess: () => {
          handleClose();
          showSucNotification('操作成功');
        },
      }).finally(() => {
        setSendLoading(false);
      });
    });
  };

  // 预览
  const handlePreview = useCallback(
    (isSendEmail = '0') => {
      WULIFormActions.get(formKey).validate((result, values) => {
        if (result === 'error') {
          return;
        }
        setPreviewLoading(true);
        const params = handleAssembleParams(values);
        fetchApi({
          ...employeeApis.notificationPreview,
          data: {
            ...params,
            // '1' - 发送邮件通知预览
            // '0' - 单纯预览
            isSendNotification: isSendEmail,
          },
          onSuccess: res => {
            if (isSendEmail === '0') {
              NoticePreview.preview({
                ...params,
                ...(res || {}),
                names: otherNamesRef.current,
              });
            } else {
              showSucNotification('操作成功');
            }
          },
        }).finally(() => {
          setPreviewLoading(false);
        });
      });
    },
    [orgList]
  );

  const FooterComponent = useCallback(() => {
    return (
      <>
        <Button onClick={handleClose}>取消</Button>
        <Button type="primary" loading={previewLoading} onClick={() => handlePreview()}>
          预览
        </Button>
        <Button type="primary" loading={previewLoading} onClick={() => handlePreview('1')}>
          邮件预览
        </Button>
        <Button type="primary" loading={sendLoading} onClick={handleOk}>
          发送通知
        </Button>
      </>
    );
  }, [form, previewLoading, sendLoading, orgList]);

  return (
    <ConfigProvider locale={zhCN}>
      <Modal
        {...otherProps}
        centered
        open={visible}
        title="发起定岗通知"
        maskClosable={false}
        onCancel={handleClose}
        getContainer={getContainer}
        footer={<FooterComponent />}
        confirmLoading={sendLoading}
        wrapClassName="dhr-notice-appointment-modal"
      >
        <div className="dhr-notice-appointment-content">
          <WULIForm formKey={formKey} formItems={formItems} defaultLayout={formLayout} />
        </div>
      </Modal>
    </ConfigProvider>
  );
};

const withParams = (params: ModalProps): IAppProps => {
  return {
    ...params,
    open: true,
  };
};

const addModal = (params: any) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
    params.onAfterClose && params.onAfterClose();
  };
  reactRender(<NoticeAppointmentModal {...params} onAfterClose={onAfterClose} />, container);
};

const modal = NoticeAppointmentModal as any;

modal.add = function addFn(props: ModalProps) {
  return addModal(withParams(props));
};

export default modal as {
  add: (props: ModalProps) => void;
};
