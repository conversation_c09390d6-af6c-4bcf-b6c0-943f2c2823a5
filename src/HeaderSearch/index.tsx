import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { Select, Spin } from 'antd';
import axios from 'axios';

import hcmApis from '@apis/hcm';
import { request } from '@utils/http';
import useDebounce from '@hooks/useDebounce';
import UserInfoDrawer from './containers/UserInfoDrawer';

import './style.less';

export interface ISearchOption {
  key: string;
  value: string;
  label: string;
  empId: string;
  empName: string;
  empDeptName: string;
  positionName: string;
  genderName: string;
  [key: string]: any;
}

const HeaderSearch: React.FC = () => {
  const [options, setOptions] = useState<ISearchOption[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  // 用于存储取消令牌
  const cancelTokenSourceRef = useRef<any>(null);

  const resultMapping = { key: 'empId', label: 'empName', value: 'empId' };

  // 清理函数，用于在组件卸载时取消请求
  useEffect(() => {
    return () => {
      if (cancelTokenSourceRef.current) {
        cancelTokenSourceRef.current.cancel('组件卸载，请求取消');
      }
    };
  }, []);

  // 使用防抖钩子函数进行关键词搜索
  const handleSearch = useDebounce(
    (searchValue: string) => {
      if (!searchValue) {
        setOptions([]);
        return;
      }

      // 如果有正在进行的请求，取消它
      if (cancelTokenSourceRef.current) {
        cancelTokenSourceRef.current.cancel('新的搜索请求发起，取消旧请求');
      }

      // 创建新的取消令牌
      cancelTokenSourceRef.current = axios.CancelToken.source();

      setLoading(true);
      // 构建搜索参数
      const params = {
        pageSize: 10,
        pageNum: 1,
        modelType: 'DEFAULT',
        isNeedPermission: '1',
        objectName: searchValue,
      };

      request({
        ...hcmApis.empSearch,
        params,
        cancelToken: cancelTokenSourceRef.current.token,
        onSuccess: data => {
          // 请求完成后清除取消令牌引用
          cancelTokenSourceRef.current = null;
          const list = data?.list || [];
          setOptions(list);
        },
        onError: error => {
          // 请求完成后清除取消令牌引用，无论成功或失败
          cancelTokenSourceRef.current = null;

          // 如果是因为取消导致的错误，不做特殊处理
          if (axios.isCancel(error)) {
            console.log('请求已被取消:', error.message);
            return;
          }

          console.error('搜索请求失败:', error);
          setOptions([]);
        },
      }).finally(() => {
        // 只有当当前请求是最后一个请求时，才设置loading为false
        if (!cancelTokenSourceRef.current) {
          setLoading(false);
        }
      });
    },
    300,
    [resultMapping]
  );

  // 员工类型
  const empTypeMapViewType = useMemo(
    () => ({
      // 专项实习生
      abb6c5b007604f1a865def0bc8b073d1: 'INTERN',
      // offer实习生
      '49124e07f5d44358bc78066bc9a545aa': 'INTERN',
      // 体验实习生
      '999f0645ac5648849aba848e4104be0f': 'INTERN',
      // 长期实习生
      '7161cc0f158549899e104eb7713d1182': 'INTERN',
      // 合作方
      '00d9feb181144f65b1bf93c17362746a': 'PARTNER',
      // 顾问
      '78d50372fa5f4e08b455959591fd6539': 'CONSULTANT',
      // 派遣
      '2dd73d4b9030498ea4adfa9afcac92cb': 'DISPATCH',
      // 项目外包
      adf4f0cc4cec4ccf8a548ba7df3ee6ff: 'PROJECT_OUTSOURCING',
      // 人力外包
      a757b39e195448b188bb9c0d4f401ced: 'HR_OUTSOURCING',
      // 正编
      '4c8f73ae6b5a4d64abfd0d24da859170': 'REGULAR_EMPLOYEE',
    }),
    []
  );

  const handleSelect = (option: ISearchOption) => {
    UserInfoDrawer.open({
      empId: option.empId,
      idcard: option.cftId,
      phone: option.empPhone,
      account: option.empAccount,
      viewType: empTypeMapViewType[option.empType],
    });
    setOptions([]);
  };

  // 自定义下拉菜单渲染
  const customDropdownRender = useCallback(() => {
    return (
      <div className="dhr-header-search-custom-dropdown-container">
        {loading ? (
          <div className="dhr-header-search-custom-dropdown-loading">
            <Spin size="small" />
          </div>
        ) : (
          <div className="dhr-header-search-custom-dropdown-option-list">
            <div>
              {options.map(option => (
                <div
                  key={option.empId}
                  className="dhr-header-search-custom-dropdown-option-item"
                  onClick={() => handleSelect(option)}
                >
                  <div>
                    <span className="dhr-header-search-custom-dropdown-option-item-name">{option.empName}</span>
                    <span className="dhr-header-search-custom-dropdown-option-item-sex">
                      [{option.genderName || '-'}]
                    </span>
                    <span>[{option.empTypeName || '-'}]</span>
                  </div>
                  <div className="dhr-header-search-custom-dropdown-option-item-detail">
                    <span className="dhr-header-search-custom-dropdown-option-item-detail-item">
                      {option.empDeptName}
                    </span>
                    <span className="dhr-header-search-custom-dropdown-option-item-detail-item">
                      {option.positionName}
                    </span>
                  </div>
                </div>
              ))}
            </div>
            <div className="dhr-header-search-custom-dropdown-option-item-total">
              <span>{`共找到 ${options?.length || 0} 个结果，仅显示前10条`}</span>
            </div>
          </div>
        )}
      </div>
    );
  }, [options, loading]);

  return (
    <div className="dhr-common-header-search-container">
      <Select
        showSearch
        open={true}
        showArrow={false}
        allowClear={true}
        options={options}
        filterOption={false}
        notFoundContent={null}
        onSearch={handleSearch}
        style={{ width: '220px' }}
        placeholder="请输入姓名/账号搜索"
        defaultActiveFirstOption={false}
        dropdownMatchSelectWidth={false}
        dropdownRender={customDropdownRender}
      />
    </div>
  );
};

export default HeaderSearch;
