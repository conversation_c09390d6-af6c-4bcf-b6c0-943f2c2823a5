.dhr-common-header-search-container {
  width: 220px;
}

.dhr-header-search-custom-dropdown-container {
  .dhr-header-search-custom-dropdown-loading {
    display: flex;
    justify-content: center;
    padding: 12px 0;
  }

  .dhr-header-search-custom-dropdown-option-list {
    max-height: 256px;
    overflow-y: auto;
    .dhr-header-search-custom-dropdown-option-item {
      padding: 5px 12px;
      cursor: pointer;
      line-height: 1.5;
      font-size: 14px;
      transition: background-color 0.3s;
      &:hover {
        background-color: #f5f5f5;
      }

      .dhr-header-search-custom-dropdown-option-item-name {
        color: var(--antd-dynamic-primary-color);
      }

      .dhr-header-search-custom-dropdown-option-item-sex {
        margin-left: 4px;
      }

      .dhr-header-search-custom-dropdown-option-item-detail {
        .dhr-header-search-custom-dropdown-option-item-detail-item {
          margin-right: 4px;
        }
      }
    }

    .dhr-header-search-custom-dropdown-option-item-total {
      padding-top: 4px;
      text-align: center;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
    }
  }
}
