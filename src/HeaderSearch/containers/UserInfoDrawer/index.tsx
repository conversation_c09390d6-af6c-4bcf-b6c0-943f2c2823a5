import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Drawer } from 'antd';

import { LCPDetailTemplate } from '@components/LcpTemplate';

interface ModalProps {
  viewType: string;
  account: string;
  idcard: string;
  phone: string;
  empId: string;
}

export interface IAppProps extends ModalProps {
  open: boolean;
}

const UserInfoDrawer: React.FC<IAppProps> = props => {
  const { open, viewType, account, idcard, phone, empId } = props;

  const [visible, setVisible] = useState<boolean>(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const formTemplateConfig = useMemo(
    () => ({
      empId,
      phone,
      idcard,
      account,
      appId: 'efa37869ee1c4930b434a4c7b1548d46',
      classId: 'd2420e9228a04b3baf6b5c7d1e8fbd6f',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        contextData: {
          empId,
          phone,
          idcard,
          account,
          viewType,
          apiName: 'TB_EMP_INFO_CHANGE_PROCESS',
        },
      },
    }),
    [viewType, account, idcard, phone, empId]
  );

  const handleClose = useCallback(() => {
    setVisible(false);
  }, []);

  return (
    <Drawer
      width="80%"
      mask={false}
      open={visible}
      title="人员信息"
      destroyOnClose
      onClose={handleClose}
      className="dhr-user-info-drawer"
    >
      {viewType && <LCPDetailTemplate {...formTemplateConfig} />}
    </Drawer>
  );
};

const withParams = (params: ModalProps): IAppProps => {
  return {
    ...params,
    open: true,
    onAfterClose: () => {},
  };
};

const addModal = (params: any) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
  };
  reactRender(<UserInfoDrawer {...params} onAfterClose={onAfterClose} />, container);
};

const modal = UserInfoDrawer as any;

modal.open = function addFn(props: ModalProps) {
  return addModal(withParams(props));
};

export default modal;
