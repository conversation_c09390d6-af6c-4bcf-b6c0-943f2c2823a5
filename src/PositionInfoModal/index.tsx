import { render, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import { Modal, ConfigProvider, Spin } from 'antd';
import axios, { CancelTokenSource } from 'axios';

import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';

import orgApis from '@apis/org';
import commonApis from '@apis/common';
import useDebounce from '@hooks/useDebounce';
import { request as fetchApi } from '@utils/http';
import { showSucNotification } from '@utils/tools';

import './style.less';

const formKey = 'positionInfoModalFormKey';
const cacheKey = 'positionInfoModalCacheKey';

const formLayout = {
  col: 24,
  labelCol: 5,
  wrapperCol: 16,
};

// 添加缓存操作相关接口
interface ICacheUtil {
  getValue: <T>(key: string) => T | undefined;
  setValue: <T>(key: string, value: T) => void;
  clear?: (key: string) => void;
}

export interface IAppProps {
  open?: boolean;
  title?: string;
  onAfterClose?: () => void;
  onOk?: (values: Record<string, any>) => void;
  configs?: {
    utils?: {
      cache?: ICacheUtil;
      context?: any;
    };
    context?: any;
  };
  fetchParams?: Record<string, any>;
}

// 添加缓存数据的接口定义
interface IPositionCache {
  jobIdMap: Record<string, string>;
  jobCategoryIdMap: Record<string, string>;
}

// 扩展接口，添加静态方法
export interface PositionInfoModalInterface extends React.FC<IAppProps> {
  add: (params: Partial<IAppProps>) => void;
}

const PositionInfoModal: PositionInfoModalInterface = ({
  title,
  onOk,
  open,
  fetchParams,
  onAfterClose,
  configs = {},
}) => {
  const recordRef = useRef<Record<string, any>>({});
  const cancelTokenSourceRef = useRef<{
    jobFetch: CancelTokenSource | null;
    positionFetch: CancelTokenSource | null;
    positionClassFetch: CancelTokenSource | null;
  }>({
    jobFetch: null,
    positionFetch: null,
    positionClassFetch: null,
  });

  const userInfo = configs?.context.getContext()?.session?.user ?? {};

  const [jobName, setJobName] = useState<string>('');
  const [visible, setVisible] = useState<boolean>(false);
  const [saveLoading, setSaveLoading] = useState<boolean>(false);
  const [positionLoading, setPositionLoading] = useState<boolean>(false);
  const [positionOptions, setPositionOptions] = useState<
    Array<{
      key: string;
      label: string;
      dataIndex: string;
      [key: string]: any;
    }>
  >([]);

  // 获取职位详情
  const onFetchPositionData = (id: string): Promise<{ cName?: string; [key: string]: any }> =>
    new Promise(resolve => {
      if (cancelTokenSourceRef.current.positionFetch) {
        cancelTokenSourceRef.current.positionFetch.cancel('取消上次请求');
        cancelTokenSourceRef.current.positionFetch = null;
      }
      if (!id) {
        return resolve({});
      }
      cancelTokenSourceRef.current.positionFetch = axios.CancelToken.source();
      const params = {
        id,
        onlyMain: true,
        keyType: 'CAMEL',
        appId: '53231334b1a2443b9d5a90baa56435ae',
        formClassId: '8f9ff7ebe0ee46d6a045884c5b9ffc00',
      };
      fetchApi({
        ...commonApis.formDetailInfo,
        params,
        headers: {
          'x-app-id': params.appId,
        },
        cancelToken: cancelTokenSourceRef.current.positionFetch.token,
        onSuccess: res => {
          resolve(res?.mainData || {});
        },
      });
    });

  // 获取职位类详情
  const onFetchPositionClassData = (id: string): Promise<{ cFullPathName?: string; [key: string]: any }> =>
    new Promise(resolve => {
      if (cancelTokenSourceRef.current.positionClassFetch) {
        cancelTokenSourceRef.current.positionClassFetch.cancel('取消上次请求');
        cancelTokenSourceRef.current.positionClassFetch = null;
      }
      if (!id) {
        return resolve({});
      }
      cancelTokenSourceRef.current.positionClassFetch = axios.CancelToken.source();
      const params = {
        id,
        onlyMain: true,
        keyType: 'CAMEL',
        appId: '53231334b1a2443b9d5a90baa56435ae',
        formClassId: 'c69f01fe275e45b39fc6ca8a3fa0d75c',
      };
      fetchApi({
        ...commonApis.formDetailInfo,
        params,
        headers: {
          'x-app-id': params.appId,
        },
        cancelToken: cancelTokenSourceRef.current.positionClassFetch.token,
        onSuccess: res => {
          resolve(res?.mainData || {});
        },
      });
    });

  // 标准职位搜索
  const handleFetchJobPosition = useDebounce(
    (keyword: string): void => {
      if (cancelTokenSourceRef.current.jobFetch) {
        cancelTokenSourceRef.current.jobFetch.cancel('取消上次请求');
        cancelTokenSourceRef.current.jobFetch = null;
      }
      cancelTokenSourceRef.current.jobFetch = axios.CancelToken.source();
      const data = {
        pageSize: 100,
        onlyMain: true,
        keyType: 'UPPER',
        appId: '53231334b1a2443b9d5a90baa56435ae',
        formClassId: '96a2f391aa2943c9bf2d5013a0317f68',
        mainParamsGroups: [
          {
            paramsList: [
              {
                value: keyword,
                operator: 'like',
                attrApi: 'C_NAME',
              },
            ],
          },
        ],
      };
      fetchApi({
        ...commonApis.formTableList,
        data,
        headers: {
          'x-app-id': data.appId,
        },
        cancelToken: cancelTokenSourceRef.current.jobFetch.token,
        onSuccess: res => {
          const content = res?.content || [];
          const options = content.map(({ mainData }) => ({
            ...mainData,
            key: mainData?.ID,
            label: mainData?.C_NAME,
            dataIndex: mainData?.ID,
          }));
          setPositionOptions(options);
        },
      });
    },
    300,
    []
  );

  useEffect(() => {
    setVisible(!!open);
  }, [open]);

  useEffect(() => {
    handleFetchJobPosition('');
    return () => {
      configs?.utils?.cache?.clear?.(cacheKey);
    };
  }, []);

  /** 字段映射 */
  const fieldMap = useMemo(
    () => ({
      // 岗位名称
      C_NAME: 'C_NAME',
      // 岗位类型
      C_TYPE: 'C_TYPE',
      // 岗位编码
      C_CODE: 'C_CODE',
      // 职位
      C_JOB_ID: 'C_JOB_ID',
      // 状态 默认传 1 有效
      C_STATUS: 'C_STATUS',
      // 所属组织 额外传
      C_ORG_HID: 'C_ORG_HID',
      // 关联标准岗位
      C_STANDARD_POSITION_ID: 'ID',
      // 开始时间 额外传
      C_BEGIN_DATE: 'C_BEGIN_DATE',
      // 任职要求
      C_REQUIREMENTS: 'C_REQUIREMENTS',
      // 职位类
      C_JOB_CATEGORY_ID: 'C_JOB_CATEGORY_ID',
      // 专业构成类别
      C_PROFESSIONAL_CATEGORY: 'C_PROFESSIONAL_CATEGORY',
      // 主要职责
      C_MAIN_RESPONSIBILITIES: 'C_MAIN_RESPONSIBILITIES',
    }),
    []
  );

  const handleOk = (): void => {
    WULIFormActions.get(formKey).validate(status => {
      if (status === 'error') {
        return;
      }

      setSaveLoading(true);
      const record = {
        ...recordRef.current,
        C_STATUS: '1',
        ...fetchParams,
        C_NAME: `${recordRef.current['C_NAME']}(测试)`,
      };
      const updateData = Object.entries(fieldMap).reduce((acc, [key, pisitionKey]) => {
        acc[key] = record[pisitionKey];
        return acc;
      }, {});
      fetchApi({
        ...orgApis.orgPositionUpdate,
        headers: {
          'x-app-id': '53231334b1a2443b9d5a90baa56435ae',
        },
        params: {
          keyType: 'create',
          formClassId: 'f5c579b5ea484e68a8b908c066495eb0',
        },
        data: {
          ...updateData,
        },
        onSuccess: res => {
          showSucNotification('操作成功');
          setVisible(false);
          onOk?.(res);
        },
      }).finally(() => setSaveLoading(false));
    });
  };

  const handleChangeStandard = (val: string | undefined, record: Record<string, any>): void => {
    recordRef.current = record;
    if (!val) {
      setJobName('-');
      return;
    }

    const jobId = record?.['C_JOB_ID'];
    const jobCategoryId = record?.['C_JOB_CATEGORY_ID'];
    const cache = configs.utils?.cache || ({} as ICacheUtil);
    const cachePositionInfo = cache?.getValue<IPositionCache>(cacheKey) || { jobIdMap: {}, jobCategoryIdMap: {} };

    // 获取缓存数据
    const { jobIdMap, jobCategoryIdMap } = cachePositionInfo;
    const cachePositionName = jobIdMap[jobId] || '';
    const cachePositionClassFullPathName = jobCategoryIdMap[jobCategoryId] || '';

    // 确定需要请求的数据
    const promiseList = [];
    !cachePositionName && promiseList.push(onFetchPositionData(jobId));
    !cachePositionClassFullPathName && promiseList.push(onFetchPositionClassData(jobCategoryId));

    // 如果已有完整缓存，直接使用缓存数据
    if (promiseList.length === 0) {
      const jobName = `${cachePositionName || '-'}(${cachePositionClassFullPathName || '-'})`;
      setJobName(jobName);
      return;
    }

    // 需要请求数据
    setPositionLoading(true);
    Promise.all(promiseList)
      .then(results => {
        // 解构结果，可能有undefined值
        const [positionData, positionClassData] = results;
        const positionName = positionData?.cName || cachePositionName;
        const positionClassFullPathName = positionClassData?.cFullPathName || cachePositionClassFullPathName;
        const jobName = `${positionName || '-'}(${positionClassFullPathName || '-'})`;

        // 更新缓存
        const updatedCache: IPositionCache = {
          jobIdMap: {
            ...jobIdMap,
            [jobId]: positionName,
          },
          jobCategoryIdMap: {
            ...jobCategoryIdMap,
            [jobCategoryId]: positionClassFullPathName,
          },
        };

        cache?.setValue<IPositionCache>(cacheKey, updatedCache);
        setJobName(jobName);
      })
      .finally(() => {
        setPositionLoading(false);
      });
  };

  const formItem: IFormItem[] = [
    {
      type: 'select',
      label: '标准岗位',
      required: true,
      key: 'positionId',
      decoratorOptions: {
        rules: [
          {
            required: true,
            message: '请选择',
          },
        ],
      },
      configs: {
        allowClear: true,
        showSearch: true,
        filterOption: false,
        placeholder: '请搜索选择',
        options: positionOptions,
        onChange: handleChangeStandard,
        onSearch: handleFetchJobPosition,
        getPopupContainer: () => document.body,
      },
    },
    {
      label: '职位',
      type: 'display',
      key: 'jobName',
      configs: {
        content: (
          <Spin spinning={positionLoading}>
            <span>{jobName}</span>
          </Spin>
        ),
      },
    },
  ];

  return (
    <ConfigProvider locale={zh_CN}>
      <Modal
        width={500}
        mask={false}
        open={visible}
        onOk={handleOk}
        afterClose={onAfterClose}
        confirmLoading={saveLoading}
        onCancel={() => setVisible(false)}
      >
        <div className="dhr-position-intro-modal">
          <div className="dhr-position-intro-info">
            <h3>{title}</h3>
            <div className="dhr-position-intro-info-red">
              <span>1.若搜索不到相关标准职位，请联系</span>
              <span className="dhr-position-intro-info-red-blue"> 倪震 </span>
              <span>添加</span>
            </div>
            <div className="dhr-position-intro-info-red">
              <span className="dhr-position-intro-info-red-red">2.</span>
              <a
                href="https://bi.cvte.com/cbi/decision/v10/entry/access/e3640148-673b-4dc6-80d9-b959cba0c79e"
                target="__blank"
              >
                点击此处
              </a>
              <span className="dhr-position-intro-info-red-red">，查看《标准岗职位体系总表》</span>
            </div>
          </div>
          <WULIForm
            formKey={formKey}
            formItems={formItem}
            defaultLayout={formLayout}
            className="dhr-position-intro-form"
          />
        </div>
      </Modal>
    </ConfigProvider>
  );
};

const withAddParams = (params: Partial<IAppProps>): IAppProps => {
  return {
    open: true,
    title: '新增部门职位',
    configs: {},
    ...params,
  };
};

// 优化渲染函数，返回关闭方法，同时保留DocumentFragment的高性能特性
const renderModal = (params: IAppProps): { close: () => void } => {
  const containers: DocumentFragment = document.createDocumentFragment();

  // 统一关闭方法
  const close = (): void => {
    reactUnmount(containers);
  };

  // 合并原有afterClose和关闭逻辑
  const handleAfterClose = (): void => {
    close();
    params.onAfterClose?.();
  };

  render(<PositionInfoModal {...params} onAfterClose={handleAfterClose} />, containers);

  return { close };
};

// 增强静态方法，返回控制权
PositionInfoModal.add = (params: Partial<IAppProps>): { close: () => void } => {
  return renderModal(withAddParams(params));
};

export default PositionInfoModal;
