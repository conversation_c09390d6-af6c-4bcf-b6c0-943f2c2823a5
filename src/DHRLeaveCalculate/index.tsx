import React, { useEffect, useMemo, useState } from 'react';
import { Table, Alert, Spin } from 'antd';
import { showErrNotification } from '@utils/tools';
import { IGlobalProps } from '../types/index.d';
import { request } from '@utils/http';
import leaveApis from '@apis/leave';
import { LEAVE_CALCULATE_TYPE } from '@constants/leave';
import columnsConfig from './columns';
import '@src/styles/atom.less';
import './style.less';

export type IAppProps = IGlobalProps;

interface ICalculateResult {
  success?: '1' | '0';
  warning?: string;
  data?: Record<string, any>[];
}

// 测算类型与API的映射关系
const CALCULATE_TYPE_API_MAP = {
  [LEAVE_CALCULATE_TYPE.EXIT_SALARY]: {
    api: 'handoverCalSalary',
    title: '离职当月薪资信息',
  },
  [LEAVE_CALCULATE_TYPE.EXIT_SECURITY]: {
    api: 'handoverCalSecurity',
    title: '社保公积金信息',
  },
  [LEAVE_CALCULATE_TYPE.EXIT_COMPENSATE]: {
    api: 'handoverCalCompensate',
    title: '离职补偿信息',
    dataHandler: (data: any) => {
      const record = data?.data?.[0] || {};
      const list = [
        { ...record, typeName: '离职补偿金1(N，N为法定算法)', compensate: record.compensateN },
        { ...record, typeName: '离职补偿金2（N+1，N为法定算法)', compensate: record.compensate },
        { ...record, typeName: '试用期内3天生活补助金', compensate: record.compensateProb },
        {
          ...record,
          typeName: '生活补助金1（N+1，N不含奖金）',
          avgSalary: record.avgSalaryBonus,
          compensate: record.compensateBonus,
          nvalue: record.nvalueBonus,
        },
        {
          ...record,
          typeName: '生活补助金2（N+1,N不含奖金和补贴）',
          avgSalary: record.avgSalarySubsidy,
          compensate: record.compensateSubsidy,
          nvalue: record.nvalueSubsidy,
        },
        { ...record, typeName: '赔偿金（2N，N为法定算法，违法解除)', compensate: record.compensate2n },
      ];
      return {
        ...data,
        data: list,
      };
    },
  },
  [LEAVE_CALCULATE_TYPE.EXIT_COMPETE]: {
    api: 'handoverCalCompete',
    title: '离职竞业限制费',
  },
  [LEAVE_CALCULATE_TYPE.EXIT_ANNUAL_LEAVE]: {
    api: 'handoverCalAnnual',
    title: '离职未休年假测算',
  },
};

const DHRLeaveCalculate: React.FC<IAppProps> = props => {
  const { configs } = props;
  const [calculateResult, setCalculateResult] = useState<ICalculateResult>({});
  const [loading, setLoading] = useState(false);
  // 获取配置参数
  const { dictConfig = {}, isVisible } = configs.config?.baseConfig || {};
  const { calculateType } = dictConfig;
  // 获取数据状态
  const formData = configs.context?.getFormData?.() || {};
  const empId = useMemo(() => formData.C_EMP_ID, [formData.C_EMP_ID]);
  const exitDate = useMemo(() => formData.C_EXIT_DATE, [formData.C_EXIT_DATE]);
  // 请求测算
  const requestCalculate = (calculateType: string, empId: string, exitDate: string) => {
    const rquestConfig = CALCULATE_TYPE_API_MAP[calculateType];
    if (!rquestConfig) return showErrNotification('未找到对应的测算类型配置，请检查测算参数');
    if (!empId || !exitDate) return showErrNotification('员工和离职日期不能为空');
    setLoading(true);
    request({
      ...leaveApis[rquestConfig.api],
      params: {
        empId,
        exitDate,
      },
      onSuccess: res => {
        const data = rquestConfig.dataHandler ? rquestConfig.dataHandler(res) : res;
        setCalculateResult(data);
      },
    }).finally(() => {
      setLoading(false);
    });
  };

  // 监听计算类型
  useEffect(() => {
    isVisible === '1' && requestCalculate(calculateType, empId, exitDate);
  }, [calculateType, empId, exitDate, isVisible]);

  // 根据类型获取对应的配置
  const currentConfig = useMemo(() => {
    return columnsConfig.find(item => item.key === calculateType);
  }, [calculateType]);
  return (
    <Spin className="dhr-leave-calculate " spinning={loading}>
      <section className="flex items-center mb-10">
        <span className="text-16 mr-20">{CALCULATE_TYPE_API_MAP[calculateType].title}</span>
        {calculateResult?.warning && (
          <Alert
            showIcon
            message={calculateResult?.warning}
            type={calculateResult?.success === '1' ? 'success' : 'error'}
            className="flex-1"
          />
        )}
      </section>

      <div className="calculate-section">
        <Table
          size="small"
          bordered
          pagination={false}
          dataSource={calculateResult?.data || []}
          columns={currentConfig.columns}
          rowKey="oid"
          className="calculate-table"
        />
      </div>
    </Spin>
  );
};

export default DHRLeaveCalculate;
