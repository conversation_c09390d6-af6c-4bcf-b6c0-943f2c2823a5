import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { LEAVE_CALCULATE_TYPE } from '@constants/leave';
export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

// 测算类型选项
const CALCULATE_TYPE_OPTIONS = [
  {
    label: '离职当月薪资信息',
    key: LEAVE_CALCULATE_TYPE.EXIT_SALARY,
    value: LEAVE_CALCULATE_TYPE.EXIT_SALARY,
  },
  {
    label: '社保公积金信息',
    key: LEAVE_CALCULATE_TYPE.EXIT_SECURITY,
    value: LEAVE_CALCULATE_TYPE.EXIT_SECURITY,
  },
  {
    label: '离职补偿信息',
    key: LEAVE_CALCULATE_TYPE.EXIT_COMPENSATE,
    value: LEAVE_CALCULATE_TYPE.EXIT_COMPENSATE,
  },
  {
    label: '离职竞业限制费',
    key: LEAVE_CALCULATE_TYPE.EXIT_COMPETE,
    value: LEAVE_CALCULATE_TYPE.EXIT_COMPETE,
  },
  {
    label: '当年剩余年假信息',
    key: LEAVE_CALCULATE_TYPE.EXIT_ANNUAL_LEAVE,
    value: LEAVE_CALCULATE_TYPE.EXIT_ANNUAL_LEAVE,
  },
];

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { calculateType, statusKey } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();

    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigCalculateType',
      curFormData?.dictConfigCalculateType || defFormData?.dictConfigCalculateType || calculateType
    );
    formRef?.current?.setFormItem?.(
      'dictConfigStatusKey',
      curFormData?.dictConfigStatusKey || defFormData?.dictConfigStatusKey || statusKey
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'select',
      key: 'dictConfigCalculateType',
      label: '测算类型',
      configs: {
        options: CALCULATE_TYPE_OPTIONS,
        placeholder: '请选择测算类型',
        onSelect: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigCalculateType');
          context?.onConfirm?.('dictConfigCalculateType', value);
        },
      },
    },
  ];
};

export default customCptBaseConfig;
