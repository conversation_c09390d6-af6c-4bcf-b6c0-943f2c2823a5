import React from 'react';
import { LEAVE_CALCULATE_TYPE } from '@constants/leave';
import { toDateFormat } from '@utils/tools';

// 配置信息
const columnsConfig = [
  {
    key: LEAVE_CALCULATE_TYPE.EXIT_SALARY,
    title: '离职当月薪资信息',
    columns: [
      { title: '集团入职时间', dataIndex: 'empLaborBeginDate', render: text => toDateFormat(text) },
      { title: '公司入职时间', dataIndex: 'laborDate', render: text => toDateFormat(text) },
      { title: '工龄补贴', dataIndex: 'subsidy' },
      { title: '基础工资', dataIndex: 'basicSalary' },
      { title: '岗位工资', dataIndex: 'empJobSalary' },
      { title: '专项工资', dataIndex: 'meetSalary' },
      { title: '月固定加班工资', dataIndex: 'overtimeSalary' },
      { title: '住房补贴(含特殊住房补贴)', dataIndex: 'housingSubsidy' },
      { title: '话费补贴', dataIndex: 'telephoneSubsidy' },
      { title: '缺勤工资', dataIndex: 'absentSalary' },
      { title: '未封存病假时长', dataIndex: 'sickHour', render: text => text || '0' },
      { title: '未封存无薪事假时长', dataIndex: 'paidHour', render: text => text || '0' },
      { title: '应发合计', dataIndex: 'totalSalary' },
    ],
  },
  {
    key: LEAVE_CALCULATE_TYPE.EXIT_SECURITY,
    title: '社保公积金信息',
    columns: [
      { title: '项目', dataIndex: 'projectName' },
      { title: '缴交城市', dataIndex: 'payLocationName' },
      { title: '缴交方式', dataIndex: 'payTypeName' },
      { title: '最后缴交年份', dataIndex: 'lastPayYear' },
      { title: '最后缴交月份', dataIndex: 'lastPayMonth' },
      { title: '终止规则', dataIndex: 'socialInsTermRule' },
    ],
  },
  {
    key: LEAVE_CALCULATE_TYPE.EXIT_COMPENSATE,
    title: '离职补偿信息',
    columns: [
      { title: '类型', dataIndex: 'typeName' },
      { title: '最后发薪月份', dataIndex: 'endDate', render: text => toDateFormat(text) },
      { title: '是否定岗', dataIndex: 'isDeterminePosts' },
      { title: 'N值', dataIndex: 'nvalue' },
      { title: '离职当月薪资', dataIndex: 'curMonthTotalSalary' },
      { title: '月平均工资', dataIndex: 'avgSalary' },
      { title: '市平均工资', dataIndex: 'salary' },
      { title: '市平均工资三倍', dataIndex: 'thirdSalary' },
      { title: '离职补偿金', dataIndex: 'compensate' },
    ],
  },
  {
    key: LEAVE_CALCULATE_TYPE.EXIT_COMPETE,
    title: '离职竞业限制费',
    columns: [
      { title: '最后发薪月份', dataIndex: 'endDate', render: text => toDateFormat(text) },
      { title: '月均工资（基础+岗位）', dataIndex: 'basicPostSalaryAvg' },
      { title: '月均工资（前12月总收入）', dataIndex: 'allSalaryAvg' },
      { title: '月均工资（基础+岗位+能力）', dataIndex: 'postAndCompAvg' },
      { title: '离职当月工资', dataIndex: 'curMonthTotalSalary' },
      { title: '版本1(月均工资(基础+岗位)的30%)', dataIndex: 'version1' },
      { title: '版本2(离职当月工资的1/3(基础+岗位))', dataIndex: 'version2' },
      { title: '版本3(月均工资(前12月总收入的1/3))', dataIndex: 'version3' },
      { title: '版本4(月均工资(基础+岗位+能力)的30%)', dataIndex: 'version4' },
    ],
  },
  {
    key: LEAVE_CALCULATE_TYPE.EXIT_ANNUAL_LEAVE,
    title: '离职未休年假测算(仅为参考)',
    desc: (
      <span className="color-danger my-10">
        当年度剩余未休年假天数 = (当年度在本单位已过日历天数 / 365天) x 本人全年应当享受的年休假天数(当年度年假基数）-
        当年度已休年休假天数，剩余不足一天不予计算
      </span>
    ),
    columns: [
      { title: '当年度年假基数天数', dataIndex: 'standardValue' },
      { title: '当年度已使用年假天数', dataIndex: 'usedValue' },
      { title: '当年度剩余年假天数', dataIndex: 'availableValue' },
      { title: '未休年假工资(法定)(二倍平均工资)', dataIndex: 'unusedAnnualSalaryV1' },
      { title: '未休年假工资(一倍平均工资)', dataIndex: 'unusedAnnualSalaryV2' },
      { title: '未休年假工资(一倍月薪)', dataIndex: 'unusedAnnualSalaryV3' },
      { title: '未休年假工资(二倍月薪)', dataIndex: 'unusedAnnualSalaryV4' },
    ],
  },
];

export default columnsConfig;
