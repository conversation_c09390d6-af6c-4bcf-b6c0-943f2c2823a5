import { CustomComponentInjectConfig } from '@cvte/cir-lcp-sdk/src/lib/customComponent';
import CustomSampleRender from './SampleRender';
import BaseConfigForm from './BaseConfig';

const customCptInjectConfig = new CustomComponentInjectConfig({
  // 组件类型编码，内容须用大写字母+下划线表示，用于做唯一标识
  code: 'DHR_LEAVE_CALCULATE',
  // 组件类型名称，用于在设计器中显示
  name: '离职测算展示组件',
});

customCptInjectConfig.setRenderConfig({
  // 组件在设计器画布区域中展示的渲染组件
  sampleRender: CustomSampleRender,
});

// 设置组件的在设计器端展示的基础配置项
customCptInjectConfig.setBaseConfig(BaseConfigForm);

export default customCptInjectConfig;
