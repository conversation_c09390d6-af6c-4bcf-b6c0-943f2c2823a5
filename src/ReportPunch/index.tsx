import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ToolBarControl } from '@uiw/react-amap-tool-bar-control';
import { ScaleControl } from '@uiw/react-amap-scale-control';
import { APILoader } from '@uiw/react-amap-api-loader';
import { LoadingOutlined } from '@ant-design/icons';
import { Button, Modal, message, Spin } from 'antd';
import { Marker } from '@uiw/react-amap-marker';
import { Circle } from '@uiw/react-amap-circle';
import { Map } from '@uiw/react-amap-map';
import classnames from 'classnames';
import dayjs from 'dayjs';

import entryApis from '@apis/entry';
import { showSucNotification, toDateFormat } from '@utils/tools';
import { request as fetchApi } from '@utils/http';

import EntryApplicationConfirm from './containers/EntryApplicationConfirm';

import { ENTRY_ACT_STATUS } from '@constants/entry';
import { AMAP_SERVICE_HOST, AMAP_AKEY } from '@constants/common';

import './style.less';

const ENTRY_REPORT = {
  /**未到时间 */
  UNARRIVED_TIME: 'UNARRIVED_TIME',
  /** 打卡范围外 */
  OUTSIDE: 'OUTSIDE',
  /** 打卡范围内 */
  INTERIOR: 'INTERIOR',
  /** 已结束成功 */
  END: 'END',
};
export interface IAppProps {
  empId: string;
  actItemId: string;
  entryProcessId: string;
  actData: Record<string, any>;
  extraData: Record<string, any>;
  onRefresh: () => void;
}
const ReportPunch: React.FC<IAppProps> = props => {
  const { actData: actInfo, entryProcessId, empId, actItemId, extraData, onRefresh } = props;
  const { instanceId, status } = actInfo || {};
  const watchId = useRef(null);
  const inviteLoadingRef = useRef<boolean>(false);

  const [open, setOpen] = useState<boolean>(false);
  const [pageLoading, setPageLoading] = useState<boolean>(false);
  const [inviteLoading, setInviteLoading] = useState<boolean>(false);
  const [inRange, setInRange] = useState<boolean>(false);
  const [position, setPosition] = useState<number[] | undefined>(undefined);
  const [entryProcessInfo, setEntryProcessInfo] = useState<Record<string, any>>({});

  /** 获取入职信息 */
  const onFetchEntryProcessInfo = useCallback(entryProcessId => {
    setPageLoading(true);
    fetchApi({
      ...entryApis.entryProcessInfo,
      params: {
        entryProcessId,
      },
      onSuccess: res => {
        setEntryProcessInfo(res || {});
      },
    }).finally(() => setPageLoading(false));
  }, []);

  useEffect(() => {
    // 后端转发
    window._AMapSecurityConfig = {
      serviceHost: AMAP_SERVICE_HOST,
    };
  }, []);

  useEffect(() => {
    entryProcessId && onFetchEntryProcessInfo(entryProcessId);
  }, [entryProcessId]);

  // 计算地理位置差距
  const calculateDistance = (currentLat, currentLng, lat, lng) => {
    // 地球半径
    const R = 6371000;
    const dLat = ((lat - currentLat) * Math.PI) / 180;
    const dLon = ((lng - currentLng) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((currentLat * Math.PI) / 180) *
        Math.cos((lat * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  };

  /** 更新当前位置的变化 */
  const handleUpdateGeolocation = useCallback(pos => {
    const lng = pos.coords.longitude;
    const lat = pos.coords.latitude;
    //计算是否范围内
    const distance = calculateDistance(lat, lng, 29.1574505, 123.5219985);
    // 范围
    const Range = 1000;
    // 打开范围内 -> 则高亮
    const newInRange = distance < Range;
    setInRange(newInRange);
    setPosition([lng, lat]);
  }, []);

  // 监听地址变化
  const requestGeolocationPermission = useCallback(() => {
    if (navigator.geolocation && !watchId.current) {
      watchId.current = navigator.geolocation.watchPosition(
        pos => {
          handleUpdateGeolocation(pos);
        },
        error => {
          console.log('监听位置变化失败：', error);
          message.warning('位置定位失败，请打开GPS');
        },
        {
          enableHighAccuracy: true,
          timeout: 3000,
          maximumAge: 0,
        }
      );
    }
  }, []);

  /** 打开位置授权 */
  const handleOpenPosition = useCallback(() => {
    navigator.geolocation.getCurrentPosition(
      pos => {
        handleUpdateGeolocation(pos);
        requestGeolocationPermission();
      },
      error => {
        console.log('第一次授权失败：', error);
        message.warning('位置定位失败，请打开GPS');
      },
      {
        enableHighAccuracy: true,
        timeout: 3000,
        maximumAge: 0,
      }
    );
  }, []);

  /** 重新打开授权 提示 */
  const handleReauthorize = useCallback(() => {
    Modal.info({
      title: '请重新授权获取地理位置',
      content: (
        <div>
          <div>请按以下步骤重新打开位置授权：</div>
          <h4>PC端（桌面浏览器）:</h4>
          <ol>
            <li>
              <strong>Google Chrome:</strong> 在地址栏左边，点击权限图标
              <img
                width="18"
                height="18"
                alt="Default (Secure)"
                src="https://itapis.cvte.com/cfile/c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3/v2/download/1cb0629a26934002a950fac39512f808"
              />
              &gt; 位置信息 &gt; 更改权限设置
            </li>
            <li>
              <strong>Mozilla Firefox:</strong> 打开右上角菜单 &gt; 选项 &gt; 隐私与安全性 &gt; 权限 &gt; 设置位置权限
              &gt; 解除封锁本站点。
            </li>
            <li>
              <strong>Microsoft Edge:</strong> 打开右上角菜单 &gt; 设置 &gt; Cookies 和站点权限 &gt; 位置 &gt;
              允许访问位置。
            </li>
          </ol>
          <h4>移动端:</h4>
          <ol>
            <li>
              <strong>Safari（iOS设备）:</strong> 打开 设置 &gt; 隐私 &gt; 定位服务 &gt; Safari 浏览器 &gt;
              选择允许访问位置。
            </li>
            <li>
              <strong>Google Chrome（Android设备）:</strong> 打开 设置 &gt; 应用和通知 &gt; Chrome &gt; 权限 &gt;
              允许位置访问。
            </li>
            <li>
              <strong>Samsung Internet（Android设备）:</strong> 打开 设置 &gt; 应用程序 &gt; Samsung Internet &gt; 权限
              &gt; 允许位置访问。
            </li>
          </ol>
          <p>更改设置后，请刷新页面以重试。</p>
        </div>
      ),
    });
  }, []);

  // 监听位置变化
  useEffect(() => {
    if (navigator.permissions) {
      navigator.permissions.query({ name: 'geolocation' }).then(permission => {
        const permissionFn = {
          /** 第一次授权 */
          prompt: handleOpenPosition,
          /** 已授权 */
          granted: requestGeolocationPermission,
          /** 已拒绝 */
          denied: handleReauthorize,
        };
        permissionFn[permission.state]?.();

        permission.onchange = () => {
          if (permission.state === 'granted') {
            requestGeolocationPermission();
          }
        };
      });
    }

    // 清除监听
    return () => {
      watchId.current && navigator.geolocation.clearWatch(watchId.current);
      watchId.current = null;
    };
  }, []);

  /** 获取相差时间 */
  const getDaysDifference = useCallback(timestamp => {
    if (!timestamp) {
      return 0;
    }
    // 将传入的时间戳转换为当天的开始时间
    const inputDate = dayjs(timestamp).startOf('day');
    // 获取当前时间的当天开始时间
    const today = dayjs().startOf('day');
    // 计算相差天数
    const diffDay = inputDate.diff(today, 'day');
    // 否则返回相差的天数
    return diffDay <= 0 ? 0 : diffDay;
  }, []);

  const entryStatus = useMemo(() => {
    if (status === ENTRY_ACT_STATUS.COMPLETE) {
      return ENTRY_REPORT.END;
    }
    const diffDay = getDaysDifference(entryProcessInfo?.entryDate);
    // 入职时间是未来 则 不开启打卡
    if (diffDay > 0) {
      return ENTRY_REPORT.UNARRIVED_TIME;
    }

    return inRange ? ENTRY_REPORT.INTERIOR : ENTRY_REPORT.OUTSIDE;
  }, [entryProcessInfo, status, inRange]);

  const title = useMemo(() => {
    const titleMapFn = {
      // 未到时间
      [ENTRY_REPORT.UNARRIVED_TIME]: `距离您入职还剩${getDaysDifference(entryProcessInfo?.entryDate)}天`,
      // 范围外
      [ENTRY_REPORT.OUTSIDE]: '您未在打卡范围内',
      // 范围内
      [ENTRY_REPORT.INTERIOR]: '您已在打卡范围内',
      // 成功
      [ENTRY_REPORT.END]: '您已完成入职报到',
    };
    return titleMapFn[entryStatus];
  }, [entryStatus]);

  const column = useMemo(
    () => [
      {
        title: '入职报到日期',
        dataIndex: 'entryDate',
        render: text => toDateFormat(text) || '-',
      },
      {
        title: '入职报到详细地址',
        dataIndex: 'workPlace',
      },
    ],
    []
  );

  // 打开确认入职
  const handleEntryConfirm = useCallback(() => {
    setOpen(true);
  }, []);

  // 打卡
  const handleReportPunch = useCallback(() => {
    if (!inRange || inviteLoadingRef.current) {
      return;
    }
    inviteLoadingRef.current = true;
    setInviteLoading(true);
    fetchApi({
      ...entryApis.entryActProcessUpdate,
      data: {
        prjProcessItemId: actItemId,
      },
      onSuccess: () => {
        showSucNotification('操作成功');
        onRefresh?.();
      },
    }).finally(() => {
      inviteLoadingRef.current = false;
      setInviteLoading(false);
    });
  }, [inRange, actItemId]);

  /** 是否展示打卡控件 */
  const isShowEntryDateAction = useMemo(() => {
    /**
     * 活动未完成
     * 当前时间 大于等于 入职时间
     *
     */
    if (![ENTRY_REPORT.END, ENTRY_REPORT.UNARRIVED_TIME].includes(entryStatus)) {
      return true;
    }
    return false;
  }, [entryStatus]);

  /** 表单默认塞进去的值 */
  const metaConfig = useMemo(
    () => ({
      formDefaultData: {
        C_EMP_ID: empId,
        C_ENTRY_REPORT_ID: instanceId,
        C_ENTRY_PROCESS_ID: entryProcessId,
        C_ENTRANT_DATE: entryProcessInfo?.entryDate,
        C_WORK_PLACE: entryProcessInfo?.workPlaceCode,
      },
    }),
    [entryProcessId, entryProcessInfo, empId, instanceId]
  );

  const handleOk = () => {
    setOpen(false);
    onRefresh?.();
  };

  const AntIcon = useCallback(() => <LoadingOutlined style={{ fontSize: 24 }} spin />, []);

  return (
    <div className="dhr-report-punch-wrap">
      <Spin spinning={pageLoading}>
        <div className="dhr-report-punch">
          <div className="dhr-report-punch-header-wrap">
            <div className="dhr-report-punch-header-title">{title}</div>
            <div className="dhr-report-punch-header-tip">
              <div dangerouslySetInnerHTML={{ __html: extraData[entryStatus] || '' }}></div>
            </div>
          </div>
          <div className="dhr-report-punch-header-content-wrap">
            <APILoader version="2.0" akey={AMAP_AKEY}>
              <div style={{ width: '100%', height: '300px' }}>
                <Map zoom={17} center={position}>
                  <Marker position={position} />
                  <Circle radius={80} strokeColor="#fff" strokeWeight={2} center={[113.433963, 23.175505]} />
                  <ScaleControl offset={[16, 30]} position="LB" />
                  <ToolBarControl offset={[16, 10]} position="RB" />
                </Map>
              </div>
            </APILoader>
            {isShowEntryDateAction && (
              <div className="dhr-report-punch-header-actions">
                <div>
                  <div
                    className={classnames(
                      'dhr-report-punch-btn',
                      inRange && !inviteLoading ? 'dhr-report-punch-pass-btn' : 'dhr-report-punch-disable-btn'
                    )}
                    onClick={handleReportPunch}
                  >
                    {inviteLoading ? <Spin tip="报到中..." indicator={<AntIcon />} /> : '报到打卡'}
                  </div>
                  {!inRange && (
                    <Button style={{ width: '100%' }} type="primary" onClick={handleEntryConfirm}>
                      发起入职确认申请
                    </Button>
                  )}
                </div>
              </div>
            )}
            <div className="dhr-report-punch-header-footer-wrap">
              <div className="dhr-report-punch-header-footer-row">
                {column.map(({ dataIndex, title, render }) => {
                  return (
                    <div key={dataIndex} className="dhr-report-punch-header-footer-col">
                      <label className="dhr-report-punch-header-footer-label">{title}：</label>
                      <span>{render ? render(entryProcessInfo[dataIndex]) : entryProcessInfo[dataIndex]}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </Spin>
      <EntryApplicationConfirm
        width="50%"
        open={open}
        mask={false}
        destroyOnClose
        onOk={handleOk}
        title="入职确认申请流程"
        metaConfig={metaConfig}
        onClose={() => setOpen(false)}
      />
    </div>
  );
};

export default ReportPunch;
