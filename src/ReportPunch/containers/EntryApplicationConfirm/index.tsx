import React, { useEffect, useMemo, useRef } from 'react';
import { DrawerProps } from 'antd/es/drawer';
import Message from '@cvte/cir-message';
import { Drawer } from 'antd';

import { LCPDetailTemplate } from '@components/LcpTemplate';

const eventPrefix = `90e83244791d4b4bb348df9e2e227046`;
export interface IAppProps extends DrawerProps {
  open: boolean;
  onOk: () => void;
  metaConfig: Record<string, any>;
}
const EntryApplicationConfirm: React.FC<IAppProps> = props => {
  const { metaConfig } = props;
  const entryConfirmRef = useRef(null);
  const formTemplateConfig = useMemo(
    () => ({
      appId: 'efa37869ee1c4930b434a4c7b1548d46',
      classId: '90e83244791d4b4bb348df9e2e227046',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig,
    }),
    [metaConfig]
  );

  useEffect(() => {
    // 监听天舟云保存事件
    Message.on(eventPrefix, res => {
      // if (res.data.success) {
      //   props.onOk();
      // }
      props.onOk();
    });
  }, []);

  return (
    <Drawer {...props}>
      <div className="dhr-entry-application-confirm-wrap">
        <LCPDetailTemplate {...formTemplateConfig} ref={entryConfirmRef} />
      </div>
    </Drawer>
  );
};

export default EntryApplicationConfirm;
