.dhr-report-punch-wrap {
  padding: 10px;
  height: 100%;
  position: relative;
  .ant-spin-nested-loading {
    height: inherit;
    .ant-spin-container {
      height: inherit;
    }
  }
  .dhr-report-punch {
    .dhr-report-punch-header-wrap {
      text-align: center;
    }
    .dhr-report-punch-header-content-wrap {
      margin-top: 10px;
      margin-bottom: 10px;
      .dhr-report-punch-header-actions {
        width: 200px;
        margin-top: 30px;
        margin-left: auto;
        margin-right: auto;
        .dhr-report-punch-btn {
          height: 100px;
          width: 100px;
          display: flex;
          cursor: pointer;
          margin: 10px auto;
          border-radius: 50%;
          align-items: center;
          justify-content: center;
          border: 6px solid transparent;
        }
        .dhr-report-punch-pass-btn {
          color: var(--antd-dynamic-primary-color);
          border-color: var(--antd-dynamic-primary-color);
          &:hover {
            color: var(--antd-dynamic-primary-hover-color);
            border-color: var(--antd-dynamic-primary-hover-color);
          }
          &:active {
            color: var(--antd-dynamic-primary-active-color);
            border-color: var(--antd-dynamic-primary-active-color);
          }
        }
        .dhr-report-punch-disable-btn {
          border-color: #f5f5f5;
          color: #00000040;
          cursor: not-allowed;
        }
      }
      .dhr-report-punch-header-footer-wrap {
        margin-top: 20px;
        margin-left: auto;
        margin-right: auto;
        width: 200px;
        .dhr-report-punch-header-footer-row {
          white-space: nowrap;
          .dhr-report-punch-header-footer-col {
            .dhr-report-punch-header-footer-label {
              width: 8rem;
              display: inline-block;
            }
          }
        }
      }
    }
  }
}
