import { promiseQueue } from '@cvte/kylin-tools';
import { request } from '@utils/http';
import { DEFAULT_LABEL_KEY, DEFAULT_PARENT_ID_KEY, DEFAULT_PRIMARY_KEY } from '../index';

// import { IBaseConfigData } from '../interface';
// import relateData from './core';
const translateUrl = '/admin/v1/org_unit_relation/def_function';

/**
 * 对象递归
 *
 * @param {Record<string,any>} data
 * @param {(record?:any)=>any} recursionCb
 * @param {(record?:any)=>boolean} recursionCondition
 */
const objectRecursionFn = (
  data: Record<string, any>,
  recursionCb: (record?: any, key?: string) => any,
  recursionCondition: (record?: any, key?: string) => boolean
) => {
  if (Array.isArray(data) || !data) return;
  Object.keys({ ...(data || {}) }).forEach(key => {
    const record = data[key];
    // 递归回调
    recursionCb(record, key);
    // 如果满足条件，则递归
    recursionCondition(record, key) && objectRecursionFn(record, recursionCb, recursionCondition);
  });
};

/**
 * 获取关联数据的配置列表
 *
 * configs形如：
 * {
 *  "FORM_CODE": {
 *    "ATTR_CODE1": { ...baseConfig },
 *    "ATTR_CODE2": { ...baseConfig },
 *  },
 * }
 *
 * @param {Record<string, any>} configs
 * @return {IBaseConfigData[]}
 */
const mappingRelateConfigsList = (configs: Record<string, any>): Record<string, any> => {
  const relateConfigs = {};
  let prtKey = '';
  objectRecursionFn(
    configs,
    (record, key) => {
      const isPrtNode = !record?.dictConfig;
      if (isPrtNode) {
        prtKey = `${key}`;
        return;
      }
      if (!prtKey) prtKey = '';

      const _key = `${key}`;
      // _key = `${prtKey || ''}:${key}`;
      if (record?.dictConfig) relateConfigs[_key] = record;
    },
    record => {
      return typeof record === 'object' && !record?.dictConfig;
    }
  );
  return relateConfigs;
};

/**
 * 获取关联字段的值列表
 *
 * @param {Record<string, any>} fields
 * @return {(Record<string, string | string[]>)}
 */
const mappingRelateFieldValuesList = (fields: Record<string, any>): Record<string, string | string[]> => {
  const readyTranslateData = {};
  let prtKey = '';
  objectRecursionFn(
    fields,
    (record, key) => {
      const isPrtNode = typeof record === 'object' && !Array.isArray(record);
      if (isPrtNode) {
        prtKey = `${key}`;
        return;
      }
      if (!prtKey) prtKey = '';

      const _key = `${key}`;
      // _key = `${prtKey || ''}:${key}`;
      readyTranslateData[_key] = record;
    },
    record => typeof record === 'object' && !Array.isArray(record)
  );
  return readyTranslateData;
};

/**
 * 批量翻译关联数据
 *
 * @param {*} configs
 * @return {*}
 */
const batchTranslate = async configs => {
  console.log('🚀 ~ relateData batchTranslate ~ constbatchTranslate:TranslatorFnType= ~ configs:', configs);
  const { fields: oldValue, baseConfigs: oldBaseConfigs, configs: _configs } = configs;
  const { baseConfigs: newBaseConfigs, value: newValue } = _configs || {};
  const baseConfigs = newBaseConfigs || oldBaseConfigs;
  const values = newValue || oldValue;

  const readyTranslateData = mappingRelateFieldValuesList(values);
  console.log('🚀 ~ relateData batchTranslate ~ readyTranslateData:', readyTranslateData);
  console.log('🚀 ~ relateData batchTranslate ~ baseConfigs:', baseConfigs);
  const recordConfigs = mappingRelateConfigsList(baseConfigs);
  console.log('🚀 ~ relateData batchTranslate ~ relateConfigs:', recordConfigs);

  /**
   * 值健组合
   * 示例：
   * ["BASIC_INFO_GROUP:ORG_62944EEB2EC6A22A","LCP_TABLE_67917F6B9D2753DA:DHR_TEST_SELECTION"]
   *
   * */
  const recordKeys = Object.keys(recordConfigs);
  if (!recordKeys.length)
    return {
      fields: {},
      map: {},
    };

  console.log('recordKeysrecordKeys', recordKeys);

  const [fields, map] = [{}, {}];
  const relatePms = recordKeys
    .map(async key => {
      const record = recordConfigs[key];
      debugger;
      const { dictConfig } = record || {};
      const { primaryKey = DEFAULT_PRIMARY_KEY } = dictConfig;

      const curValues = readyTranslateData[`${key || ''}`];
      console.log('🚀 ~ batchTranslate .map ~ code:', curValues);

      // const relateConfig = dictConfig as IBaseConfigData;
      if (!curValues || !curValues?.length) return;
      const options =
        (await request({
          url: translateUrl,
          method: 'get',
          params: {
            hids: Array.isArray(curValues) ? curValues.join(',') : curValues,
          },
        })) || [];
      const keySPlit = key?.split(':');
      debugger;
      const [prtKey, chdKey] = keySPlit.length > 1 ? keySPlit : ['', key];
      console.log('🚀 ~ batchTranslate .map ~ key:', prtKey, chdKey);
      if (!prtKey) {
        fields[chdKey] = options;
        map[chdKey] = options.reduce((acc, cur) => {
          if (!cur.value) return acc;
          acc[cur.value] = cur;
          return acc;
        }, {});
      } else {
        if (!fields[`${prtKey}`]) fields[`${prtKey}`] = {};
        if (!map[`${prtKey}`]) map[`${prtKey}`] = {};
        fields[`${prtKey}`][chdKey] = options;
        map[`${prtKey}`][chdKey] = options.reduce((acc, cur) => {
          if (!cur[primaryKey]) return acc;
          acc[cur[primaryKey]] = cur;
          return acc;
        }, {});
      }
    })
    .filter(item => !!item);

  await promiseQueue(relatePms, 1);
  console.log('🚀 ~ relateData batchTranslate ~ relateDataList:', fields, map);

  return {
    fields,
    map,
    options: fields,
    optionsMap: map,
  };
};

export default batchTranslate;
