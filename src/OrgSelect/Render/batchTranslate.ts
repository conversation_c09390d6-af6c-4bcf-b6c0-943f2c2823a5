import { promiseQueue } from '@cvte/kylin-tools';
import { request } from '@utils/http';
import cnbApis from '@apis/cnb';

import { DEFAULT_PRIMARY_KEY } from '../index';

/**
 * 对象递归
 *
 * @param {Record<string,any>} data
 * @param {(record?:any)=>any} recursionCb
 * @param {(record?:any)=>boolean} recursionCondition
 */
const objectRecursionFn = (
  data: Record<string, any>,
  recursionCb: (record?: any, key?: string) => any,
  recursionCondition: (record?: any, key?: string) => boolean
) => {
  if (Array.isArray(data) || !data) return;
  Object.keys({ ...(data || {}) }).forEach(key => {
    const record = data[key];
    // 递归回调
    recursionCb(record, key);
    // 如果满足条件，则递归
    recursionCondition(record, key) && objectRecursionFn(record, recursionCb, recursionCondition);
  });
};

/**
 * 批量翻译关联数据
 *
 * @param {*} configs
 * @return {*}
 */
const batchTranslate = async configs => {
  console.log('🚀 ~ relateData batchTranslate ~ constbatchTranslate:TranslatorFnType= ~ configs:', configs);
  const { fields: oldValue, baseConfigs: oldBaseConfigs, configs: _configs } = configs;
  const { baseConfigs: newBaseConfigs, value: newValue } = _configs || {};
  const baseConfigs = newBaseConfigs || oldBaseConfigs;
  const values = newValue || oldValue;
  const recordKeys = Object.keys(baseConfigs);
  if (!recordKeys.length)
    return {
      fields: {},
      map: {},
    };

  const [fields, map] = [{}, {}];

  const relatePms = recordKeys.map(async key => {
    const record = baseConfigs[key];
    const { dictConfig } = record || {};
    const primaryKey = dictConfig?.primaryKey || DEFAULT_PRIMARY_KEY;

    const curValues = values[key];
    const params = {};
    console.log('🚀 ~ batchTranslate .map ~ code:', curValues);
    // 如果没有则设置默认的options
    if (!curValues || !curValues?.length) {
      const { isAuth, subModelTypes } = dictConfig;
      try {
        subModelTypes && Object.assign(params, JSON.parse(subModelTypes));
      } catch (error) {
        console.info('subModelTypes格式错误:', subModelTypes);
      }
      params['isAuth'] = isAuth === '1' ? '1' : '0';
    } else {
      params['hids'] = Array.isArray(curValues) ? curValues.join(',') : curValues;
    }
    const options =
      (await request({
        ...cnbApis.dimList,
        params,
      })) || [];
    fields[key] = options;
    map[key] = options.reduce((acc, cur) => {
      acc[cur[primaryKey]] = cur;
      return acc;
    }, {});
  });

  await promiseQueue(relatePms, 1);
  console.log('🚀 ~ relateData batchTranslate ~ relateDataList:', fields, map);
  return {
    fields,
    map,
    options: fields,
    optionsMap: map,
  };
};

export default batchTranslate;
