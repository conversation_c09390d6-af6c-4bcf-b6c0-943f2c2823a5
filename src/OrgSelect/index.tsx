import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { TreeSelect, Spin } from 'antd';
import * as R from 'ramda';

import cnbApis from '@apis/cnb';
import { request } from '@utils/http';
import CirCache from '@cvte/cir-cache';
import list2tree from '@cvte/list2tree';

import { IGlobalProps } from '../types/index.d';
import { WULI_MODE, CONTAINER_TYPE } from '@constants/common';

export const DEFAULT_LABEL_KEY = 'name';
export const DEFAULT_PRIMARY_KEY = 'hid';
export const DEFAULT_PARENT_ID_KEY = 'parentHid';

const cacheTool = new CirCache({ storage: 'memory' });

/** 额外填充map */
export const genExtraSetValueMap = (extraSetValue: string) => {
  return extraSetValue?.split(';')?.reduce((acc, cur) => {
    const [field, key] = cur.split('=');
    acc[field] = key;
    return acc;
  }, {});
};

export type IAppProps = IGlobalProps;

const SelectTree: React.FC<IAppProps> = props => {
  const { configs, onChange, value, data, getCache, setCache, utils } = props;

  const valueRef = useRef(value);
  const [treeData, setTreeData] = React.useState([]);
  const [originList, setOriginList] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [droploading, setDroploading] = React.useState(false);
  const { wuliMode, containerType, code: formItemCode } = configs;
  const isTable = CONTAINER_TYPE.TABLE === containerType; // 是否为明细表内

  console.log('业务ID', wuliMode);
  const isView = wuliMode === WULI_MODE.VIEW;
  const { formCode, dictConfig = {} } = configs?.config?.baseConfig || {};

  // 缓存dimList key
  const dimListKey = useMemo(() => `${formItemCode}-dimList`, [formItemCode]);
  // 缓存 dimTreeData key
  const dimTreeDataKey = useMemo(() => `${formItemCode}-dimTreeData`, [formItemCode]);
  // 缓存 dimDynamicSearchKey key
  const dimDynamicSearchKey = useMemo(() => `${formItemCode}-dimDynamicSearchKey`, [formItemCode]);
  const { isAuth, categories, extraSetValue, subModelTypes, dynamicSubModelTypes, isAutoTrigger } = dictConfig;
  const parnentIdKey = dictConfig?.parnentIdKey || DEFAULT_PARENT_ID_KEY;
  const primaryKey = dictConfig?.primaryKey || DEFAULT_PRIMARY_KEY;
  const labelKey = dictConfig?.labelKey || DEFAULT_LABEL_KEY;

  const options = data?.options || [];

  const _subModelTypes = useMemo(() => {
    let tempData = {};
    try {
      if (subModelTypes) {
        tempData = JSON.parse(subModelTypes);
      }
    } catch (error) {
      console.info('subModelTypes格式错误:', subModelTypes);
    }
    return tempData;
  }, [subModelTypes]);

  // 动态查询参数
  const _dynamicSubModelTypes = useMemo(() => {
    let tempData;
    try {
      if (dynamicSubModelTypes) {
        tempData = JSON.parse(dynamicSubModelTypes);
      }
    } catch (error) {
      console.info('dynamicSubModelTypes格式错误:', dynamicSubModelTypes);
    }
    return tempData;
  }, [dynamicSubModelTypes]);

  const getAdditionnalInfo = useCallback(
    (selectedOption: Record<string, any>) => {
      if (!extraSetValue) {
        return {};
      }
      const extraSetValueMap = genExtraSetValueMap(extraSetValue);
      return extraSetValueMap
        ? Object.keys(extraSetValueMap).reduce((pre, cur) => {
            // key 为目标表单字段，value 为来源字段
            const _value = selectedOption[extraSetValueMap[cur]];
            pre[cur] = _value;
            return pre;
          }, {})
        : {};
    },
    [extraSetValue]
  );

  const transformTreeData = originDimData => {
    return list2tree({
      idKey: primaryKey,
      parentIdKey: parnentIdKey,
      newKey: {
        key: primaryKey,
        value: primaryKey,
        title: labelKey,
        name: labelKey,
      },
    })(originDimData);
  };
  const onFetchdimList = (isShowLoading = true, isShowDroploading = false) => {
    isShowLoading && setLoading(true);
    isShowDroploading && setDroploading(true);
    let dynamicParams = {};
    // 动态查询参数
    if (_dynamicSubModelTypes) {
      const formData = configs?.context?.getFormData();
      dynamicParams = Object.entries(_dynamicSubModelTypes).reduce((pre, [key, value]) => {
        pre[key] = formData?.[value];
        return pre;
      }, {});
    }
    const params = {
      isAuth: isAuth === '1' ? '1' : '0',
      ..._subModelTypes,
      ...dynamicParams,
    };
    if ((categories || []).length > 0) {
      params.categories = categories.join(',');
    } else {
      params.excludeCategories = 'COMPANY'; // 过滤法人公司
    }
    request({
      ...cnbApis.dimList,
      params,
    })
      .then(dimData => {
        const _treeData = transformTreeData(dimData || []);
        setOriginList(dimData || []);
        setTreeData(_treeData);
        utils.extra.setValue(dimListKey, dimData);
        utils.extra.setValue(dimTreeDataKey, _treeData);
        _dynamicSubModelTypes && utils.extra.setValue(dimDynamicSearchKey, dynamicParams);
        if (isTable && setCache) {
          setCache({
            [dimListKey]: dimData,
            [dimTreeDataKey]: _treeData,
          });
        }
      })
      .finally(() => {
        isShowLoading && setLoading(false);
        isShowDroploading && setDroploading(false);
      });
  };

  useEffect(() => {
    if (options.length > 0) {
      const _treeData = transformTreeData(options || []);
      setTreeData(_treeData);
      setOriginList(options || []);
    }
  }, [options.length]);

  // 缓存的数据
  // const cacheDataMapping = useMemo(() => {
  //   const cache: any = getCache?.();
  //   return cache;
  // }, [value, getCache, originList, treeData]);
  //todo 添加缓存useMemo写法
  // const cacheDataMapping = getCache?.();
  const cacheDataMapping = cacheTool.getCache() || {};
  const cacheDimList = utils.extra.getValue(dimListKey);
  const cacheDimTreeData = utils.extra.getValue(dimTreeDataKey);
  const renderDimList = originList.length > 0 ? originList : cacheDimList || [];
  // console.log('重点测试cacheDataMapping', cacheDataMapping);
  // console.log('重点测试cacheDimList', cacheDimList);
  // console.log('重点测试cacheDimTreeData', cacheDimTreeData);
  // console.log('重点测试renderDimList', renderDimList);

  const renderTreeData = treeData.length > 0 ? treeData : cacheDimTreeData || [];

  // 如果缓存数据为空，则将options数据设置到缓存中
  useEffect(() => {
    if (!cacheDimList && !originList.length && options.length > 0) {
      utils.extra.setValue(dimListKey, options);
    }
  }, [originList.length, options.length]);

  // 翻译数据
  const dataMapping = useMemo(() => {
    return options?.reduce((pre, cur) => {
      pre[cur[primaryKey]] = cur;
      return pre;
    }, {});
  }, [options, primaryKey]);

  // 翻译
  const handletranslateText = value => {
    if (value) {
      // 非初始化状态只需要取options的值
      let detail = renderDimList?.find(optionItem => optionItem[primaryKey] === value);
      // 优先映射查是否存在 且是初始化的时候
      // 次先 options
      // 缓存里的数据
      if (!detail && cacheDataMapping[value] && !R.isEmpty(cacheDataMapping[value])) {
        detail = cacheDataMapping?.[value];
      }
      // 翻译的数据
      if (!detail && dataMapping?.[value] && !R.isEmpty(dataMapping?.[value])) {
        detail = dataMapping?.[value];
      }
      return detail?.[labelKey] || value;
    }
    return '';
  };

  // 检测改变
  const handleChange = (value, isOnChange = true) => {
    const target = renderDimList.find(item => item[primaryKey] === value) || {};
    const additionnalInfo = getAdditionnalInfo(target);
    /**
     * 明细表模式
     * 1、更新缓存，用于展示翻译
     * 2、更新明细表数据
     */
    if (isTable) {
      value &&
        setCache({
          [value]: target,
        });
      if (configs.context?.setFormData) {
        const detailList = configs.context?.getFormData()?.[formCode];
        const curRow = detailList[data.rowIndex];
        const newRow = Object.assign(curRow, additionnalInfo);
        detailList[data.rowIndex] = newRow;
        detailList[data.rowIndex] = curRow;
        configs.context?.setFormData({
          [formCode]: detailList,
        });
      }
    }
    /**
     * 非明细表模式
     */
    if (!isTable) {
      if (!R.isEmpty(additionnalInfo) && configs.context?.setFormData) {
        configs.context?.setFormData(additionnalInfo);
      }
    }
    valueRef.current = value;
    // !value && setOptions([]);
    isOnChange && onChange?.(value);
  };

  useEffect(() => {
    !isView && !renderTreeData.length && onFetchdimList();
  }, [isAuth, isView, categories, renderTreeData.length, dynamicSubModelTypes]);

  // set进来的值的场景 避免触发onChange
  useEffect(() => {
    valueRef.current !== value && handleChange(value, isAutoTrigger === '1');
  }, [value, isAutoTrigger]);

  // 下拉框
  const handleDropdownVisibleChange = (visible: boolean) => {
    // 对比缓存的搜索参数 - 变化 则重新请求
    if (visible && _dynamicSubModelTypes) {
      const cacheSearchParams = utils.extra.getValue(dimDynamicSearchKey);
      const formData = configs?.context?.getFormData();
      const isSearchParamsChange = Object.entries(_dynamicSubModelTypes).some(
        ([key, value]) => formData[value] !== cacheSearchParams[key]
      );
      isSearchParamsChange && onFetchdimList(false, true);
    }
  };

  return isView ? (
    <div>{handletranslateText(value)}</div>
  ) : (
    <Spin spinning={loading}>
      <TreeSelect
        showSearch
        allowClear
        value={value}
        dropdownRender={menu => {
          return droploading ? (
            <div style={{ textAlign: 'center' }}>
              <Spin size="small" />
            </div>
          ) : (
            menu
          );
        }}
        style={{ width: '100%' }}
        className="treeSelectComp"
        treeData={renderTreeData}
        treeNodeFilterProp="title"
        dropdownMatchSelectWidth={false}
        onChange={hid => handleChange?.(hid)}
        onDropdownVisibleChange={handleDropdownVisibleChange}
      />
    </Spin>
  );
};

export default SelectTree;
