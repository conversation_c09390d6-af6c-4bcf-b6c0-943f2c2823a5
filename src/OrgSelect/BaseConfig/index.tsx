import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const WHETHER_OPTIONS = [
  {
    label: '是',
    key: '1',
    value: '1',
  },
  {
    label: '否',
    key: '0',
    value: '0',
  },
];

const customCptBaseConfig = async (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  setTimeout(() => {
    Promise.resolve(context?.dataEntity?.fetchDict('DHR_ORG_TYPE')).then((list: any[]) => {
      const options = (list || []).map(listItem => ({
        ...listItem,
        label: listItem.name,
        value: listItem.itemValue,
      }));
      formRef?.current?.diffFormItem?.(
        [
          {
            type: 'select',
            key: 'dictConfigCategories',
            configs: {
              options,
            },
          },
        ],
        []
      );
    });
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'select',
      key: 'dictConfigIsAutoTrigger',
      label: '是否自动触发联动',
      configs: {
        options: WHETHER_OPTIONS,
        placeholder: '默认不自动触发联动(选择是，联动配置编辑时改变会触发)',
        onSelect: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigIsAutoTrigger');
          context?.onConfirm?.('dictConfigIsAutoTrigger', value);
          setDictConfig(formRef, 'isAutoTrigger', value, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigIsAuth',
      label: '是否开启数据过滤权限',
      configs: {
        options: WHETHER_OPTIONS,
        onSelect: () => {
          const value = formRef?.current?.getFormItem?.('dictConfigIsAuth');
          context?.onConfirm?.('dictConfigIsAuth', value);
          setDictConfig(formRef, 'isAuth', value, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigCategories',
      label: '组织类型',
      configs: {
        options: [],
        mode: 'multiple',
        placeholder: '请选择',
        optionFilterProp: 'label',
        onChange: value => {
          formRef.current?.setFormItem?.('dictConfigCategories', value);
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigPrimaryKey',
      label: '组织主键',
      configs: {
        placeholder: '请输入组织主健：例：orgId',
        onBlur: e => {
          formRef.current?.setFormItem?.('dictConfigPrimaryKey', e.target.value);
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigLabelKey',
      label: '组织Label',
      configs: {
        placeholder: '请输入组织主健：例：orgName',
        onBlur: e => {
          formRef.current?.setFormItem?.('dictConfigLabel', e.target.value);
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigParnentIdKey',
      label: '节点父级ID',
      configs: {
        placeholder: '请输入节点父级Id字段：例：parnentId',
        onBlur: e => {
          formRef.current?.setFormItem?.('dictConfigParnentId', e.target.value);
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigExtraSetValue',
      label: '额外赋值【选择项赋值表单字段】',
      configs: {
        placeholder: 'formField1=valueKey1;formField2=valueKey2',
        onBlur: e => {
          formRef.current?.setFormItem?.('dictConfigExtraSetValue', e.target.value);
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigSubModelTypes',
      label: '额外查询参数',
      configs: {
        placeholder: 'JSON字符串格式',
        onBlur: e => {
          formRef.current?.setFormItem?.('dictConfigSubModelTypes', e.target.value);
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigDynamicSubModelTypes',
      label: '关联表单查询参数',
      configs: {
        placeholder: 'JSON字符串格式，例：{"id":"C_ID"}',
        onBlur: e => {
          formRef.current?.setFormItem?.('dictConfigDynamicSubModelTypes', e.target.value);
        },
      },
    },
  ];
};

export default customCptBaseConfig;
