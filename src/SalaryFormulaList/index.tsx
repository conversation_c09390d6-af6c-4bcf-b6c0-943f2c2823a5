import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { request as fetchApi } from '@utils/http';
import queryString from 'query-string';
import { Modal, Spin } from 'antd';

import payProcessApis from '@apis/payProcess';

import WULIWholeTable from '@components/WholeTable/AgGrid';
import { showSucNotification, showWarnNotification } from '@utils/tools';

export interface IAppProps {
  cnbSetId: string;
  cnbSetItemId: string;
  cnbSetItemName: string;
  systemHelper: any;
  scene?: string;
}
const SalaryFormulaList: React.FC<IAppProps> = ({ cnbSetId, cnbSetItemId, cnbSetItemName, scene, systemHelper }) => {
  const editAgGridRef = useRef(null);
  const [tableList, setTableList] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [copyLoading, setCopyLoading] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState<{ cdefault?: string; id?: string }[]>([]);

  const handleRestSelected = () => {
    editAgGridRef.current?.setSelected(
      selectedKeys.map(k => ({
        rowKey: k,
        isSelected: false,
      }))
    );
    setSelectedRows([]);
    setSelectedKeys([]);
  };

  const handleJumpToPayroll = useCallback(record => {
    systemHelper.history.push(
      `/portal/ddcl84ou/cmPage?${queryString.stringify({
        pageId: record.id,
        pageFlag: record.id,
        cnbSetItemId: record.itemId,
        cnbSetItemName: record.itemName,
        scene,
        cnbSetId: record.salarySetId,
        closeOnSave: '1',
        exposeName: 'ObjectLinkView',
        resourceName: 'lcp-data-object',
        appId: '3e42150b4e6c49789443df4cbb9c4bb9',
        classId: 'd97043fc2e804c829523c9773704e11e',
        pageName: `${record.itemName}-项目公式信息`,
      })}`
    );
  }, []);

  const columns = useMemo(
    () => [
      {
        title: '公式内容',
        dataIndex: 'content',
        key: 'content',
        render: record => {
          const data = record.data || {};
          return <a onClick={() => handleJumpToPayroll(data)}>{data.content}</a>;
        },
      },
      {
        title: '是否默认公式',
        dataIndex: 'cdefaultName',
        key: 'cdefaultName',
      },
      {
        title: '薪资项目',
        dataIndex: 'itemName',
        key: 'itemName',
      },
      {
        title: '工资套',
        dataIndex: 'salarySetName',
        key: 'salarySetName',
      },
    ],
    []
  );

  useEffect(() => {
    cnbSetId && cnbSetItemId && onFetchFormulaList();
  }, [cnbSetId, cnbSetItemId]);

  const onFetchFormulaList = useCallback(() => {
    setLoading(true);
    fetchApi({
      ...payProcessApis.formulaList,
      params: {
        salarySetId: cnbSetId,
        itemId: cnbSetItemId,
      },
      onSuccess: list => setTableList(list),
    }).finally(() => setLoading(false));
  }, [cnbSetId, cnbSetItemId]);

  /** 新增 */
  const handleAdd = () => {
    systemHelper.history.push(
      `/portal/ddcl84ou/cmPage?${queryString.stringify({
        cnbSetId,
        cnbSetItemId,
        cnbSetItemName,
        scene,
        closeOnSave: '1',
        exposeName: 'ObjectLinkView',
        resourceName: 'lcp-data-object',
        appId: '3e42150b4e6c49789443df4cbb9c4bb9',
        classId: 'd97043fc2e804c829523c9773704e11e',
        pageName: `${cnbSetItemName}-项目公式信息`,
        pageFlag: cnbSetItemId,
      })}`
    );
  };

  /** 删除 */
  const handleDel = () => {
    const [id] = selectedKeys;
    Modal.confirm({
      title: '提醒',
      content: '请确认是否删除该条数据?',
      onOk: () =>
        new Promise((resolve, reject) => {
          fetchApi({
            ...payProcessApis.formulaDel,
            params: {
              id,
            },
            onSuccess: res => {
              resolve(true);
              handleRestSelected();
              onFetchFormulaList();
              showSucNotification('操作成功~');
            },
          }).finally(() => reject(false));
        }),
    });
  };

  /** 设置默认 */
  const handleSetDefault = () => {
    const { cdefault } = selectedRows[0] || {};
    const isDefault = cdefault === '1';
    if (isDefault) {
      return showWarnNotification('该条数据已是默认公式了哦~');
    }
    Modal.confirm({
      title: '提醒',
      content: '请确认是否设置为默认值?',
      onOk: () =>
        new Promise((resolve, reject) => {
          const [id] = selectedKeys;
          fetchApi({
            ...payProcessApis.formulaDefaultSet,
            data: {
              id,
            },
            onSuccess: res => {
              resolve(true);
              showSucNotification('操作成功~');
              onFetchFormulaList();
              handleRestSelected();
            },
          }).finally(() => resolve(false));
        }),
    });
  };

  const handleCopyItem = () => {
    const { id } = selectedRows[0] || {};
    setCopyLoading(true);
    fetchApi({
      ...payProcessApis.formulaTempCopy,
      data: {
        id,
      },
      onSuccess: res => {
        showSucNotification('操作成功~');
        onFetchFormulaList();
      },
    }).finally(() => setCopyLoading(false));
  };

  const actionBtnItems = useMemo(
    () => [
      {
        key: 'add',
        content: '新增',
        type: 'button',
        config: {
          type: 'primary',
          onClick: handleAdd,
        },
      },
      {
        key: 'add',
        content: '复制',
        type: 'button',
        config: {
          type: 'primary',
          onClick: handleCopyItem,
          loading: copyLoading,
          disabled: selectedKeys.length === 0,
        },
      },
      {
        key: 'del',
        content: '删除',
        type: 'button',
        config: {
          type: 'primary',
          onClick: handleDel,
          disabled: selectedKeys.length === 0,
        },
      },
      {
        key: 'del',
        content: '设置默认',
        type: 'button',
        config: {
          type: 'primary',
          onClick: handleSetDefault,
          disabled: selectedKeys.length === 0,
        },
      },
    ],
    [selectedKeys, selectedRows]
  );

  return (
    <div>
      <Spin spinning={loading}>
        <WULIWholeTable
          canSelect
          rowKey="id"
          size="small"
          data={tableList}
          columns={columns}
          selectType="radio"
          ref={editAgGridRef}
          action={actionBtnItems}
          paginationConfig={{
            size: 'small',
          }}
          onSelect={(newSelectedRow, isSelect, selectKeys, selectRows, c) => {
            setSelectedKeys(selectKeys);
            setSelectedRows(selectRows);
          }}
        />
      </Spin>
    </div>
  );
};

export default SalaryFormulaList;
