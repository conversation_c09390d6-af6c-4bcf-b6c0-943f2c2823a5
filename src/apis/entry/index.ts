const hrFrontBaseUrl = '/apis/common/proxy/lcpGw/tz_api/hr_frontend_api';

const apis: IAPI = {
  entryProcessInfo: {
    url: '/admin/v1/staffing/entry/wf/:entryProcessId/uc_info',
    method: 'get',
  },
  /** 入职活动更新 */
  entryActProcessUpdate: {
    url: '/admin/v1/act_prj_process_item/:prjProcessItemId/complete',
    method: 'post',
  },
  /** 体检预约日期查询 */
  entryPhysical: {
    url: '/admin/v1/staffing/entry/physical/schedule',
    method: 'get',
  },
  /** html字符串生成pdf */
  htmlPdf: {
    method: 'post',
    url: '/api/v1/html/pdf/generate',
    baseURL: hrFrontBaseUrl,
  },
};

export default apis;
