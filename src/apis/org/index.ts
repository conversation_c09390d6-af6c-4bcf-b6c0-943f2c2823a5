const apis: IAPI = {
  // 获取组织架构列表信息
  orgUnitLsit: {
    url: '/admin/v1/org_unit/list',
    method: 'get',
  },
  // 获取法人组织架构列表信息
  orgUnitLaborList: {
    url: '/admin/v1/org_unit/labor/list',
    method: 'get',
  },
  orgSort: {
    url: '/admin/v1/org_unit_relation/sort_no',
    method: 'post',
  },
  /**
   * 版本更新
   * keyType 可选值：create(创建)/adjust(调整)
   * formClassId 表单classId
   */
  orgPositionUpdate: {
    method: 'post',
    url: '/admin/v1/common/version/form/:formClassId/:keyType',
  },
};

export default apis;
