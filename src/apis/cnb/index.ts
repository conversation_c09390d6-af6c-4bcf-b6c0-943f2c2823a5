/// <reference path="../../../index.d.ts" />
const apis: IAPI = {
  /** 获取组织架构列表 */
  dimList: {
    url: '/admin/v1/org_unit_relation/def_function',
    method: 'get',
  },
  /** 查询数据集分类以及数据集 */
  infosetClassifyList: {
    url: '/admin/v1/infoset/classify/tree',
    method: 'get',
  },
  /** 查询函数分类以及函数 */
  functionClassifyList: {
    url: '/admin/v1/function/classify/tree',
    method: 'get',
  },
  /** 查询薪资项目分类以及函数 */
  cnbItemClassifyList: {
    url: '/admin/v1/cnb/item/category/tree',
    method: 'get',
  },
  /** 获取数据库中指定表的字段元数据 */
  metaDataColumn: {
    url: '/admin/v1/database/meta_data/column',
    method: 'get',
  },
  /** 查询薪资期间列表 */
  periodList: {
    url: '/admin/v1/cnb/period/list',
    method: 'get',
  },
  /** 查询薪资日历列表 */
  calendarList: {
    url: '/admin/v1/cnb/calendar/list',
    method: 'get',
  },
  /** 薪资档案列表 */
  cnbArchivesList: {
    url: '/admin/v1/cnb/archives',
    method: 'get',
  },
  /** 根据员工ID获取员工当前的薪酬档案信息 */
  cnbArchivesDetail: {
    url: '/admin/v1/cnb/archives/:empId',
    method: 'get',
  },
  /** 根据员工ID获取员工薪资组变动历史 */
  cnbArchivesGroupHistory: {
    url: '/admin/v1/cnb/archives/:empId/group/his',
    method: 'get',
  },
  /** 根据员工ID获取员工固定薪资项目变动历史 */
  cnbArchivesStructureHistory: {
    url: '/admin/v1/cnb/archives/:empId/structure/his',
    method: 'get',
  },
  /** 根据发放过程ID获取计算人员分页数据 */
  payEmpProcess: {
    url: '/admin/v1/cnb/pay_emp/:processId',
    method: 'get',
  },
  /** 根据工资套ID获取工资套详情 */
  salarySetDetail: {
    url: '/admin/v1/cnb/salary_set/:id',
    method: 'get',
  },
  // 获取薪资组管理详情
  groupEmpDetail: {
    url: '/admin/v1/cnb/group_emp/:id',
    method: 'get',
  },
  // 获取固定薪资结构
  structureEmpList: {
    url: '/admin/v1/cnb/structure_emp',
    method: 'get',
  },
  // 获取固定薪资结构详情
  structureEmpDetail: {
    url: '/admin/v1/cnb/structure_emp/:id',
    method: 'get',
  },
  /** 公式校验 */
  expresstionValidate: {
    url: '/admin/v1/calculator/expression/valid',
    method: 'post',
  },
  /** 根据工资套与薪资项目获取默认公式信息 */
  getDefaultFormala: {
    url: '/admin/v1/formula/get_def',
    method: 'get',
  },
  /** 重新计算发薪人员的工资条 */
  empReCalculate: {
    url: '/admin/v1/cnb/salaryslip/emp/re_calculate',
    method: 'post',
  },
  /** 获取工资套列表 */
  salarySetList: {
    url: '/admin/v1/cnb/salary_set/newest/list',
    method: 'get',
  },
};

export default apis;
