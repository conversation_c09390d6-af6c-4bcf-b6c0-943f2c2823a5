const apis: IAPI = {
  /** 根据发放过程ID获取计算人员分页数据 */
  payEmpProcess: {
    url: '/admin/v1/cnb/pay_emp/:processId',
    method: 'get',
  },
  /** 根据工资套ID获取工资套详情 */
  salarySetDetail: {
    url: '/admin/v1/cnb/salary_set/:id',
    method: 'get',
  },
  /** 重新初始化 */
  reInit: {
    url: '/admin/v1/cnb/pay_process/re_init/:processId',
    method: 'post',
  },
  /** 删除算薪过程 */
  delProgress: {
    url: '/admin/v1/cnb/pay_process/:processId',
    method: 'delete',
  },
  /** 重新计算 */
  relCalc: {
    url: '/admin/v1/cnb/pay_record/re_cal',
    method: 'post',
  },
  /** 根据过程ID，获取可以追加的员工分页信息 */
  getAddEmp: {
    url: '/admin/v1/cnb/pay_emp/add_emp/:processId',
    method: 'get',
  },
  /**  追加员工到算薪过程 */
  addEmpToProgress: {
    url: '/admin/v1/cnb/pay_emp/add_emp/:processId',
    method: 'post',
  },
  /** 从算薪过程中移除员工 */
  removeEmpFromProgress: {
    url: '/admin/v1/cnb/pay_emp/del_emp/:processId',
    method: 'post',
  },
  /** 创建算薪过程 */
  createProgress: {
    url: '/admin/v1/cnb/pay_process',
    method: 'post',
  },
  /** 保存重新计算的修改 */
  saveRelCalc: {
    url: '/admin/v1/cnb/pay_record/:processId/save_temp_value',
    method: 'post',
  },
  /** 生成备注 */
  createRemark: {
    url: '/admin/v1/cnb/pay_emp/remark/:processId',
    method: 'post',
  },
  /** 根据算薪过程ID获取算薪过程详情 */
  payProcessDetail: {
    url: '/admin/v1/cnb/pay_process/:processId',
    method: 'get',
  },
  /** 获取公式设置列表 */
  formulaList: {
    url: '/admin/v1/formula',
    method: 'get',
  },
  /** 删除公式 */
  formulaDel: {
    url: '/admin/v1/formula/:id',
    method: 'delete',
  },
  /** 公式设置为默认值 */
  formulaDefaultSet: {
    url: '/admin/v1/formula/set_def/:id',
    method: 'post',
  },
  /** 更新临时的值 */
  formulaTempValUpdate: {
    url: '/admin/v1/cnb/pay_record/:processId/update_temp_value',
    method: 'post',
  },
  /** 复制公式 */
  formulaTempCopy: {
    url: '/admin/v1/formula/copy/:id',
    method: 'post',
  },
};

export default apis;
