// 天舟云的通用前缀
const lcpCsbUrl = '/apis/common/proxy/csbUrl';
const lcpAppUrl = '/apis/common/proxy/lcpApp';
const systemBaseUrl = '/apis/common/proxy/lcpCore';
const lcpDataUrl = '/apis/common/proxy/lcpGw/tz_api/dhr_api/data';
const apis: IAPI = {
  /** 获取天舟云的apiCongiMap数据 */
  apiCongiMapData: {
    url: '/app/common/api-config/:classId',
    method: 'get',
    baseURL: systemBaseUrl,
  },
  /** 获取天舟云表单详情 */
  formDetailInfo: {
    url: '/openapi/:appId/:formClassId/:id',
    method: 'get',
    baseURL: lcpDataUrl,
  },
  /** 获取天舟云表单列表 */
  formTableList: {
    url: '/openapi/:appId/:formClassId/search',
    method: 'post',
    baseURL: lcpDataUrl,
  },
  /** 新增表单数据天舟云表单数据 */
  newFormData: {
    url: '/openapi/:appId/:formClassId/:keyType',
    method: 'post',
    baseURL: lcpDataUrl,
  },
  /** 新增表单数据天舟云表单数据 */
  updateFormData: {
    url: '/openapi/:appId/:formClassId/:id/:keyType',
    method: 'put',
    baseURL: lcpDataUrl,
  },
  /** 获取天舟云角色权限树 */
  rolePermissionTreeData: {
    url: '/role/:id/func_permission',
    method: 'get',
    baseURL: lcpCsbUrl,
  },
  // 获取角色按钮权限
  csbViews: {
    method: 'get',
    baseURL: lcpCsbUrl,
    url: '/menu/page/code/:code/auth_button',
  },
  /** 获取天舟云表单列表 - 携带权限的 */
  tzFormDataList: {
    url: '/v1/list/view/render',
    method: 'post',
    baseURL: lcpAppUrl,
  },
  /** 地址搜索 */
  addressSearch: {
    url: '/admin/v1/administration/area/analyzer',
    method: 'get',
  },
  /** 根据经纬度获取地址信息 */
  getLnglatAddress: {
    url: '/admin/v1/administration/area/inverse_geocode',
    method: 'get',
  },
  /** 流程审批提交 */
  flowAuditSubmit: {
    method: 'post',
    baseURL: systemBaseUrl,
    url: '/v1/app/openapi/workflow/approveProcess',
  },
  /**
   * 天舟云表单数据更新
   * keyType  可选值：UPPER(大写)/LOWER(小写)/CAMEL(驼峰)
   * id 页面id
   * 组装简单 - 弊端：需要排除虚拟字段
   * 注意：需要去掉 虚拟字段
   * */
  formDataUpdate: {
    method: 'put',
    baseURL: lcpDataUrl,
    url: '/openapi/:appId/:formClassId/:id/:keyType',
  },
  /**
   * 天舟云表单数据更新
   * keyType  可选值：UPPER(大写)/LOWER(小写)/CAMEL(驼峰)
   * id 页面id
   *  组装结构稍微复杂点
   * */
  formDataUpdateApp: {
    method: 'put',
    baseURL: lcpAppUrl,
    url: '/v1/app/openapi/form/:formClassId',
  },
  /**
   * 天舟云表单 批量创建
   * 流程与非流程表单 创建 不区分
   */
  formDateBatchCreate: {
    method: 'post',
    baseURL: lcpAppUrl,
    url: '/v1/app/openapi/form/:formClassId/batch',
  },
  /** 附件上传成功 - 回调到天舟云 */
  fileCallback: {
    method: 'post',
    baseURL: systemBaseUrl,
    url: '/obj-file/callback',
  },
  // 字典列表
  dictList: {
    method: 'post',
    baseURL: systemBaseUrl,
    url: '/dict/batch_dict_list',
  },
  // 流程表单提交
  flowFormSubmit: {
    method: 'post',
    baseURL: lcpAppUrl,
    url: '/v1/app/openapi/form/:formClassId',
  },
};

export default apis;
