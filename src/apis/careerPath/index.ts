const apis: IAPI = {
  // 保存\更新职级通道详细信息
  careerPathSave: {
    url: '/admin/v1/career_path/save',
    method: 'post',
  },
  // 获取职级通道详情
  careerPathDetail: {
    url: '/admin/v1/career_path/:careerPathId',
    method: 'get',
  },
  salaryStandardList: {
    url: '/admin/v1/salary_standard/list/:careerPathId',
    method: 'get',
  },
  salaryStandardEmptyTpl: {
    url: '/admin/v1/salary_standard/:careerPathId/grade',
    method: 'get',
  },
  // 获取职级标准
  careerStandardDetail: {
    url: '/admin/v1/model/:careerPathId/model_detail/table',
    method: 'get',
  },
  // 职级标准 - 相关资料附件列表
  standardAttachmentList: {
    url: '/admin/v1/model/:modelId/attachment',
    method: 'get',
  },
  // 职级标准 - 相关资料上传附件
  standardAttachmentUpload: {
    url: '/admin/v1/model/:modelId/attachment/batch',
    method: 'post',
  },
  // 职级标准 - 删除相关资料上传附件
  standardAttachmentDel: {
    url: '/admin/v1/model/:modelId/attachment/:csbFileId',
    method: 'delete',
  },
  // 职级标准 - 获取历史附件
  careerStandardHistoryFiles: {
    url: '/admin/v1/model/:modelId/change_history',
    method: 'get',
  },
  // 职级标准 - 获取能力项
  careerStandardItemAbility: {
    url: '/admin/v1/model/:modelId/model/item',
    method: 'get',
  },
  // 职级标准 -  设置能力项
  careerStandardItemAbilityUpdate: {
    url: '/admin/v1/model/:careerPathId/model/:modelId/item',
    method: 'post',
  },
  //  职级标准 -  导入模板校验
  standardImportCheck: {
    url: '/admin/v1/model/:modelId/model/import_check',
    method: 'post',
  },
  //  职级标准 -  导入模板
  standardImport: {
    url: '/admin/v1/model/:modelId/model/import',
    method: 'post',
  },
  // 职级标准 - 获取excel内容
  getModalDetailExcel: {
    url: '/admin/v1/model/:careerPathId/model_detail/excel',
    method: 'get',
  },
};

export default apis;
