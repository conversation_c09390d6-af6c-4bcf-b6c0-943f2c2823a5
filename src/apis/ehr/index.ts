const ehrBaseUrl = '/apis/common/proxy/lcpGw/tz_api/ehr_api';
const handleConvertType = ({ url }) => {
  const env = window?.ENV?.value || '';
  const addPrefixKeys = ['dev', 'fat', 'uat'];
  const prefixUrl = addPrefixKeys.includes(env) ? '/admin' : '';
  return `${prefixUrl}/eex-hcm-admin-backend${url}`;
};
const apis: IAPI = {
  // 员工基本信息
  employeeBaseInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/base_info',
  },
  // 任职信息
  employeeBaseInfoJob: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/base_info/current_job',
  },
  // 入职信息
  employeeEntryInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/base_info/entry_info',
  },
  // 员工简介信息
  employeeSummary: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/summary/dhr',
  },
  // 绩效结果信息
  performanceInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/performance',
  },
  // 内部流程信息
  turnoverInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/work_experience/turnover',
  },
  // 外部经历(社会工作履历信息)
  socialResumeInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/work_experience/social_resume',
  },
  // 获取关键人才信息
  employeeExpert: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/expert_info',
  },
  // 末位访谈
  finalInterviewInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/talks/last',
  },
  // 日常访谈
  dailyInterviewInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/talks/daily',
  },
  // 上任访谈
  inductionInterviewInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/talks/application',
  },
  // 试用期访谈
  probationInterviewInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/talks/probation',
  },
  // 见习期转正访谈
  talentProbationDefenceInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/talks/probation_defence',
  },
  // 定岗访谈数据
  talentDefineJobInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/talks/define_job',
  },
  // 人才盘点访谈数据
  talentTalksInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/talks/talent',
  },
  // 获取继任地图
  talentMapInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/successors',
  },
  // 获取培训记录
  trainRecordInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/management_training',
  },
  // 人才盘点 - 360能力报告
  talentInventoryInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/talent_inventory',
  },
  // 关键事项查询
  talentKeyItemInfo: {
    method: 'get',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/key_issues',
  },
  //人才档案权限
  talentFilePermission: {
    method: 'post',
    baseURL: ehrBaseUrl,
    url: '/api/v1/staffing/:employeeId/permission',
  },
};

Object.keys(apis).forEach(key => {
  apis[key] = {
    ...apis[key],
    url: handleConvertType(apis[key]),
  };
});

export default apis;
