const apis: IAPI = {
  // 离职交接数据列表
  handoverList: {
    url: '/admin/v1/staffing/exit/handover/list',
    method: 'get',
  },
  // 住宿交接数据列表
  dmsHandover: {
    url: '/admin/v1/staffing/exit/handover/dms',
    method: 'get',
  },
  // 离职当月工资测算
  handoverCalSalary: {
    url: '/admin/v1/staffing/exit/handover/cal/:empId/salary',
    method: 'get',
  },
  //离职年假测算
  handoverCalAnnual: {
    url: '/admin/v1/staffing/exit/handover/cal/:empId/annual',
    method: 'get',
  },
  //离职竞业限制测算
  handoverCalCompete: {
    url: '/admin/v1/staffing/exit/handover/cal/:empId/compete',
    method: 'get',
  },
  //离职补偿信息
  handoverCalCompensate: {
    url: '/admin/v1/staffing/exit/handover/cal/:empId/compensate',
    method: 'get',
  },
  //社保公积金信息
  handoverCalSecurity: {
    url: '/admin/v1/staffing/exit/handover/cal/:empId/security',
    method: 'get',
  },
  // /admin/v1/staffing/exit/handover/eam/handover
  handoverEamHandover: {
    url: '/admin/v1/staffing/exit/handover/eam/handover',
    method: 'post',
  },
  // 发起oa交接
  handoverOaHandover: {
    url: '/admin/v1/staffing/exit/handover/oa/handover',
    method: 'post',
  },
  // /admin/v1/staffing/exit/handover/fee/{domainCode}/update
  handoverFeeUpdate: {
    url: '/admin/v1/staffing/exit/handover/fee/:domainCode/update',
    method: 'post',
  },
};

export default apis;
