const apis: IAPI = {
  /** 工资条 - 第一步 - 查询模板基本信息 */
  salarySlipBaseInfo: {
    url: '/admin/v1/salary_slip/tpl/:id',
    method: 'get',
  },
  /** 工资条 - 第一步 - 保存模板基本信息 */
  salarySlipBaseSave: {
    url: '/admin/v1/salary_slip/tpl',
    method: 'post',
  },
  /** 工资条 - 第二步 - 查询所有项目表/子集 */
  salaryTplAllList: {
    url: '/admin/v1/salary_slip/tpl/table/list/item/all',
    method: 'get',
  },
  /** 工资条 - 第二步/第三步 - 查询项目表 */
  salarySlipTplList: {
    url: '/admin/v1/salary_slip/tpl/table/list',
    method: 'get',
  },
  /** 工资条 - 第二步 - 创建/更新项目表 */
  salarySlipTplTableUpdate: {
    url: '/admin/v1/salary_slip/tpl/table',
    method: 'post',
  },
  /** 工资条 - 第二步 - 删除项目表 */
  salarySlipTplTableDel: {
    url: '/admin/v1/salary_slip/tpl/table/:id',
    method: 'delete',
  },
  /** 工资条 - 第二步 - 查询项目表里的项目列表 */
  salarySlipTplTableItemList: {
    url: '/admin/v1/salary_slip/tpl/table/item/list',
    method: 'get',
  },
  /** 工资条 - 第二步 - 删除项目表的项目list */
  salarySlipTplTableItemDel: {
    url: '/admin/v1/salary_slip/tpl/table/item/:id',
    method: 'delete',
  },
  /**  工资条 - 第二步 - 创建/更新项目表的项目 */
  salarySlipTplTableItemUpdate: {
    url: '/admin/v1/salary_slip/tpl/table/item',
    method: 'post',
  },
  /** 工资条 - 第二步/第三步 - 模板下的表预览 */
  payrollTplAllPreview: {
    url: '/admin/v1/salary_slip/tpl/preview',
    method: 'post',
  },
  /**  工资条 - 第三步 - 查询工资条表关联条件 */
  salarySlipConditionList: {
    url: '/admin/v1/salary_slip/tpl/condition/list',
    method: 'get',
  },
  /** 工资条 - 第三步 - 保存/更新人员关联条件和通知配置 */
  salarySlipConditionUpdate: {
    url: '/admin/v1/salary_slip/tpl/condition',
    method: 'post',
  },
  /** 工资条 -获取最新的工资套列表 */
  payrollList: {
    url: '/admin/v1/cnb/salary_set/newest/list',
    method: 'get',
  },
  /** 工资条 -获取薪资项目下拉列表 */
  payrollItemList: {
    url: '/admin/v1/cnb/item/list',
    method: 'get',
  },
  /** 工资条 -获取信息集下拉列表 */
  payrollInfosetList: {
    url: '/admin/v1/infoset/list',
    method: 'get',
  },
  /** 工资条 -获取固定薪资下拉列表 */
  payrollStructureList: {
    url: '/admin/v1/cnb/structure/list',
    method: 'get',
  },
  // 获取员工状态下拉列表
  payrollEmpStatusList: {
    url: '/admin/v1/staffing/emp_status/list',
    method: 'get',
  },
  // 获取薪资组下拉列表
  payrollGroupList: {
    url: '/admin/v1/cnb/group/list',
    method: 'get',
  },
  // 工资条模版下拉列表
  salarySlipTpl: {
    url: '/admin/v1/salary_slip/tpl/list',
    method: 'get',
  },
  // 工资条发放列表
  salaryslipPublishList: {
    url: '/admin/v1/cnb/salaryslip/list',
    method: 'get',
  },
  // 算薪人员列表
  salaryslipEmpPayList: {
    url: '/admin/v1/cnb/salaryslip/emp_pay/list',
    method: 'get',
  },
  // 工资条发放列表
  salaryslipPublish: {
    url: '/admin/v1/cnb/salaryslip/publish',
    method: 'post',
  },
  // 工资条通知发送详情列表
  salaryslipNotifyList: {
    url: '/admin/v1/cnb/salaryslip/notification/list',
    method: 'get',
  },
  // 工资条通知重发
  salaryslipNotifyResend: {
    url: '/admin/v1/cnb/salaryslip/notify/resend',
    method: 'post',
  },
  // 工资条模板 - 拖动排序
  salarySlipTplMoveSort: {
    url: '/admin/v1/salary_slip/tpl/table/item/move_sort',
    method: 'post',
  },
  // 测试工资条发放
  salaryslipTest: {
    url: '/admin/v1/cnb/salaryslip/send/test',
    method: 'post',
  },
};

export default apis;
