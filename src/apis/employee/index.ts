const itBaseUrl = '/apis/common/proxy/lcpGw/tz_api/it_apis';
const apis: IAPI = {
  /** 获取员工基础信息 */
  employeeBaseInfo: {
    url: '/admin/v1/staffing/emp/:empId/basic_info',
    method: 'get',
  },
  employeeAvatar: {
    method: 'get',
    baseURL: itBaseUrl,
    url: '/cavatar/api/v1/avatar',
  },
  // 人才档案人员列表信息
  employeeTalentFileList: {
    method: 'get',
    url: '/admin/v1/staffing/info',
  },
  // 定岗发送通知
  notificationDefineSend: {
    method: 'post',
    url: '/admin/v1/staffing/defence/send_defence_notification',
  },
  // 定岗通知预览
  notificationPreview: {
    method: 'post',
    url: '/admin/v1/staffing/defence/preview_defence_notification',
  },
  // 指派答辩官
  appointDefenceOfficer: {
    method: 'post',
    url: '/admin/v1/staffing/defence/appoint_defence_officer',
  },
  // 重新指派答辩官
  reAppointDefenceOfficer: {
    method: 'post',
    url: '/admin/v1/staffing/defence/reappoint_defence_officer',
  },
};

export default apis;
