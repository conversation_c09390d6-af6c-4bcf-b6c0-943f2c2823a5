const apis: IAPI = {
  /** 保存工作日程表 */
  scheduleDetailSave: {
    method: 'post',
    url: '/admin/v1/tmg/schedule',
  },
  /** 预览工作日程明细 */
  calendarDetailPreview: {
    method: 'post',
    url: '/admin/v1/tmg/schedule_detail/:scheduleId/preview',
  },
  // 更新已编辑状态
  calendarDetailStatusUpdate: {
    method: 'post',
    url: '/admin/v1/tmg/schedule_detail/:scheduleDetailId/update_page_edit',
  },
  /** 更新工作日程班次信息 */
  scheduleDetailUpdate: {
    method: 'post',
    url: '/admin/v1/tmg/schedule_detail/:scheduleDetailId/update_shift',
  },
  /** 获取工作日程明细 */
  scheduleDetailInfo: {
    method: 'get',
    url: '/admin/v1/tmg/schedule_detail/:scheduleId',
  },
  /** 获取scheduleId */
  getScheduleId: {
    method: 'post',
    url: '/admin/v1/staffing/entry/wf/emp/schedule_calendar',
  },
  /** 获取人员定额核算列表 */
  quotaProcessEmpList: {
    method: 'get',
    url: '/admin/v1/tmg/quota_process/emp/:processId',
  },
  /** 定额核算-重新执行 */
  quotaProcessAgain: {
    method: 'post',
    url: '/admin/v1/tmg/quota_process/re_cal',
  },
  /** 班次时间分割 */
  scheduleEmp: {
    method: 'get',
    url: '/admin/v1/tmg/schedule_emp/emp/detail',
  },
  /** 获取人员考勤核算列表 */
  attendanceProcessEmpList: {
    method: 'get',
    url: '/admin/v1/tmg/attendance/process/emp/:processId',
  },
  /** 考勤核算-重新执行 */
  attendanceProcessAgain: {
    method: 'post',
    url: '/admin/v1/tmg/attendance/process/re_cal',
  },
  /** 考勤记录-日明细 */
  attendanceDailyRecordList: {
    method: 'get',
    url: '/admin/v1/tmg/attendance/daily_record',
  },
  /** 考勤记录-汇总期间数据 */
  attendanceSummaryRecordList: {
    method: 'get',
    url: '/admin/v1/tmg/attendance/summary_record',
  },
  /** 考勤封存-重新执行 */
  attendanceSealProcessAgain: {
    method: 'post',
    url: '/admin/v1/tmg/seal/process/re_cal',
  },
  /** 考勤封存-人员列表 */
  attendanceSealProcessEmpList: {
    method: 'get',
    url: '/admin/v1/tmg/seal/process/emp/:processId',
  },
  /** 考勤封存-汇总信息列表 */
  attendanceSealProcessRecordList: {
    method: 'get',
    url: '/admin/v1/tmg/seal/process/record/:processId',
  },
  /** 考勤记录-封存汇总数据 */
  attendanceSummarySealList: {
    method: 'get',
    url: '/admin/v1/tmg/seal/process/cnb_record',
  },
  // 请假流程数据
  leaveReportFlowList: {
    method: 'post',
    url: '/admin/v1/tmg/leave/data_report/flow/list',
  },
  // 请假流程数据（导出）
  leaveReportFlowExport: {
    method: 'post',
    url: '/admin/v1/tmg/leave/data_report/flow/list/export',
  },
  // 产假流程数据
  maternityLeaveFlowList: {
    method: 'post',
    url: '/admin/v1/tmg/leave/data_report/maternity/list',
  },
  // 假种数据
  absenceCategaryList: {
    method: 'get',
    url: '/admin/v1/tmg/leave_flow/emp/leave_category/list',
  },
  // 提前返岗数据报表
  maternityEearlyReturnWorkList: {
    method: 'post',
    url: '/admin/v1/tmg/leave/data_report/maternity/early_return_work/list',
  },
  // 提前返岗数据更新计算
  maternityEearlyReturnWorkUpdate: {
    method: 'post',
    url: '/admin/v1/tmg/leave/data_report/maternity/early_return_work/calculated',
  },
  // 提前返岗数据封存
  maternityEearlyReturnWorkSealed: {
    method: 'post',
    url: '/admin/v1/tmg/leave/data_report/maternity/early_return_work/sealed',
  },
  // 提前返岗数据解封
  maternityEearlyReturnWorkUnsealed: {
    method: 'post',
    url: '/admin/v1/tmg/leave/data_report/maternity/early_return_work//un_sealed',
  },
  // 查看进行中或者异常的提前返岗工资计算任务
  maternityEearlyReturnWorkTask: {
    method: 'get',
    url: '/admin/v1/tmg/leave/data_report/maternity/early_return_work/task',
  },
  // 查看进行中或者异常的提前返岗工资计算任务
  maternityEearlyReturnWorkTaskReExcute: {
    method: 'post',
    url: '/admin/v1/task/re_execute/:taskId',
  },
};

export default apis;
