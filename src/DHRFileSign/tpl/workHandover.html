<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>工作交接单</title>
  <style>
    @page {
      size: A4;
      margin: 0;
    }

    body {
      width: 210mm;
      height: 297mm;
      margin: 0 auto;
      padding: 20mm;
      box-sizing: border-box;
      font-family: SimSun, "宋体", serif;
      position: relative;
    }

    .title {
      text-align: center;
      font-size: 24pt;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .subtitle {
      text-align: center;
      font-size: 12pt;
      color: #ff0000;
      margin-bottom: 30px;
    }

    .main-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14pt;
    }

    .main-table th,
    .main-table td {
      border: 2px solid #000;
      padding: 8px;
      vertical-align: top;
    }

    .basic-info-label {
      background-color: #f0f0f0;
      font-weight: bold;
      width: 15%;
      text-align: center;
    }

    .basic-info-value {
      width: 35%;
      text-align: center;
    }

    .name-highlight,
    .dept-highlight,
    .position-highlight,
    .date-highlight {
      display: inline-block;
      background-color: #ffffcc;
      padding: 0 5px;
      min-width: 50px;
      text-align: center;
    }

    .section-title-cell {
      font-size: 16pt;
      font-weight: bold;
      text-align: center;
      background-color: #f0f0f0;
      padding: 12px;
    }

    .guidance-label {
      font-weight: bold;
      text-align: center;
      background-color: #f0f0f0;
      width: 15%;
      vertical-align: middle;
    }

    .guidance-cell {
      padding: 10px;
      text-align: left;
      width: 85%;
    }

    .guidance-content {
      font-size: 12pt;
      line-height: 1.8;
    }

    .content-header-cell {
      background-color: #f0f0f0;
      font-weight: bold;
      text-align: center;
      font-size: 14pt;
      padding: 8px;
    }

    .content-col {
      width: 50%;
      text-align: left;
    }

    .receiver-col,
    .supervisor-col {
      width: 25%;
      text-align: center;
    }

    .content-row {
      height: 40px;
      font-size: 12pt;
    }

    .signature-section {
      margin-top: 40px;
      text-align: right;
      font-size: 14pt;
    }

    .signature-line {
      display: inline-block;
      border-bottom: 1px solid #000;
      min-width: 200px;
      margin-left: 10px;
    }
  </style>
</head>

<body>
  <div class="title">工作交接单</div>
  <!-- <div class="subtitle">(注意：该模版仅供参考，不强制要求使用，亦可上传自己的交接文档)</div> -->

  <table class="main-table">
    <!-- 基本信息：2x2布局 -->
    <tr>
      <td class="basic-info-label">姓名</td>
      <td class="basic-info-value"><span class="name-highlight">{{ name }}</span></td>
      <td class="basic-info-label">部门</td>
      <td class="basic-info-value"><span class="dept-highlight">{{ department }}</span></td>
    </tr>
    <tr>
      <td class="basic-info-label">岗位</td>
      <td class="basic-info-value"><span class="position-highlight">{{ position }}</span></td>
      <td class="basic-info-label">离职交接日期</td>
      <td class="basic-info-value"><span class="date-highlight">{{ handoverDate }}</span></td>
    </tr>

    <!-- 交接情况标题 -->
    <tr>
      <td class="section-title-cell" colspan="4">交接情况</td>
    </tr>

    <!-- 填写指引 -->
    <tr>
      <td class="guidance-label">填写指引</td>
      <td class="guidance-cell" colspan="3">
        <div class="guidance-content">
          <div>1.监交人可以是接收人的上级，亦可与接收人是同一人；</div>
          <div>2.内容可写关于交接的、符合进的、未完结的工作项目；</div>
          <div>3.如无相关内容请写"无"；</div>
          <div>4.填写如有疑问请联系人事HR。</div>
        </div>
      </td>
    </tr>

    <!-- 工作交接内容表头 -->
    <tr>
      <td class="content-header-cell" colspan="2">工作交接内容</td>
      <td class="content-header-cell">接收人</td>
      <td class="content-header-cell">监交人</td>
    </tr>

    <!-- 动态数据行 -->
    {% for item in handoverItems %}
    <tr class="content-row">
      <td colspan="2">{{ item.content }}</td>
      <td style="text-align: center;">{{ item.receiver }}</td>
      <td style="text-align: center;">{{ item.supervisor }}</td>
    </tr>
    {% endfor %}
  </table>

  <div class="signature-section">
    移交人签字：<span class="signature-line"></span>
  </div>
</body>

</html>