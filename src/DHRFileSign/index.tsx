import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import { IGlobalProps } from '../types/index.d';
import { WULI_MODE } from '@constants/common';
import { SIGN_FILE_TYPE } from '@constants/leave';
import useTzTableList from '@hooks/useTzTableList';
import JobHandoverModal from './components/JobHandoverModal';

import useAttachment, { ApiConfigParams } from '@hooks/useAttachment';
import { showErrNotification } from '@utils/tools';
import nunjucks from 'nunjucks';
import { request as fetchApi } from '@utils/http';
import signApis from '@apis/sign';
import entryApis from '@apis/entry';
import QRCode from 'react-qr-code';
import moment from 'moment';
import './style.less';

export interface FileSignInfo {
  fileId: string;
  fileName: string;
}

// 缓存签署token信息
interface ICacheSignTokenInfo {
  signToken: string;
  fileId: string;
  signType: string;
  signUrl: string;
}

const SIGN_TOKEN_KEY = 'dhr:sign:token';
// 扩展 IGlobalProps 类型
export interface IAppProps extends IGlobalProps {
  apiConfigMap?: ApiConfigParams;
}

const DHRFileSign: React.FC<IAppProps> = props => {
  const { configs, onChange, value } = props;
  // console.log('apiConfigMap===', props);
  const { wuliMode } = configs;

  const apiConfigMap = configs.context?.apiConfigMap;
  console.log('apiConfigMap===', apiConfigMap);
  const [modalVisible, setModalVisible] = useState(false);
  const { dictConfig = {} } = configs.config?.baseConfig || {};
  const [loading, setLoading] = useState(false);
  const [pdfLoading, setPdfLoading] = useState(false);
  const [cacheSignTokenInfo, setCacheSignTokenInfo] = useState<ICacheSignTokenInfo | null>(null);
  const [jobHandoverModalVisible, setJobHandoverModalVisible] = useState(false);
  const modalVisibleRef = useRef(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    modalVisibleRef.current = modalVisible;
  }, [modalVisible]);

  // 获取当前类型模版
  const { runAction: fetchTplList } = useTzTableList({
    // 模版表单
    appId: '96dd1d629bb24193bb8630c9dcb61aad',
    formClassId: 'af2bd6a60a2f4ff9bcac07b89be94a65',
    immediate: false,
  });

  const { type: fileType, typeName: fileTypeName, buttonText, dataTpl } = dictConfig;
  const { onUploadAttachment, onUpdateApiCongiMap, getDownloadUrl, onPreview, onDownload } = useAttachment({
    manual: true,
  });
  console.log('fileType===', fileType);
  console.log('dictConfig===', dictConfig);

  const isView = wuliMode === WULI_MODE.VIEW;

  // 加载附件服务配置
  useEffect(() => {
    apiConfigMap && onUpdateApiCongiMap(apiConfigMap);
  }, [apiConfigMap]);

  // 生成模板数据
  const genTplData = (dataTpl: string) => {
    const formData = configs.context?.getFormData();
    // const { dataTpl } = dictConfig;
    // 从传入的dictConfig中获取dataTpl，如果存在则使用，否则使用默认模板
    // const dataTpl1 = `
    //   return {
    //     name: data.name,
    //     idNumber: data.idNumber,
    //     date: moment().format('YYYY年MM月DD日'),
    //   }
    // `;
    const tplData = new Function('data', 'moment', dataTpl)(formData, moment);
    return tplData;
  };

  // 将HTML转换为PDF并上传
  const generatePdfAndUpload = async (tplContent: string, _tplData?: Record<string, any>) => {
    try {
      console.log('生成PDF并上传===');
      // 使用nunjucks渲染模板
      const tplData = _tplData || genTplData(dataTpl);
      const renderedHtml = nunjucks.renderString(tplContent, tplData);
      console.log('renderedHtml===', renderedHtml);

      // 调用API生成PDF
      const response = await fetchApi({
        ...entryApis.htmlPdf,
        data: {
          html: renderedHtml,
        },
      });

      // 将response.data转换为Uint8Array
      const uint8Array = new Uint8Array(response.data);
      const blob = new Blob([uint8Array], { type: 'application/pdf' });
      const file = new File([blob], `签署文件.pdf`, {
        type: 'application/pdf',
      });

      // 上传到附件服务
      const fileData = await onUploadAttachment(file, { configs });
      console.log('fileData===', fileData);
      const fileId = fileData?.fileIds?.[0] || '';

      if (!fileId) {
        throw new Error('文件上传失败');
      }
      // 构建文件信息
      const fileInfo: FileSignInfo = {
        fileId,
        fileName: '签署文件.pdf',
        fileUrl: getDownloadUrl(fileId),
      };

      return fileInfo;
    } catch (error) {
      console.error('PDF生成或上传失败:', error);
      showErrNotification('PDF生成或上传失败');
      return null;
    }
  };

  // 获取模版列表
  const getTplList = async () => {
    return await fetchTplList({
      pageSize: 100,
      onlyMain: true,
      keyType: 'CAMEL',
      mainParamsGroups: [
        {
          paramsList: [
            {
              operator: '=',
              attrApi: 'C_TYPE',
              value: fileType,
            },
          ],
        },
      ],
    });
  };

  // 轮训获取签署状态
  const handleQrcodePolling = async (signToken: string) => {
    timerRef.current = setInterval(async () => {
      console.log('轮训获取签署状态');
      const signDocumentLogQueryRes = await fetchApi({
        ...signApis.signDocumentQuery,
        params: {
          token: signToken,
        },
      });
      console.log('signDocumentLogQueryRes===', modalVisible, signDocumentLogQueryRes);
      if (signDocumentLogQueryRes?.signFileId) {
        clearInterval(timerRef.current);
        // 如果弹窗处于打开状态，则关闭
        if (modalVisibleRef.current) setModalVisible(false);

        // 更新value
        const signedFileInfo: FileSignInfo = {
          fileId: signDocumentLogQueryRes?.signFileId,
          fileName: fileTypeName,
        };
        // setSignedFileInfo(signedFileInfo);
        // 更新value
        onChange?.(JSON.stringify(signedFileInfo));
      }
    }, 5000);
  };

  useEffect(() => {
    //页面进来，判断是否需要轮训
    const cacheSignTokenInfo = sessionStorage.getItem(`${SIGN_TOKEN_KEY}:${fileType}`);
    if (fileType && cacheSignTokenInfo) {
      const cacheSignTokenInfoObj = JSON.parse(cacheSignTokenInfo);
      // 只有当前类型和token都存在，才进行轮训
      if (cacheSignTokenInfoObj.signToken && cacheSignTokenInfoObj.signType === fileType) {
        // 设置缓存
        setCacheSignTokenInfo(cacheSignTokenInfoObj);
        // 开启轮训
        handleQrcodePolling(cacheSignTokenInfoObj.signToken);
      }
    }
  }, [fileType]);

  const handleQrcodeGenerate = async (fileId: string) => {
    if (fileId) {
      const userInfo = configs.context.getContext()?.session?.user ?? {};

      console.log('userInfo======', userInfo);
      // 创建签署记录
      const signToken = await fetchApi({
        ...signApis.signDocumentLog,
        data: {
          signType: fileType,
          fileId,
          empId: userInfo.id,
        },
      });
      if (!signToken) return showErrNotification('获取签署信息失败');
      // 设置二维码URL
      const qrCodeUrl = `https://m-dhr-dev.gz.cvte.cn/pages/file_sign?fileUrl=${getDownloadUrl(
        fileId
      )}&token=${signToken}&fileName=${userInfo.name}-${fileTypeName}`;
      // setQrCodeUrl(qrCodeUrl);
      console.log('signDocumentLogRes===1111', signToken);
      const cacheSignTokenInfo: ICacheSignTokenInfo = {
        signToken,
        fileId,
        signType: fileType,
        signUrl: qrCodeUrl,
      };
      // 将token存储到sessionStorage,下次进来的时候直接获取
      sessionStorage.setItem(`${SIGN_TOKEN_KEY}:${fileType}`, JSON.stringify(cacheSignTokenInfo));
      // 设置缓存
      setCacheSignTokenInfo(cacheSignTokenInfo);
      // 开启轮训
      handleQrcodePolling(signToken);
      console.log('signDocumentLogRes===', signToken);
    }
  };

  // 获取模板并生成HTML内容
  const handleGetTplFile = async (tplData?: Record<string, any>) => {
    setLoading(true);
    try {
      console.log('获取模板文件');
      // 获取模版内容
      const tplList = await getTplList();
      console.log('模板列表:', tplList);
      const _tplContent = tplList?.[0]?.cContent;

      if (!_tplContent) {
        setLoading(false);
        return showErrNotification('没有找到模板内容');
      }

      // setTplContent(_tplContent);
      // 生成PDF并上传
      const fileInfo = await generatePdfAndUpload(_tplContent, tplData);
      console.log('fileInfo===', fileInfo);
      // 生成二维码,开启轮训
      fileInfo && handleQrcodeGenerate(fileInfo.fileId);
    } catch (error) {
      console.error('获取模板失败:', error);
      showErrNotification('获取模板失败');
    } finally {
      setLoading(false);
    }
  };

  // 关闭工作交接单弹窗
  const handleCancelJobHandoverModal = () => {
    setJobHandoverModalVisible(false);
  };

  // 确认工作交接单
  const handleConfirmJobHandover = (workHandoverData: any[]) => {
    setJobHandoverModalVisible(false);
    const formData = configs.context?.getFormData();
    const data = {
      handoverItems: workHandoverData,
      name: formData.C_EMP_NAME,
      department: formData.C_DEPT_FULL_NAME,
      position: formData.C_POSITION_NAME,
      handoverDate: moment().format('YYYY-MM-DD'),
    };
    handleGetTplFile(data);
    // 收集完数据后，打开扫码弹窗
    setModalVisible(true);
    console.log('工作交接数据=====', workHandoverData);
  };

  // 处理扫码签署特殊逻辑
  const handleScanSignSpecialLogic = () => {
    // // 若文件类型为CHECK_LIST，需要先弹出交接数据框，用户填完数据再生成pdf进行交接
    if (fileType === SIGN_FILE_TYPE.CHECK_LIST) {
      setJobHandoverModalVisible(true);
    }
  };

  // 处理扫码签署
  const handleScanSign = async () => {
    // 检查是否存在缓存
    const cacheSignTokenInfo = sessionStorage.getItem(`${SIGN_TOKEN_KEY}:${fileType}`);
    if (cacheSignTokenInfo) {
      const cacheSignTokenInfoObj = JSON.parse(cacheSignTokenInfo);
      // 只有当前类型和token都存在，才进行轮训
      if (cacheSignTokenInfoObj.signToken && cacheSignTokenInfoObj.signType === fileType) {
        setModalVisible(true);
        handleQrcodePolling(cacheSignTokenInfoObj.signToken);
        return;
      }
    }
    // 检验必填字段
    const validateFields = dictConfig?.validateFields;
    console.log('validateFields===', validateFields);
    const validateFieldsArr = validateFields?.split(',');
    const formData = configs.context?.getFormData();
    console.log('formData===', formData);

    const errorValidateFields = validateFieldsArr?.filter(field => !formData[field]).join(',');
    if (errorValidateFields) {
      showErrNotification(`请填写必填字段: ${errorValidateFields}`);
      return;
    }
    if ([SIGN_FILE_TYPE.CHECK_LIST].includes(fileType)) {
      // 特殊逻辑处理
      handleScanSignSpecialLogic();
      return;
    }
    // 校验通过，继续
    setModalVisible(true);
    handleGetTplFile();

    // if (validateFieldsArr?.length > 0) {
    //   const res11 = await configs.context.form.validateFields(validateFieldsArr);
    //   console.log('res11===', res11);
    //   // const formData = configs.context?.getFormData();
    //   // const validateFieldsObj = validateFieldsArr.map(field => {
    //   //   return {
    //   //     ...field,
    //   //   };
    //   // });
    // }

    console.log('validateFieldsArr===', validateFieldsArr);
    // setModalVisible(true);
    // handleGetTplFile();
  };

  // 关闭签署弹窗
  const handleCancelModal = () => {
    setModalVisible(false);
  };

  // 完成签署
  const handleCompleteSign = async () => {
    setPdfLoading(true);
    try {
      // 调用接口获取签署文件
      const signDocumentLogQueryRes = await fetchApi({
        ...signApis.signDocumentQuery,
        params: {
          token: cacheSignTokenInfo?.signToken,
        },
      });

      if (!signDocumentLogQueryRes?.signFileId) {
        Modal.warn({
          title: '提示',
          content: '签署还没完成噢!请签署完毕再点击获取签署文件',
        });
        return;
      }

      if (modalVisibleRef.current) setModalVisible(false);

      // 更新value
      const signedFileInfo: FileSignInfo = {
        fileId: signDocumentLogQueryRes?.signFileId,
        fileName: fileTypeName,
      };
      // setSignedFileInfo(signedFileInfo);
      // 更新value
      onChange?.(JSON.stringify(signedFileInfo));

      // 清除轮训
      timerRef.current && clearInterval(timerRef.current);
    } catch (error) {
      console.error('签署失败:', error);
      showErrNotification('签署失败');
    } finally {
      setPdfLoading(false);
    }
  };
  // 接收到签署推送
  const handlePushSign = () => {
    // 在实际应用中，这里可能需要实现推送二维码到用户手机的逻辑
    console.log('推送签署二维码');
  };
  // 删除签署文件
  const handleDelete = async (fileInfo: FileSignInfo) => {
    console.log('删除签署文件', fileInfo);
    Modal.confirm({
      title: '提示',
      content: '确定删除签署文件吗？删除后需要重新签署',
      onOk: async () => {
        // 删除本地缓存
        sessionStorage.removeItem(`${SIGN_TOKEN_KEY}:${fileType}`);
        // 清除缓存
        setCacheSignTokenInfo(null);
        // 删除value
        onChange?.(undefined);
      },
    });
  };

  // 渲染附件组件
  const renderAttachment = () => {
    if (!value) return null;
    console.log('value===', value);
    let fileInfo: FileSignInfo;
    try {
      fileInfo = JSON.parse(value);
      console.log('fileInfo===', fileInfo);
    } catch (error) {
      console.error('value转换失败=====:', error);
    }
    if (!fileInfo) return '附件加载中';
    return (
      <div className="dhr-file-attachment">
        <div className="attachment-info">
          <span className="file-name">{fileInfo.fileName || '已签署文件'}</span>
          <span
            onClick={() =>
              onPreview({
                fileId: fileInfo.fileId,
                fileName: fileInfo.fileName,
                fileType: 'application/pdf',
              })
            }
            className="attachment-action"
          >
            预览
          </span>
          <span onClick={() => onDownload({ fileId: fileInfo.fileId })} className="attachment-action">
            下载
          </span>
          <span onClick={() => handleDelete({ fileId: fileInfo.fileId })} className="attachment-action">
            删除
          </span>
        </div>
      </div>
    );
  };

  // 渲染签署弹窗内容
  const renderModalContent = () => {
    return (
      <Spin spinning={loading}>
        <div className="dhr-file-sign-modal">
          <header className="qrcode-container">
            {/* 这里应该显示实际的二维码图像 */}
            {cacheSignTokenInfo?.signUrl && (
              <div className="qrcode-placeholder">
                <QRCode value={cacheSignTokenInfo?.signUrl} />
              </div>
            )}
          </header>
          <section className="sign-tips" style={{ textAlign: 'center', margin: '20px 0' }}>
            <p>使用手机扫码签署后</p>
            <p>点击按钮即可获取签署文件</p>
          </section>
          <footer className="action-buttons" style={{ textAlign: 'center', marginBottom: '15px' }}>
            <Button type="primary" onClick={handleCompleteSign} style={{ marginRight: 10 }} loading={pdfLoading}>
              完成签署，点击获取签署文件
            </Button>
            <Button onClick={handlePushSign}>推送签署二维码</Button>
          </footer>
        </div>
      </Spin>
    );
  };

  const scanButtonText = cacheSignTokenInfo ? '等待签署' : buttonText || '扫码签署';
  return (
    <Spin spinning={loading || pdfLoading}>
      <div className="dhr-file-sign">
        {value ? (
          // 如果有value，显示附件组件
          <div>{renderAttachment()}</div>
        ) : (
          // 否则显示签署按钮
          !isView && (
            <Button type="primary" onClick={handleScanSign}>
              {scanButtonText}
            </Button>
          )
        )}
        <Modal title="扫码签署" open={modalVisible} onCancel={handleCancelModal} footer={null} width={400} centered>
          {renderModalContent()}
        </Modal>
        {/* 特殊逻辑---工作交接单 */}
        <JobHandoverModal
          visible={jobHandoverModalVisible}
          onCancel={handleCancelJobHandoverModal}
          onConfirm={handleConfirmJobHandover}
          tzContext={configs.context}
        />
      </div>
    </Spin>
  );
};

export default DHRFileSign;
