import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Modal, Button, message } from 'antd';
import { SingleTable, XiooTableActions } from '@cvte/xioo-ag-table';
import type { ITableInfo } from '@cvte/xioo-ag-table/es/SingleTable/type';
import EmployeeSelect from '../../DHRJobHandover/components/EmployeeSelect';
import EmpSelect from './EmpSelect';

interface IUserInfo {
  name?: string;
  department?: string;
  position?: string;
  exitDate?: string;
  manager?: string;
}

interface JobHandoverModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (data: any[]) => void;
  title?: string;
  // userInfo: IUserInfo;
  tzContext: any;
}

interface HandoverData {
  key: string;
  content: string;
  receiver: string;
  supervisor: string;
}

XiooTableActions.init({
  tableConfig: {
    // 配置自定义组件
    custEditorComponents: {
      EmpSelect,
    },
    // 配置自定义组件的ValueGetters，处理数据的翻译问题
    custEditorValueGetters: {
      EmpSelect: (params, column) => {
        const { data } = params;
        return data[`${column.columnNo}_Name`];
      },
    },
  },
});

const TABLE_ID = 'JOB_HANDOVER_TABLE';

const JobHandoverModal: React.FC<JobHandoverModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  title = '工作交接单',
  tzContext,
}) => {
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<IUserInfo>({});

  useEffect(() => {
    if (visible && tzContext) {
      const userInfo = tzContext.getFormData();
      setUserInfo({
        name: userInfo.C_EMP_NAME,
        department: userInfo.C_DEPT_FULL_NAME,
        position: userInfo.C_POSITION_NAME,
        exitDate: userInfo.C_EXIT_DATE,
        manager: userInfo.C_MANAGER_NAME,
      });
    }
  }, [visible, tzContext]);

  // 自定义渲染器 - 员工选择组件
  const employeeSelectRenderer = (field: string) => {
    const EmployeeRenderer = (params: any) => {
      const { value, data } = params;
      return (
        <EmployeeSelect
          value={value}
          placeholder="请选择员工"
          optionKey="empId"
          optionLabel="data.empOtherName-data.empDeptFullPathName"
          style={{ width: '100%' }}
          onChange={(newValue, option) => {
            // 存储中文名字而不是ID
            const tableRef = XiooTableActions.getTable(TABLE_ID);
            const rowNode = tableRef.getRowNode(data.key);
            rowNode.setData({
              ...rowNode.data,
              [field]: newValue,
            });
          }}
        />
      );
    };

    EmployeeRenderer.displayName = `EmployeeRenderer_${field}`;
    return EmployeeRenderer;
  };

  // 表格配置
  const tableInfo: ITableInfo = useMemo(
    () => ({
      dvProgNo: TABLE_ID,
      columnsList: [
        {
          columnName: '工作交接内容',
          columnNo: 'content',
          flex: 2,
          isEdit: true,
          columnType: 'input',
          required: true,
        },
        {
          columnName: '接收人',
          columnNo: 'receiver',
          columnType: 'EmpSelect',
          isEdit: true,
          flex: 1,
          // required: true,
          // render: employeeSelectRenderer('receiver'),
        },
        {
          columnName: '监交人',
          columnNo: 'supervisor',
          flex: 1,
          columnType: 'EmpSelect',
          isEdit: true,
        },
      ],
      initialData: [],
      canEdit: true,
      showSettingColumn: false,
      rowKey: 'key',
    }),
    [employeeSelectRenderer]
  );

  // 行操作配置
  const cellSetting = useMemo(
    () => ({
      width: 80,
      pinned: 'right' as const,
      actionList: ['delete'],
      actionEvent: {
        handleDelete: (data: any) => {
          XiooTableActions.getTable(TABLE_ID)?.onRemoveRowItems(data);
        },
      },
    }),
    []
  );

  // 新增行
  const handleAddRow = useCallback(() => {
    const newRow: HandoverData = {
      key: `row_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: '',
      receiver: '',
      supervisor: '',
    };

    const currentData = XiooTableActions.getTable(TABLE_ID)?.getAllData() || [];
    XiooTableActions.getTable(TABLE_ID)?.onAddRowItems([newRow], currentData.length);
  }, []);

  // 确认提交
  const handleConfirm = async () => {
    try {
      setLoading(true);

      // 获取表格数据
      const tableData = XiooTableActions.getTable(TABLE_ID)?.getAllData() || [];

      // 数据验证
      if (tableData.length === 0) {
        message.warning('请至少添加一条工作交接内容');
        return;
      }

      console.log('工作交接数据=====', tableData);

      // // 检查必填字段
      // const invalidRows = tableData.filter(
      //   row => !row.content?.trim() || !row.receiver?.trim() || !row.supervisor?.trim()
      // );

      // if (invalidRows.length > 0) {
      //   message.warning('请填写完整的工作交接内容、接收人和监交人');
      //   return;
      // }

      // 调用确认回调
      onConfirm(tableData);

      // 清空表格数据
      XiooTableActions.getTable(TABLE_ID)?.updateData([]);
    } catch (error) {
      console.error('提交工作交接单失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 取消操作
  const handleCancel = useCallback(() => {
    // 清空表格数据
    XiooTableActions.getTable(TABLE_ID)?.updateData([]);
    onCancel();
  }, [onCancel]);

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      onOk={handleConfirm}
      confirmLoading={loading}
      width={800}
      destroyOnClose
      okText="确认"
      cancelText="取消"
    >
      <div className="mb-16">
        <section className="mb-16">
          <span className="mr-8">姓名: {userInfo.name || '-'}</span>
          <span className="mr-8">部门: {userInfo.department || '-'}</span>
          <span>职位: {userInfo.position || '-'}</span>
        </section>
        <section className="mb-16">
          <span className="mr-8">部门经理: {userInfo.manager || '-'} </span>
          <span>离职日期: {userInfo.exitDate || '-'}</span>
        </section>
        <section className="mb-16 ml-8 text-12 color-gray">
          <p>填写指引：</p>
          <p>1.监交人可以是接收人的上级，亦可与接收人是同一人；</p>
          <p>2.内容可写未交接的、待跟进的、未完结的工作项目；</p>
          <p>3.如无相关内容请写&ldquo;无&rdquo;；</p>
          <p>4.填写如有疑问请联系人事HR。</p>
        </section>
      </div>

      <SingleTable
        tableInfo={tableInfo}
        hasPagination={false}
        // tableHeight={300}
        cellSetting={cellSetting}
        toolExtraContent={{
          right: (
            <Button type="primary" onClick={handleAddRow}>
              增加
            </Button>
          ),
          left: null,
        }}
      />
    </Modal>
  );
};

JobHandoverModal.displayName = 'JobHandoverModal';

export default JobHandoverModal;
