import React from 'react';
import EmployeeSelect from '@src/DHRJobHandover/components/EmployeeSelect';
const employeeSelectRenderer = props => {
  const { value, onChange, stopEditing, data, colDef } = props;

  return (
    <EmployeeSelect
      value={value}
      placeholder="请选择员工"
      optionKey="empId"
      optionLabel="data.empOtherName-data.empDeptFullPathName"
      style={{ width: '100%' }}
      onChange={(newValue, option) => {
        onChange(newValue);
        stopEditing();
        data[`${colDef.field}_Name`] = option?.empOtherName || newValue;
      }}
    />
  );
};

export default employeeSelectRenderer;
