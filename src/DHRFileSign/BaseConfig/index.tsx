import React from 'react';
import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import DictSelectList from '@components/DictSelectList';
// export const setDictConfig = (
//   formRef: IInjectFormItemFnParamsContext['formRef'],
//   key: string,
//   value?: any,
//   ctx?: {
//     context?: IInjectFormItemFnParamsContext;
//   }
// ) => {
//   const dictConfig =
//     formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
//   dictConfig[key] = value;
//   formRef?.current?.setFormItem?.('dictConfig', dictConfig);
//   ctx?.context?.onConfirm?.('dictConfig', dictConfig);
// };

const customCptBaseConfig = async (context?: IInjectFormItemFnParamsContext): Promise<IFormItem[]> => {
  console.log('context===1111', context);
  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    // 文件签署类型
    {
      type: 'custom',
      key: 'dictConfigType',
      label: '文件签署类型',
      render: ({ value }) => (
        <DictSelectList
          onSelect={(_value: string, typeName: string) => {
            // context?.onConfirm?.('dictConfigType', _value);
            context?.formRef.current?.setFormItem?.('dictConfigType', _value);
            context?.formRef.current?.setFormItem?.('dictConfigTypeName', typeName);
          }}
          value={value}
        />
      ),
    },
    // 文件签署类型名称
    {
      type: 'input',
      key: 'dictConfigTypeName',
      wuliMode: 'hide',
      label: '文件签署类型名称',
    },
    {
      type: 'textarea',
      key: 'dictConfigDataTpl',
      label: '数据模板',
      configs: {
        placeholder:
          '数据模板为一段js代码，需要执行：return {name: data.name, idNumber: data.idNumber, date: moment().format("YYYY年MM月DD日")}',
        onBlur: e => {
          const value = e.target.value;
          context?.formRef.current?.setFormItem?.('dictConfigDataTpl', value);
        },
      },
    },
    // 签署前置非空字段
    {
      type: 'input',
      key: 'dictConfigValidateFields',
      label: '签署前置非空字段',
      configs: {
        placeholder: '签署前非空字段编码，多个用逗号隔开，如：C_NAME|名字,C_ID_NUMBER|身份证号',
        onBlur: e => {
          const value = e.target.value;
          context?.formRef.current?.setFormItem?.('dictConfigValidateFields', value);
        },
      },
    },

    // {
    //   type: 'input',
    //   key: 'dictConfigTitle',
    //   label: '签署标题',
    //   configs: {
    //     placeholder: '请输入签署标题',
    //     onBlur: e => {
    //       const value = e.target.value;
    //       context?.formRef.current?.setFormItem?.('dictConfigTitle', value);
    //     },
    //   },
    // },
    // {
    //   type: 'input',
    //   key: 'dictConfigSuccessMessage',
    //   label: '签署成功提示语',
    //   configs: {
    //     placeholder: '请输入签署成功后显示的提示语',
    //     onBlur: e => {
    //       const value = e.target.value;
    //       context?.formRef.current?.setFormItem?.('dictConfigSuccessMessage', value);
    //     },
    //   },
    // },
    {
      type: 'input',
      key: 'dictConfigButtonText',
      label: '签署按钮文字',
      configs: {
        placeholder: '请输入签署按钮显示的文字',
        onBlur: e => {
          const value = e.target.value;
          context?.formRef.current?.setFormItem?.('dictConfigButtonText', value);
        },
      },
    },
  ];
};

export default customCptBaseConfig;
