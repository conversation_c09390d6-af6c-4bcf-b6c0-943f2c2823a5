import { Row, Col, Avatar, Tag } from 'antd';
import React from 'react';

import { request as fetchApi } from '@utils/http';
import { IOriginalProps } from '@src/types';

import './style.less';

type IAppProps = IOriginalProps;
const BaseInfo: React.FC<IAppProps> = props => {
  console.log('props', props);
  return (
    <Row className="dhr-employee-info-card">
      <Col className="dhr-employee-info-card-avatar">
        <Avatar size={64} shape="square" src="">
          USER
        </Avatar>
      </Col>
      <Col span={18} className="dhr-employee-info-card-intro">
        <Row gutter={[14, 0]}>
          <Col>
            <span>USER</span>
            <span>｜CVTE01803</span>
          </Col>
          <Col>
            <Tag>标签1</Tag>
            <Tag>标签2</Tag>
            <Tag>标签3</Tag>
          </Col>
        </Row>
        <Row gutter={14}>
          <Col>
            <span>岗位：</span>
            <span>测试</span>
          </Col>
        </Row>
      </Col>
    </Row>
  );
};

export default BaseInfo;
