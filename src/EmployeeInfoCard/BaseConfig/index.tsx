import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

import { EMPLOYEE_INFO_BOTTOM_SHOW_TYPES } from '@constants/common';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};

  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const {
      tagKeys,
      fetchUrl,
      userName,
      separator,
      topIntros,
      avatarShape,
      bottomIntros,
      bottomSeparator,
      bottomLabelIntros,
    } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();
    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );

    formRef?.current?.setFormItem?.(
      'dictConfigFetchUrl',
      curFormData?.dictConfigFetchUrl || defFormData?.dictConfigFetchUrl || fetchUrl
    );
    formRef?.current?.setFormItem?.(
      'dictConfigAvatarShape',
      curFormData?.dictConfigAvatarShape || defFormData?.dictConfigAvatarShape || avatarShape
    );
    formRef?.current?.setFormItem?.(
      'dictConfigUserName',
      curFormData?.dictConfigUserName || defFormData?.dictConfigUserName || userName
    );
    formRef?.current?.setFormItem?.(
      'dictConfigTopIntros',
      curFormData?.dictConfigTopIntros || defFormData?.dictConfigTopIntros || topIntros
    );
    formRef?.current?.setFormItem?.(
      'dictConfigTagKeys',
      curFormData?.dictConfigTagKeys || defFormData?.dictConfigTagKeys || tagKeys
    );
    formRef?.current?.setFormItem?.(
      'dictConfigBottomLabelIntros',
      curFormData?.dictConfigBottomLabelIntros || defFormData?.dictConfigBottomLabelIntros || bottomLabelIntros
    );
    formRef?.current?.setFormItem?.(
      'dictConfigBottomIntros',
      curFormData?.dictConfigBottomIntros || defFormData?.dictConfigBottomIntros || bottomIntros
    );
    formRef?.current?.setFormItem?.(
      'dictConfigSeparator',
      curFormData?.dictConfigSeparator || defFormData?.dictConfigSeparator || separator
    );
    formRef?.current?.setFormItem?.(
      'dictConfigBottomSeparator',
      curFormData?.dictConfigBottomSeparator || defFormData?.dictConfigBottomSeparator || bottomSeparator
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'input',
      key: 'dictConfigFetchUrl',
      label: '请求路径',
      configs: {
        placeholder: '默认使用人力的人员信息',
        onBlur: () => {
          const dictConfigFetchUrl = formRef?.current?.getFormItem?.('dictConfigFetchUrl');
          context?.onConfirm?.('dictConfigFetchUrl', dictConfigFetchUrl);
          setDictConfig(formRef, 'fetchUrl', dictConfigFetchUrl, {
            context,
          });
        },
      },
    },
    {
      type: 'select',
      key: 'dictConfigAvatarShape',
      label: '头像形状',
      configs: {
        options: [
          { label: '圆形', value: 'circle' },
          { label: '方形', value: 'square' },
        ],
        showSearch: true,
        placeholder: '请选择',
        onChange: (value: string) => {
          context?.onConfirm?.('dictConfigAvatarShape', value);
          setDictConfig(formRef, 'avatarShape', value, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigUserName',
      label: '用户名称',
      configs: {
        placeholder: '默认为name',
        onBlur: () => {
          const dictConfigUserName = formRef?.current?.getFormItem?.('dictConfigUserName');
          context?.onConfirm?.('dictConfigUserName', dictConfigUserName);
          setDictConfig(formRef, 'userName', dictConfigUserName, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigTopIntros',
      label: '顶部信息',
      configs: {
        placeholder: '需展示多个字段，请以逗号隔开',
        onBlur: () => {
          const dictConfigTopIntros = formRef?.current?.getFormItem?.('dictConfigTopIntros');
          context?.onConfirm?.('dictConfigTopIntros', dictConfigTopIntros);
          setDictConfig(formRef, 'topIntros', dictConfigTopIntros, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigSeparator',
      label: '顶部信息分隔符',
      configs: {
        placeholder: '请填写，默认以空格分割',
        onBlur: () => {
          const dictConfigSeparator = formRef?.current?.getFormItem?.('dictConfigSeparator');
          context?.onConfirm?.('dictConfigSeparator', dictConfigSeparator);
          setDictConfig(formRef, 'separator', dictConfigSeparator, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigTagKeys',
      label: '标签信息',
      configs: {
        placeholder: '需展示多个字段，请以逗号隔开',
        onBlur: () => {
          const dictConfigTagKeys = formRef?.current?.getFormItem?.('dictConfigTagKeys');
          context?.onConfirm?.('dictConfigTagKeys', dictConfigTagKeys);
          setDictConfig(formRef, 'tagKeys', dictConfigTagKeys, {
            context,
          });
        },
      },
    },
    // 底部信息分隔符
    {
      type: 'input',
      key: 'dictConfigBottomSeparator',
      label: '底部信息分隔符',
      configs: {
        placeholder: '请填写，默认以空格分割',
        onBlur: () => {
          const dictConfigBottomSeparator = formRef?.current?.getFormItem?.('dictConfigBottomSeparator');
          context?.onConfirm?.('dictConfigBottomSeparator', dictConfigBottomSeparator);
          setDictConfig(formRef, 'bottomSeparator', dictConfigBottomSeparator, {
            context,
          });
        },
      },
    },
    {
      type: 'textarea',
      label: '底部信息值(label选填)',
      key: 'dictConfigBottomIntros',
      configs: {
        rows: 5,
        placeholder: 'JSON格式：[[{"label":"","value":""}]]',
        onBlur: () => {
          const dictConfigBottomIntros = formRef?.current?.getFormItem?.('dictConfigBottomIntros');
          context?.onConfirm?.('dictConfigBottomIntros', dictConfigBottomIntros);
          setDictConfig(formRef, 'bottomIntros', dictConfigBottomIntros, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
