import React, { useCallback, useEffect, useState } from 'react';
import { Row, Col, Avatar, Tag } from 'antd';
import { DHR_SASS_PROXY } from '@utils/http';
import axios from 'axios';

import employeeApis from '@apis/employee';
import { IOriginalProps } from '@src/types';

import './style.less';

interface IAppProps extends IOriginalProps {
  empId?: string;
  account: string;
  configs: {
    config: {
      baseConfig: {
        dictConfig?: {
          /** 标签信息 */
          tagKeys?: string;
          /** 请求路径 */
          fetchUrl?: string;
          /** 用户名 */
          userName?: string;
          /** 顶部信息 */
          topIntros?: string;
          /** 顶部信息分隔符 */
          separator?: string;
          /** 底部信息 */
          bottomIntros?: string;
          /** 底部信息分隔符 */
          bottomSeparator?: string;
          /** 底部信息label */
          // bottomLabelIntros?: string;
          /** 头像形状 */
          avatarShape?: 'square' | 'circle';
        };
      };
    };
    context: {
      globalContext: {
        PageTools: {
          getDetailEngineProps: () => Record<string, any>;
        };
      };
      config: {
        contextDataMap: {
          contextData: Record<string, any>;
        };
      };
    };
  };
}
const EmployeeInfoCard: React.FC<IAppProps> = props => {
  const routerParams = props?.configs?.context?.globalContext?.PageTools?.getDetailEngineProps();
  const { empId, account } = routerParams;

  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const [initValues, setInitValues] = useState<Record<string, any>>(undefined);

  const configs = props?.configs;
  const dictConfig = configs?.config?.baseConfig?.dictConfig || {};
  const {
    fetchUrl,
    tagKeys,
    topIntros,
    separator,
    bottomIntros,
    bottomSeparator,
    // bottomLabelIntros,
    userName = 'name',
    avatarShape = 'square',
  } = dictConfig;
  const tagKeysArr = tagKeys ? tagKeys.split(/[,，]/g) : [];
  const topIntrosArr = topIntros ? topIntros.split(/[,，]/g) : [];
  const bottomIntroRows = bottomIntros ? JSON.parse(bottomIntros) : [];
  // const bottomLabelIntrosArr = bottomLabelIntros ? bottomLabelIntros.split(/[,，]/g) : [];

  // 获取员工信息
  const onFetchEmpData = () => {
    const url = fetchUrl ? fetchUrl : `${DHR_SASS_PROXY}${employeeApis.employeeBaseInfo.url}`;
    const newUrl = url.replace(':empId', empId);
    axios({
      url: newUrl,
      method: 'GET',
      params: {
        empId,
        queryDate: new Date().getTime(),
      },
    }).then(res => {
      if (res?.data?.status === '0' || res?.status === 200) {
        const newInitValues = res?.data?.data || {};
        setInitValues(newInitValues);
      }
    });
  };

  // 获取头像
  const onFetchEmployeeAvatar = useCallback(() => {
    const { baseURL, url } = employeeApis.employeeAvatar;
    axios
      .get(`${baseURL}${url}`, {
        params: {
          type: 'SYSTEM',
          username: account,
        },
        headers: {
          Accept: 'application/octet-stream',
        },
        responseType: 'blob',
      })
      .then(res => {
        const url = res?.data;
        const newAvatarUrl = url ? URL.createObjectURL(url) : '';
        setAvatarUrl(newAvatarUrl);
      });
  }, [account]);

  useEffect(() => {
    empId && onFetchEmpData();
  }, [fetchUrl, empId]);

  useEffect(() => {
    account && onFetchEmployeeAvatar();
  }, [account]);

  const handleReplaceVal = useCallback((key: string, data: any) => {
    if (key.includes('${')) {
      return key.replace(/\$\{([^}]*)\}/gi, (match, propKey) => {
        // 检查propKey是否在data中存在，如不存在则保留原始模板标记
        return data.hasOwnProperty(propKey) ? data[propKey] || '-' : '-';
      });
    }
    // 如果key是data对象的一个属性，则返回该属性值
    if (data.hasOwnProperty(key)) {
      return data[key] || '-';
    }

    // 其他情况，返回原始key
    return '-';
  }, []);

  const separatorVal = separator || ` `;
  const bottomSeparatorVal = bottomSeparator || ` `;
  const surname = (initValues?.[userName] || '').split('').shift();
  return (
    <Row className="dhr-employee-info-card">
      <Col className="dhr-employee-info-card-avatar">
        <Avatar size={72} shape={avatarShape} src={avatarUrl}>
          {surname || '-'}
        </Avatar>
      </Col>
      {initValues && (
        <Col span={22} className="dhr-employee-info-card-intro">
          <Row gutter={[14, 0]} style={{ marginBottom: '4px' }}>
            {topIntrosArr?.length > 0 && (
              <Col>
                <span>{initValues[userName]}</span>
                {(topIntrosArr || [])
                  .filter(intro => !!initValues[intro])
                  .map((intro, index) => (
                    <span key={index}>
                      <span>{separatorVal}</span>
                      <span>{handleReplaceVal(intro, initValues)}</span>
                    </span>
                  ))}
              </Col>
            )}
            {tagKeysArr?.length > 0 && (
              <Col>
                {(tagKeysArr || []).map((tag, index) => (
                  <Tag key={index}>{handleReplaceVal(tag, initValues)}</Tag>
                ))}
              </Col>
            )}
          </Row>
          {bottomIntroRows.length > 0 &&
            bottomIntroRows.map((introRow, rowIndex) => {
              return (
                <Row gutter={[0, 8]} key={rowIndex} className="dhr-employee-info-card-intro-row">
                  {introRow
                    // 增加除了模板匹配的value，其余在initValues没有的且值为空的则需要过滤掉
                    .filter(intro => {
                      return intro?.value.includes('${')
                        ? true
                        : initValues.hasOwnProperty(intro?.value) && !!initValues[intro?.value];
                    })
                    .map((intro, colIndex) => {
                      return (
                        <Col key={`${rowIndex}-${colIndex}`}>
                          {colIndex !== 0 && <span>{bottomSeparatorVal}</span>}
                          {intro?.label && <span>{intro?.label}</span>}
                          <span>{handleReplaceVal(intro?.value, initValues)}</span>
                        </Col>
                      );
                    })}
                </Row>
              );
            })}
        </Col>
      )}
    </Row>
  );
};

export default EmployeeInfoCard;
