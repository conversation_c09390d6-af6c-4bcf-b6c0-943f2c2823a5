import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Spin } from 'antd';
import queryString from 'query-string';

import tmgApis from '@apis/tmg';
import commomApis from '@apis/common';
import useFetch from '@hooks/useFetch';
import { IOriginalProps } from '@src/types';
import useDictCode from '@hooks/useDictCode';
import { request as fetchApi } from '@utils/http';
import excelTask from '@components/ExportLargeExcel';
import { exportMultiExcel, IExport } from '@utils/excel';
import { defaultObj, toDateFormat, toEndDate, toStartDate, roundToOneDecimals } from '@utils/tools';
import { IGNORE_EMP_TYPES } from '@constants/absence';

import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

const filterFormKey = 'maternityLeaveListFormKey';
export interface IAppProps extends IOriginalProps {
  channel: string;
  processId: string;
  systemHelper: {
    history: {
      push: any;
    };
  };
}

const MaternityLeaveList: React.FC<IAppProps> = props => {
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);
  const [leaveTypeOptions, setLeaveTypeOptions] = useState<any[]>([]);
  const [employeeTypeOptions, setEmployeeTypeOptions] = useState<any[]>([]);
  const [employeeStatusOptions, setEmployeeStatusOptions] = useState<any[]>([]);

  /** 请求过程 */
  const {
    Data: payroll,
    Loading: payrollListLoading,
    runAction: runActionFetchNotofyList,
  } = useFetch({
    ...tmgApis.maternityLeaveFlowList,
    data: {
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  // 获取搜索参数
  const onGetSearchQuery = () => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    const result = {
      ...formData,
      actualLeaveTime: undefined,
      calculateLeaveTime: undefined,
      lastModifiedTime: toStartDate(formData.lastModifiedTime),
      actualLeaveTimeStart: toStartDate(formData.actualLeaveTime?.[0]),
      actualLeaveTimeEnd: toEndDate(formData.actualLeaveTime?.[1]),
      calculateLeaveTimeStart: toStartDate(formData.calculateLeaveTime?.[0]),
      calculateLeaveTimeEnd: toEndDate(formData.calculateLeaveTime?.[1]),
    };

    return result;
  };

  /** 请求过程 */
  const onFetchNotifyList = (data: Record<string, any> = {}) => {
    const params = onGetSearchQuery();
    runActionFetchNotofyList({
      data: {
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...params,
        ...data,
      },
    });
  };

  // 获取员工类型
  const onFetchEmployeeType = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      appId: '96dd1d629bb24193bb8630c9dcb61aad',
      formClassId: '95985d5e087d4cd58dc2e2b6556bd56a',
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list
          .map(({ mainData }) => ({
            ...mainData,
            key: mainData?.id,
            label: mainData?.cName,
            dataIndex: mainData?.id,
          }))
          .filter(k => !IGNORE_EMP_TYPES.includes(k.id));
        setEmployeeTypeOptions(options);
      },
    });
  }, []);

  // 获取员工状态
  const onFetchEmployeeStatus = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      mainParamsGroups: [],
      appId: '96dd1d629bb24193bb8630c9dcb61aad',
      formClassId: '4f6de7a4056240ea97913fb54a9d7ad3',
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.id,
          label: mainData?.cName,
          dataIndex: mainData?.id,
        }));
        setEmployeeStatusOptions(options);
      },
    });
  }, []);

  // 获取休假类型
  const onFetchLeaveTypes = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
      formClassId: 'df385a82be694700acf58d86de8bbbd2',
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.cCode,
          label: mainData?.cName,
          dataIndex: mainData?.cCode,
        }));
        setLeaveTypeOptions(options);
      },
    });
  }, []);

  useEffect(() => {
    onFetchLeaveTypes();
    onFetchEmployeeType();
    onFetchEmployeeStatus();
  }, []);

  const columns = useMemo(() => {
    return [
      {
        key: 'wfCode',
        title: '流程编号',
        dataIndex: 'wfCode',
        render: record => {
          const handleJumpTodetail = () => {
            const rowData = record.data;
            props.systemHelper.history.push(
              `/portal/y51o6js7/cmPage?${queryString.stringify({
                pageId: rowData.wfId,
                pageName: rowData.wfName,
                pageFlag: rowData.wfId,
                exposeName: 'LCPDetailTemplate',
                resourceName: 'tz-render',
                appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
                classId: '695ee45fbcc84a0093180f490aca79d5',
                tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
                apiName: 'TB_TMG_LEAVE_FLOW',
              })}`
            );
          };
          return <a onClick={() => handleJumpTodetail()}>{record.value}</a>;
        },
      },
      {
        key: 'wfName',
        title: '流程名称',
        dataIndex: 'wfName',
      },
      {
        title: '申请人别名',
        dataIndex: 'empOtherName',
        key: 'empOtherName',
      },
      {
        title: '域账号',
        key: 'empAccount',
        dataIndex: 'empAccount',
      },
      {
        title: '请假时行政组织',
        dataIndex: 'deptFullName',
        key: 'deptFullName',
      },
      {
        title: '当前行政组织',
        dataIndex: 'currentDeptFullName',
        key: 'currentDeptFullName',
      },
      {
        title: '人员状态',
        dataIndex: 'empStatusName',
        key: 'empStatusName',
      },
      {
        title: '用工类型',
        dataIndex: 'empTypeName',
        key: 'empTypeName',
      },

      {
        title: '集团入职时间',
        dataIndex: 'empGroupLaborDate',
        key: 'empGroupLaborDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value) || '-';
        },
        formatteFn: text => toDateFormat(text) || '-',
      },
      {
        title: '离职时间',
        key: 'empExitDate',
        dataIndex: 'empExitDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value) || '-';
        },
        formatteFn: text => toDateFormat(text) || '-',
      },
      {
        title: '请假类型',
        dataIndex: 'leaveCategoryName',
        key: 'leaveCategoryName',
      },
      {
        title: '生育情形',
        dataIndex: 'fertilitySituationType',
        key: 'fertilitySituationType',
      },
      {
        title: '多胞胎情形',
        dataIndex: 'multiplePregnancieType',
        key: 'multiplePregnancieType',
      },
      {
        title: '生育次数',
        dataIndex: 'productionTimesName',
        key: 'productionTimesName',
      },
      {
        title: '法人公司所在地',
        dataIndex: 'empWorkCityName',
        key: 'empWorkCityName',
      },
      {
        title: '社保所属地',
        dataIndex: 'empSiCityName',
        key: 'empSiCityName',
      },
      {
        title: '实际休假开始时间',
        dataIndex: 'beginDate',
        key: 'beginDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM-DD HH:mm') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM-DD HH:mm') || '-',
      },
      {
        title: '实际休假结束时间',
        key: 'endDate',
        dataIndex: 'endDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM-DD HH:mm') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM-DD HH:mm') || '-',
      },
      {
        title: '实际休假天数',
        dataIndex: 'durationDay',
        key: 'durationDay',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '实际产假额度可休天数',
        dataIndex: 'canLeaveDays',
        key: 'canLeaveDays',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '实际产假额度休完时间',
        key: 'normalEndDate',
        dataIndex: 'normalEndDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM-DD HH:mm') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM-DD HH:mm') || '-',
      },
      {
        title: '是否有提前返岗',
        dataIndex: 'whetherReturnWorkEarly',
        key: 'whetherReturnWorkEarly',
      },
      {
        title: '提前返岗总工作时数',
        dataIndex: 'returnWorkEarlyHours',
        key: 'returnWorkEarlyHours',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '提前返岗总自然天数',
        dataIndex: 'returnWorkEarlyDays',
        key: 'returnWorkEarlyDays',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '计算提前返岗工作时数',
        dataIndex: 'calReturnWorkEarlyHours',
        key: 'calReturnWorkEarlyHours',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '计算提前返岗自然天数',
        dataIndex: 'calReturnWorkEarlyDays',
        key: 'calReturnWorkEarlyDays',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '提前返岗期间请假时数',
        dataIndex: 'returnEarlyOtherLeaveHours',
        key: 'returnEarlyOtherLeaveHours',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '提前返岗期间请假天数',
        dataIndex: 'returnEarlyOtherLeaveDays',
        key: 'returnEarlyOtherLeaveDays',
        render: ({ value }) => roundToOneDecimals(value),
      },
      // {
      //   title: '最终明细维护时间',
      //   dataIndex: 'crtTime',
      //   key: 'crtTime',
      //   render: record => {
      //     const value = record?.value;
      //     return toDateFormat(value) || '-';
      //   },
      //   formatteFn: text => toDateFormat(text) || '-',
      // },
      {
        title: '操作销假时间',
        key: 'destroyTime',
        dataIndex: 'destroyTime',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM-DD HH:mm') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM-DD HH:mm') || '-',
      },
      {
        title: '最后修改时间',
        key: 'lastModifiedTime',
        dataIndex: 'lastModifiedTime',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM-DD HH:mm') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM-DD HH:mm') || '-',
      },
      {
        title: '流程节点',
        dataIndex: 'wfCurrentNodeName',
        key: 'wfCurrentNodeName',
      },
    ];
  }, []);

  const filterFormItems: any[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'wfCode',
        label: '所属流程编号',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'input',
        key: 'nameOrAccount',
        label: '申请人别名或域帐号',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'input',
        key: 'thenOrgName',
        label: '请假时行政部门',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'input',
        key: 'currentOrgName',
        label: '当前行政部门',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'select',
        key: 'empTypeList',
        label: '用工关系类型',
        configs: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          optionFilterProp: 'label',
          options: employeeTypeOptions,
          mode: 'multiple',
        },
      },
      {
        type: 'select',
        key: 'empStatus',
        label: '人员状态',
        configs: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          optionFilterProp: 'label',
          options: employeeStatusOptions,
        },
      },
      {
        type: 'select',
        key: 'leaveTypes',
        label: '休假类型',
        configs: {
          mode: 'multiple',
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          optionFilterProp: 'label',
          options: leaveTypeOptions,
        },
      },
      {
        type: 'rangePicker',
        label: '实际休假时间区间',
        key: 'actualLeaveTime',
      },
      {
        type: 'rangePicker',
        label: '计算休假时间区间',
        key: 'calculateLeaveTime',
      },
      {
        col: 8,
        type: 'datePicker',
        label: '最后修改时间',
        key: 'lastModifiedTime',
      },
    ],
    [employeeTypeOptions.length, employeeStatusOptions.length, leaveTypeOptions.length]
  );

  const { pagination = {}, list: payrollList = [] } = defaultObj(payroll);

  const handleExport = () => {
    const newColumns = [...columns].map(columnItem => ({
      ...columnItem,
      render: columnItem.formatteFn && columnItem.formatteFn,
    }));

    if (selectedRows.length > 0) {
      const exportArr: IExport[] = [
        {
          data: selectedRows,
          sheetName: 'Sheet1',
          columns: newColumns,
        },
      ];
      return exportMultiExcel(exportArr, '产假流程数据.xlsx');
    }

    const searchParams = onGetSearchQuery();

    const params = {
      fetchParams: {
        ...tmgApis.maternityLeaveFlowList,
        data: {
          ...searchParams,
        },
      },
      xlsxName: '产假流程数据',
      configs: {
        maxPageSize: 300,
        columns: newColumns,
      },
    };
    excelTask.add(params);
  };

  const actionBtnItems: any[] = useMemo(
    () => [
      {
        key: 'export',
        content: '导出',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleExport,
        },
      },
    ],
    [selectedRows, payrollList.length]
  );

  return (
    <Spin spinning={payrollListLoading}>
      <div className="CnbPayProcessWrap">
        <WULIWholeTable
          pagination
          canSelect
          multiSelect
          rowKey="id"
          columns={columns}
          data={payrollList}
          paginationConfig={{
            showSizeChanger: true,
            showQuickJumper: true,
            total: pagination.total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
            onChange: (pageNum, pageSize) => onFetchNotifyList({ pageNum, pageSize }),
          }}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            onSearch: () => onFetchNotifyList(),
            onClear: () => {
              WULIFormActions.get(filterFormKey)?.reset();
              onFetchNotifyList();
            },
            defaultLayout: {
              col: 8,
              labelCol: 9,
              wrapperCol: 15,
            },
          }}
          action={actionBtnItems}
          selectedRows={selectedRows}
          afterCancelSelect={newRows => setSelectedRows(newRows)}
          onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => setSelectedRows(_selectedRows)}
        />
      </div>
    </Spin>
  );
};
export default MaternityLeaveList;
