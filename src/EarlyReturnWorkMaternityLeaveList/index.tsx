import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { Spin, Modal } from 'antd';
import queryString from 'query-string';
import Big from 'big.js';

import tmgApis from '@apis/tmg';
import commomApis from '@apis/common';
import useFetch from '@hooks/useFetch';
import { IOriginalProps } from '@src/types';
import { request as fetchApi } from '@utils/http';
import excelTask from '@components/ExportLargeExcel';
import { exportMultiExcel, IExport } from '@utils/excel';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { ENUM_TASK_STATUS } from '@constants/task';

import {
  roundToOneDecimals,
  defaultObj,
  showErrNotification,
  showSucNotification,
  toDateFormat,
  toEndDate,
  toStartDate,
} from '@utils/tools';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';
import RenderFormModal from '@components/RenderFormModal';
import useTask from '@hooks/useTask';
import { IGNORE_EMP_TYPES } from '@constants/absence';

import './style.less';

const filterFormKey = 'tempFormKey';
export interface IAppProps extends IOriginalProps {
  channel: string;
  processId: string;
  systemHelper: {
    history: {
      push: any;
    };
  };
}

const genBaseDateItems = (key = 'monthDate', label = '更新日期'): IFormItem[] => [
  {
    type: 'datePicker',
    key,
    label,
    required: true,
    decoratorOptions: {
      rules: [
        {
          required: true,
          message: '请选择',
        },
      ],
    },
    configs: {
      allowClear: true,
      placeholder: '请选择',
      picker: 'month',
    },
  },
];
const LeaveProcessList: React.FC<IAppProps> = props => {
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);
  const [leaveTypeOptions, setLeaveTypeOptions] = useState<any[]>([]);
  const [employeeTypeOptions, setEmployeeTypeOptions] = useState<any[]>([]);
  const [employeeStatusOptions, setEmployeeStatusOptions] = useState<any[]>([]);
  const containerRef = useRef(null);

  /** 请求过程 */
  const {
    Data: payroll,
    Loading: payrollListLoading,
    runAction: runActionFetchMainList,
  } = useFetch({
    ...tmgApis.maternityEearlyReturnWorkList,
    data: {
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  // 获取搜索参数
  const onGetSearchQuery = () => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    const result = {
      ...formData,
      actualLeaveTime: undefined,
      lastModifiedTime: toStartDate(formData.lastModifiedTime),
      actualLeaveTimeStart: toStartDate(formData.actualLeaveTime?.[0]),
      actualLeaveTimeEnd: toEndDate(formData.actualLeaveTime?.[1]),
      subsidyCalMonth: toStartDate(formData.subsidyCalMonth, 'month'),
      subsidyBelongMonth: toStartDate(formData.subsidyBelongMonth, 'month'),
    };

    return result;
  };

  // 获取月初0点的时间
  const formatMonthTime = (timestamp: number) => {
    const currentDate = new Date(timestamp);
    currentDate.setHours(0, 0, 0, 0);
    currentDate.setDate(1);
    return currentDate.getTime();
  };

  /** 请求过程 */
  const onFetchNotifyList = (data: Record<string, any> = {}) => {
    const params = onGetSearchQuery();
    params.subsidyCalMonth && (params.subsidyCalMonth = formatMonthTime(params.subsidyCalMonth));
    params.subsidyBelongMonth && (params.subsidyBelongMonth = formatMonthTime(params.subsidyBelongMonth));
    runActionFetchMainList({
      data: {
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...params,
        ...data,
      },
    });
  };

  const { handleGetTaskStatus } = useTask({
    // handleAfterTaskStatus: (status: ENUM_TASK_STATUS) => void;
    queryTaskRequest: {
      ...tmgApis.maternityEearlyReturnWorkTask,
    },
    reExecuteTaskRequest: {
      ...tmgApis.maternityEearlyReturnWorkTaskReExcute,
    },
    containerRef,

    handleAfterTaskStatus: status => {
      if (status === ENUM_TASK_STATUS.DONE) {
        onFetchNotifyList();
      }
    },
  });

  // 获取员工类型
  const onFetchEmployeeType = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      appId: '96dd1d629bb24193bb8630c9dcb61aad',
      formClassId: '95985d5e087d4cd58dc2e2b6556bd56a',
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list
          .map(({ mainData }) => ({
            ...mainData,
            key: mainData?.id,
            label: mainData?.cName,
            dataIndex: mainData?.id,
          }))
          .filter(k => !IGNORE_EMP_TYPES.includes(k.id));
        setEmployeeTypeOptions(options);
      },
    });
  }, []);

  // 获取员工状态
  const onFetchEmployeeStatus = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      mainParamsGroups: [],
      appId: '96dd1d629bb24193bb8630c9dcb61aad',
      formClassId: '4f6de7a4056240ea97913fb54a9d7ad3',
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.id,
          label: mainData?.cName,
          dataIndex: mainData?.id,
        }));
        setEmployeeStatusOptions(options);
      },
    });
  }, []);

  // 获取休假类型
  const onFetchLeaveTypes = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
      formClassId: 'df385a82be694700acf58d86de8bbbd2',
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.cCode,
          label: mainData?.cName,
          dataIndex: mainData?.cCode,
        }));
        setLeaveTypeOptions(options);
      },
    });
  }, []);

  useEffect(() => {
    onFetchLeaveTypes();
    onFetchEmployeeType();
    onFetchEmployeeStatus();
    handleGetTaskStatus();
  }, []);

  const columns = useMemo(() => {
    return [
      {
        key: 'wfCode',
        title: '流程编号',
        dataIndex: 'wfCode',
        render: record => {
          const handleJumpTodetail = () => {
            const rowData = record.data;
            props.systemHelper.history.push(
              `/portal/y51o6js7/cmPage?${queryString.stringify({
                pageId: rowData.processId,
                pageName: `${rowData.empOtherName}-产假流程`,
                pageFlag: rowData.processId,
                exposeName: 'LCPDetailTemplate',
                resourceName: 'tz-render',
                appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
                classId: '695ee45fbcc84a0093180f490aca79d5',
                tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
                apiName: 'TB_TMG_LEAVE_FLOW',
              })}`
            );
          };
          return <a onClick={() => handleJumpTodetail()}>{record.value}</a>;
        },
      },
      {
        title: '申请人别名',
        dataIndex: 'empOtherName',
        key: 'empOtherName',
      },
      {
        title: '域账号',
        key: 'empAccount',
        dataIndex: 'empAccount',
      },
      {
        title: '请假时行政组织',
        dataIndex: 'deptFullName',
        key: 'deptFullName',
      },
      {
        title: '当前行政组织',
        dataIndex: 'currentDeptFullName',
        key: 'currentDeptFullName',
      },
      {
        title: '人员状态',
        dataIndex: 'empStatusName',
        key: 'empStatusName',
      },
      {
        title: '用工关系类型',
        dataIndex: 'empTypeName',
        key: 'empTypeName',
      },
      // 待
      {
        title: '集团入职时间',
        dataIndex: 'empGroupLaborDate',
        key: 'empGroupLaborDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value) || '-';
        },
        formatteFn: text => toDateFormat(text) || '-',
      },
      {
        title: '离职时间',
        key: 'empExitDate',
        dataIndex: 'empExitDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value) || '-';
        },
        formatteFn: text => toDateFormat(text) || '-',
      },
      {
        title: '法人公司所在地',
        dataIndex: 'empWorkCityName',
        key: 'empWorkCityName',
      },
      {
        title: '社保所属地',
        dataIndex: 'empSiCityName',
        key: 'empSiCityName',
      },
      {
        title: '实际休假开始时间',
        dataIndex: 'actualBeginTime',
        key: 'actualBeginTime',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM-DD HH:mm') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM-DD HH:mm') || '-',
      },
      {
        title: '实际休假结束时间',
        key: 'actualEndTime',
        dataIndex: 'actualEndTime',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM-DD HH:mm') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM-DD HH:mm') || '-',
      },
      {
        title: '实际产假额度休完时间',
        key: 'actualQuotaEndDate',
        dataIndex: 'actualQuotaEndDate',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM-DD HH:mm') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM-DD HH:mm') || '-',
      },
      {
        title: '补贴所属月份',
        dataIndex: 'subsidyBelongMonth',
        key: 'subsidyBelongMonth',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM') || '-',
      },
      {
        title: '补贴计算月份',
        dataIndex: 'subsidyCalMonth',
        key: 'subsidyCalMonth',
        render: record => {
          const value = record?.value;
          return toDateFormat(value, 'YYYY-MM') || '-';
        },
        formatteFn: text => toDateFormat(text, 'YYYY-MM') || '-',
      },
      {
        title: '提前返岗小时数',
        dataIndex: 'earlyDurationHour',
        key: 'earlyDurationHour',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '当月工作时数',
        dataIndex: 'monthWorkingHour',
        key: 'monthWorkingHour',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '当月请假时数',
        dataIndex: 'monthLeaveHour',
        key: 'monthLeaveHour',
        render: ({ value }) => roundToOneDecimals(value),
      },
      {
        title: '基础工资',
        dataIndex: 'basicSalary',
        key: 'basicSalary',
      },
      {
        title: '岗位工资',
        dataIndex: 'positionSalary',
        key: 'positionSalary',
      },
      // 专项工资
      {
        title: '专项工资',
        dataIndex: 'meetSalary',
        key: 'meetSalary',
      },
      // 月固定加班工资
      {
        title: '月固定加班工资',
        dataIndex: 'monthOvertimePay',
        key: 'monthOvertimePay',
      },
      // 补贴基数
      {
        title: '补贴基数',
        dataIndex: 'subsidyBase',
        key: 'subsidyBase',
      },
      // 提前返岗金额
      {
        title: '提前返岗金额',
        dataIndex: 'earlyWorkSubsidyValue',
        key: 'earlyWorkSubsidyValue',
        render: ({ value }) => roundToOneDecimals(value, 2),
      },
      // 提前返岗金额差额
      {
        title: '提前返岗金额差额',
        dataIndex: 'earlyWorkSubsidyDifferent',
        key: 'earlyWorkSubsidyDifferent',
        render: ({ value }) => {
          const val = new Big(value || 0).round(2).valueOf();
          return val || 0;
        },
      },
      // 封存状态
      {
        title: '封存状态',
        dataIndex: 'sealedStatusName',
        key: 'sealedStatusName',
      },
    ];
  }, []);

  const filterFormItems: any[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'wfCode',
        label: '所属流程编号',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'input',
        key: 'nameOrAccount',
        label: '申请人别名或域帐号',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'input',
        key: 'thenOrgName',
        label: '请假时行政部门',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'input',
        key: 'currentOrgName',
        label: '当前行政部门',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'select',
        key: 'empStatus',
        label: '人员状态',
        configs: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          optionFilterProp: 'label',
          options: employeeStatusOptions,
        },
      },
      {
        type: 'select',
        key: 'empTypeList',
        label: '用工关系类型',
        configs: {
          showSearch: true,
          allowClear: true,
          placeholder: '请选择',
          optionFilterProp: 'label',
          options: employeeTypeOptions,
          mode: 'multiple',
        },
      },
      // {
      //   type: 'select',
      //   key: 'leaveTypes',
      //   label: '休假类型',
      //   configs: {
      //     mode: 'multiple',
      //     showSearch: true,
      //     allowClear: true,
      //     placeholder: '请选择',
      //     optionFilterProp: 'label',
      //     options: leaveTypeOptions,
      //   },
      // },
      {
        type: 'rangePicker',
        label: '实际休假时间区间',
        key: 'actualLeaveTime',
      },
      {
        type: 'datePicker',
        label: '补贴所属月份',
        key: 'subsidyBelongMonth',
        configs: {
          picker: 'month',
        },
      },
      {
        type: 'datePicker',
        label: '补贴计算月份',
        key: 'subsidyCalMonth',
        configs: {
          picker: 'month',
        },
      },
    ],
    [employeeTypeOptions.length, employeeStatusOptions.length, leaveTypeOptions.length]
  );

  const { pagination = {}, list: payrollList = [] } = defaultObj(payroll);

  const handleExport = () => {
    const newColumns = [...columns].map(columnItem => ({
      ...columnItem,
      render: columnItem.formatteFn && columnItem.formatteFn,
    }));

    console.log('newColumnsnewColumns', newColumns);

    if (selectedRows.length > 0) {
      const exportArr: IExport[] = [
        {
          data: selectedRows,
          sheetName: 'Sheet1',
          columns: newColumns,
        },
      ];
      return exportMultiExcel(exportArr, '产假流程数据.xlsx');
    }

    const searchParams = onGetSearchQuery();

    const params = {
      fetchParams: {
        ...tmgApis.maternityEearlyReturnWorkList,
        data: {
          ...searchParams,
        },
      },
      xlsxName: '产假流程数据',
      configs: {
        maxPageSize: 300,
        columns: newColumns,
      },
    };
    excelTask.add(params);
  };

  // 更新日期
  const handleupdate = () => {
    let handleClose;
    RenderFormModal.add({
      formKey: 'updateDate',
      open: true,
      title: '更新日期',
      onAfterClose: () => {
        handleClose = null;
      },
      onOk: async (values: IObject) => {
        if (!values.calculateMonthDate) {
          return showErrNotification('请选择更新月份');
        }
        values.calculateMonthDate && (values.calculateMonthDate = formatMonthTime(values.calculateMonthDate));
        fetchApi({
          ...tmgApis.maternityEearlyReturnWorkUpdate,
          data: {
            ...values,
          },
          onSuccess: res => {
            console.log('更新请求结果', res);
            if (res) {
              handleClose();
              showSucNotification(res || '更新成功');
              handleClose = null;
              handleGetTaskStatus();
            }
          },
          onError: err => {
            console.log('出错了', err);
          },
        });
        // const res = await fetchApi()
      },
      onInit: fn => {
        handleClose = fn;
      },
      // width: 800,
      formItems: [...genBaseDateItems('calculateMonthDate', '更新日期')],
    });
  };

  // 封存
  const handleSeal = () => {
    let handleClose;
    RenderFormModal.add({
      formKey: 'sealDate',
      open: true,
      title: '封存日期',
      onAfterClose: () => {
        handleClose = null;
      },
      onOk: async (values: IObject) => {
        values.sealMonthDate && (values.sealMonthDate = formatMonthTime(values.sealMonthDate));
        fetchApi({
          ...tmgApis.maternityEearlyReturnWorkSealed,
          data: {
            ...values,
          },
          onSuccess: res => {
            console.log('封存请求结果', res);
            if (res) {
              handleClose();
              showSucNotification(res || '封存成功');
              handleClose = null;
              onFetchNotifyList();
            }
          },
          onError: err => {
            console.log('出错了', err);
          },
        });
        // const res = await fetchApi()
      },
      onInit: fn => {
        handleClose = fn;
      },
      // width: 800,
      formItems: [...genBaseDateItems('sealMonthDate', '封存日期')],
    });
  };
  // 解封
  const handleUnseal = () => {
    let handleClose;
    RenderFormModal.add({
      formKey: 'unsealDate',
      open: true,
      title: '解封日期',
      onAfterClose: () => {
        handleClose = null;
      },
      onOk: async (values: IObject) => {
        values.sealMonthDate && (values.sealMonthDate = formatMonthTime(values.sealMonthDate));
        fetchApi({
          ...tmgApis.maternityEearlyReturnWorkUnsealed,
          data: {
            ...values,
          },
          onSuccess: res => {
            console.log('解封请求结果', res);
            if (res) {
              handleClose();
              showSucNotification(res || '解封成功');
              handleClose = null;
              onFetchNotifyList();
            }
          },
          onError: err => {
            console.log('出错了', err);
          },
        });
      },
      onInit: fn => {
        handleClose = fn;
      },
      formItems: [...genBaseDateItems('sealMonthDate', '解封日期')],
    });
  };
  
  const actionBtnItems: any[] = useMemo(
    () => [
      {
        key: 'export',
        content: '导出',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleExport,
        },
      },
      {
        key: 'update',
        content: '更新',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleupdate,
        },
      },
      {
        key: 'seal',
        content: '封存',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleSeal,
        },
      },
      // 解封
      {
        key: 'unseal',
        content: '解封',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick:  handleUnseal,
        },
      },

    ],
    [selectedRows, payrollList.length]
  );

  return (
    <Spin spinning={payrollListLoading}>
      <div className="EarlyReturnWorkMaternityLeaveList limitModalWrap" ref={containerRef}>
        <WULIWholeTable
          pagination
          canSelect
          multiSelect
          rowKey="id"
          columns={columns}
          data={payrollList}
          paginationConfig={{
            showSizeChanger: true,
            showQuickJumper: true,
            total: pagination.total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
            onChange: (pageNum, pageSize) => onFetchNotifyList({ pageNum, pageSize }),
          }}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            onSearch: () => onFetchNotifyList(),
            onClear: () => {
              WULIFormActions.get(filterFormKey)?.reset();
              onFetchNotifyList();
            },
            defaultLayout: {
              col: 8,
              labelCol: 9,
              wrapperCol: 15,
            },
          }}
          action={actionBtnItems}
          selectedRows={selectedRows}
          afterCancelSelect={newRows => setSelectedRows(newRows)}
          onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => setSelectedRows(_selectedRows)}
        />
      </div>
    </Spin>
  );
};
export default LeaveProcessList;
