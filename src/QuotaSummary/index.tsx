import React, { useCallback, useMemo } from 'react';
import { Spin } from 'antd';

import useFetch from '@hooks/useFetch';
import useDictCode from '@hooks/useDictCode';
import salarySlipApis from '@apis/salarySlip';
import { request as fetchApi } from '@utils/http';
import excelTask from '@components/ExportLargeExcel';
import { exportMultiExcel, IExport } from '@utils/excel';
import { defaultObj, showSucNotification, toDateFormat } from '@utils/tools';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

import { DICT_CODE_MAP_ID } from '@constants/common';

import { IOriginalProps } from '../types/index';

const filterFormKey = 'quotaSummaryFormKey';

export interface IAppProps extends IOriginalProps {
  channel: string;
  processId: string;
}

const QuotaSummary: React.FC<IAppProps> = ({ processId, channel }) => {
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<string[]>([]);

  const { DHR_NOTIFICATION_STATUS = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_NOTIFICATION_STATUS]);

  /** 请求过程 */
  const {
    Data: payroll,
    Loading: payrollListLoading,
    runAction: runActionFetchNotofyList,
  } = useFetch({
    ...salarySlipApis.salaryslipNotifyList,
    params: {
      channel,
      processId,
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  /** 请求过程 */
  const onFetchNotifyList = (data: Record<string, any> = {}) => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    runActionFetchNotofyList({
      params: {
        channel,
        processId,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...formData,
        ...data,
      },
    });
  };

  const columns = useMemo(
    () => [
      {
        title: '姓名',
        dataIndex: 'receiverCode',
        key: 'receiverCode',
      },
      {
        title: '域账号',
        dataIndex: 'channelTypeName',
        key: 'channelTypeName',
      },
      {
        title: '所属公司',
        dataIndex: 'statusName',
        key: 'statusName',
      },
      {
        title: '部门全路径',
        dataIndex: 'typeName',
        key: 'typeName',
      },
      {
        title: '职位',
        dataIndex: 'sendCount',
        key: 'sendCount',
      },
      {
        title: '岗位',
        dataIndex: 'definedSendTime',
        key: 'definedSendTime',
      },
      {
        title: '定额名称',
        dataIndex: 'sendTime',
        key: 'sendTime',
      },
      {
        title: '单位',
        dataIndex: 'crtTime',
        key: 'crtTime',
      },
      {
        title: '使用年份',
        dataIndex: 'crtTime1',
        key: 'crtTime1',
      },
      {
        title: '享有时长',
        dataIndex: 'crtTime2',
        key: 'crtTime2',
      },
      {
        title: '可用时长',
        dataIndex: 'crtTime3',
        key: 'crtTime3',
      },
      {
        title: '结账时长',
        dataIndex: 'crtTime4',
        key: 'crtTime4',
      },
      {
        title: '失效时长',
        dataIndex: 'crtTime5',
        key: 'crtTime5',
      },
    ],
    []
  );

  const filterFormItems: any[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'receiverCode',
        label: '组织',
        configs: {
          allowClear: true,
          placeholder: '请输入',
        },
        col: 8,
      },
      {
        type: 'input',
        key: 'name',
        label: '定额名称',
        configs: {
          allowClear: true,
          placeholder: '请输入',
        },
        col: 8,
      },
      {
        type: 'select',
        key: 'status',
        label: '生成年份',
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: (DHR_NOTIFICATION_STATUS || []).map(codeItem => ({
            ...codeItem,
            label: codeItem.name,
            value: codeItem.itemValue,
            key: codeItem.itemValue,
          })),
        },
        col: 8,
      },
      {
        type: 'select',
        key: 'useYear',
        label: '使用年份',
        configs: {
          allowClear: true,
          placeholder: '请选择',
          options: (DHR_NOTIFICATION_STATUS || []).map(codeItem => ({
            ...codeItem,
            label: codeItem.name,
            value: codeItem.itemValue,
            key: codeItem.itemValue,
          })),
        },
        col: 8,
      },
    ],
    [DHR_NOTIFICATION_STATUS]
  );

  const handleExport = useCallback(() => {
    if (selectedRowKeys.length > 0) {
      const exportArr: IExport[] = [
        {
          columns,
          data: selectedRowKeys,
          sheetName: 'Sheet1',
        },
      ];
      return exportMultiExcel(exportArr, '定额汇总.xlsx');
    }
    // const formValues = WULIFormActions.get(filterFormKey).getValue();
    // const params = {
    //   fetchParams: {
    //     ...cnbApis.structureEmpList,
    //     params: {
    //       ...formValues,
    //     },
    //   },
    //   xlsxName: '人员固定薪资结构管理列表',
    //   configs: {
    //     columns,
    //     extraHandle: handleAssembletableList,
    //   },
    // };
    // excelTask.add(params);
  }, []);

  const actionBtnItems: any[] = useMemo(
    () => [
      {
        key: 'export',
        content: '导出',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleExport,
        },
      },
    ],
    [selectedRowKeys]
  );

  const { list: payrollList = [], pagination = {} } = defaultObj(payroll);
  return (
    <Spin spinning={payrollListLoading}>
      <div className="CnbPayProcessWrap">
        <WULIWholeTable
          pagination
          canSelect
          multiSelect
          rowKey="id"
          columns={columns}
          data={payrollList}
          paginationConfig={{
            current: pagination.pageNum,
            pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (pageNum, pageSize) => onFetchNotifyList({ pageNum, pageSize }),
          }}
          filter={{
            formKey: filterFormKey,
            filters: filterFormItems,
            onSearch: () => onFetchNotifyList(),
            onClear: () => {
              WULIFormActions.get(filterFormKey)?.reset();
              onFetchNotifyList();
            },
            defaultLayout: {
              col: 6,
              labelCol: 6,
              wrapperCol: 18,
            },
          }}
          action={actionBtnItems}
          onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => {
            setSelectedRowKeys(selectKeys);
            setSelectedRows(_selectedRows);
          }}
          selectedRows={selectedRows}
          showSelectedDetail
          rowNameKey="receiverCode"
          afterCancelSelect={newRows => {
            setSelectedRows(newRows);
            setSelectedRowKeys(newRows.map(k => k.id));
          }}
        />
      </div>
    </Spin>
  );
};
export default QuotaSummary;
