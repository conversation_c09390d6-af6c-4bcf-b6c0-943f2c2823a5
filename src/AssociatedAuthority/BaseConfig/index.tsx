import { IInjectFormItemFnParamsContext } from '@cvte/cir-lcp-sdk/src/interface/customComponent/context';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';

export const setDictConfig = (
  formRef: IInjectFormItemFnParamsContext['formRef'],
  key: string,
  value?: any,
  ctx?: {
    context?: IInjectFormItemFnParamsContext;
  }
) => {
  const dictConfig =
    formRef?.current?.getFormItem?.('dictConfig') || formRef?.current?.getInitFormData?.('dictConfig') || {};
  dictConfig[key] = value;
  formRef?.current?.setFormItem?.('dictConfig', dictConfig);
  ctx?.context?.onConfirm?.('dictConfig', dictConfig);
};

const customCptBaseConfig = (context?: IInjectFormItemFnParamsContext): IFormItem[] => {
  const { formRef } = context || {};
  setTimeout(() => {
    const { dictConfig: dictConfigByProps } = (context as any)?.baseFormProps?.componentItem?.config?.baseConfig || {};
    const { height, fieldSource } = dictConfigByProps || {};
    const defFormData = formRef?.current?.getInitFormData?.();
    const curFormData = formRef?.current?.getFormItem?.();
    formRef?.current?.setFormItem?.(
      'dictConfig',
      curFormData?.dictConfig || defFormData?.dictConfig || dictConfigByProps
    );
    formRef?.current?.setFormItem?.(
      'dictConfigHeight',
      curFormData?.dictConfigHeight || defFormData?.dictConfigHeight || height
    );
    formRef?.current?.setFormItem?.(
      'dictConfigFieldSource',
      curFormData?.dictConfigFieldSource || defFormData?.dictConfigFieldSource || fieldSource
    );
  });

  return [
    {
      type: 'hidecomp',
      key: 'dictConfig',
    },
    {
      type: 'inputNumber',
      key: 'dictConfigHeight',
      label: '滚动容器高度',
      configs: {
        placeholder: '请填写',
        onBlur: () => {
          const dictConfigHeight = formRef?.current?.getFormItem?.('dictConfigHeight');
          context?.onConfirm?.('dictConfigHeight', dictConfigHeight);
          setDictConfig(formRef, 'height', dictConfigHeight, {
            context,
          });
        },
      },
    },
    {
      type: 'input',
      key: 'dictConfigFieldSource',
      label: '数据源字段',
      configs: {
        onBlur: () => {
          const dictConfigFieldSource = formRef?.current?.getFormItem?.('dictConfigFieldSource');
          context?.onConfirm?.('dictConfigFieldSource', dictConfigFieldSource);
          setDictConfig(formRef, 'fieldSource', dictConfigFieldSource, {
            context,
          });
        },
      },
    },
  ];
};

export default customCptBaseConfig;
