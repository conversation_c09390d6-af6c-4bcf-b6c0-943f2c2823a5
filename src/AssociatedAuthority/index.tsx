import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { TableRowSelection } from 'antd/es/table/interface';
import type { ColumnsType } from 'antd/es/table';
import { Table, Checkbox } from 'antd';

import commonApis from '@apis/common';
import list2tree from '@cvte/list2tree';
import { request as fetchApi } from '@utils/http';

import './style.less';

const CheckboxGroup = Checkbox.Group;

const btnTypeMap = { line: '行内', tool: '工具' };

interface IAppProps {
  onChange?: (val) => void;
  configs?: {
    config: {
      rulesConfig: any[];
      baseConfig: {
        dictConfig: {
          height?: number;
          fieldSource?: string;
        };
      };
    };
    context: {
      getFormData: () => any;
    };
  };
}

interface DataType {
  id?: string;
  btnType?: string;
  pageName: string;
  key: React.ReactNode;
  children?: DataType[];
  pageButtonList?: any[] | undefined;
}

const AssociatedAuthority: React.FC<IAppProps> = props => {
  const { onChange } = props;

  const listRef = useRef([]);
  const [tableList, setTableList] = useState([]);
  const [selectRows, setSelectRows] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const configs = props?.configs;
  const config = configs?.config;
  const context = configs?.context;
  const rulesConfig = config?.rulesConfig || [];
  const formData = context?.getFormData?.() || {};
  const dictConfig = configs?.config?.baseConfig?.dictConfig || {};
  // 当前字段是否必填
  const isRequired = rulesConfig.some(
    ({ isEnabled, type, value }) => isEnabled && type === 'required' && value === '1'
  );

  const { height, fieldSource } = dictConfig;
  const fieldSourceValues = formData[fieldSource];

  // 初始化数据 - 组装关联权限数据
  const onInitData = useCallback(list => {
    if (list.length > 0) {
      const result = list.reduce((pre, cur) => {
        const pageId = cur['C_PAGE_ID'];
        const btnId = cur['C_BTN_ID'];
        pre.has(pageId)
          ? pre.get(pageId).btnIds.push(btnId)
          : pre.set(pageId, {
              pageId,
              btnIds: btnId ? [btnId] : [],
            });
        return pre;
      }, new Map());
      const newSelectRows = Array.from(result.values());
      setSelectRows(newSelectRows);
    }
  }, []);

  useEffect(() => {
    Array.isArray(fieldSourceValues) && onInitData(fieldSourceValues);
  }, [fieldSourceValues]);

  useEffect(() => {
    if (isRequired && selectRows.length === 0) {
      onChange?.(undefined);
    } else {
      onChange?.([...selectRows]);
    }
  }, [selectRows, isRequired]);

  // 按钮分类
  const handleGroupByBtnType = useCallback(list => {
    if ((list || []).length === 0) {
      return [];
    }
    const btnTypeMapArr = (list || []).reduce((pre, cur) => {
      const btnType = cur.btnType;
      if (pre.has(btnType)) {
        pre.get(btnType).options.push({ ...cur, value: cur.id, label: cur.btnName });
      } else {
        pre.set(btnType, {
          btnType,
          btnTypeName: btnTypeMap[btnType],
          options: [
            {
              ...cur,
              value: cur.id,
              label: cur.btnName,
            },
          ],
        });
      }
      return pre;
    }, new Map());
    const result = Array.from(btnTypeMapArr.values());
    return result;
  }, []);

  // 获取所有向上父节点id
  const handleParentAllIds = useCallback(({ parentId }) => {
    const result = [];
    const newList = [...listRef.current];
    // 向上查找所有关联的id
    const queueIds = [parentId];
    while (queueIds.length > 0) {
      const filterParentId = queueIds.shift();
      const parendDetail = newList.find(({ id }) => filterParentId === id);
      if (parendDetail) {
        const nextParentId = parendDetail?.parentId;
        result.push(filterParentId);
        queueIds.push(nextParentId);
      }
    }

    return result;
  }, []);

  // 向上查找接口有无勾选
  const handleLookUpParentChecked = useCallback((record, list) => {
    const parentIds = handleParentAllIds(record);
    const allSelectIds = list.map(({ pageId }) => pageId);
    const addItems = parentIds
      .filter(parentId => !allSelectIds.includes(parentId))
      .map(pageId => ({
        pageId,
        btnIds: [],
      }));
    const newSelectRows = list.concat(addItems);
    return newSelectRows;
  }, []);

  // 全选/取消全选
  const handleSelectAllBtn = useCallback(
    (isSelected, record, allIds) => {
      const { id } = record;
      let newSelectRows = [...selectRows];
      const index = newSelectRows.findIndex(({ pageId }) => pageId === id);
      const addItem = {
        pageId: id,
        btnIds: isSelected ? allIds : [],
      };
      index > -1 ? newSelectRows.splice(index, 1, addItem) : newSelectRows.push(addItem);
      // 如果是勾选 -> 则需要检查向上的节点有无勾选
      if (addItem.btnIds.length > 0) {
        newSelectRows = handleLookUpParentChecked(record, [...newSelectRows]);
      }
      setSelectRows(newSelectRows);
    },
    [selectRows]
  );

  // 改变按钮复选框
  const handleChangeBtn = useCallback(
    (values, record, list) => {
      const { id } = record;
      let newSelectRows = [...selectRows];
      const index = newSelectRows.findIndex(({ pageId }) => pageId === id);
      const addItem = {
        pageId: id,
        btnIds: values,
      };
      if (index > -1) {
        // 需要去掉的id
        const delIds = list.filter(listItem => !values.includes(listItem.id)).map(({ id }) => id);
        const btnIds = newSelectRows[index]?.btnIds || [];
        // 过滤掉不需要的
        const newBtnIds = [...btnIds, ...values].filter(id => !delIds.includes(id));
        // 去重
        addItem.btnIds = Array.from(new Set(newBtnIds));
        newSelectRows.splice(index, 1, addItem);
      } else {
        newSelectRows.push(addItem);
      }
      // 如果是勾选 -> 则需要检查向上的节点有无勾选
      if (addItem.btnIds.length > 0) {
        newSelectRows = handleLookUpParentChecked(record, [...newSelectRows]);
      }
      setSelectRows(newSelectRows);
    },
    [selectRows]
  );

  // 勾选节点的复选框
  const handleSelect = useCallback(
    (record, isChecked) => {
      let newSelectRows = [...selectRows];
      // 如果是勾选 -> 则需要检查向上的节点有无勾选
      if (isChecked) {
        newSelectRows = handleLookUpParentChecked(record, [...newSelectRows, { pageId: record.id, btnIds: [] }]);
        setSelectRows(newSelectRows);
      } else {
        // 取消复选框时 - 前面如果有勾选 则也要去掉勾选 - 子集的全部取消勾选
        handleSelectTable(record, isChecked);
      }
    },
    [selectRows]
  );

  const handleAllChildRows = useCallback(record => {
    const newList = [...listRef.current];
    const queueIds = [record];
    const result = [];
    const rowKeys = [];
    while (queueIds.length > 0) {
      const currentDetail = queueIds.shift();
      const { id: filterId, pageButtonList: currentButtonList } = currentDetail;
      const btnIds = (currentButtonList || []).map(({ id }) => id);
      const childrenInfos = newList.filter(listItem => listItem.parentId === filterId);
      const newSelectRows = childrenInfos.map(rowItem => {
        const { id, pageButtonList } = rowItem;
        const btnIds = (pageButtonList || []).map(({ id }) => id);
        return {
          pageId: id,
          btnIds,
        };
      });
      rowKeys.push(filterId);
      queueIds.push(...childrenInfos);
      result.push({ pageId: currentDetail.id, btnIds }, ...newSelectRows);
    }

    return {
      rowKeys,
      result,
    };
  }, []);

  // table-复选框 - 勾选则全部勾选
  const handleSelectTable = useCallback(
    (record, selected) => {
      const oldSelectedRowKeys = [...selectedRowKeys];
      const { rowKeys, result } = handleAllChildRows(record);
      const oldSelectRows = [...selectRows].filter(({ pageId }) => !rowKeys.includes(pageId));
      const mergeSelectedRowKeys = selected
        ? oldSelectedRowKeys.concat(rowKeys)
        : oldSelectedRowKeys.filter(key => !rowKeys.includes(key));
      const newSelectedRowKeys = Array.from(new Set(mergeSelectedRowKeys));
      const newSelectRows = selected ? oldSelectRows.concat(result) : oldSelectRows;
      setSelectRows(newSelectRows);
      setSelectedRowKeys(newSelectedRowKeys);
    },
    [selectRows, selectedRowKeys]
  );

  // 全选/取消全选
  const handleSelectAll = useCallback(
    (selected, selectedRows) => {
      const selectedRowKeys = selectedRows.map(({ key }) => key);
      if (selected) {
        const newSelectRows = selectedRows.map(rowItem => {
          const { id, pageButtonList } = rowItem;
          const btnIds = (pageButtonList || []).map(({ id }) => id);
          return {
            pageId: id,
            btnIds,
          };
        });
        setSelectRows(newSelectRows);
      } else {
        setSelectRows([]);
      }
      setSelectedRowKeys(selectedRowKeys);
    },
    [selectRows, selectedRowKeys]
  );

  const columns: ColumnsType<DataType> = useMemo(
    () => [
      {
        width: 300,
        title: '页面名称',
        key: 'pageName',
        dataIndex: 'pageName',
        render: (text, record: any) => {
          const { key } = record;
          const isChecked = selectRows.some(({ pageId }) => pageId === key);
          return (
            <span>
              <Checkbox checked={isChecked} onChange={({ target: { checked } }) => handleSelect(record, checked)} />
              <span className="lcp-margin-left-4">{text}</span>
            </span>
          );
        },
      },
      {
        title: '权限',
        key: 'permission',
        dataIndex: 'permission',
        render: (text, record) => {
          const pageButtonList = record.pageButtonList || [];
          const detail = selectRows.find(rowItem => rowItem.pageId === record.key) || {};
          const btnIds = detail?.btnIds || [];
          const btnList = handleGroupByBtnType(pageButtonList);
          const allButtonIds = pageButtonList.map(({ id }) => id);
          const isNotAllSelect = btnIds.length !== pageButtonList.length;
          return (
            <div className="dhr-associated-permission-btn-container">
              {btnList.length > 0 && (
                <div
                  className="dhr-associated-permission-all-select-btn"
                  onClick={() => handleSelectAllBtn(isNotAllSelect, record, allButtonIds)}
                >
                  <span>{isNotAllSelect ? '全选' : '全不选'}</span>
                </div>
              )}
              <div className="dhr-associated-permission-btn-list-container">
                {btnList.map((btnItem: any) => {
                  return (
                    <div key={btnItem.btnType} className="dhr-associated-permission-btn-list">
                      <div className="dhr-associated-permission-btn-type">{btnItem.btnTypeName}</div>
                      <div className="dhr-associated-permission-checkbox-list">
                        <CheckboxGroup
                          value={btnIds}
                          options={btnItem.options}
                          onChange={values => handleChangeBtn(values, record, btnItem.options)}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        },
      },
    ],
    [selectRows]
  );

  const rowSelection: TableRowSelection<DataType> = useMemo(
    () => ({
      selectedRowKeys,
      onSelect: handleSelectTable,
      onSelectAll: handleSelectAll,
    }),
    [selectedRowKeys, selectRows]
  );

  // 获取角色权限树
  const onFetchTreeData = useCallback(() => {
    fetchApi({
      ...commonApis.rolePermissionTreeData,
      params: {
        id: '65F0A2E4AF89C802E0531F5611AC0157',
      },
      onSuccess: list => {
        const newTableList = list2tree({
          idKey: 'id',
          parentIdKey: 'parentId',
          newKey: {
            key: 'id',
            pageButtonList: 'pageButtonList',
          },
        })(list || []);
        listRef.current = list || [];
        setTableList(newTableList);
      },
    });
  }, []);

  useEffect(() => {
    onFetchTreeData();
  }, []);

  const scrollConfig = useMemo(() => {
    const result = height ? { y: height } : undefined;
    return result;
  }, [height]);

  return (
    <div className="dhr-associated-authority-container">
      <Table
        rowKey="key"
        columns={columns}
        pagination={false}
        scroll={scrollConfig}
        dataSource={tableList}
        rowSelection={rowSelection}
      />
    </div>
  );
};

export default AssociatedAuthority;
