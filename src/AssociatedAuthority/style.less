.dhr-associated-authority-container,
.dhr-associated-authority-template-container {
  .ant-table-tbody {
    tr {
      .dhr-associated-permission-btn-container {
        display: flex;
        .dhr-associated-permission-all-select-btn {
          width: 50px;
          cursor: pointer;
          color: var(--antd-dynamic-primary-color);
          &:hover {
            color: var(--antd-dynamic-primary-hover-color);
          }
        }
        .dhr-associated-permission-btn-list-container {
          flex: 1;
          .dhr-associated-permission-btn-list {
            display: flex;
            flex-wrap: wrap;
            .dhr-associated-permission-btn-type {
              width: 50px;
            }
            .dhr-associated-permission-checkbox-list {
              flex: 1;
            }
          }
        }
      }
      &.ant-table-row-selected {
        & > td {
          background: initial;
        }

        &:hover > td {
          background: #fafafa;
        }
      }
    }
  }
}

.dhr-associated-authority-template-container {
  .ant-table-tbody {
    tr {
      .dhr-associated-permission-btn-container {
        .dhr-associated-permission-all-select-btn {
          color: #1890ff;
          &:hover {
            color: #6687ff;
          }
        }
      }
    }
  }
}
