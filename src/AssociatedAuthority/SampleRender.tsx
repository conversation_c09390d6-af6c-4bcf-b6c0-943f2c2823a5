import type { TableRowSelection } from 'antd/es/table/interface';
import type { ColumnsType } from 'antd/es/table';
import React, { useCallback, useMemo, useState } from 'react';
import { Table, Checkbox } from 'antd';

import { IOriginalProps } from '@src/types';

import './style.less';

interface DataType {
  id?: string;
  btnType?: string;
  pageName: string;
  key: React.ReactNode;
  children?: DataType[];
  pageButtonList?: any[] | undefined;
}

const btnTypeMap = { line: '行内', tool: '工具' };

const CheckboxGroup = Checkbox.Group;

interface IAppProps {
  configs?: {
    config: {
      baseConfig: {
        dictConfig: {
          height?: number;
          fieldSource?: string;
        };
      };
    };
    context: {
      getFormData: () => any;
    };
  };
}
const BaseInfo: React.FC<IAppProps> = props => {
  const configs = props?.configs;
  const dictConfig = configs?.config?.baseConfig?.dictConfig || {};
  const { height } = dictConfig;
  const tableList = useMemo(
    () => [
      {
        id: '07796f5d479847f892c6921581f73a10',
        parentId: '-1',
        pageName: '页面1',
        pageButtonList: [
          {
            id: 'abad9553bb734c758002c2d6544dfd283',
            pageId: '9351927584de45f2a368aebb8484a2d93',
            btnCode: 'save',
            btnName: '按钮一',
            btnIcon: 'save',
            btnType: 'line',
          },
          {
            id: 'a797dfcab51a4002a4222af5a2307f2f2',
            pageId: '9351927584de45f2a368aebb8484a2d92',
            btnCode: 'creat',
            btnName: '按钮二',
            btnIcon: 'plus',
            btnType: 'tool',
            daType: null,
            roleFuncPermissionId: null,
            isHasPermission: '0',
            btnSortNo: '2',
          },
        ],
        key: '07796f5d479847f892c6921581f73a10',
      },
      {
        id: '07796f5d479847f892c6921581f73a1077',
        parentId: '-1',
        pageName: '页面2',
        pageButtonList: [],
        key: '07796f5d479847f892c6921581f73a1077',
        children: [
          {
            id: '07796f5d479847f892c6921581f73a107700',
            parentId: '07796f5d479847f892c6921581f73a1077',
            pageName: '页面2-1',
            pageButtonList: [],
            key: '07796f5d479847f892c6921581f73a107700',
          },
        ],
      },
    ],
    []
  );

  // 按钮分类
  const handleGroupByBtnType = useCallback(list => {
    if ((list || []).length === 0) {
      return [];
    }
    const btnTypeMapArr = (list || []).reduce((pre, cur) => {
      const btnType = cur.btnType;
      if (pre.has(btnType)) {
        pre.get(btnType).options.push({ ...cur, value: cur.id, label: cur.btnName });
      } else {
        pre.set(btnType, {
          btnType,
          btnTypeName: btnTypeMap[btnType],
          options: [
            {
              ...cur,
              value: cur.id,
              label: cur.btnName,
            },
          ],
        });
      }
      return pre;
    }, new Map());
    const result = Array.from(btnTypeMapArr.values());
    return result;
  }, []);

  const columns: ColumnsType<DataType> = useMemo(
    () => [
      {
        width: 300,
        title: '页面名称',
        key: 'pageName',
        dataIndex: 'pageName',
        render: (text, record: any) => {
          return (
            <span>
              <Checkbox />
              <span className="lcp-margin-left-4">{text}</span>
            </span>
          );
        },
      },
      {
        title: '权限',
        key: 'permission',
        dataIndex: 'permission',
        render: (text, record) => {
          const pageButtonList = record.pageButtonList || [];
          const btnList = handleGroupByBtnType(pageButtonList);
          return (
            <div className="dhr-associated-permission-btn-container">
              {btnList.length > 0 && (
                <div className="dhr-associated-permission-all-select-btn">
                  <span>全选</span>
                </div>
              )}
              <div className="dhr-associated-permission-btn-list-container">
                {btnList.map((btnItem: any) => {
                  return (
                    <div key={btnItem.btnType} className="dhr-associated-permission-btn-list">
                      <div className="dhr-associated-permission-btn-type">{btnItem.btnTypeName}</div>
                      <div className="dhr-associated-permission-checkbox-list">
                        <CheckboxGroup options={btnItem.options} />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        },
      },
    ],
    []
  );

  const scrollConfig = useMemo(() => {
    const result = height ? { y: height } : undefined;
    return result;
  }, [height]);
  return (
    <div className="dhr-associated-authority-template-container">
      <Table
        rowKey="key"
        columns={columns}
        pagination={false}
        scroll={scrollConfig}
        dataSource={tableList}
        expandable={{
          defaultExpandAllRows: true,
        }}
      />
    </div>
  );
};

export default BaseInfo;
