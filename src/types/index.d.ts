export { IProps as IOriginalProps } from '@cvte/cir-page-generator-resource-types/src/interface/IProps';
declare interface IAPI {
  [key: string]: {
    url: string;
    method: 'get' | 'post' | 'put' | 'delete';
  };
}

// 天舟云表单组件传入props类型
export interface IGlobalProps {
  onChange: (hid: string) => void;
  value: string;
  data: any;
  id: string;
  configs: {
    wuliMode: string;
    containerType: string;
    config: {
      baseConfig: {
        [key: string]: any;
      };
    };
    utils: {
      [key: string]: any;
    };
    code: string; // 表单字段编码
    context: {
      getFormData: () => any;
      setFormData: (params: any) => void;
      getBatchTranslateCompleteFlag: () => void;
    };
  };
  setCache: (params: any) => void;
  getCache: () => void;
}
