import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { Modal, ConfigProvider, Tooltip } from 'antd';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import { ModalProps } from 'antd/lib/modal';
import React, { useMemo } from 'react';
import axios from 'axios';

import hcmApis from '@apis/hcm';
import internApis from '@apis/intern';
import useDebounce from '@hooks/useDebounce';
import { request as fetchApi } from '@utils/http';
import { showSucNotification } from '@utils/tools';

import './style.less';

const REPLY_MODAL_FORM_KEY = 'interReallocateEvaluationHrFormKey';

export interface IAppProps extends ModalProps {
  onOk?: () => void;
  selectedRows: Record<string, any>[];
}

const ReplyModal: React.FC<IAppProps> = props => {
  const { selectedRows, onOk, ...restProps } = props;
  console.log('selectedRows===', selectedRows);
  const [loading, setLoading] = React.useState(false);
  const [empList, setEmpList] = React.useState([]);
  const cancelTokenSourceRef = React.useRef(null);
  const [visible, setVisible] = React.useState<boolean>(false);

  React.useEffect(() => {
    setVisible(props.open);
  }, [props.open]);

  const handleClose = () => {
    setVisible(false);
  };

  const handleSearchEmp = useDebounce(
    keyword => {
      if (cancelTokenSourceRef.current) {
        cancelTokenSourceRef.current.cancel('取消上次请求');
        cancelTokenSourceRef.current = null;
      }
      cancelTokenSourceRef.current = axios.CancelToken.source();
      fetchApi({
        ...hcmApis.empSearch,
        params: {
          pageNum: 1,
          pageSize: 20,
          objectName: keyword,
        },
        cancelToken: cancelTokenSourceRef.current.token,
        onSuccess: res => {
          const newEmpList = (res?.list || []).map(item => ({
            ...item,
            key: item.empId,
            value: item.empId,
            label: `${item.empName}-${item.empCompanyName}`,
          }));
          setEmpList(newEmpList);
        },
      });
    },
    300,
    []
  );

  const fields: IFormItem[] = React.useMemo(
    () => [
      {
        type: 'select',
        key: 'newHr',
        label: '请选择HR',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '请选择HR',
            },
          ],
        },
        configs: {
          showSearch: true,
          options: empList,
          filterOption: false,
          onSearch: handleSearchEmp,
          placeholder: '请搜索并选择HR人员',
          getPopupContainer: () => document.body,
        },
      },
    ],
    [empList]
  );
  const handleOk = () => {
    WULIFormActions.get(REPLY_MODAL_FORM_KEY).validate((result, { newHr }) => {
      if (result === 'error') {
        return;
      }
      setLoading(true);
      const hrDetail = empList.find(item => item.empId === newHr);
      const newHrName = hrDetail?.empName;
      const assessIdList = selectedRows.map(({ id }) => id);
      fetchApi({
        ...internApis.hrReassignment,
        data: {
          newHr,
          newHrName,
          assessIdList,
        },
        onSuccess: () => {
          handleClose();
          showSucNotification('操作成功');
          onOk?.();
        },
      }).finally(() => {
        setLoading(false);
      });
    });
  };

  const internNames = useMemo(() => {
    return selectedRows.map(item => item.internName).join(',');
  }, [selectedRows]);
  return (
    <ConfigProvider locale={zhCN}>
      <Modal
        {...restProps}
        centered
        open={visible}
        onOk={handleOk}
        maskClosable={false}
        onCancel={handleClose}
        confirmLoading={loading}
        wrapClassName="dhr-intern-reallocate-modal"
        getContainer={() => document.querySelector('.lcp-portal-frame-content-tabs-item')}
      >
        <div className="dhr-intern-reallocate-modal-content">
          <div className="dhr-intern-reallocate-modal-content-header mb-10">
            <div className="dhr-intern-reallocate-modal-content-header-selectTrainee">
              <span className="dhr-intern-reallocate-modal-content-header-selectTrainee-intern-text">
                已选择实习生：
              </span>
              <Tooltip title={internNames}>
                <span className="dhr-intern-reallocate-modal-content-header-selectTrainee-intern-list">
                  {internNames}
                </span>
              </Tooltip>
            </div>
          </div>
          <WULIForm
            layout="vertical"
            formItems={fields}
            formKey={REPLY_MODAL_FORM_KEY}
            defaultLayout={{
              col: 12,
              // labelCol: 7,
              // wrapperCol: 18,
            }}
          />
        </div>
      </Modal>
    </ConfigProvider>
  );
};

ReplyModal.defaultProps = {
  title: '重新分配HR',
  maskClosable: false,
};

const withParams = (params: IAppProps): IAppProps => {
  return {
    ...params,
    open: true,
  };
};

const addModal = (params: IAppProps) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
    params.afterClose && params.afterClose();
  };
  reactRender(<ReplyModal {...params} afterClose={onAfterClose} />, container);
};

const modal = ReplyModal as any;

modal.add = function addFn(props: IAppProps) {
  return addModal(withParams(props));
};

export default modal as {
  (props: IAppProps): JSX.Element;
  defaultProps: typeof ReplyModal.defaultProps;
  add: (props: IAppProps) => void;
};
