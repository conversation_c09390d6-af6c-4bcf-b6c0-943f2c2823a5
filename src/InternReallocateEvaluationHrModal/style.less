.ant-modal-mask:has(+ .dhr-intern-reallocate-modal) {
  position: absolute;
  left: 16px;
  top: 16px;
  bottom: 16px;
  right: 16px;
  height: initial;
  border-radius: 4px;
}
.dhr-intern-reallocate-modal {
  pointer-events: none;
}

.dhr-intern-reallocate-modal-content {
  .dhr-intern-reallocate-modal-content-header {
    margin-bottom: 10px;
    .dhr-intern-reallocate-modal-content-header-selectTrainee {
      padding-left: 8px;
      padding-top: 6px;
      padding-bottom: 6px;
      display: flex;
      align-items: center;
      .dhr-intern-reallocate-modal-content-header-selectTrainee-intern-text {
        width: 100px;
      }
      .dhr-intern-reallocate-modal-content-header-selectTrainee-intern-list {
        flex: 1;
        cursor: pointer;
        overflow: hidden;
        display: -moz-box;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -moz-box-orient: vertical;
        text-decoration: underline;
        -webkit-box-orient: vertical;
        color: var(--antd-dynamic-primary-color);
      }
    }
  }
}
