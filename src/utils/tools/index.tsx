import { notification, Tooltip } from 'antd';
import dayjs, { OpUnitType } from 'dayjs';
import React from 'react';

export const compineLoading = (loadingList: boolean[]) => (loadingList || []).some(k => k);
export const defaultObj = obj => obj || {};
export const defaultArray = arr => arr || [];

export const notificationHandler = type => (description, opts?) => {
  const defaultOpts = {
    message: '提醒',
    duration: 5,
    key: `${Date.now()}`,
  };
  notification[type]({
    description,
    ...defaultOpts,
    ...(opts || {}),
  });
};
// 提示方法
export const showErrNotification = notificationHandler('error');
export const showSucNotification = notificationHandler('success');
export const showWarnNotification = notificationHandler('warn');

/**
 *
 * @param text 需要省略展示的文案
 * @param length 最大显示长度
 * @returns 经过省略显示的 ReactNode
 */
export const ellipsisTag = (text = '', length = 5) => {
  return text?.length > length ? <Tooltip title={text}>{text.slice(0, length)}...</Tooltip> : text;
};

// example: 2018-7-31 xx:xx:xx -> 1532966400000 (2018-7-31 00:00:00)
export const toStartDate = (data, key: OpUnitType = 'day') => {
  if (!data) return data;
  const time = data;
  try {
    return dayjs(data).startOf(key).valueOf();
  } catch (e) {
    return time;
  }
};

// example: 2018-7-31 xx:xx:xx -> 1532966400000 (2018-7-31 23:59:59)
export const toEndDate = (data, key: OpUnitType = 'day') => {
  if (!data) return data;
  const time = data;
  try {
    return dayjs(data).endOf(key).valueOf();
  } catch (e) {
    return time;
  }
};

export const toDateFormat = (value, option = 'YYYY-MM-DD') => {
  if (!value) {
    return value;
  }
  const timeStramp = Number(value);
  if (Number.isNaN(timeStramp)) return '-';
  return dayjs(timeStramp).format(option);
};

export const findNodeWithValue = (tree, value, varKey = 'value') => {
  // 用于存储找到的节点的数组
  // const foundNodes = [];
  let target;

  // 定义递归函数来遍历树结构
  function traverse(node) {
    if (target) return;
    if (node[varKey] === value) {
      // 如果当前节点的value等于要查找的值，则将其添加到foundNodes数组中
      // foundNodes.push(node);
      target = node;
    }
    // 如果当前节点有children，则递归遍历它的children
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => traverse(child));
    }
  }

  // 从树的根节点开始递归遍历
  tree.forEach(rootNode => traverse(rootNode));

  // 返回找到的所有节点
  return target;
};
/**
 * 简易版本防抖函数
 * @param func
 * @param wait
 * @returns
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
};

export const customTemplate = (template, data) => {
  // 使用正则表达式匹配模板中的变量占位符，支持data对象的属性
  const reg = /data\.([a-zA-Z_][a-zA-Z0-9_]*)/g;

  // 替换函数，将匹配到的变量占位符替换为data对象中的值
  const replacer = (match, key) => {
    // 去除前缀 'data.' 并获取属性值
    return data.hasOwnProperty(key) ? data[key] : match;
  };

  // 返回替换后的字符串
  return template.replace(reg, replacer);
};

/**
 *
 * @param list 待过滤对象列表
 * @param uniqueKey 对象唯一key
 * @returns 去重之后的对象列表
 */
export const uniqueArrayByObjKey = (list: Record<string, any>[] = [], uniqueKey) => {
  const uniqueIds = new Set();
  return list.reduce((uniqueArray, item) => {
    if (!uniqueIds.has(item[uniqueKey])) {
      uniqueIds.add(item[uniqueKey]);
      uniqueArray.push(item);
    }
    return uniqueArray;
  }, []) as Record<string, any>[];
};
/**
 *
 * @param num 待转换小数
 * @param decimal  转换小数位
 * @returns 转换后小数
 */
export const roundToOneDecimals = (num, decimal = 1) => {
  if (window.isNaN(num) || num < 0) return 0;
  const base = 10 ** decimal;
  return Math.round(num * base) / base;
};

export const isString = (value: unknown) => {
  return Object.prototype.toString.call(value) === '[object String]';
};

/**
 * 实现一个方法实现，实习以下formValues转换为result的逻辑，要求转换后的key为驼峰小写,去除C_前缀,如C_NAME => name,C_CALENDAR_RULE_ID => calendarRuleId
 */
export const transformFormTZValues = (formValues: Record<string, any> = {}) => {
  const result = {};
  Object.keys(formValues).forEach(key => {
    const parts = key.replace(/^C_/, '').split('_');
    const newKey =
      parts[0].toLowerCase() +
      parts
        .slice(1)
        .map(part => part.charAt(0).toUpperCase() + part.toLowerCase().slice(1))
        .join('');

    result[newKey] = formValues[key];
  });
  return result;
};

// 附件大小尺寸
export const fileDimensionMap = {
  A4: {
    width: 595.276,
    height: 841.89,
  },
  A3: {
    width: 841.89,
    height: 1190.55,
  },
  // 可以添加更多标准尺寸
};

// 判断页面尺寸是否符合标准尺寸的辅助函数
export const isStandardSizePage = (width: number, height: number, standardSize: string): boolean => {
  const standard = fileDimensionMap[standardSize];
  if (!standard) return false;

  // 定义固定误差范围 - 允许±3点的误差
  const TOLERANCE_POINTS = 3;

  // 检查宽高是否在标准尺寸的误差范围内（考虑正向和横向情况）
  const isPortrait =
    Math.abs(width - standard.width) <= TOLERANCE_POINTS && Math.abs(height - standard.height) <= TOLERANCE_POINTS;

  const isLandscape =
    Math.abs(width - standard.height) <= TOLERANCE_POINTS && Math.abs(height - standard.width) <= TOLERANCE_POINTS;

  // 如果是正向或横向标准尺寸，则返回true
  return isPortrait || isLandscape;
};

/**
 * 从字典列表中根据键值获取对应的名称
 * @param value 要查找的值
 * @param dictList 字典列表
 * @param key 比较的键名，默认为'cosCode'
 * @param name 返回的名称键名，默认为'cosName'
 * @returns 找到的名称或原始值（如果未找到）
 */
export const getDictionaryName = <T extends Record<string, any>>(
  value: any,
  dictList: T[] | undefined | null,
  key: keyof T = 'cosCode' as keyof T,
  name: keyof T = 'cosName' as keyof T
): any => {
  // 处理无效值
  if (value === undefined || value === null || value === false) {
    return value;
  }

  // 处理空列表或非数组
  if (!Array.isArray(dictList) || dictList.length === 0) {
    return value;
  }

  // 查找匹配项
  const found = dictList.find(item => item[key] === value);

  // 返回找到的名称或原始值
  return found ? found[name] : value;
};

// 字典选项转换
export const transformDictOptions = (options: Record<string, any>[] = []) => {
  return options.map(item => ({
    label: item.name,
    value: item.itemValue,
  }));
};
