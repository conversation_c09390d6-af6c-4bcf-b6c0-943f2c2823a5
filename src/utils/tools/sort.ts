import { message } from 'antd';
import React from 'react';
import { showWarnNotification } from '.';

export enum ENUM_SORT_TYPES {
  'UP' = 'UP',
  'DOWN' = 'DOWN',
}

export interface ISort {
  selectedRowKeys: React.Key[];
  list: Record<string, any>[];
  rowKey: string;
  // 需要排序的key
  sortKey: string;
}

export const sortUp = ({ selectedRowKeys, list, rowKey, sortKey }: ISort): Record<string, any>[] | null => {
  const newList = [...list];
  const idMapIndexs = newList.reduce((pre, cur, index) => ({ ...pre, [cur[rowKey]]: index }), {});
  // 从小到大排序
  const arrIndexs = selectedRowKeys.map((key: string) => idMapIndexs[key]).sort((a, b) => a - b);
  // 获取最小的索引
  const minIndex = arrIndexs[0];
  // 本来就是第一个元素，不必要了
  if (minIndex === 0) {
    showWarnNotification('当前已经是第一个元素无需上移');
    return null;
  }
  const upIndex = minIndex - 1;
  // 变换位置
  arrIndexs.forEach((arrIndex, index) => {
    const [delItem] = newList.splice(arrIndex, 1);
    newList.splice(upIndex + index, 0, delItem);
  });

  if (sortKey) {
    const result = newList.map((listItem, index) => ({
      ...listItem,
      [sortKey]: index,
    }));
    return result;
  }
  return newList;
};

/** 下移 */
export const sortDown = ({ selectedRowKeys, list, rowKey, sortKey }: ISort): Record<string, any>[] | null => {
  const newList = [...list];
  const idMapIndexs = newList.reduce((pre, cur, index) => ({ ...pre, [cur[rowKey]]: index }), {});
  // 从大到小排序
  const arrIndexs = selectedRowKeys.map((key: string) => idMapIndexs[key]).sort((a, b) => b - a);
  // 获取最大的索引
  const maxIndex = arrIndexs[0];

  const len = newList.length - 1;
  // 本来就是第一个元素，不必要了
  if (maxIndex === len) {
    showWarnNotification('当前已经是最后一个元素无需下移');
    return null;
  }

  const downIndex = maxIndex + 1 - len;
  // 变换位置
  arrIndexs.forEach((arrIndex, index) => {
    const [insertItem] = newList.splice(arrIndex, 1);
    const diffIndex = downIndex - index;
    const insertIndex = diffIndex === 0 ? len : diffIndex;
    newList.splice(insertIndex, 0, insertItem);
  });
  if (sortKey) {
    const result = newList.map((listItem, index) => ({
      ...listItem,
      [sortKey]: index,
    }));
    return result;
  }
  return newList;
};
