import React from 'react';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

/**
 * 根据 valueArr 生成展示值
 *
 * @param {any[]} valueArr
 * @return {*}
 */
const getDisplayValueArr = (valueArr: any[]) => {
  const formatValue = item => {
    if (typeof item === 'string') {
      return {
        displayValue: item,
        tipLabel: item,
      };
    }
    const { description = '', label, title } = item || {};
    const _label = typeof label === 'string' ? label : title;
    return {
      displayValue: (
        <>
          {_label}
          {description ? (
            <Tooltip title={description}>
              <QuestionCircleOutlined />
            </Tooltip>
          ) : null}
        </>
      ),
      tipLabel: _label,
    };
  };
  return valueArr?.reduce(
    (sum, cur, index) => {
      const { displayValue, tipLabel } = formatValue(cur);
      // 第一个不需要逗号
      if (index === 0) {
        // 如果是对象，需要格式化
        sum.displayValue.push(displayValue);
        sum.tipLabel += tipLabel;
        return sum;
      }
      sum.displayValue.push(', ');
      sum.displayValue.push(displayValue);
      sum.tipLabel += `, ${tipLabel}`;
      return sum;
    },
    { displayValue: [], tipLabel: '' }
  );
};
/**
 * 根据 options 生成 optionsMap，用于快速查找
 *
 * @param {*} [_options=[]]
 * @return {*}
 */
export const getOptionsMap = (_options = []) => {
  const result = {};
  _options.forEach(opt => {
    const { description, title, label, key, value } = opt;
    // 如果 label 是字符串，直接赋值
    if (typeof label === 'string') {
      result[key || value] = {
        label,
      };
      return;
    }
    // 如果 description 和 title 都存在，label有可能是组件，所以需要用title作为原始值
    if (description && title) {
      result[key || value] = {
        label: title,
        description,
      };
      return;
    }
    // 其他情况，label 为 label
    result[key || value] = {
      label,
    };
  });
  return result;
};

export /**
 * 根据 value 和 optionsMap 生成展示值
 *
 * @param {({
 *   options?: Record<string, any>[];
 *   value: string | string[];
 *   optionsMap: Record<string, { label: string; description?: string }>;
 *   mode?: 'multiple';
 * })} configs
 * @return {*}
 */
const getDisplayValue = (configs: {
  options?: Record<string, any>[];
  value: string | string[];
  optionsMap?: Record<string, { label: string; description?: string }>;
}) => {
  const { value, options, optionsMap } = configs || {};
  const displayValue = '';
  // 如果没有值，直接返回空
  if (!value?.length) {
    return {
      displayValue,
      optionsMap: optionsMap || getOptionsMap(options),
    };
  }
  // 处理成数组
  const _v = Array.isArray(value) ? value : `${value}`.split(',');
  // 如果没有 optionsMap，需要生成
  const optObj = Object.keys(optionsMap || {})?.length ? optionsMap : getOptionsMap(options);
  // 根据 value 生成展示值
  const valueArr = Object.keys(optObj || {})?.length ? _v?.map(v => optObj[v] || v) : _v;
  // 生成展示值
  const { displayValue: _displayValue, tipLabel } = getDisplayValueArr(valueArr);
  return {
    displayValue: _displayValue,
    tipLabel,
    optionsMap: optObj,
  };
};

/**
 * 对象递归
 *
 * @param {Record<string,any>} data
 * @param {(record?:any)=>any} recursionCb
 * @param {(record?:any)=>boolean} recursionCondition
 */
const objectRecursionFn = (
  data: Record<string, any>,
  recursionCb: (record?: any, key?: string) => any,
  recursionCondition: (record?: any, key?: string) => boolean
) => {
  if (Array.isArray(data) || !data) return;
  Object.keys({ ...(data || {}) }).forEach(key => {
    const record = data[key];
    // 递归回调
    recursionCb(record, key);
    // 如果满足条件，则递归
    recursionCondition(record, key) && objectRecursionFn(record, recursionCb, recursionCondition);
  });
};

/**
 * 回显的配置
 * @param configs
 * @returns 翻译配置
 */

export const mappingRelateConfigsList = (configs: Record<string, any>): Record<string, any> => {
  const relateConfigs = {};
  let prtKey: string = '';
  objectRecursionFn(
    configs,
    (record, key) => {
      const isPrtNode = !record?.dictConfig;
      if (isPrtNode) {
        prtKey = `${key}`;
        return;
      }
      if (!prtKey) prtKey = '';

      let _key = `${key}`;
      _key = `${prtKey || ''}:${key}`;
      if (record?.dictConfig) relateConfigs[_key] = record;
    },
    record => {
      return typeof record === 'object' && !record?.dictConfig;
    }
  );
  return relateConfigs;
};
/**
 *
 * @param dataList 天舟云接口数据列表
 * @param columnList 天舟云接口column列表
 * @returns 翻译列名后的数据
 */
export const formListDataTransform = (dataList: Record<string, any>[] = [], columnList: Record<string, any> = []) => {
  /**
   * 字段映射
   */
  const fieldMap = columnList.reduce((pre, cur) => {
    pre[cur.id] = cur.apiName;
    return pre;
  }, {});

  /**
   * 固定字段映射
   */
  fieldMap.objId = 'objId';

  console.log('fieldMap====', fieldMap);

  return dataList.map(data => {
    const _data = {};
    Object.keys(data).forEach(key => {
      _data[fieldMap[key]] = data[key];
    });
    return _data;
  });
};
