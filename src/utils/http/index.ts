import { http, Fetch } from '@cvte/cir-core-net';
import { AxiosRequestHeaders as IAxiosRequestHeaders, AxiosRequestConfig as IAxiosRequestConfig } from 'axios';
import * as pathToRegexp from 'path-to-regexp';
import { notification } from 'antd';
import { v4 } from 'uuid';

// 移除破折号并截取前16位
const genUuid = () => v4()?.replace(/-/g, '').substring(0, 16);

export type RequestConfig = IAxiosRequestConfig;

export const DHR_SASS_PROXY = '/apis/common/proxy/lcpGw/tz_api/dhr_api/hcm/dhr-lcp-hcm';
export default class Http {
  private static GlobalHttp: Fetch = http;

  private static GlobalRequestHeaders: IAxiosRequestHeaders = {};

  /**
   * 头部信息注入
   *
   * @static
   * @param {IAxiosRequestHeaders} headers
   * @memberof Http
   */
  static injectHeaders(headers: IAxiosRequestHeaders) {
    this.GlobalRequestHeaders = {
      ...this.GlobalRequestHeaders,
      ...(headers || {}),
    };
  }

  /**
   * 请求主体
   *
   * @static
   * @param {IAxiosRequestConfig} reqConfig
   * @return {*}
   * @memberof Http
   */
  static fetch<T>(reqConfig: IAxiosRequestConfig) {
    const config: IAxiosRequestConfig = {
      ...(reqConfig || {}),
      headers: {
        ...(reqConfig?.headers || {}),
        ...(this.GlobalRequestHeaders || {}),
      },
    };
    return this.GlobalHttp.fetch<T>(config as any);
  }
}

interface IFetchApiParams extends IAxiosRequestConfig {
  onError?: (response?: any) => any;
  onSuccess?: (response?: any) => any;
}

/**
 * 请求体钩子核心方法
 *
 * @template T
 * @param {{
 *   configs: IAxiosRequestConfig;
 *   setLoading: React.Dispatch<React.SetStateAction<boolean>>;
 *   setData: (value: T) => void | React.Dispatch<React.SetStateAction<T>>;
 *   onSuccess: (response?: T) => T | Promise<T> | undefined;
 *   onError: (response?: T) => T | Promise<T> | undefined;
 * }} params
 */
export const fetchApi = <T>(params: {
  configs: IFetchApiParams;
  setLoading?: React.Dispatch<React.SetStateAction<boolean>>;
  setData?: (value: T) => void | React.Dispatch<React.SetStateAction<T>>;
  onSuccess?: (response: T) => T | Promise<T> | void;
  onError?: (response: T) => T | Promise<T> | void;
}) => {
  const { configs, setData, setLoading, onSuccess, onError } = params;
  configs.params && (configs.url = pathToRegexp.compile(configs.url)(configs.params));
  configs.data && !Array.isArray(configs.data) && (configs.url = pathToRegexp.compile(configs.url)(configs.data));
  // 统一在configs.headers中注入x-b3-traceid
  configs.headers = {
    ...(configs.headers || {}),
    'x-b3-traceid': genUuid(),
  };
  Http.fetch<T>(configs)
    .then(async resp => {
      const isneedFullResp = ['blob'].includes(configs.responseType as string);
      const { success, response } = resp || {};
      if (!success) {
        const result = await onError?.(response?.data);
        setData?.((result || undefined) as T);
        setLoading?.(false);
        return;
      }
      let result = response?.data;
      if (isneedFullResp) {
        result = await onSuccess?.(response?.data);
        setData?.(result || response?.data);
        setLoading?.(false);
        return;
      }
      if (response?.data?.status !== '0') {
        result = await onError?.(response?.data);
        notification.error({
          description: (response?.data as any)?.message || (response as any)?.message || '请求失败',
          message: '请求失败',
          duration: 2.5,
        });
      } else {
        result = await onSuccess?.(response.data);
      }
      setData?.(result || response?.data);
      setLoading?.(false);
    })
    .catch(async error => {
      notification.error({
        description: error || '发送请求失败',
        message: '请求失败',
        duration: 2.5,
      });
      const result = await onError?.(error);
      setData?.((result || undefined) as T);
      setLoading?.(false);
    });
};

/**
 *
 * @param configs 请求配置
 * @returns
 */
export const request = <T>(_configs: IFetchApiParams): Promise<any> => {
  const configs = {
    baseURL: DHR_SASS_PROXY,
    ..._configs,
  };
  const { onError, onSuccess } = configs;
  configs.params && (configs.url = pathToRegexp.compile(configs.url)(configs.params));
  configs.data && !Array.isArray(configs.data) && (configs.url = pathToRegexp.compile(configs.url)(configs.data));
  configs.headers = {
    ...(configs.headers || {}),
    'x-b3-traceid': genUuid(),
  };
  onError && delete configs.onError;
  onSuccess && delete configs.onSuccess;
  return new Promise((resolve, reject) => {
    Http.fetch<T>(configs)
      .then(async resp => {
        const { success, response } = resp || {};
        if (!success) {
          reject(response?.data);
          onError?.(response?.data);
          return response?.data;
        }
        if (response?.data?.status !== '0') {
          reject(response?.data);
          onError?.(response?.data);
          notification.error({
            description: (response?.data as any)?.message || (response as any)?.message || '请求失败',
            message: '请求失败',
            duration: 2.5,
          });
        } else {
          resolve(response?.data?.data);
          onSuccess?.(response?.data?.data);
        }
        resolve(response?.data?.data);
        return response?.data?.data;
      })
      .catch(async error => {
        notification.error({
          description: error || '发送请求失败',
          message: '请求失败',
          duration: 2.5,
        });
        reject(error);
        onError?.(error);
      });
  });
};

export { IAxiosRequestHeaders, IAxiosRequestConfig };
