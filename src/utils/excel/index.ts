import * as ExcelJS from 'exceljs';

interface ExportXlsxHeader {
  title: string;
  key?: string;
  width?: number;
  ellipsis?: boolean;
  dataIndex?: string;
  render?: (text, record, index) => any;
  onCell?: (record, rowIndex) => { rowSpan?: number; colSpan?: number };
}

export interface SpecialExportXlsxHeader extends ExportXlsxHeader {
  notes?: string | undefined | string[]; // 注解
  config?: {
    options?: any[];
    optionStart?: number;
    optionEnd?: number;
    // formulaConfig?:{
    //   formula:string | ((rowNum:number, colNum:number, colLetter:string) => any);
    //   result?:any;
    // }
    align?: 'topLeft' | 'center' | 'bottomRight'; // 优先级高于全局align
    dataValidation?: {
      type?: string;
      formulae: any[];
      allowBlank?: boolean;
    };
  };
}

export interface IExport {
  data?: any[];
  sheetName: string;
  columns: SpecialExportXlsxHeader[];
  align?: 'topLeft' | 'center' | 'bottomRight';
}

/**
 * 在指定列中添加下拉验证
 * @param {Worksheet} ws 工作表对象
 * @param {Number} columnNumber 列号，从1开始
 * @param {Number} rowStart 起始行号，从1开始
 * @param {Number} rowEnd 结束行号，从1开始
 */
const addDropdownValidation = (ws, columnNumber, config) => {
  let alignment;
  let dataValidations;
  const rowStart = config.optionStart || 2;
  const rowEnd = config.optionEnd || 999;
  const column = ws.getColumn(columnNumber);
  // 列字母
  const colKey = column.letter;
  // 设置对其方式
  if (config.align) {
    switch (config.align) {
      case 'topLeft':
        alignment = { vertical: 'top', horizontal: 'left' };
        break;
      case 'center':
        alignment = { vertical: 'middle', horizontal: 'center' };
        break;
      case 'bottomRight':
        alignment = { vertical: 'bottom', horizontal: 'right' };
        break;
      default:
        break;
    }
  }

  // 设置下拉菜单
  if (config.options || config.dataValidation) {
    const optionsStr = (config.options || []).join(',');
    const optionsJson = JSON.stringify(optionsStr);
    dataValidations = {
      type: 'list',
      allowBlank: true,
      formulae: [optionsJson],
      ...config.dataValidation,
    };
  }

  // 设置
  if (alignment || dataValidations) {
    // 公式
    /** 一定要设置result 不好用 */
    //  const formula = (config.formulaConfig || {}).formula;
    //  const result = (config.formulaConfig || {}).result || '';
    //  const formulaProtoType = Object.prototype.toString.apply(formula);
    for (let i = rowStart; i <= rowEnd; i++) {
      const cell = ws.getCell(`${colKey}${i}`);
      // formula && (cell.value = {
      //   ...cell.value,
      //   result:result,
      //   formula:formulaProtoType === '[object Function]' ? formula(i, columnNumber, colKey) : formula,
      // });
      alignment && (cell.alignment = alignment);
      dataValidations && (cell.dataValidation = dataValidations);
    }
  }
};

/**
 *
 * @param exportArr
 *  格式：[{data:[], columns:[], sheetName:''}]
 * @param xlsxName
 *  导出xlsx名 例如：xx.xlsx
 */
export const exportMultiExcel = async (exportArr: IExport[], xlsxName: string) => {
  const defaultFontConfig: any = {
    font: {
      size: 10,
      family: 2,
      color: { theme: 0 },
      name: 'Calibri',
      scheme: 'minor',
    },
  };
  const wb = new ExcelJS.Workbook();
  exportArr.forEach(exportItem => {
    const ws = wb.addWorksheet(exportItem.sheetName);
    const exportColums = exportItem.columns.map(({ title, key, dataIndex, width }) => ({
      header: title,
      key: key || dataIndex,
      width: width ? Math.max(Math.ceil(width / 6), 30) : 30,
    }));
    ws.columns = exportColums;
    // 针对render 处理
    if (exportItem.data) {
      const aoa = [];
      const mergeArr = [];
      exportItem.data.forEach((dataItem, dataIndex) => {
        let row = {};
        exportItem.columns.forEach((columItem, index) => {
          const key = columItem.key || columItem.dataIndex;
          const text = dataItem[key];
          const result = columItem.render ? columItem.render(text, dataItem, dataIndex) : text;
          row = {
            ...row,
            [key]: Object.prototype.toString.apply(result) === '[object Object]' ? text : result,
          };

          // 合并
          // 源数据存在rowSpan/colSpan 则合并
          // rowSpan/colSpan 按照antd合并格式
          if (columItem.onCell) {
            const { rowSpan, colSpan } = columItem.onCell(dataItem, dataIndex);
            if (rowSpan || colSpan) {
              const startRow = dataIndex + 2;
              const startCol = index + 1;
              const endRow = startRow + (rowSpan > 0 ? rowSpan - 1 : 0);
              const endCol = startCol + (colSpan > 0 ? colSpan - 1 : 0);
              // 开始行，开始列，结束行，结束列
              mergeArr.push([startRow, startCol, endRow, endCol]);
            }
          }
        });
        aoa.push(row);
      });
      ws.addRows(aoa);
      // 合并单元格
      if (mergeArr.length > 0) {
        mergeArr.forEach(mergeItem => {
          const [startRow, startCol, endRow, endCol] = mergeItem;
          ws.mergeCells(startRow, startCol, endRow, endCol);
        });
      }
    }
    exportItem.columns.forEach(({ notes, config = {} }, index) => {
      // 获取列的字母
      const code = ws.getColumn(index + 1).letter;
      if (notes) {
        const cell = ws.getCell(`${code}1`);
        const isArr = Object.prototype.toString.apply(notes) === '[object Array]';
        cell.note = {
          texts: isArr
            ? ((notes as string[]) || []).map(note => ({ text: note, ...defaultFontConfig }))
            : [{ text: notes as string, ...defaultFontConfig }],
          margins: {
            insetmode: 'custom',
            inset: [0.25, 0.25, 0.35, 0.35],
          },
          editAs: 'twoCells',
        };
      }

      // 设置/下拉选项
      addDropdownValidation(ws, index + 1, {
        ...config,
        align: config.align ? config.align : exportItem.align,
      });
    });
  });

  wb.xlsx.writeBuffer().then(buffer => {
    // 将数据流转换为Blob对象
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    // 创建一个下载链接，并模拟用户单击下载
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = xlsxName;
    link.click();
  });
};
