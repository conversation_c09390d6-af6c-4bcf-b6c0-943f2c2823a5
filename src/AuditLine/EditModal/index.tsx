import React, { forwardRef, useImperativeHandle, useMemo } from 'react';
import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import list2tree from '@cvte/list2tree';
import { ConfigProvider } from 'antd';
import * as uuid from 'uuid';

import cnbApis from '@apis/cnb';
import buzroleApis from '@apis/buzrole';
import useFetch from '@hooks/useFetch';
import { toStartDate } from '@utils/tools';
import useDictCode from '@hooks/useDictCode';
import { request as fetchApi } from '@utils/http';

import EmpSelect from '@components/EmpSelect';

import { DICT_CODE_MAP_ID } from '@constants/common';

import './style.less';

const defaultLayout = {
  col: 24,
  labelCol: 6,
  wrapperCol: 16,
};

const formKey = 'auditLineEditFormKey';

export interface IAppProps {}
const EditModal: React.FC<IAppProps> = forwardRef((props, ref) => {
  const { DHR_ORG_AUDIT_LINE_ROLE_TYPE = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_ORG_AUDIT_LINE_ROLE_TYPE]);

  const typeMapCode = useMemo(
    () => ({
      // 组织全路径
      C_ORG_HID: 'orgHid',
      // 生效时间
      C_BEGIN_DATE: 'beginDate',
      // HRVP
      C_CVTE_HRVP: 'CVTE_HRVP',
      // HRD
      C_HRMANAGER: 'hrManager',
      // 直接主管
      C_UNITMANAGER: 'unitManager',
      // HRMO
      C_CVTE_HR_CONTACTS: 'CVTE_HR_CONTACTS',
      // 预算负责人
      C_CVTE_BUDGET_MANAGER: 'CVTE_Budget_Manager',
      // 初级审核人
      C_CVTE_FIRST_APPROVAL: 'CVTE_FIRST_APPROVAL',
      // 中级审核人
      C_CVTE_SECOND_APPROVAL: 'CVTE_SECOND_APPROVAL',
      // 高级审核人
      C_CVTE_THIRD_APPROVAL: 'CVTE_THIRD_APPROVAL',
    }),
    []
  );

  const codeMapType = useMemo(() => {
    const result = Object.keys(typeMapCode).reduce((pre, key) => {
      pre[typeMapCode[key]] = key;
      return pre;
    }, {});
    return result;
  }, [typeMapCode]);

  /** 组装成table的数据 */
  const handleTableDataAssemble = list => {
    const tableList = [];
    for (let i = 0; i < (list || []).length; i++) {
      const listItem = list?.[i];
      const { orgHid, beginDate, empInfoViewList } = listItem || {};
      const tableItem = {
        ID: uuid.v4().replace(/-/g, ''),
        [codeMapType[typeMapCode.C_ORG_HID]]: orgHid,
        [codeMapType[typeMapCode.C_BEGIN_DATE]]: beginDate,
      };
      for (let index = 0; index < (empInfoViewList || []).length; index++) {
        const element = empInfoViewList[index];
        const key = element.roleCode || '';
        if (tableItem[codeMapType[key]]) {
          tableItem[codeMapType[key]] = tableItem[codeMapType[key]].concat(',', element.empId);
          tableItem[`${codeMapType[key]}_NAME`] = tableItem[`${codeMapType[key]}_NAME`].concat(',', element.empName);
        } else if (!!codeMapType[key]) {
          tableItem[codeMapType[key]] = element.empId;
          tableItem[`${codeMapType[key]}_NAME`] = element.empName;
        }
      }
      tableList.push(tableItem);
    }
    return tableList;
  };

  // table数据转请求的数据
  const handleAssembleFetchData = list => {
    const fetchList = [];
    for (let index = 0; index < (list || []).length; index++) {
      const listItem = list?.[index];
      const empInfoViewList = [];
      Object.keys(listItem)
        .filter(key => key.includes('_NAME'))
        .forEach(key => {
          const [roleCodeKey] = key.split('_NAME');
          const empNames = (listItem[key] || '').split(',');
          const empIds = (listItem[roleCodeKey] || '').split(',');
          const roleCode = typeMapCode[roleCodeKey];
          const newEmpInfoViewItem = empIds.map((empId, empIndex) => ({
            empId,
            roleCode,
            empName: empNames[empIndex],
          }));
          empInfoViewList.push(...newEmpInfoViewItem);
        });
      const updateParams = {
        empInfoViewList: empInfoViewList,
        [typeMapCode.C_ORG_HID]: listItem[codeMapType[typeMapCode.C_ORG_HID]],
        [typeMapCode.C_BEGIN_DATE]: listItem[codeMapType[typeMapCode.C_BEGIN_DATE]],
      };
      fetchList.push(updateParams);
    }
    const result = {
      isNew: '0',
      auditLineDetailViews: fetchList,
    };
    return result;
  };

  useImperativeHandle(ref, () => ({
    onOk: propList =>
      new Promise((resolve, reject) => {
        WULIFormActions.get(formKey).validate((status, formValues) => {
          if (status === 'error') {
            return reject(false);
          }
          const params = {
            ...formValues,
            empId: formValues.empId?.value,
            beginDate: toStartDate(formValues.beginDate),
            orgHides: (formValues.orgHides || []).join(','),
            roleCodes: (formValues.roleCodes || []).join(','),
          };

          fetchApi({
            ...buzroleApis.auditLineFlowData,
            params,
            onSuccess(res) {
              const newDetail = {
                isNew: '1',
                auditLineDetailViews: res || [],
              };
              const oldDetail = handleAssembleFetchData(propList);
              fetchApi({
                ...buzroleApis.auditLineFilterData,
                data: [newDetail, oldDetail],
                onSuccess(result) {
                  console.log('cccc====', result);

                  const tableList = handleTableDataAssemble(result || []);
                  console.log('数据结果：', tableList);

                  return resolve(tableList);
                },
                onError: reject,
              });
            },
            onError: reject,
          });
        });
      }),
  }));

  const { Data: dimRes, Loading: dimLoading } = useFetch({
    ...cnbApis.dimList,
    params: {
      isAuth: '1',
      status: '1',
      excludeCategories: 'COMPANY', // 过滤法人公司
    },
  });

  const dimData = (dimRes as Record<string, any>) || [];
  /** 数据转换 */
  const transformTreeData = data => {
    return list2tree({
      idKey: 'hid',
      parentIdKey: 'parentHid',
      newKey: {
        key: 'hid',
        value: 'hid',
        title: 'name',
        name: 'name',
      },
    })(data);
  };

  const treeData = useMemo(() => {
    const _treeData = transformTreeData(dimData);
    return _treeData;
  }, [dimData]);

  const formItems: IFormItem[] = useMemo(
    () => [
      {
        type: 'custom',
        key: 'empId',
        label: '用户名(单选)',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '必填',
            },
          ],
        },
        render: params => <EmpSelect {...params} />,
      },
      {
        type: 'select',
        key: 'roleCodes',
        label: '角色名称(多选)',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '必填',
            },
          ],
        },
        configs: {
          mode: 'multiple',
          allowClear: true,
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          options: DHR_ORG_AUDIT_LINE_ROLE_TYPE,
          fieldNames: { label: 'name', value: 'itemValue' },
        },
      },
      {
        type: 'treeSelect',
        key: 'orgHides',
        label: '管控单元(多选)',
        required: true,
        configs: {
          multiple: true,
          allowClear: true,
          showSearch: true,
          options: treeData,
          placeholder: '请选择',
          treeNodeFilterProp: 'title',
        },
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '必填',
            },
          ],
        },
      },
      {
        type: 'datePicker',
        key: 'beginDate',
        label: '生效日期',
        required: true,
        configs: {
          allowClear: true,
          placeholder: '请选择',
        },
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '必填',
            },
          ],
        },
      },
    ],
    [DHR_ORG_AUDIT_LINE_ROLE_TYPE, treeData]
  );
  return (
    <div>
      <ConfigProvider locale={zhCN}>
        <WULIForm formKey={formKey} formItems={formItems} defaultLayout={defaultLayout} />
      </ConfigProvider>
    </div>
  );
});

export default EditModal;
