import React, { useCallback, useEffect, useMemo, useState } from 'react';
import type { DatePickerProps } from 'antd/es/date-picker';
import { Spin, message, DatePicker } from 'antd';
import * as uuid from 'uuid';
import moment from 'moment';

import useFetch from '@hooks/useFetch';
import buzroleApis from '@apis/buzrole';
import useDictCode from '@hooks/useDictCode';
import { request as fetchApi } from '@utils/http';
import { defaultObj, toStartDate } from '@utils/tools';

import TreeSelect from '@components/TreeSelect';
import RoleAdjust from './containers/RoleAdjust';
import WULIWholeTable, { DEFAULT_PAGE_SIZE } from '@components/WholeTable/AgGrid';

import { DICT_CODE_MAP_ID } from '@constants/common';

import './style.less';
import { IOriginalProps } from '../../types';

export type IAppProps = IOriginalProps;

const AuditLineAdjustment: React.FC<IAppProps> = props => {
  const [open, setOpen] = useState<boolean>(false);
  const [selectRows, setSelectRows] = useState<any[]>([]);
  const [deptHid, setDeptHid] = useState<string>(undefined);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [defaultLoading, setDefaultLoading] = useState<boolean>(false);
  const [queryDate, setQueryDate] = useState<number>(moment().valueOf());

  const { DHR_TP_ROLE_TYPE = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_TP_ROLE_TYPE]);
  console.log('restPropsrestProps', DHR_TP_ROLE_TYPE);

  /** 请求过程 */
  const {
    Data: cnbArchives,
    Loading: cnbArchivesLoading,
    runAction: runActionOfCnbArchivesList,
  } = useFetch({
    ...buzroleApis.auditLineList,
    params: {},
    isImmediate: false,
  });

  const { list: cnbArchivesList = [], pagination = {} } = defaultObj(cnbArchives);

  const roleTypeNameMap = useMemo(() => {
    const result = (DHR_TP_ROLE_TYPE || []).reduce((pre, cur) => {
      pre[cur?.itemValue] = cur?.name;
      return pre;
    }, {});

    return result;
  }, [DHR_TP_ROLE_TYPE]);

  const columns = [
    {
      title: '角色名称',
      key: 'roleName',
      dataIndex: 'roleName',
      render: ({ value }) => value || '-',
    },
    {
      title: '选择类型',
      key: 'objType',
      dataIndex: 'objType',
      render: ({ value }) => roleTypeNameMap[value] || '-',
    },
    {
      title: '设置内容',
      key: 'objectInfoViews',
      dataIndex: 'objectInfoViews',
      render: ({ value }) => (value || []).map(({ objectName }) => objectName).join(',') || '-',
    },
    {
      title: '数据来源',
      key: 'sourceName',
      dataIndex: 'sourceName',
      render: ({ value }) => value || '-',
    },
  ];

  const onFetchCnbArchivesList = (data = {}) => {
    runActionOfCnbArchivesList({
      params: {
        pageNum: 1,
        queryDate,
        orgHid: deptHid,
        pageSize: DEFAULT_PAGE_SIZE,
        ...data,
      },
    });
  };

  useEffect(() => {
    deptHid && queryDate && onFetchCnbArchivesList();
  }, [deptHid, queryDate]);

  const handleDefault = () => {
    setDefaultLoading(true);
    const { objType, roleCode } = selectRows[0] || {};
    fetchApi({
      ...buzroleApis.auditLineDefault,
      params: {
        orgId: deptHid,
        objectType: objType,
        roleCodes: roleCode,
        beginDate: toStartDate(new Date().getTime()),
      },
      onSuccess: res => {
        message.success('操作成功');
        onFetchCnbArchivesList();
      },
    }).finally(() => setDefaultLoading(false));
  };

  const handleRoleAdjust = values => {
    setConfirmLoading(true);
    fetchApi({
      ...buzroleApis.auditLineAdjust,
      params: {
        orgId: deptHid,
        ...values,
        beginDate: toStartDate(values.beginDate),
        objectIds: (values.objectIds || []).map(({ value }) => value).join(','),
      },
      onSuccess: res => {
        setOpen(false);
        setSelectRows([]);
        message.success('操作成功');
        onFetchCnbArchivesList();
      },
    }).finally(() => setConfirmLoading(false));
  };

  const actionBtnItems = useMemo(
    () => [
      {
        key: 'adjust',
        content: '调整',
        type: 'button',
        className: 'lcp-margin-left-10',
        config: {
          type: 'primary',
          onClick: () => setOpen(true),
          disabled: selectRows.length !== 1,
        },
      },
      {
        key: 'default',
        content: '恢复默认值',
        type: 'button',
        config: {
          type: 'primary',
          onClick: handleDefault,
          loading: defaultLoading,
          disabled: selectRows.length !== 1,
        },
      },
    ],
    [selectRows, defaultLoading]
  );

  const handleChangeQueryDate = useCallback((val: DatePickerProps['value']) => {
    const newQueryDate = val.valueOf();
    setQueryDate(newQueryDate);
  }, []);

  const tableList = useMemo(() => {
    const result = (cnbArchivesList || []).map(
      listItem => ({
        ...listItem,
        rowId: uuid.v4(),
      }),
      []
    );
    return result;
  }, [cnbArchivesList]);

  return (
    <Spin spinning={cnbArchivesLoading}>
      <div className="dhr-audit-line-adjust-content">
        <div className="dhr-audit-line-adjust-left">
          <DatePicker
            allowClear={false}
            style={{ width: '100%' }}
            defaultValue={moment(queryDate)}
            className="lcp-margin-bottom-10"
            onChange={handleChangeQueryDate}
          />
          <TreeSelect
            fetchParams={{
              isAuth: '1',
              status: '1',
            }}
            onSelectNode={data => setDeptHid(data?.[0])}
          />
        </div>
        <div className="dhr-audit-line-adjust-right">
          <WULIWholeTable
            canSelect
            pagination
            rowKey="rowId"
            columns={columns}
            data={tableList}
            action={actionBtnItems}
            paginationConfig={{
              size: 'small',
              ...pagination,
              current: pagination?.pageNum || 1,
              showSizeChanger: true,
              showQuickJumper: true,
              onChange: (pageNum, pageSize) =>
                onFetchCnbArchivesList({
                  pageNum,
                  pageSize,
                }),
            }}
            onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => setSelectRows(_selectedRows)}
          />
        </div>
      </div>
      <RoleAdjust
        open={open}
        onOk={handleRoleAdjust}
        initValues={selectRows[0]}
        confirmLoading={confirmLoading}
        options={DHR_TP_ROLE_TYPE || []}
        onCancel={() => setOpen(false)}
      />
    </Spin>
  );
};
export default AuditLineAdjustment;
