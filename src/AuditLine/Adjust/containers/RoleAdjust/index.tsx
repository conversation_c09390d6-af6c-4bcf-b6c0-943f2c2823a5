import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import React, { useEffect, useMemo } from 'react';
import { Modal } from 'antd';
import moment from 'moment';

import { WULIForm, WULIFormActions } from '@cvte/wuli-antd';

import EmpSelect from '@components/EmpSelect';

const ROLE_ADJUST_FORM_KEY = 'roleAdjustFormKey';

import './style.less';

export interface IAppProps {
  open: boolean;
  options: any[];
  initValues: any;
  onCancel: () => void;
  confirmLoading: boolean;
  onOk: (values: any) => void;
}
const RoleAdjust: React.FC<IAppProps> = ({ confirmLoading, open, onOk, onCancel, options, initValues }) => {
  const fields: IFormItem[] = useMemo(
    () => [
      {
        type: 'display',
        key: 'orgName',
        label: '行政组织',
        configs: {
          content: initValues?.orgName || '-',
        },
      },
      {
        type: 'custom',
        key: 'objectIds',
        label: '人员',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '必填',
            },
          ],
        },
        render: props => <EmpSelect mode="multiple" {...props} />,
      },
      {
        type: 'select',
        key: 'objectType',
        label: '选择类型',
        required: true,
        configs: {
          placeholder: '请选择',
          filterOption: true,
          allowClear: true,
          showSearch: true,
          options: options,
          optionFilterProp: 'name',
          fieldNames: { label: 'name', value: 'itemValue' },
        },
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '必填',
            },
          ],
        },
      },
      {
        type: 'datePicker',
        key: 'beginDate',
        label: '生效日期',
        required: true,
        decoratorOptions: {
          rules: [
            {
              required: true,
              message: '必填',
            },
          ],
        },
      },
      {
        type: 'textarea',
        key: 'remarks',
        label: '变更原因',
      },
    ],
    [options, initValues]
  );

  const handleOk = () => {
    WULIFormActions.get(ROLE_ADJUST_FORM_KEY).validate((result, values) => {
      if (result === 'error') {
        return;
      }
      onOk?.({
        ...values,
        roleCodes: initValues?.roleCode,
      });
    });
  };

  const initialData = useMemo(() => {
    if (initValues) {
      const { objectInfoViews, beginDate, remarks, objType } = initValues;
      const objectIds = (objectInfoViews || []).map(objItem => ({
        ...objItem,
        value: objItem.objectId,
        label: objItem.objectName,
      }));

      const updateParams = {
        objectIds,
        remarks,
        objectType: objType,
        beginDate: beginDate ? beginDate : new Date().getTime(),
      };

      return updateParams;
    }
  }, [initValues]);

  useEffect(() => {
    open &&
      WULIFormActions.get(ROLE_ADJUST_FORM_KEY).setValue({
        ...initialData,
      });
  }, [open, initialData]);

  return (
    <Modal title="组织业务角色关系设置" confirmLoading={confirmLoading} open={open} onCancel={onCancel} onOk={handleOk}>
      <div className="dhr-role-adjust-modal-form">
        <WULIForm
          formItems={fields}
          formKey={ROLE_ADJUST_FORM_KEY}
          defaultLayout={{
            col: 24,
            labelCol: 6,
            wrapperCol: 16,
          }}
        />
      </div>
    </Modal>
  );
};

export default RoleAdjust;
