/**
 * 本页面专门用于系统升级
 * extratips
 */
import { Result } from 'antd';
import React from 'react';
import { IOriginalProps } from '../types';

export interface IAppProps extends IOriginalProps {
  extraTips?: string;
  title?: string;
}
const defaultTitle = '请假流程更新中，请升级完成后再试';
const BaseInfo: React.FC<IAppProps> = props => {
  const { title, extraTips } = props;
  return (
    <div>
      <Result title={title || defaultTitle} subTitle={extraTips || ''} />
    </div>
  );
};

export default BaseInfo;
