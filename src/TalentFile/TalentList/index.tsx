import React, { useEffect, useRef } from 'react';
import { Spin } from 'antd';

import useFetch from '@hooks/useFetch';
import employeeApis from '@apis/employee';
import { defaultObj, toDateFormat } from '@utils/tools';

import TreeSelect from '@components/TreeSelect';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

import './style.less';
import '../../styles/atom.less';
import { IOriginalProps } from '../../types';

const filterFormKey = 'talentFormKey';

export interface IAppProps extends IOriginalProps {
  systemHelper: {
    history: any;
    menu: {
      currentUrl: string;
    };
  };
}

const TalentList: React.FC<IAppProps> = props => {
  const { systemHelper, ...restProps } = props;

  const [deptHid, setDeptHid] = React.useState<string>(undefined);
  console.log('restPropsrestProps', restProps);

  /** 请求过程 */
  const {
    Data: cnbArchives,
    Loading: cnbArchivesLoading,
    runAction: runActionOfCnbArchivesList,
  } = useFetch({
    ...employeeApis.employeeTalentFileList,
    params: {},
  });
  const { list: cnbArchivesList = [], pagination = {} } = defaultObj(cnbArchives);
  const handleJumpToNoticeProgress = (data: Record<string, any>) => {
    try {
      const jumpUrl = `/portal/b5cbqdyq/b5cbqdyq_talent_manage_archive/detail?empId=${data?.empId || ''}&account=${
        data?.account || ''
      }&pageName=人才档案详情-${data?.otherName || ''}`;
      systemHelper.history.push(jumpUrl);
      console.log('跳转链接：', jumpUrl);
    } catch (error) {
      console.log('跳转失败:', error);
    }
  };

  const columns = [
    {
      title: '别名',
      key: 'otherName',
      dataIndex: 'otherName',
      render: ({ data, value }) => (
        <div className="dhr-talent-name-container">
          <div className="dhr-talent-name" onClick={() => handleJumpToNoticeProgress(data)}>
            {value}
          </div>
        </div>
      ),
    },
    {
      title: '域账号',
      key: 'account',
      dataIndex: 'account',
      render: ({ value }) => value || '-',
    },
    {
      title: '工号',
      key: 'code',
      dataIndex: 'code',
      render: ({ value }) => value || '-',
    },
    {
      title: '法人公司',
      key: 'laborName',
      dataIndex: 'laborName',
      render: ({ value }) => value || '-',
    },
    {
      title: '部门',
      key: 'deptFullPathName',
      dataIndex: 'deptFullPathName',
      render: ({ value }) => value || '-',
    },
    {
      title: '职位',
      key: 'positionName',
      dataIndex: 'positionName',
      render: ({ value }) => value || '-',
    },
    {
      title: '入职时间',
      key: 'beginDate',
      dataIndex: 'beginDate',
      render: ({ value }) => toDateFormat(value) || '-',
    },
    {
      title: '出生日期',
      key: 'brithDate',
      dataIndex: 'brithDate',
      render: ({ value }) => toDateFormat(value) || '-',
    },
    {
      title: '管理层级',
      key: 'jobManageLevel',
      dataIndex: 'jobManageLevel',
      render: ({ value }) => value || '-',
    },
  ];

  const filterFormItems = [
    {
      type: 'input',
      key: 'empName',
      label: '人员别名',
      configs: {
        placeholder: '请输入人员别名',
      },
      col: 8,
    },
  ];

  useEffect(() => {
    deptHid && onFetchCnbArchivesList();
  }, [deptHid]);

  const onFetchCnbArchivesList = (data = {}) => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    runActionOfCnbArchivesList({
      params: {
        deptHid,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...formData,
        ...data,
      },
    });
  };

  return (
    <Spin spinning={cnbArchivesLoading}>
      <div className="flex">
        <div
          style={{
            width: '300px',
          }}
        >
          <TreeSelect
            fetchParams={{
              isAuth: '1',
              status: '1',
            }}
            onSelectNode={data => setDeptHid(data?.[0])}
          />
        </div>
        <div className="archivesList">
          <WULIWholeTable
            pagination
            rowKey="empId"
            columns={columns}
            data={cnbArchivesList}
            filter={{
              formKey: filterFormKey,
              filters: filterFormItems,
              onSearch: () => onFetchCnbArchivesList(),
              defaultLayout: {
                col: 6,
                labelCol: 6,
                wrapperCol: 18,
              },
              className: 'mb-10',
              onClear: () => {
                WULIFormActions.get(filterFormKey)?.reset();
                onFetchCnbArchivesList();
              },
            }}
            paginationConfig={{
              size: 'small',
              ...pagination,
              current: pagination?.pageNum || 1,
              showSizeChanger: true,
              showQuickJumper: true,
              onChange: (pageNum, pageSize) =>
                onFetchCnbArchivesList({
                  pageNum,
                  pageSize,
                }),
            }}
          />
        </div>
      </div>
    </Spin>
  );
};
export default TalentList;
