import React from 'react';

import useViews from '@hooks/useViews';

import TalentDetail, { IAppProps as TalentDetailProps } from '../TalentDetail';

import { CSB_VIEW_CODES } from '@constants/common';

export interface IAppProps extends TalentDetailProps {}

const TalentDetailTag: React.FC<IAppProps> = props => {
  const { Data: views } = useViews(CSB_VIEW_CODES.TALENT_DETAIL_TAG);
  return <TalentDetail {...props} views={views} />;
};

export default TalentDetailTag;
