import React, { useCallback, useEffect, useState } from 'react';
import { Spin, Row, Col } from 'antd';
import axios from 'axios';

import ehrApis from '@apis/ehr';
import employeeApis from '@apis/employee';
import { request as fetchApi } from '@utils/http';

import KeyItem from './containers/KeyItem';
import BaseInfo from './containers/BaseInfo';
import TalentMap from './containers/TalentMap';
import TrainInfo from './containers/TrainInfo';
import TalentTags from './containers/TalentTags';
import TurnoverInfo from './containers/TurnoverInfo';
import EmployeeInfo from './containers/EmployeeInfo';
import NotPermission from './containers/NotPermission';
import RecentInterview from './containers/RecentInterview';
import PerformanceInfo from './containers/PerformanceInfo';
import SocialResumeInfo from './containers/SocialResumeInfo';
import CapabilityReport from './containers/CapabilityReport';

import { TALENT_DETAIL_MODULE_TYPES } from '@constants/common';

import './style.less';

export interface EmployeeInfoType {
  baseInfo: {
    empName?: string;
    codeGender?: string;
    empBirthDay?: number;
    empConstellation?: string;
  };
  baseInfoJob: {
    companyName?: string;
    deptFullPath?: string;
    positionName?: string;
    employeeStatus?: string;
  };
  baseInfoEntryInfo: {
    laborDate?: number;
    recruitMode?: string;
    determinePostsDate?: number;
  };
  summaryInfo: {
    jobLevel?: string;
    gradeName?: string;
    isLastEmp?: boolean;
    graduationDate?: number;
    universityCollege?: string;
    currPositionDuration?: string;
    managementPositionDuration?: string;
  };
}

export interface IAppProps {
  views: string[];
  empId: string;
  account: string;
  systemHelper: {
    userInfo: {
      id: string;
      account: string;
    };
  };
}
const TalentDetail: React.FC<IAppProps> = props => {
  const { empId, account, systemHelper, views = [] } = props;
  const userAccount = systemHelper?.userInfo?.account;
  // 前端权限控制 - 只能查看非本人的档案信息
  const isHasPermission = userAccount !== account;

  const [employeeInfo, setEmployeeInfo] = useState<EmployeeInfoType>({
    baseInfo: {},
    summaryInfo: {},
    baseInfoJob: {},
    baseInfoEntryInfo: {},
  });
  const [avatarUrl, setAvatarUrl] = useState('');
  // 接口 - 权限控制
  const [passStatus, setPassStatus] = useState<string>(undefined);
  const [jobInfoLoading, setJobInfoLoading] = useState<boolean>(false);
  const [employeeLoading, setEmployeeLoading] = useState<boolean>(false);
  const [entryInfoLoading, setEntryInfoLoading] = useState<boolean>(false);
  const [permissionLoading, setPermissionLoading] = useState<boolean>(false);
  const [summaryInfoLoading, setSummaryInfoLoading] = useState<boolean>(false);

  const isPassFetch = passStatus === '1';
  const isNotPassFetch = passStatus === '0';

  const onFetchEmployeeAvatar = useCallback(() => {
    const { baseURL, url } = employeeApis.employeeAvatar;
    axios
      .get(`${baseURL}${url}`, {
        params: {
          type: 'SYSTEM',
          username: account,
        },
        headers: {
          Accept: 'application/octet-stream',
        },
        responseType: 'blob',
      })
      .then(res => {
        const blobData = res?.data;
        const newAvatarUrl = blobData ? URL.createObjectURL(blobData) : '';
        setAvatarUrl(newAvatarUrl);
      })
      .catch(err => {
        console.log('获取头像数据====err', err);
      });
  }, [account]);

  // 员工简介信息
  const onFetchEmployeeSummaryInfo = useCallback(
    () =>
      new Promise(resolve => {
        setSummaryInfoLoading(true);
        fetchApi({
          ...ehrApis.employeeSummary,
          params: {
            employeeId: empId,
          },
          onSuccess: res => resolve(res || {}),
          onError: error => {
            resolve({});
            console.log('获取员工简介信息报错啦：', error);
          },
        }).finally(() => setSummaryInfoLoading(false));
      }),
    [empId]
  );

  // 员工基本信息
  const onFetchEmployeeInfo = useCallback(
    () =>
      new Promise(resolve => {
        setEmployeeLoading(true);
        fetchApi({
          ...ehrApis.employeeBaseInfo,
          params: {
            employeeId: empId,
          },
          onSuccess: res => resolve(res || {}),
          onError: error => {
            resolve({});
            console.log('获取员工基本信息报错啦：', error);
          },
        }).finally(() => setEmployeeLoading(false));
      }),
    [empId]
  );

  // 任职信息
  const onFetchEmployeeJobInfo = useCallback(
    () =>
      new Promise(resolve => {
        setJobInfoLoading(true);
        fetchApi({
          ...ehrApis.employeeBaseInfoJob,
          params: {
            employeeId: empId,
          },
          onSuccess: res => resolve(res || {}),
          onError: error => {
            resolve({});
            console.log('获取任职信息报错啦：', error);
          },
        }).finally(() => setJobInfoLoading(false));
      }),
    [empId]
  );

  // 入职信息
  const onFetchEmployeeEntryInfo = useCallback(
    () =>
      new Promise(resolve => {
        setEntryInfoLoading(true);
        fetchApi({
          ...ehrApis.employeeEntryInfo,
          params: {
            employeeId: empId,
          },
          onSuccess: res => resolve(res || {}),
          onError: error => {
            resolve({});
            console.log('获取入职信息报错啦：', error);
          },
        }).finally(() => setEntryInfoLoading(false));
      }),
    [empId]
  );

  const onEmpInitData = useCallback(() => {
    Promise.all([
      onFetchEmployeeSummaryInfo(),
      onFetchEmployeeInfo(),
      onFetchEmployeeJobInfo(),
      onFetchEmployeeEntryInfo(),
    ]).then(([summaryInfo, baseInfo, baseInfoJob, baseInfoEntryInfo]) => {
      setEmployeeInfo({
        baseInfo,
        summaryInfo,
        baseInfoJob,
        baseInfoEntryInfo,
      });
    });
  }, [empId]);

  // 权限
  const onFetchTalentFilePermission = useCallback(employeeId => {
    setPermissionLoading(true);
    fetchApi({
      ...ehrApis.talentFilePermission,
      data: {
        employeeId,
      },
      onSuccess: newPassStatus => {
        setPassStatus(newPassStatus);
      },
    }).finally(() => setPermissionLoading(false));
  }, []);

  useEffect(() => {
    isHasPermission && account && onFetchEmployeeAvatar();
  }, [account, isHasPermission]);

  useEffect(() => {
    isHasPermission && empId && isPassFetch && onEmpInitData();
    empId && onFetchTalentFilePermission(empId);
  }, [empId, isHasPermission, isPassFetch]);

  const pageLoading = [permissionLoading, summaryInfoLoading, employeeLoading, jobInfoLoading, entryInfoLoading].some(
    loading => loading
  );
  return (
    <Spin spinning={pageLoading} wrapperClassName="dhr-talent-detail-spin-container">
      <div className="dhr-talent-detail-container">
        {isPassFetch && isHasPermission && (
          <Row gutter={[0, 8]}>
            <Col span={6} className="dhr-talent-file-pr-4">
              <EmployeeInfo initValues={employeeInfo} avatarUrl={avatarUrl} />
            </Col>
            {views.includes(TALENT_DETAIL_MODULE_TYPES.EMPLOYEE_BASE) && (
              <Col span={10} className="dhr-talent-file-pl-4 dhr-talent-file-pr-4 ">
                <BaseInfo initValues={employeeInfo} />
              </Col>
            )}
            {views.includes(TALENT_DETAIL_MODULE_TYPES.TALENT_TAGS) && (
              <Col span={8} className="dhr-talent-file-pl-4">
                <TalentTags {...props} />
              </Col>
            )}
            {views.includes(TALENT_DETAIL_MODULE_TYPES.PERFORMANCE) && (
              <Col span={8} className="dhr-talent-file-pl-4 dhr-talent-file-pr-4 ">
                <PerformanceInfo {...props} />
              </Col>
            )}
            {views.includes(TALENT_DETAIL_MODULE_TYPES.TURNOVER) && (
              <Col span={8} className="dhr-talent-file-pr-4 ">
                <TurnoverInfo {...props} />
              </Col>
            )}
            {views.includes(TALENT_DETAIL_MODULE_TYPES.SOCIAL_RESUME) && (
              <Col span={8} className="dhr-talent-file-pl-4">
                <SocialResumeInfo {...props} />
              </Col>
            )}
            {views.includes(TALENT_DETAIL_MODULE_TYPES.RECENT_INTERVIEW) && (
              <Col span={12} className="dhr-talent-file-pr-4 ">
                <RecentInterview {...props} />
              </Col>
            )}
            {views.includes(TALENT_DETAIL_MODULE_TYPES.TRAIN) && (
              <Col span={12} className="dhr-talent-file-pl-4">
                <TrainInfo {...props} />
              </Col>
            )}
            {(views.includes(TALENT_DETAIL_MODULE_TYPES.KEY_ITEM) ||
              views.includes(TALENT_DETAIL_MODULE_TYPES.CAPABILITY_REPORT)) && (
              <Col span={12} className="dhr-talent-file-pr-4 ">
                <Row gutter={[0, 8]}>
                  {views.includes(TALENT_DETAIL_MODULE_TYPES.KEY_ITEM) && (
                    <Col span={24}>
                      <KeyItem {...props} />
                    </Col>
                  )}
                  {views.includes(TALENT_DETAIL_MODULE_TYPES.CAPABILITY_REPORT) && (
                    <Col span={24}>
                      <CapabilityReport {...props} />
                    </Col>
                  )}
                </Row>
              </Col>
            )}
            {views.includes(TALENT_DETAIL_MODULE_TYPES.SUCCEED_MAP) && (
              <Col span={12} className="dhr-talent-file-pl-4">
                <TalentMap {...props} avatarUrl={avatarUrl} />
              </Col>
            )}
          </Row>
        )}
        {isNotPassFetch && <NotPermission />}
      </div>
    </Spin>
  );
};

export default TalentDetail;
