import React, { useCallback, useEffect, useRef, useState } from 'react';

import { Spin } from 'antd';
import axios from 'axios';

import ehrApis from '@apis/ehr';
import list2tree from '@cvte/list2tree';
import employeeApis from '@apis/employee';
import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import DrawerDetail from '@components/DrawerDetail';
import VisTree from './containers/VisTree';

import './style.less';

export interface IAppProps {
  empId: string;
  account: string;
  avatarUrl: string;
}
const TalentMap: React.FC<IAppProps> = ({ empId, account, avatarUrl }) => {
  const tileDataRef = useRef([]);
  const serializeRef = useRef(1);
  const avatarUrlMapRef = useRef({});
  const [dataSource, setDataSource] = useState<any>({ key: account });
  const [avatarLoading, setAvatarLoading] = useState<boolean>(false);
  const [talentMapLoading, setTalentMapLoading] = useState<boolean>(false);

  // 获取头像
  const onFetchEmployeeAvatar = useCallback(
    username =>
      new Promise(resolve => {
        const { baseURL, url } = employeeApis.employeeAvatar;
        !avatarLoading && setAvatarLoading(true);
        axios
          .get(`${baseURL}${url}`, {
            params: {
              type: 'SYSTEM',
              username,
            },
            headers: {
              Accept: 'application/octet-stream',
            },
            responseType: 'blob',
          })
          .then(res => {
            try {
              const blobData = res?.data;
              const newAvatarUrl = blobData ? URL.createObjectURL(blobData) : '';
              const newAvatarUrlMap = {
                ...avatarUrlMapRef.current,
                [username]: newAvatarUrl,
              };
              avatarUrlMapRef.current = newAvatarUrlMap;
              resolve(true);
            } catch (error) {
              resolve(true);
            }
          })
          .catch(error => {
            resolve(true);
          });
      }),
    []
  );

  const onFetchTalentMapInfo = useCallback(
    (params, startLevelIndex, lastLevelIndex) => {
      fetchApi({
        ...ehrApis.talentMapInfo,
        params,
        onSuccess: res => {
          try {
            const avatarsFetch = [];
            const successors = res?.successors || [];
            const managerAccount = res?.manager?.username;
            const newTileData = [...tileDataRef.current];
            if (startLevelIndex === 1) {
              const manager = res?.manager || {};
              const managerDept = res?.managerDept || {};
              const treeItem = {
                type: '',
                parentId: '0',
                account: managerAccount,
                key: managerAccount,
                name: manager.name || '-',
                posname: res?.managerJob || '-',
                deptName: managerDept.name || '-',
              };
              managerAccount && avatarsFetch.push(onFetchEmployeeAvatar(managerAccount));
              managerAccount && newTileData.push(treeItem);
            }

            // 处理children
            successors.forEach(successorItem => {
              const childItem = {
                parentId: managerAccount,
                account: successorItem?.username,
                name: successorItem?.name || '-',
                key: successorItem?.username,
                type: successorItem?.successionType,
                deptName: successorItem.deptName || '-',
                posname: successorItem?.successionJob || '-',
              };
              newTileData.push(childItem);
              successorItem.username && avatarsFetch.push(onFetchEmployeeAvatar(successorItem.username));
            });

            tileDataRef.current = newTileData;

            // 全部头像请求结束 则loading -> false
            Promise.all(avatarsFetch).then(() => startLevelIndex >= lastLevelIndex && setAvatarLoading(false));
            // 请求下一层的数据
            if (startLevelIndex < lastLevelIndex) {
              const nextStartLevelIndex = startLevelIndex + 1;
              successors.forEach(({ username }) => {
                const newSerialize = serializeRef.current + 1;
                serializeRef.current = newSerialize;
                onFetchTalentMapInfo({ ...params, successorAccount: username }, nextStartLevelIndex, lastLevelIndex);
              });
            }

            serializeRef.current -= 1;
            // 最后一个请求完成
            if (serializeRef.current <= 0) {
              setTalentMapLoading(false);
              const treeList = list2tree({
                idKey: 'account',
                parentIdKey: 'parentId',
              })(newTileData);
              const newDataSource = [...treeList].pop() || { key: account };
              setDataSource(newDataSource);
            }
          } catch (error) {
            console.log('继任地图报错了：', error);
            setAvatarLoading(false);
            setTalentMapLoading(false);
          }
        },
        onError: error => {
          setAvatarLoading(false);
          setTalentMapLoading(false);
          console.log('获取数据失败啦~~');
        },
      });
    },
    [account]
  );

  useEffect(() => {
    empId && onFetchTalentMapInfo({ employeeId: empId }, 1, 2);
  }, [empId, account]);

  const handleReadAll = useCallback(() => {
    DrawerDetail.detail({
      title: '继任信息详情',
      width: '80%',
      content: <VisTree initValues={dataSource} avatarUrlMap={avatarUrlMapRef.current} />,
    });
  }, [dataSource]);

  const pageLoading = [avatarLoading, talentMapLoading].some(loading => loading);
  return (
    <Spin spinning={pageLoading}>
      <CardBlock title="继任地图" onRight={handleReadAll} isShowAction={!!dataSource.name}>
        <div className="dhr-talent-map-container">
          {!!dataSource.name ? (
            <VisTree initValues={dataSource} avatarUrlMap={{ ...avatarUrlMapRef.current }} />
          ) : (
            '暂无继任人员'
          )}
        </div>
      </CardBlock>
    </Spin>
  );
};

export default TalentMap;
