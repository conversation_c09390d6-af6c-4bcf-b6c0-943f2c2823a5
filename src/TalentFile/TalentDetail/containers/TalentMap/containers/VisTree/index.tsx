import React, { useCallback, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';
import { Slider } from 'antd';

import VisTreeReact from '@vis-tree/react';
import TalentMapItem, { statusColorMap } from '../TalentMapItem';

import './style.less';

// 节点宽高
export const NODE_WIDTH = 250;
export const NODE_HEIGHT = 270;
export const LINE_COLOR = '#e0e0e0';

export interface IAppProps {
  initValues: any;
  avatarUrlMap: any;
}
const VisTree: React.FC<IAppProps> = ({ initValues, avatarUrlMap }) => {
  const treeRef = useRef(null);
  const [scaleRatio, setScaleRatio] = useState<number>(0.5);

  const statusMapVal = useMemo(
    () => ({
      'Ready now': '即可继任',
      'One-job away': '一年后继任',
      'Two-job away': '两年后继任',
    }),
    []
  );

  const optionConfigs = useMemo(
    () => ({
      defaultExpandAll: true,
      defaultScrollInfo: {
        top: 40,
        key: initValues.key,
      },
      nodeWidth: NODE_WIDTH,
      nodeHeight: NODE_HEIGHT,
      lineColor: LINE_COLOR,
    }),
    [initValues]
  );

  const handleChangeItemExpanded = useCallback(key => {
    treeRef.current.toggleNodeExpanded(key);
  }, []);

  const statusMapColorList = Object.keys(statusMapVal);

  return (
    <div className="dhr-talent-file-vis-tree-container">
      <div className="dhr-talent-map-color-map-card">
        {statusMapColorList.map(key => (
          <div className={classnames('dhr-talent-map-color-map-card-item', statusColorMap[key])}>
            <div>{key}</div>
            <div>{statusMapVal[key]}</div>
          </div>
        ))}
      </div>
      <div className="dhr-talent-file-vis-tree-slider-container">
        <Slider
          min={0.1}
          max={2}
          step={0.01}
          vertical={true}
          value={scaleRatio}
          tooltip={{ open: false }}
          onChange={val => setScaleRatio(val)}
        />
      </div>
      <div className="dhr-talent-map-vis-tree-container">
        <VisTreeReact
          ref={treeRef}
          dataSource={initValues}
          options={optionConfigs}
          scaleRatio={scaleRatio}
          className="dhr-talent-map-tree-container"
          renderNode={props => (
            <TalentMapItem {...props} avatarUrlMap={avatarUrlMap} onChange={handleChangeItemExpanded} />
          )}
        />
      </div>
    </div>
  );
};

export default VisTree;
