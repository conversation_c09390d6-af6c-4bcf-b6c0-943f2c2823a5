.dhr-talent-file-vis-tree-container {
  position: relative;
  height: inherit;
  .dhr-talent-map-color-map-card {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 20;
    .dhr-talent-map-color-map-card-item {
      text-align: center;
      color: #fff;
      line-height: 18px;
      border-radius: 2px;
      padding: 4px 10px;
      margin-bottom: 6px;
      font-size: 12px;
    }
  }

  .dhr-talent-file-vis-tree-slider-container {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 20;
    height: 100px;
  }

  .dhr-talent-item-blue {
    background-color: #46c26f;
  }

  .dhr-talent-item-red {
    background-color: #f0a800;
  }

  .dhr-talent-item-orange {
    background-color: #d941c0;
  }
  .dhr-talent-map-vis-tree-container {
    position: relative;
    height: inherit;
    .dhr-talent-map-tree-container {
      width: 100%;
      height: inherit;
      // height: 560px;
    }
  }
}
