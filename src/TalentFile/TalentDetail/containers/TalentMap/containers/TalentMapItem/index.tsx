import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import classnames from 'classnames';
import { Avatar } from 'antd';
import React, { useMemo } from 'react';

import './style.less';

export const statusColorMap = {
  'Ready now': 'dhr-talent-item-blue',
  'One-job away': 'dhr-talent-item-red',
  'Two-job away': 'dhr-talent-item-orange',
};
export interface IAppProps {
  node: any;
  expanded: boolean;
  parentNode: any;
  avatarUrlMap: any;
  onChange: (key) => void;
}
const TalentMapItem: React.FC<IAppProps> = ({ node, expanded, avatarUrlMap, parentNode, onChange }) => {
  console.log('node===', node);
  return (
    <div className={classnames('dhr-talent-map-item-container')}>
      {statusColorMap[node.type] && (
        <div className={classnames('dhr-talent-map-item-header', statusColorMap[node.type])}>{node.name}</div>
      )}
      <div className="dhr-talent-map-item-avatar">
        <Avatar size={64} src={avatarUrlMap?.[node.account]}>
          {node.name}
        </Avatar>
      </div>
      <div className={classnames('dhr-talent-map-item-name')}>{node.name}</div>
      <div className="dhr-talent-map-item-intro-info">
        <span>{node.deptName}</span>
        <span> | </span>
        <span>{node.posname}</span>
      </div>
      <div className="dhr-talent-map-item-successor">
        <span>
          <span>{`继任者｜${node?.children?.length || 0}人`}</span>
        </span>
      </div>
      {node.children && node.children.length > 0 && (
        <div className="dhr-talent-map-item-expand" onClick={() => onChange(node.key)}>
          {expanded ? (
            <MinusCircleOutlined style={{ display: 'block' }} />
          ) : (
            <PlusCircleOutlined style={{ display: 'block' }} />
          )}
        </div>
      )}
    </div>
  );
};

export default TalentMapItem;
