.dhr-talent-map-item-container {
  height: 100%;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 40px 10px 20px;
  text-align: center;
  position: relative;
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.1);

  .dhr-talent-item-green,
  .dhr-talent-item-red,
  .dhr-talent-item-orange {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
  }

  .dhr-talent-map-item-header {
    text-align: center;
    color: #fff;
    top: 0;
    left: 0;
    right: 0;
    padding-top: 2px;
    position: absolute;
    padding-bottom: 2px;
  }

  .dhr-talent-map-item-name {
    text-align: center;
    margin-top: 14px;
    font-size: 16px;
    font-weight: bold;
  }

  .dhr-talent-map-item-intro-info {
    color: #00000073;
    font-weight: 300;
  }

  .dhr-talent-map-item-successor {
    margin-top: 20px;
    & > span {
      padding: 4px 12px;
      border-radius: 12px;
      background-color: #eee;
    }
  }

  .dhr-talent-map-item-avatar {
    & > span {
      & > img {
        height: auto;
        filter: invert(4%);
      }
    }
  }

  .dhr-talent-map-item-expand {
    width: 14;
    height: 14;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    background: #fff;
    color: var(--antd-dynamic-primary-color);
  }
}
