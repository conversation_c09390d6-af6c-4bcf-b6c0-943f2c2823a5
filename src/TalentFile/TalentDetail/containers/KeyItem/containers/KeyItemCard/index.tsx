import classnames from 'classnames';
import { Tooltip } from 'antd';
import React from 'react';

import { toDateFormat } from '@utils/tools';

import './style.less';

export interface IAppProps {
  list: any;
  isShowAll?: boolean;
  className?: string;
}
const KeyItemCard: React.FC<IAppProps> = ({ isShowAll = false, list, className }) => {
  const tableList = isShowAll ? list : list.slice(0, 5);
  return (
    <div className={classnames('dhr-talent-file-key-item-card-container', className || '')}>
      {tableList.map((tableItem, index) => {
        return (
          <div key={index} className="dhr-talent-key-item-card-item">
            <div className="dhr-talent-key-item-card-item-title-container">
              <span className="dhr-talent-key-item-card-item-title">{tableItem.matterType}</span>
              <span> | </span>
              <Tooltip title={tableItem.matterContent}>
                <span className="dhr-talent-key-item-card-item-content">{tableItem.matterContent}</span>
              </Tooltip>
            </div>
            <div className="dhr-talent-key-item-card-item-time-container">
              <span>{toDateFormat(tableItem.matterTime) || '-'}</span>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default KeyItemCard;
