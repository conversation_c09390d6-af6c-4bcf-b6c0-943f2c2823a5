import React, { useCallback, useEffect, useState } from 'react';

import ehrApis from '@apis/ehr';
import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import DrawerDetail from '@components/DrawerDetail';
import KeyItemCard from './containers/KeyItemCard';

import './style.less';

export interface IAppProps {
  empId: string;
  account: string;
}
const KeyItem: React.FC<IAppProps> = ({ empId, account }) => {
  const [tableList, setTableList] = useState<any[]>([]);

  const onFetchKeyItemList = useCallback(() => {
    fetchApi({
      ...ehrApis.talentKeyItemInfo,
      params: {
        employeeId: empId,
      },
      onSuccess: list => {
        const newTableList = (list || []).map(listItem => {
          const employeeInfo = (listItem.detail || []).find(detailItem => detailItem.account === account);
          const { matterContent, matterTime, matterType } = employeeInfo || {};
          return {
            ...listItem,
            matterType,
            matterTime,
            matterContent,
          };
        });

        setTableList(newTableList);
      },
    });
  }, [empId, account]);

  useEffect(() => {
    empId && onFetchKeyItemList();
  }, [empId]);

  const handleReadAll = useCallback(() => {
    DrawerDetail.detail({
      title: '关键事项详情',
      width: 700,
      content: <KeyItemCard isShowAll list={tableList} />,
    });
  }, [tableList]);

  return (
    <CardBlock title="关键事项" isShowAction={tableList.length > 0} onRight={handleReadAll}>
      <div className="dhr-talent-key-item-container">
        <div className="dhr-talent-key-item-card-container">
          {tableList.length > 0 ? <KeyItemCard list={tableList} /> : '暂无数据'}
        </div>
      </div>
    </CardBlock>
  );
};

export default KeyItem;
