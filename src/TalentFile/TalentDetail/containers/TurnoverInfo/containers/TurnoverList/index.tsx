import classnames from 'classnames';
import { Timeline } from 'antd';
import React from 'react';

import { toDateFormat } from '@utils/tools';

import './style.less';

export interface IAppProps {
  list: any[];
  isShowAll?: boolean;
}
const TurnoverList: React.FC<IAppProps> = ({ list, isShowAll = false }) => {
  const turnoverList = isShowAll ? list : list.slice(0, 5);

  return (
    <div className="dhr-talent-file-turnover-list-container">
      <Timeline mode="left">
        {turnoverList.map((turnoverItem, index) => {
          return (
            <Timeline.Item key={turnoverItem.id} label={turnoverItem.turnoverType}>
              <div
                className={classnames('dhr-talent-file-turnover-item', {
                  'dhr-talent-file-turnover-shadow': index === 0,
                })}
              >
                <div className="dhr-talent-file-turnover-item-title">
                  <span>
                    <span>{toDateFormat(turnoverItem.beginDate)}</span>
                    <span> ~ </span>
                    <span>{toDateFormat(turnoverItem.actualEndDate) || '至今'}</span>
                  </span>
                </div>
                <div className="dhr-talent-file-turnover-item-content">
                  <span className="dhr-talent-turnover-item-pos-name">{turnoverItem.newPosName}</span>
                  <span className="dhr-talent-turnover-item-company">
                    <span> | </span>
                    <span>{turnoverItem.newCompanyName}</span>
                    <span>-</span>
                    <span>{turnoverItem.newDeptName}</span>
                  </span>
                </div>
              </div>
            </Timeline.Item>
          );
        })}
      </Timeline>
    </div>
  );
};

export default TurnoverList;
