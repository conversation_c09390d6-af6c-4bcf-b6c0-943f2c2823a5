import React, { useCallback, useEffect, useState } from 'react';
import { Spin, Timeline } from 'antd';
import classnames from 'classnames';

import ehrApis from '@apis/ehr';

import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import DrawerDetail from '@components/DrawerDetail';
import TurnoverList from './containers/TurnoverList';

import './style.less';

export interface IAppProps {
  empId: string;
}
const PerformanceInfo: React.FC<IAppProps> = ({ empId }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [turnoverList, setTurnoverList] = useState<any[]>([]);

  const onFetchPerformanceInfo = useCallback(() => {
    setLoading(true);
    fetchApi({
      ...ehrApis.turnoverInfo,
      params: {
        employeeId: empId,
      },
      onSuccess: res => {
        setTurnoverList(res?.list || []);
      },
    }).finally(() => setLoading(false));
  }, [empId]);

  useEffect(() => {
    empId && onFetchPerformanceInfo();
  }, [empId]);

  const handleReadDetail = useCallback(() => {
    DrawerDetail.detail({
      title: '内部经历详情',
      width: 700,
      content: <TurnoverList list={turnoverList} isShowAll />,
    });
  }, [turnoverList]);

  return (
    <Spin spinning={loading}>
      <CardBlock title="内部经历" onRight={handleReadDetail} isShowAction={turnoverList.length > 0}>
        <div className="dhr-talent-file-turnover-container">
          {turnoverList.length > 0 ? <TurnoverList list={turnoverList} /> : '暂无数据'}
        </div>
      </CardBlock>
    </Spin>
  );
};

export default PerformanceInfo;
