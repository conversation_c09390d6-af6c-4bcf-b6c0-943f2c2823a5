import React, { useCallback, useEffect, useState } from 'react';
import { Spin } from 'antd';

import ehrApis from '@apis/ehr';
import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import DrawerDetail from '@components/DrawerDetail';
import PerformanceList from './containers/PerformanceList';

import './style.less';

export interface IAppProps {
  empId: string;
}
const PerformanceInfo: React.FC<IAppProps> = ({ empId }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [performanceList, setPerformanceList] = useState<any[]>([]);

  const onFetchPerformanceInfo = useCallback(() => {
    setLoading(true);
    fetchApi({
      ...ehrApis.performanceInfo,
      params: {
        employeeId: empId,
      },
      onSuccess: res => {
        const list = res?.list || [];
        setPerformanceList(list);
      },
    }).finally(() => setLoading(false));
  }, [empId]);

  useEffect(() => {
    empId && onFetchPerformanceInfo();
  }, [empId]);

  const handleReadAll = useCallback(() => {
    DrawerDetail.detail({
      title: '绩效详情',
      content: <PerformanceList isShowAll list={performanceList} />,
    });
  }, [performanceList]);

  return (
    <Spin spinning={loading}>
      <CardBlock title="绩效" onRight={handleReadAll} isShowAction={performanceList.length > 0}>
        <div className="dhr-talent-file-performance-container">
          {performanceList.length > 0 ? <PerformanceList list={performanceList} /> : '暂无数据'}
        </div>
      </CardBlock>
    </Spin>
  );
};

export default PerformanceInfo;
