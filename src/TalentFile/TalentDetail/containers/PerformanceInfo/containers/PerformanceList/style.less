.dhr-talent-performance-list-container {
  & > .ant-timeline {
    & > .ant-timeline-item {
      padding-bottom: 6px;
      .ant-timeline-item-head {
        top: 20px;
      }
      .ant-timeline-item-tail {
        top: 30px;
        border-color: var(--antd-dynamic-primary-color);
      }
      .ant-timeline-item-content {
        margin-left: 20px;
        .dhr-talent-file-performance-item {
          padding: 10px 20px;
          border-radius: 6px;
          color: var(--antd-dynamic-primary-color);
          background-color: rgba(56, 88, 230, 0.064);
          transition: all 0.3s ease;
          &:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
          }
          .dhr-talent-file-performance-item-title {
            margin-bottom: 4px;
          }
          .dhr-talent-file-performance-item-content {
            font-weight: 500;
            .dhr-talent-performance-item-grade {
              margin-right: 6px;
            }
          }
        }
        .dhr-talent-file-performance-shadow {
          color: #fff;
          background-color: rgba(56, 88, 230, 0.8);
        }
      }
    }
  }
}
