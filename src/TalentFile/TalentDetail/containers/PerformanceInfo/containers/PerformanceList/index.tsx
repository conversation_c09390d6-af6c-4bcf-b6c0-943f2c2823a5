import React from 'react';
import { Timeline } from 'antd';

import classnames from 'classnames';
import './style.less';

export interface IAppProps {
  list: any[];
  isShowAll?: boolean;
}
const PerformanceList: React.FC<IAppProps> = ({ list, isShowAll = false }) => {
  const newList = isShowAll ? list : list.slice(0, 5);
  return (
    <div className="dhr-talent-performance-list-container">
      <Timeline>
        {newList.map((performanceItem, index) => {
          return (
            <Timeline.Item key={performanceItem.id}>
              <div
                className={classnames('dhr-talent-file-performance-item', {
                  'dhr-talent-file-performance-shadow': index === 0,
                })}
              >
                <div className="dhr-talent-file-performance-item-title">
                  <span>{`${performanceItem.year}年`}</span>
                  <span>{performanceItem.quarterName}</span>
                </div>
                <div className="dhr-talent-file-performance-item-content">
                  <span className="dhr-talent-performance-item-grade">{`${performanceItem.performanceGrade}`}</span>
                  <span className="dhr-talent-performance-item-score">{performanceItem.deptRawScore || '0'}</span>
                </div>
              </div>
            </Timeline.Item>
          );
        })}
      </Timeline>
    </div>
  );
};

export default PerformanceList;
