import React, { useCallback, useEffect, useState } from 'react';
import { Spin } from 'antd';

import ehrApis from '@apis/ehr';
import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import CarouselItem from './containers/CarouselItem';
import TalentDetailModal from '../TalentDetailModal';
import AbilityReportDetail from './containers/AbilityReportDetail';

import './style.less';

// ’能力绩效‘
const INVENTORY_RESULTE_MAP = {
  AD: '1',
  AE: '1',
  BD: '1',
  BE: '1',

  AC: '2',
  BC: '2',

  AB: '3',
  AA: '3',
  BB: '3',
  BA: '3',

  CD: '4',
  CE: '4',

  CC: '5',

  CB: '6',
  CA: '6',

  DD: '7',
  DE: '7',
  ED: '7',
  EE: '7',

  DC: '8',
  EC: '8',

  DB: '9',
  DA: '9',
  EB: '9',
  EA: '9',
};

const INVENTORY_RESULTE_VALUE_MAP = {
  1: '待观察',
  2: '骨干',
  3: '优秀',
  4: '待观察',
  5: '适岗',
  6: '骨干',
  7: '不胜任',
  8: '适岗',
  9: '适岗',
};
export interface IAppProps {
  empId: string;
  account: string;
}
const CapabilityReport: React.FC<IAppProps> = ({ empId, account }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [inventoryList, setInventoryList] = useState<any[]>([]);

  const onFetchTalentInventoryInfo = useCallback(
    employeeId => {
      setLoading(true);
      fetchApi({
        ...ehrApis.talentInventoryInfo,
        params: {
          employeeId,
        },
        onSuccess: list => {
          const newInventoryList = (list || [])
            .map(listItem => {
              const employeeDetail = (listItem.detail || []).find(detailItem => detailItem.account === account);
              const { ability, performance } = employeeDetail || {};
              const abilityPerformance = `${ability}${performance}`;
              const inventoryResulte = INVENTORY_RESULTE_MAP[abilityPerformance] || '';
              return {
                ...listItem,
                ability,
                performance,
                inventoryResulte,
                inventoryResulteName: INVENTORY_RESULTE_VALUE_MAP[inventoryResulte] || '-',
              };
            })
            .sort((a, b) => b?.applyDate - a?.applyDate);
          setInventoryList(newInventoryList);
        },
      }).finally(() => setLoading(false));
    },
    [empId, account]
  );

  useEffect(() => {
    empId && onFetchTalentInventoryInfo(empId);
  }, [empId]);

  const handleReadAll = useCallback(() => {
    TalentDetailModal.detail({
      title: '人才盘点详情',
      width: 600,
      content: <CarouselItem list={inventoryList} />,
    });
  }, [inventoryList]);

  const latestInventoryDetail = inventoryList[0] || {};
  return (
    <Spin spinning={loading}>
      <CardBlock title="人才盘点" isShowAction={inventoryList.length > 0} onRight={handleReadAll}>
        <div className="dhr-talent-ability-report-container">
          {inventoryList.length > 0 ? <AbilityReportDetail initValues={latestInventoryDetail} /> : '暂无数据'}
        </div>
      </CardBlock>
    </Spin>
  );
};

export default CapabilityReport;
