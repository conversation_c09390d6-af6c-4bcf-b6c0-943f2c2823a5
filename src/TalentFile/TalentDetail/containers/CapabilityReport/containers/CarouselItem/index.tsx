import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons';
import React, { useCallback, useRef } from 'react';
import { Carousel } from 'antd';

import './style.less';

import AbilityReportDetail from '../AbilityReportDetail';

export interface IAppProps {
  list: any[];
}

const CarouselItem: React.FC<IAppProps> = ({ list }) => {
  const carouselRef = useRef(null);

  const handlePrev = useCallback(() => {
    carouselRef.current?.prev();
  }, []);

  const handleNext = useCallback(() => {
    carouselRef.current?.next();
  }, []);

  return (
    <div className="dhr-talent-file-carousel-item-container">
      <LeftCircleOutlined className="dhr-talent-file-carousel-prev" onClick={handlePrev} />
      <RightCircleOutlined className="dhr-talent-file-carousel-next" onClick={handleNext} />
      <Carousel ref={carouselRef} dots={{ className: 'dhr-talent-file-carousel-dots' }}>
        {list.map((listItem, index) => (
          <div key={index}>
            <div className="dhr-talent-file-carousel-item">
              <AbilityReportDetail initValues={listItem} />
            </div>
          </div>
        ))}
      </Carousel>
    </div>
  );
};

export default CarouselItem;
