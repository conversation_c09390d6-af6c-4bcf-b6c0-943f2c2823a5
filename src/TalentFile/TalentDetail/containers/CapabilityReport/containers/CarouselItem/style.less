.dhr-talent-file-carousel-item-container {
  position: relative;
  .dhr-talent-file-carousel-prev,
  .dhr-talent-file-carousel-next {
    top: 50%;
    z-index: 10;
    color: #a6a1a1;
    font-size: 24px;
    cursor: pointer;
    position: absolute;
    transform: translateY(-50%);
    &:hover {
      color: #737171;
    }
  }
  .dhr-talent-file-carousel-prev {
    left: 0;
  }

  .dhr-talent-file-carousel-next {
    right: 0;
  }

  .dhr-talent-file-carousel-item {
    height: 340px;
    width: 510px;
  }

  .dhr-talent-file-carousel-dots {
    & > li {
      button {
        background-color: var(--antd-dynamic-primary-color);
      }

      &.slick-active {
        button {
          background-color: var(--antd-dynamic-primary-color);
        }
      }
    }
  }
}
