.dhr-ability-report-item-container {
  .dhr-ability-report-item-title-container {
    margin-bottom: 26px;
    .dhr-talent-ability-report-title-label {
      padding-left: 6px;
      font-weight: bold;
    }
  }

  .dhr-talent-ability-report-sudoku {
    margin-top: 10px;
    position: relative;
    .dhr-talent-ability-report-coordinate {
      position: relative;
      &::after {
        content: '';
        top: 0;
        bottom: -4px;
        right: 2px;
        width: 2px;
        position: absolute;
        border-radius: 4px;
        background-color: #eee;
      }
      & > span {
        top: 50%;
        right: 10px;
        position: absolute;
        transform: translateY(-50%);
        color: #06060694;
      }

      .dhr-talent-ability-report-coordinate-title {
        position: absolute;
        top: -22px;
        right: -10px;
        color: #06060694;
      }
    }

    .dhr-talent-ability-report-coordinate-performance {
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: -4px;
        left: -4px;
        right: 0;
        height: 2px;
        border-radius: 4px;
        background-color: #eee;
      }

      & > span {
        position: absolute;
        bottom: -24px;
        left: 50%;
        color: #06060694;
        transform: translateX(-50%);
      }

      .dhr-talent-ability-report-coordinate-title {
        position: absolute;
        right: -32px;
        bottom: -14px;
        color: #06060694;
      }
    }

    .dhr-talent-ability-report-sudoku-item {
      height: 62px;
      position: relative;
      .dhr-talent-ability-report-sudoku-result-sign {
        position: absolute;
        top: 8px;
        right: 20px;
        font-size: 18px;
        color: var(--antd-dynamic-primary-color);
      }
      .dhr-talent-ability-report-sudoku-content {
        top: 50%;
        left: 50%;
        font-size: 16px;
        font-weight: bold;
        position: absolute;
        transform: translate(-50%, -50%);
      }
    }

    .dhr-talent-file-sudoku-1,
    .dhr-talent-file-sudoku-4 {
      color: #d857a3;
      background-color: #fff0f6;
    }

    .dhr-talent-file-sudoku-5,
    .dhr-talent-file-sudoku-8,
    .dhr-talent-file-sudoku-9 {
      color: #8aacff;
      background-color: #ccd9fd;
    }

    .dhr-talent-file-sudoku-2,
    .dhr-talent-file-sudoku-6 {
      color: #67ac90;
      background-color: #cff2df;
    }

    .dhr-talent-file-sudoku-3 {
      color: #ff9f33;
      background-color: #ffe7cc;
    }

    .dhr-talent-file-sudoku-7 {
      color: #fff;
      background-color: #f86252;
    }

    .dhr-talent-file-sudoku-1 {
      border-top-left-radius: 4px;
    }
    .dhr-talent-file-sudoku-3 {
      border-top-right-radius: 4px;
    }
    .dhr-talent-file-sudoku-7 {
      border-bottom-left-radius: 4px;
    }
    .dhr-talent-file-sudoku-9 {
      border-bottom-right-radius: 4px;
    }
  }
}
