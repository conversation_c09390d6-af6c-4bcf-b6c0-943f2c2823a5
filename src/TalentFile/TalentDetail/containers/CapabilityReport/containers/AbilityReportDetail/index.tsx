import type { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import React, { useCallback, useMemo } from 'react';
import Icon from '@ant-design/icons';
import { Row, Col } from 'antd';

import { toDateFormat } from '@utils/tools';

import './style.less';

export interface IAppProps {
  initValues: any;
}

const AbilityReportItem: React.FC<IAppProps> = ({ initValues }) => {
  const abilityType = useMemo(
    () => ({
      REMAIN_OBSERVE: '待观察',
      BACKBONE: '骨干',
      SUITABLE: '适岗',
      EXCELLENCE: '优秀',
      INCOMPETENT: '不胜任',
    }),
    []
  );

  const FlagSvg = useCallback(
    () => (
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8241" width="32" height="32">
        <path d="M896 384L160 128v448z" fill="#FE2E30" p-id="8242"></path>
        <path
          d="M160 896V96c0-19.2-12.8-32-32-32s-32 12.8-32 32V896c-19.2 0-32 12.8-32 32s12.8 32 32 32h64c19.2 0 32-12.8 32-32s-12.8-32-32-32z"
          fill="#4C4848"
          p-id="8243"
        ></path>
      </svg>
    ),
    []
  );

  const FlagIcon = useCallback(
    (props: Partial<CustomIconComponentProps>) => <Icon component={FlagSvg} {...props} />,
    []
  );

  return (
    <div className="dhr-ability-report-item-container">
      <Row className="dhr-ability-report-item-title-container">
        <Col span={24}>
          <span className="dhr-talent-ability-report-title-label">盘点名称：</span>
          <span className="dhr-talent-ability-report-title-content">{initValues.projectName}</span>
        </Col>
        <Col span={24}>
          <span className="dhr-talent-ability-report-title-label">盘点时间：</span>
          <span className="dhr-talent-ability-report-title-content">{toDateFormat(initValues.applyDate)}</span>
        </Col>
        <Col span={24}>
          <span className="dhr-talent-ability-report-title-label">盘点结果：</span>
          <span className="dhr-talent-ability-report-title-content">{initValues.inventoryResulteName}</span>
        </Col>
      </Row>
      <Row className="dhr-talent-ability-report-sudoku" gutter={[4, 4]}>
        <Col span={4} className="dhr-talent-ability-report-coordinate">
          <span>A/B</span>
          <div className="dhr-talent-ability-report-coordinate-title">能力</div>
        </Col>
        <Col span={6}>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-1">
            <span className="dhr-talent-ability-report-sudoku-content"> {abilityType.REMAIN_OBSERVE}</span>
            {initValues.inventoryResulte === '1' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
        <Col span={6}>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-2">
            <span className="dhr-talent-ability-report-sudoku-content">{abilityType.BACKBONE}</span>
            {initValues.inventoryResulte === '2' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
        <Col span={6}>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-3">
            <span className="dhr-talent-ability-report-sudoku-content">{abilityType.EXCELLENCE}</span>
            {initValues.inventoryResulte === '3' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
        <Col span={4} className="dhr-talent-ability-report-coordinate">
          <span>C</span>
        </Col>
        <Col span={6}>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-4">
            <span className="dhr-talent-ability-report-sudoku-content">{abilityType.REMAIN_OBSERVE}</span>
            {initValues.inventoryResulte === '4' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
        <Col span={6}>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-5">
            <span className="dhr-talent-ability-report-sudoku-content">{abilityType.SUITABLE}</span>
            {initValues.inventoryResulte === '5' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
        <Col span={6}>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-6">
            <span className="dhr-talent-ability-report-sudoku-content">{abilityType.BACKBONE}</span>
            {initValues.inventoryResulte === '6' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
        <Col span={4} className="dhr-talent-ability-report-coordinate">
          <span>D/E</span>
        </Col>
        <Col span={6} className="dhr-talent-ability-report-coordinate-performance">
          <span>D/E</span>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-7">
            <span className="dhr-talent-ability-report-sudoku-content">{abilityType.INCOMPETENT}</span>
            {initValues.inventoryResulte === '7' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
        <Col span={6} className="dhr-talent-ability-report-coordinate-performance">
          <span>C</span>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-8">
            <span className="dhr-talent-ability-report-sudoku-content">{abilityType.SUITABLE}</span>
            {initValues.inventoryResulte === '8' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
        <Col span={6} className="dhr-talent-ability-report-coordinate-performance">
          <span>B/A</span>
          <div className="dhr-talent-ability-report-coordinate-title">绩效</div>
          <div className="dhr-talent-ability-report-sudoku-item dhr-talent-file-sudoku-9">
            <span className="dhr-talent-ability-report-sudoku-content">{abilityType.SUITABLE}</span>
            {initValues.inventoryResulte === '9' && (
              <FlagIcon className="dhr-talent-ability-report-sudoku-result-sign" />
            )}
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default AbilityReportItem;
