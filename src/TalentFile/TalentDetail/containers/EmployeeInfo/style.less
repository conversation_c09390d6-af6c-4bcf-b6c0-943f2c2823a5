.dhr-talent-emp-info-container {
  min-height: 422px;
  text-align: center;
  border-radius: 4px;
  border-radius: 6px;
  padding: 80px 40px 80px;
  background-color: #fff;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  .dhr-talent-emp-info-avatar-container {
    .dhr-talent-emp-avatar {
      position: relative;
      display: inline-block;
      &::after {
        content: '';
        position: absolute;
        top: -8%;
        left: -9%;
        height: 118%;
        width: 118%;
        border-radius: 50%;
        border: 2px solid var(--antd-dynamic-primary-color);
      }
      & > span {
        & > img {
          height: auto;
        }
      }
    }
    .dhr-talent-emp-info-status-container {
      text-align: center;
      .dhr-talent-emp-status {
        top: -8px;
        font-size: 12px;
        line-height: 18px;
        padding-left: 8px;
        padding-right: 8px;
        border-radius: 3px;
        position: relative;
        display: inline-block;
        background-color: #fff;
        color: var(--antd-dynamic-primary-color);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .dhr-talent-emp-info-name {
    position: relative;
    & > h2 {
      margin-bottom: 4px;
      display: inline-block;
    }
    .dhr-talent-emp-info-gender-container {
      position: absolute;
      top: 8px;
      & > .anticon {
        padding: 2px;
        margin-left: 4px;
        border-radius: 50%;
        vertical-align: baseline;
      }
      & > .dhr-talent-emp-info-gender-man {
        background-color: #1296db;
      }
      & > .dhr-talent-emp-info-gender-woman {
        background-color: #d4237a;
      }
    }
  }

  .dhr-talent-emp-info-intro-container {
    min-height: 28px;
  }
}
