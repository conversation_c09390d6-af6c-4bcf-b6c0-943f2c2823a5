import type { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import React, { useCallback, useMemo } from 'react';
import Icon from '@ant-design/icons';
import { Avatar } from 'antd';

import { EmployeeInfoType } from '../../index';

import './style.less';

export interface IAppProps {
  avatarUrl: string;
  initValues: EmployeeInfoType;
}
const BaseInfo: React.FC<IAppProps> = ({ initValues, avatarUrl }) => {
  const { baseInfo, baseInfoJob } = initValues;
  const nameIntro = (baseInfo.empName || '').split('').shift();

  const empIntroList = [['companyName', 'deptName'], ['positionName']];

  const ManIconSvg: any = useCallback(
    () => (
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2859" width="14" height="14">
        <path
          p-id="2860"
          fill="#ffffff"
          d="M795.**********.917333H682.666667a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V219.946667l-105.**********.866666A350.**********.613333 0 0 1 821.**********.666667c0 194.4-157.6 352-352 352S117.**********.**********.**********.666667s157.6-352 352-352a350.**********.538667 0 0 1 221.6 78.506666l104.256-104.256zM469.**********.666667c159.061333 0 288-128.**********-288S628.**********.**********.**********.**********.**********.**********.**********.666667s128.********** 288 288z"
        ></path>
      </svg>
    ),
    []
  );

  const WomanIconSvg: any = useCallback(
    () => (
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3789" width="14" height="14">
        <path
          p-id="3790"
          fill="#ffffff"
          d="M485.333333 768v-43.765333C321.077333 710.688 192 573.088 192 405.333333 192 228.597333 335.264 85.333333 512 85.333333c176.736 0 320 143.264 320 320 0 164.106667-123.52 299.349333-282.666667 317.845334V768H640a32 32 0 0 1 0 64h-90.666667v77.333333a32 32 0 0 1-64 0V832H384a32 32 0 0 1 0-64h101.333333zM512 661.333333c141.386667 0 256-114.613333 256-256S653.386667 149.333333 512 149.333333 256 263.946667 256 405.333333s114.613333 256 256 256z"
        ></path>
      </svg>
    ),
    []
  );

  const ManIcon = useCallback(
    (props: Partial<CustomIconComponentProps>) => <Icon component={ManIconSvg} {...props} />,
    [ManIconSvg]
  );

  const WomanIcon = useCallback(
    (props: Partial<CustomIconComponentProps>) => <Icon component={WomanIconSvg} {...props} />,
    [WomanIconSvg]
  );

  const isGenderMan = useMemo(() => baseInfo?.codeGender === '1', [baseInfo]);
  return (
    <div className="dhr-talent-emp-info-container">
      <div className="dhr-talent-emp-info-avatar-container">
        <div className="dhr-talent-emp-avatar">
          <Avatar size={100} shape="circle" src={avatarUrl}>
            {nameIntro}
          </Avatar>
        </div>
        <div className="dhr-talent-emp-info-status-container">
          <span className="dhr-talent-emp-status">{baseInfoJob.employeeStatus || ''}</span>
        </div>
      </div>
      <div className="dhr-talent-emp-info-name">
        <h2>{baseInfo.empName}</h2>
        <span className="dhr-talent-emp-info-gender-container">
          {isGenderMan ? (
            <ManIcon className="dhr-talent-emp-info-gender-man" />
          ) : (
            <WomanIcon className="dhr-talent-emp-info-gender-woman" />
          )}
        </span>
      </div>
      <div className="dhr-talent-emp-info-intro-container">
        {empIntroList.map((arrItem, index) => (
          <div key={`intro_${index}`}>
            {arrItem
              .filter(key => !!baseInfoJob[key])
              .map((key, childrenIndex) => (
                <span key={key}>
                  {childrenIndex !== 0 && <span> | </span>}
                  <span>{baseInfoJob[key]}</span>
                </span>
              ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default BaseInfo;
