import React, { useCallback, useEffect, useState } from 'react';
import { Spin } from 'antd';

import ehrApis from '@apis/ehr';
import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import DrawerDetail from '@components/DrawerDetail';
import TrainList from './containers/TrainList';

import './style.less';

export interface IAppProps {
  empId: string;
}
const TrainInfo: React.FC<IAppProps> = ({ empId }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [trainList, setTrainList] = useState<any[]>([]);
  const onFetchPerformanceInfo = useCallback(employeeId => {
    setLoading(true);
    fetchApi({
      ...ehrApis.trainRecordInfo,
      params: {
        employeeId,
      },
      onSuccess: list => {
        setTrainList(list);
      },
    }).finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    empId && onFetchPerformanceInfo(empId);
  }, [empId]);

  const handleReadAll = useCallback(() => {
    DrawerDetail.detail({
      title: '培训经历详情',
      width: 700,
      content: <TrainList isShowAll list={trainList} />,
    });
  }, [trainList]);
  return (
    <Spin spinning={loading}>
      <CardBlock title="培训经历" onRight={handleReadAll} isShowAction={trainList.length > 0}>
        <div className="dhr-talent-file-train-container">
          {trainList.length > 0 ? <TrainList list={trainList} /> : '暂无数据'}
        </div>
      </CardBlock>
    </Spin>
  );
};

export default TrainInfo;
