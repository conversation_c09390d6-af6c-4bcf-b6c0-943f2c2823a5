import classnames from 'classnames';
import { Timeline } from 'antd';
import React from 'react';

import { toDateFormat } from '@utils/tools';

import './style.less';

export interface IAppProps {
  list: any[];
  isShowAll?: boolean;
}
const BaseInfo: React.FC<IAppProps> = ({ list, isShowAll }) => {
  const trainList = isShowAll ? list : list.slice(0, 5);
  return (
    <div className="dhr-talent-file-train-list-container">
      <Timeline>
        {trainList.map(trainItem => {
          return (
            <Timeline.Item key={trainItem.id}>
              <div className={classnames('dhr-talent-file-train-item')}>
                <div className="dhr-talent-file-train-item-title-container">
                  <div className="dhr-talent-file-train-item-title">
                    <span>
                      <span>{trainItem.trainProject}</span>
                      <span> | </span>
                      <span>{trainItem.result || '暂无结论'}</span>
                    </span>
                    <span>
                      <span>{toDateFormat(trainItem.trainBeginDate)}</span>
                      <span> ~ </span>
                      <span>{toDateFormat(trainItem.trainEndDate)}</span>
                    </span>
                  </div>
                </div>
                <div className="dhr-talent-file-train-item-content">
                  <span className="dhr-talent-train-item-pos-evaluate">{trainItem.trainEvaluate || '暂无评价'}</span>
                </div>
              </div>
            </Timeline.Item>
          );
        })}
      </Timeline>
    </div>
  );
};

export default BaseInfo;
