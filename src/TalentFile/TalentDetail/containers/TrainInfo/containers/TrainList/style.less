.dhr-talent-file-train-list-container {
  & > .ant-timeline {
    & > .ant-timeline-item {
      padding-bottom: 6px;
      .ant-timeline-item-head {
        top: 20px;
      }
      .ant-timeline-item-tail {
        top: 30px;
        border-color: var(--antd-dynamic-primary-color);
      }
      .ant-timeline-item-content {
        margin-left: 20px;
        .dhr-talent-file-train-item {
          padding: 10px 20px;
          border-radius: 6px;
          background-color: rgba(246, 246, 246, 0.82);
          transition: all 0.3s ease;
          &:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
          }
          .dhr-talent-file-train-item-title-container {
            margin-bottom: 4px;
            font-weight: 500;
            .dhr-talent-file-train-item-title {
              display: flex;
              align-items: center;
              justify-content: space-between;
            }
          }
          .dhr-talent-file-train-item-content {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            .dhr-talent-train-item-pos-evaluate {
              color: #00000073;
            }
          }
        }
      }
    }
  }
}
