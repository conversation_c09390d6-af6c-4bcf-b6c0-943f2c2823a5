import React, { useMemo } from 'react';
import classnames from 'classnames';
import { Row, Col } from 'antd';

import { toDateFormat } from '@utils/tools';

import './style.less';

// 访谈类型
const INTERVIEW_TYPES = {
  /** 日常访谈 */
  DAILY: 'DAILY',
  /** 试用期访谈 */
  PROBATION: 'PROBATION',
  /** 上任访谈 */
  APPLICATION: 'APPLICATION',
  /** 人才盘点访谈 */
  TALENT_INVENTORY: 'TALENT_INVENTORY',
  /** 定岗访谈 */
  DEFINE_JOB_TALK: 'DEFINE_JOB_TALK',
  /** 见习转正访谈 */
  PROBATION_DEFENCE: 'PROBATION_DEFENCE',
  // 末位访谈
  LAST_EMPLOYEE: 'LAST_EMPLOYEE',
};

export interface IAppProps {
  initValues: any;
}

const RecentInterviewItem: React.FC<IAppProps> = ({ initValues }) => {
  const recentColumns = useMemo(
    () => [
      {
        col: 12,
        label: '访谈类型',
        key: 'talkTypeName',
        showTypes: [
          INTERVIEW_TYPES.DAILY,
          INTERVIEW_TYPES.PROBATION,
          INTERVIEW_TYPES.APPLICATION,
          INTERVIEW_TYPES.LAST_EMPLOYEE,
          INTERVIEW_TYPES.DEFINE_JOB_TALK,
          INTERVIEW_TYPES.TALENT_INVENTORY,
        ],
      },
      {
        col: 12,
        label: '访谈时间',
        key: 'talkDate',
        showTypes: [
          INTERVIEW_TYPES.DAILY,
          INTERVIEW_TYPES.PROBATION,
          INTERVIEW_TYPES.APPLICATION,
          INTERVIEW_TYPES.LAST_EMPLOYEE,
          INTERVIEW_TYPES.DEFINE_JOB_TALK,
          INTERVIEW_TYPES.TALENT_INVENTORY,
        ],
        render: text => toDateFormat(text) || '-',
      },
      {
        col: 12,
        label: '访谈官',
        key: 'talkUser',
        showTypes: [
          INTERVIEW_TYPES.DAILY,
          INTERVIEW_TYPES.PROBATION,
          INTERVIEW_TYPES.APPLICATION,
          INTERVIEW_TYPES.LAST_EMPLOYEE,
          INTERVIEW_TYPES.TALENT_INVENTORY,
        ],
        render: text => text?.name || '-',
      },
      {
        col: 24,
        label: '访谈官',
        key: 'defenceOfficerName',
        showTypes: [INTERVIEW_TYPES.DEFINE_JOB_TALK],
        render: text => text || '-',
      },
      {
        col: 12,
        label: '评级',
        key: 'level',
        showTypes: [INTERVIEW_TYPES.TALENT_INVENTORY],
      },
      {
        col: 24,
        label: '末位类型',
        key: 'objectType',
        showTypes: [INTERVIEW_TYPES.LAST_EMPLOYEE],
      },
      {
        col: 24,
        label: '末位原因',
        key: 'lastReason',
        showTypes: [INTERVIEW_TYPES.LAST_EMPLOYEE],
        render: text => text || '-',
      },
      {
        col: 24,
        label: '访谈记录',
        key: 'talkRecord',
        showTypes: [INTERVIEW_TYPES.LAST_EMPLOYEE],
        render: text => text || '-',
      },
      {
        col: 24,
        label: '访谈背景',
        key: 'talkBgc',
        showTypes: [INTERVIEW_TYPES.DAILY],
      },
      {
        col: 24,
        label: '访谈结论',
        key: 'talkResult',
        showTypes: [
          INTERVIEW_TYPES.PROBATION,
          INTERVIEW_TYPES.LAST_EMPLOYEE,
          INTERVIEW_TYPES.DEFINE_JOB_TALK,
          // INTERVIEW_TYPES.TALENT_INVENTORY,
          INTERVIEW_TYPES.PROBATION_DEFENCE,
        ],
      },
      {
        col: 24,
        label: '访谈结论',
        key: 'talkResult',
        showTypes: [INTERVIEW_TYPES.APPLICATION],
        render: text => {
          return <span className="dhr-talent-recent-interview-item-color-highlight">{text || '-'}</span>;
        },
      },
      {
        col: 24,
        label: '附件',
        key: 'resultAttachment',
        showTypes: [
          INTERVIEW_TYPES.DAILY,
          INTERVIEW_TYPES.PROBATION,
          INTERVIEW_TYPES.APPLICATION,
          INTERVIEW_TYPES.LAST_EMPLOYEE,
        ],
        render: files => {
          return files
            ? files.map((fileItem, index) => (
                <div key={index}>
                  <a target="__blank" href={fileItem.url}>
                    {fileItem.name}
                  </a>
                </div>
              ))
            : '-';
        },
      },
      {
        col: 24,
        label: '综合评价',
        key: 'evaluate',
        showTypes: [
          INTERVIEW_TYPES.DAILY,
          INTERVIEW_TYPES.PROBATION,
          INTERVIEW_TYPES.APPLICATION,
          INTERVIEW_TYPES.DEFINE_JOB_TALK,
          INTERVIEW_TYPES.TALENT_INVENTORY,
          INTERVIEW_TYPES.PROBATION_DEFENCE,
        ],
      },
      {
        col: 24,
        label: '相关方评价',
        key: 'relateEvaluate',
        showTypes: [INTERVIEW_TYPES.DAILY, INTERVIEW_TYPES.APPLICATION, INTERVIEW_TYPES.TALENT_INVENTORY],
      },
      {
        col: 24,
        label: '沟通记录',
        key: 'defenceRecord',
        showTypes: [INTERVIEW_TYPES.DEFINE_JOB_TALK],
      },
      // {
      //   col: 24,
      //   label: '风险',
      //   key: 'risk',
      //   showTypes: [INTERVIEW_TYPES.TALENT_INVENTORY],
      //   className: 'dhr-talent-recent-interview-status-error',
      // },
    ],
    []
  );

  return (
    <div className="dhr-talent-file-recent-interview-item-container">
      <Row gutter={[0, 6]} className="dhr-talent-file-recent-interview-item">
        {recentColumns
          .filter(({ showTypes }) => showTypes.includes(initValues?.talkType))
          .map(columnItem => (
            <Col span={columnItem.col} key={columnItem.key} className={columnItem.className || ''}>
              <Row wrap={false}>
                <Col>
                  <span className="dhr-talent-recent-interview-label">{`${columnItem.label}：`}</span>
                </Col>
                <Col flex={1}>
                  <span className="dhr-talent-recent-interview-content">
                    {columnItem.render
                      ? columnItem.render(initValues?.[columnItem.key], initValues)
                      : initValues?.[columnItem.key] || '-'}
                  </span>
                </Col>
              </Row>
            </Col>
          ))}
      </Row>
    </div>
  );
};

export default RecentInterviewItem;
