import React from 'react';

import RecentInterviewItem from '../RecentInterviewItem';

export interface IAppProps {
  list: any;
}
const RecentInterviewList: React.FC<IAppProps> = ({ list }) => {
  return (
    <div className="dhr-talent-file-recent-interview-list-container">
      {list.slice(0, 1).map((recordItem, index) => (
        <RecentInterviewItem key={index} initValues={recordItem || {}} />
      ))}
    </div>
  );
};

export default RecentInterviewList;
