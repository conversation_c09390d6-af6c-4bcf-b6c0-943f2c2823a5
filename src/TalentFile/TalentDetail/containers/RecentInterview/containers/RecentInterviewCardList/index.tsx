import React from 'react';

import { toDateFormat } from '@utils/tools';

import FreeCard from '@components/FreeCard';
import RecentInterviewItem from '../RecentInterviewItem';

import './style.less';

export interface IAppProps {
  list: any;
}
const RecentInterviewList: React.FC<IAppProps> = ({ list }) => {
  return (
    <div className="dhr-talent-file-recent-interview-card-list-container">
      {list.map((recordItem, index) => (
        <FreeCard key={index} title={recordItem?.talkTypeName} subTitle={toDateFormat(recordItem?.talkDate) || ''}>
          <RecentInterviewItem initValues={recordItem || {}} />
        </FreeCard>
      ))}
    </div>
  );
};

export default RecentInterviewList;
