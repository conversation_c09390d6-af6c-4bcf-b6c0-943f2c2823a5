import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Row, Col, Spin } from 'antd';

import ehrApis from '@apis/ehr';

import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import TalentDetailModal from '../TalentDetailModal';
import RecentInterviewList from './containers/RecentInterviewList';
import RecentInterviewCardList from './containers/RecentInterviewCardList';

import './style.less';

export interface IAppProps {
  empId: string;
}
const RecentInterview: React.FC<IAppProps> = ({ empId }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [interviewRecordList, setInterviewRecordList] = useState<any[]>([]);

  // 末位访谈
  const onFetchFinalInterviewInfo = useCallback(
    employeeId =>
      new Promise(resolve => {
        fetchApi({
          ...ehrApis.finalInterviewInfo,
          params: {
            employeeId,
          },
          onSuccess: list => {
            resolve(list || []);
          },
          onError: error => {
            resolve([]);
            console.log('获取访谈数据报错-末位：', error);
          },
        });
      }),
    []
  );

  // 日常访谈
  const onFetchDailyInterviewInfo = useCallback(
    employeeId =>
      new Promise(resolve => {
        fetchApi({
          ...ehrApis.dailyInterviewInfo,
          params: {
            employeeId,
          },
          onSuccess: list => {
            resolve(list || []);
          },
          onError: error => {
            resolve([]);
            console.log('获取访谈数据报错-日常：', error);
          },
        });
      }),
    []
  );

  // 上任访谈
  const onFetchInductionInterviewInfo = useCallback(
    employeeId =>
      new Promise(resolve => {
        fetchApi({
          ...ehrApis.inductionInterviewInfo,
          params: {
            employeeId,
          },
          onSuccess: list => {
            resolve(list || []);
          },
          onError: error => {
            resolve([]);
            console.log('获取访谈数据报错-上任：', error);
          },
        });
      }),
    []
  );

  // 试用期访谈
  const onFetchProbationInterviewInfo = useCallback(
    employeeId =>
      new Promise(resolve => {
        fetchApi({
          ...ehrApis.probationInterviewInfo,
          params: {
            employeeId,
          },
          onSuccess: list => {
            resolve(list || []);
          },
          onError: error => {
            resolve([]);
            console.log('获取访谈数据报错-试用期：', error);
          },
        });
      }),
    []
  );

  // 见习期转正访谈
  const onFetchProbationDefenceInfo = useCallback(
    employeeId =>
      new Promise(resolve => {
        fetchApi({
          ...ehrApis.talentProbationDefenceInfo,
          params: {
            employeeId,
          },
          onSuccess: list => {
            resolve(list || []);
          },
          onError: error => {
            resolve([]);
            console.log('获取访谈数据报错-见习期转正：', error);
          },
        });
      }),
    []
  );

  // 定岗访谈数据
  const onFetchDefineJobInfo = useCallback(
    employeeId =>
      new Promise(resolve => {
        fetchApi({
          ...ehrApis.talentDefineJobInfo,
          params: {
            employeeId,
          },
          onSuccess: list => {
            resolve(list || []);
          },
          onError: error => {
            resolve([]);
            console.log('获取访谈数据报错-定岗访谈：', error);
          },
        });
      }),
    []
  );

  // 人才盘点访谈
  const onFetchTalentTalksInfo = useCallback(
    employeeId =>
      new Promise(resolve => {
        fetchApi({
          ...ehrApis.talentTalksInfo,
          params: {
            employeeId,
          },
          onSuccess: list => {
            resolve(list || []);
          },
          onError: error => {
            resolve([]);
            console.log('获取访谈数据报错-人才盘点：', error);
          },
        });
      }),
    []
  );

  const onFetchAllData = useCallback(empId => {
    try {
      setLoading(true);
      Promise.all([
        onFetchFinalInterviewInfo(empId),
        onFetchDailyInterviewInfo(empId),
        onFetchInductionInterviewInfo(empId),
        onFetchProbationInterviewInfo(empId),
        onFetchProbationDefenceInfo(empId),
        onFetchDefineJobInfo(empId),
        onFetchTalentTalksInfo(empId),
      ]).then(
        ([
          finalList,
          dailyList,
          inductionList,
          probationList,
          probationDefenceList,
          defineJobList,
          talkList,
        ]: any[]) => {
          const newInterviewRecordList = [
            ...finalList,
            ...dailyList,
            ...talkList,
            ...inductionList,
            ...probationList,
            ...defineJobList,
            ...probationDefenceList,
          ].sort((a, b) => b.talkDate - a.talkDate);
          setLoading(false);
          setInterviewRecordList(newInterviewRecordList);
        }
      );
    } catch (error) {
      setLoading(false);
      console.log('获取访谈数据报错啦：', error);
    }
  }, []);

  useEffect(() => {
    empId && onFetchAllData(empId);
  }, [empId]);

  const handleReadAll = useCallback(() => {
    TalentDetailModal.detail({
      width: 700,
      title: '近期访谈记录详情',
      content: <RecentInterviewCardList list={interviewRecordList} />,
    });
  }, [interviewRecordList]);

  return (
    <Spin spinning={loading}>
      <CardBlock title="近期访谈记录" isShowAction={interviewRecordList.length > 0} onRight={handleReadAll}>
        <div className="dhr-talent-recent-interview-container">
          {interviewRecordList.length > 0 ? <RecentInterviewList list={interviewRecordList} /> : '暂无数据'}
        </div>
      </CardBlock>
    </Spin>
  );
};

export default RecentInterview;
