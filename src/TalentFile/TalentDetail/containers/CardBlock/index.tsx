import React from 'react';
import { ArrowRightOutlined } from '@ant-design/icons';

import './style.less';

export interface IAppProps {
  title: string;
  onRight?: () => void;
  isShowAction?: boolean;
  children: React.ReactNode;
}

const CardBlock: React.FC<IAppProps> = props => {
  const { title, onRight, children, isShowAction = true } = props;
  return (
    <div className="dhr-talent-card-block-container">
      <div className="dhr-talent-card-block-header">
        <span className="dhr-talent-block-title">{title}</span>
        {isShowAction && (
          <span className="dhr-talent-block-right-action" onClick={onRight}>
            <ArrowRightOutlined style={{ fontSize: '14px' }} />
          </span>
        )}
      </div>
      <div>{children}</div>
    </div>
  );
};

export default CardBlock;
