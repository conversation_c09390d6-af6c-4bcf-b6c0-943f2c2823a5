import { render as reactRender, unmountComponentAtNode as reactUnmount } from 'react-dom';
import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import classnames from 'classnames';
import { Modal } from 'antd';

import './style.less';

export interface ModalProps {
  title: string;
  width?: number;
  className?: string;
  content: ReactNode;
}

export interface ExtraProps {
  onAfterClose: () => void;
}

export interface IAppProps extends ModalProps, ExtraProps {
  open: boolean;
}
const TalentDetail: React.FC<IAppProps> = ({ open, title, content, width = 500, className, onAfterClose }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    setVisible(open);
  }, [open]);

  const handleCancel = useCallback(() => {
    setVisible(false);
  }, []);

  return (
    <Modal
      centered
      open={visible}
      title={title}
      footer={null}
      width={width}
      onCancel={handleCancel}
      afterClose={onAfterClose}
    >
      <div className={classnames('dhr-file-talent-detail-modal-container', className || '')}>{content}</div>
    </Modal>
  );
};

const withParams = (params: ModalProps) => {
  return {
    ...params,
    open: true,
  };
};

const previewModal = (params: any) => {
  const container = document.createDocumentFragment();
  const onAfterClose = () => {
    reactUnmount(container);
  };
  reactRender(<TalentDetail {...params} onAfterClose={onAfterClose} />, container);
};

const TalentDetailModal = TalentDetail as any;

TalentDetailModal.detail = function previewFn(props: ModalProps) {
  return previewModal(withParams(props));
};

export default TalentDetailModal;
