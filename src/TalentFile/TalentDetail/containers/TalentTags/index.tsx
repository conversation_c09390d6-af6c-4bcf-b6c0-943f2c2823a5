import React, { useCallback, useEffect, useState } from 'react';

import talentApis from '@apis/talent';
import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import TalentTagItem from './containers/TalentTagItem';

import './style.less';

export interface IAppProps {
  empId: string;
}
const TalentTags: React.FC<IAppProps> = ({ empId }) => {
  const [tagList, setTagList] = useState<any[]>([]);

  const onFetchEmpTags = useCallback(employeeId => {
    fetchApi({
      ...talentApis.empTagList,
      params: {
        employeeId,
      },
      onSuccess: list => {
        setTagList(list || []);
      },
    });
  }, []);

  useEffect(() => {
    empId && onFetchEmpTags(empId);
  }, [empId]);

  console.log('tagList===', tagList);

  return (
    <CardBlock title="人才标签" isShowAction={false}>
      <div className="dhr-talent-tags-container">
        <TalentTagItem list={tagList} />
      </div>
    </CardBlock>
  );
};

export default TalentTags;
