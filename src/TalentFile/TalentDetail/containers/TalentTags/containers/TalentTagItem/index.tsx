import React, { useCallback } from 'react';
import { Tooltip } from 'antd';

import './style.less';

export interface IAppProps {
  list: any[];
}
const TalentTagItem: React.FC<IAppProps> = ({ list }) => {
  const TagItem = useCallback(({ data }) => {
    return (
      <Tooltip title={data.recordDesc || '暂无额外信息'} key={data.id}>
        <span
          style={{
            cursor: 'pointer',
            color: data.color && '#fff',
            backgroundColor: data.color,
          }}
          className="dhr-talent-tag-item"
        >
          {data.name}
        </span>
      </Tooltip>
    );
  }, []);

  return (
    <div className="dhr-talent-tags-item-container">
      {list.map(tagItem => {
        return (
          <div key={tagItem.id} className="dhr-talent-tag-item-container">
            <div className="dhr-talent-tag-item-title">{tagItem.name}</div>
            <div className="dhr-talent-tags">
              {(tagItem.tagValueList || []).map(tag => {
                return <TagItem key={tag.id} data={tag} />;
              })}
              {(tagItem.tagValueList || []).length === 0 && (
                <div key="not_Tag" className="dhr-talent-tag-item dhr-talent-tag-item-gray">
                  暂无标签
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TalentTagItem;
