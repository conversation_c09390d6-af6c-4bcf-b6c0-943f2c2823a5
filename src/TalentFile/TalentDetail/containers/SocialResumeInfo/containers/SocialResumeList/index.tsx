import classnames from 'classnames';
import { Timeline } from 'antd';
import React from 'react';

import { toDateFormat } from '@utils/tools';

import './style.less';

export interface IAppProps {
  list: any[];
  isShowAll?: boolean;
}
const SocialResumeList: React.FC<IAppProps> = ({ list, isShowAll = false }) => {
  const socialResumeList = isShowAll ? list : list.slice(0, 5);
  return (
    <div className="dhr-talent-file-social-resume-list-container">
      <Timeline>
        {socialResumeList.map((socialResumeItem, index) => {
          return (
            <Timeline.Item key={socialResumeItem.id}>
              <div className={classnames('dhr-talent-file-social-resume-item')}>
                <div className="dhr-talent-file-social-resume-item-title">
                  <span>
                    <span>{socialResumeItem.post}</span>
                    <span> | </span>
                    <span>{socialResumeItem.companyName}</span>
                  </span>
                </div>
                <div className="dhr-talent-file-social-resume-item-content">
                  <span className="dhr-talent-social-resume-item-pos-name">
                    <span>{toDateFormat(socialResumeItem.beginDate)}</span>
                    <span> ~ </span>
                    <span>{toDateFormat(socialResumeItem.endDate)}</span>
                  </span>
                </div>
              </div>
            </Timeline.Item>
          );
        })}
      </Timeline>
    </div>
  );
};

export default SocialResumeList;
