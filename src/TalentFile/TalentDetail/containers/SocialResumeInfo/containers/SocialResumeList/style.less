.dhr-talent-file-social-resume-list-container {
  & > .ant-timeline {
    & > .ant-timeline-item {
      padding-bottom: 6px;
      .ant-timeline-item-head {
        top: 20px;
      }
      .ant-timeline-item-tail {
        top: 30px;
        border-color: var(--antd-dynamic-primary-color);
      }
      .ant-timeline-item-content {
        margin-left: 20px;
        .dhr-talent-file-social-resume-item {
          padding: 10px 20px;
          border-radius: 6px;
          background-color: rgba(246, 246, 246, 0.82);
          transition: all 0.3s ease;
          &:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
          }
          .dhr-talent-file-social-resume-item-title {
            margin-bottom: 4px;
            font-weight: 500;
          }
          .dhr-talent-file-social-resume-item-content {
            .dhr-talent-social-resume-item-pos-name {
              margin-right: 6px;
              color: #00000073;
            }
          }
        }
      }
    }
  }
}
