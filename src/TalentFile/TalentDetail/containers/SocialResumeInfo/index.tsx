import React, { useCallback, useEffect, useState } from 'react';
import { Spin, Timeline } from 'antd';

import ehrApis from '@apis/ehr';

import { request as fetchApi } from '@utils/http';

import CardBlock from '../CardBlock';
import DrawerDetail from '@components/DrawerDetail';
import SocialResumeList from './containers/SocialResumeList';

import './style.less';

export interface IAppProps {
  empId: string;
}
const SocialResumeInfo: React.FC<IAppProps> = ({ empId }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [socialResumeList, setSocialResumeList] = useState<any[]>([]);

  const onFetchPerformanceInfo = useCallback(() => {
    setLoading(true);
    fetchApi({
      ...ehrApis.socialResumeInfo,
      params: {
        employeeId: empId,
      },
      onSuccess: res => {
        setSocialResumeList(res?.list || []);
      },
    }).finally(() => setLoading(false));
  }, [empId]);

  useEffect(() => {
    empId && onFetchPerformanceInfo();
  }, [empId]);

  const handleReadAll = useCallback(() => {
    DrawerDetail.detail({
      title: '外部经历详情',
      width: 700,
      content: <SocialResumeList isShowAll list={socialResumeList} />,
    });
  }, [socialResumeList]);

  return (
    <Spin spinning={loading}>
      <CardBlock title="外部经历" onRight={handleReadAll} isShowAction={socialResumeList.length > 0}>
        <div className="dhr-talent-file-social-resume-container">
          {socialResumeList.length > 0 ? <SocialResumeList list={socialResumeList} /> : '暂无数据'}
        </div>
      </CardBlock>
    </Spin>
  );
};

export default SocialResumeInfo;
