import React, { useCallback, useMemo } from 'react';

import CardBlock from '../CardBlock';
import { EmployeeInfoType } from '../../index';
import BaseInfoItem from './containers/BaseInfoItem';
import TalentDetailModal from '../TalentDetailModal';

import './style.less';

export interface IAppProps {
  initValues: EmployeeInfoType;
}
const BaseInfo: React.FC<IAppProps> = ({ initValues }) => {
  const handleReadAll = useCallback(() => {
    TalentDetailModal.detail({
      title: '基本信息详情',
      width: 600,
      content: <BaseInfoItem initValues={initValues} />,
    });
  }, [initValues]);

  return (
    <CardBlock title="基础信息" onRight={handleReadAll}>
      <div className="dhr-talent-emp-base-info-container">
        <BaseInfoItem initValues={initValues} />
      </div>
    </CardBlock>
  );
};

export default BaseInfo;
