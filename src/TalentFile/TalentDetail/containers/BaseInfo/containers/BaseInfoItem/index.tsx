import React, { useCallback, useMemo } from 'react';
import classnames from 'classnames';
import { Row, Col } from 'antd';
import Big from 'big.js';

import { toDateFormat } from '@utils/tools';

import './style.less';

const monthBig = new Big(12);

export interface IAppProps {
  initValues: any;
}
const BaseInfoItem: React.FC<IAppProps> = ({ initValues }) => {
  const separateList = useMemo(
    () => [
      {
        label: '所属BG',
        key: 'companyName',
        source: 'baseInfoJob',
        render: text => text || '-',
      },
      {
        label: '所属部门全路径',
        key: 'deptFullPath',
        source: 'baseInfoJob',
        render: text => text || '-',
      },
      {
        label: '职级通道',
        key: 'careerName',
        source: 'summaryInfo',
        render: text => text || '-',
      },
    ],
    []
  );

  const handleComputePositionDuration = useCallback((val, title) => {
    try {
      const timeNum = Number(val);
      const year = Math.floor(timeNum);
      const totalMonth = monthBig.times(timeNum).toNumber();
      const totalMonthRound = Math.round(totalMonth);
      const month = totalMonthRound % 12;
      const monthStr = month > 0 ? String(totalMonthRound % 12).padStart(2, '0') : month;
      return `${year}年${monthStr}月`;
    } catch (error) {
      console.log(`${title}计算报错啦~~`, error);
      return val;
    }
  }, []);

  const biserialList = useMemo(
    () => [
      {
        label: '毕业时间',
        key: 'graduationDate',
        source: 'summaryInfo',
        render: text => toDateFormat(text) || '-',
      },
      {
        label: '最高学历',
        key: 'universityCollege',
        source: 'summaryInfo',
        render: (text, record) => `${record.educationLevel || '-'}/${text || '-'}`,
      },
      {
        label: '入职时间',
        key: 'laborDate',
        source: 'baseInfoEntryInfo',
        render: text => toDateFormat(text) || '-',
      },
      {
        label: '定岗时间',
        key: 'determinePostsDate',
        source: 'baseInfoEntryInfo',
        render: text => (text ? toDateFormat(text) : '未定岗'),
      },
      {
        label: '出生日期（生日类型）',
        key: 'empBirthDay',
        source: 'baseInfo',
        render: (text, record) => `${toDateFormat(text)}(${record.birthDayType})`,
      },
      {
        label: '招聘渠道',
        key: 'recruitMode',
        source: 'baseInfoEntryInfo',
        render: text => text || '-',
      },
      {
        label: '职位',
        key: 'positionName',
        source: 'baseInfoJob',
        render: text => text || '-',
      },
      {
        label: '管理层级',
        key: 'jobLevel',
        source: 'summaryInfo',
        render: text => text || '-',
      },
      {
        label: '职级通道等级名称',
        key: 'gradeName',
        source: 'summaryInfo',
        render: text => text || '-',
      },
      {
        label: '当前岗位任期',
        source: 'summaryInfo',
        key: 'currPositionDuration',
        render: text => (text ? handleComputePositionDuration(text, '当前岗位任期') : text || '-'),
      },
      {
        label: '管理岗位任期',
        source: 'summaryInfo',
        key: 'managementPositionDuration',
        render: text => (text ? handleComputePositionDuration(text, '管理岗位任期') : text || '-'),
      },
    ],
    []
  );

  return (
    <div className="dhr-talent-file-base-info-item-container">
      <Row className="dhr-talent-base-info-row" gutter={[0, 4]}>
        {separateList.map(separateItem => (
          <Col key={separateItem.key} span={24}>
            <div className="dhr-talent-base-info-label">
              <span>{separateItem.label}</span>
            </div>
            <div className="dhr-talent-base-info-content">
              <span>{separateItem.render(initValues[separateItem.source]?.[separateItem.key])}</span>
            </div>
          </Col>
        ))}
      </Row>
      <Row className="dhr-talent-base-info-row" gutter={[0, 2]}>
        {biserialList.map(biserialItem => (
          <Col span={8} key={biserialItem.key}>
            <div className="dhr-talent-base-info-label">
              <span>{biserialItem.label}</span>
            </div>
            <div className="dhr-talent-base-info-content">
              <span>
                {biserialItem.render(
                  initValues[biserialItem.source]?.[biserialItem.key],
                  initValues[biserialItem.source] || {}
                )}
              </span>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default BaseInfoItem;
