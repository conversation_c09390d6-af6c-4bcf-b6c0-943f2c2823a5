import React from 'react';

import TalentDetail, { IAppProps as TalentDetailProps } from '../TalentDetail';

import useViews from '@hooks/useViews';

import { CSB_VIEW_CODES } from '@constants/common';

export interface IAppProps extends TalentDetailProps {}

const TalentDetailArchives: React.FC<IAppProps> = props => {
  const { Data: views } = useViews(CSB_VIEW_CODES.TALENT_DETAIL_ARCHIVES);

  return <TalentDetail {...props} views={views} />;
};

export default TalentDetailArchives;
