import { AutoSizer, List as VirtualizedList, CellMeasurer, CellMeasurerCache } from 'react-virtualized';
import { LoadingOutlined } from '@ant-design/icons';
import { Row, Col, Spin } from 'antd';
import React, { useCallback } from 'react';

import useThrottle from '@hooks/useThrottle';

import TagEmpCard from '../TagEmpCard';

import './style.less';

const cache = new CellMeasurerCache({
  fixedWidth: true,
  defaultHeight: 150, // 默认高度
  keyMapper: rowIndex => rowIndex, // 唯一的键，根据行索引生成
});

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

export interface IAppProps {
  list: any[];
  loading: boolean;
  pagination: any;
  onJumpDetail: (params) => void;
  onSearch: (params: any, isLastAdd: boolean) => void;
}
const ScrollTagEmpList: React.FC<IAppProps> = ({ pagination, onSearch, list, onJumpDetail, loading }) => {
  // 滚动到底
  const handleScroll = useThrottle(
    ({ scrollTop, clientHeight, scrollHeight }) => {
      const isNotScrollTop = scrollTop !== 0;
      const isLoadMoreData = scrollTop + clientHeight >= scrollHeight - 10;
      const { pageNum, pages } = pagination;
      if (isLoadMoreData && isNotScrollTop && pageNum < pages) {
        const nextPageNum = pageNum + 1;
        onSearch({ pageNum: nextPageNum, pageSize: 20 }, true);
      }
    },
    200,
    [pagination]
  );

  const rowRenderer = useCallback(
    ({ key, index, parent, style }) => {
      return (
        <CellMeasurer key={key} cache={cache} parent={parent} columnIndex={0} rowIndex={index}>
          <div style={style}>
            <TagEmpCard data={list[index] || {}} onJumpDetail={onJumpDetail} />
          </div>
        </CellMeasurer>
      );
    },
    [list]
  );

  const { total = 0, pageNum = 0, pages = 0 } = pagination || {};
  return (
    <div className="dhr-talent-tags-scroll-container">
      <Row className="dhr-talent-tags-search-content-container">
        <Col span={24}>
          <div className="dhr-talent-tags-result-count-container">
            <span>共</span>
            <span className="dhr-talent-tags-result-count">{total}</span>
            <span>个结果</span>
          </div>
          <AutoSizer disableHeight>
            {({ height, width }) => {
              return (
                <VirtualizedList
                  width={width}
                  height={500}
                  overscanRowCount={3}
                  rowCount={list.length}
                  onScroll={handleScroll}
                  rowRenderer={rowRenderer}
                  rowHeight={cache.rowHeight} // 使用 CellMeasurerCache 的动态行高
                  deferredMeasurementCache={cache}
                />
              );
            }}
          </AutoSizer>
        </Col>
      </Row>
      <div className="dhr-talent-tags-footer-load">
        {loading && (
          <>
            <Spin indicator={antIcon} />
            <span className="dht-talent-tags-footer-load-text">数据加载中...</span>
          </>
        )}
        {pageNum >= pages && <span className="dht-talent-tags-footer-data-end">数据到底拉~</span>}
      </div>
    </div>
  );
};

export default ScrollTagEmpList;
