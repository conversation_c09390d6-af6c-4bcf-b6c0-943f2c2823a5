import { CaretRightOutlined } from '@ant-design/icons';
import React from 'react';

import './style.less';

export interface IAppProps {
  key: string;
  name: string;
}
const TreeNode: React.FC<IAppProps> = props => {
  const { key, name } = props;

  return (
    <span key={key} className="dhr-talent-tag-tree-node">
      <span className="dhr-talent-tag-tree-node-name">{name}</span>
      <span className="dhr-talent-tag-tree-node-right-icon">
        <CaretRightOutlined style={{ fontSize: '12px' }} />
      </span>
    </span>
  );
};

export default TreeNode;
