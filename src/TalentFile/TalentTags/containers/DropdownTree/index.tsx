import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';
import { Tree } from 'antd';
import * as R from 'ramda';

import TreeNode from '../TreeNode';

import './style.less';

export interface IAppProps {
  ref?: any;
  tagMapList: any;
  treeList: any[];
  selectTagRows: any[];
  categoryList: any[];
  selectTagIds: string[];
  onSelectTagIds: (params) => void;
  onSelectTagRows: (params) => void;
}
const DropdownTree: React.FC<IAppProps> = forwardRef(
  ({ treeList, tagMapList, categoryList, selectTagIds, selectTagRows, onSelectTagIds, onSelectTagRows }, ref) => {
    const tagMapCategorysRef = useRef({});

    const [hoverTag, setHoverTag] = useState(undefined);
    const [selectCategorys, setSelectCategorys] = useState([]);
    const [tagMouseStatus, setTagMouseStatus] = useState<string>('hidden');

    useImperativeHandle(ref, () => ({
      // 删除所有标签
      onDelAllTags: handleDelAllTags,
      // 删除某个标签
      onDelSelectTag: (tagItem, ids) => handleSelectTag(tagItem, ids),
    }));

    // 删除所有标签
    const handleDelAllTags = useCallback(() => {
      onSelectTagIds([]);
      onSelectTagRows([]);
      setSelectCategorys([]);
    }, []);

    // 移入
    const handleMouseEnterTag = useCallback(() => {
      setTagMouseStatus('enter');
    }, []);

    const handleMouseLeave = useCallback(() => {
      setHoverTag(undefined);
      setTagMouseStatus('leave');
    }, []);

    // 动画结束
    const handleAnimationEnd = useCallback(() => {
      tagMouseStatus === 'leave' ? setTagMouseStatus('hidden') : setTagMouseStatus('show');
    }, [tagMouseStatus]);

    // 移入分类
    const handleTreeMouseEnter = useCallback(({ node }) => {
      const { id, key, pos, title } = node;
      setHoverTag({ id, key, pos, title });
    }, []);

    const tagList = useMemo(() => {
      if (!hoverTag) {
        return [];
      }
      const { key } = hoverTag;
      return tagMapList?.[key] || [];
    }, [hoverTag, tagMapList]);

    const handleListFormTreeLeaf = useCallback((list, leaf, key, parentKey) => {
      const record = list.find(item => item[key] === leaf);
      if (!record) {
        return [];
      }
      if (!record[parentKey]) {
        return [record];
      }
      return [...handleListFormTreeLeaf(list, record[parentKey], key, parentKey), record];
    }, []);

    const handleSelectTag = (tagItem, ids?: string[]) => {
      const selectTagId = tagItem.id;
      let categoryIds = ids;
      if (!categoryIds) {
        const { id } = hoverTag;
        const categorysList = handleListFormTreeLeaf(categoryList, id, 'id', 'parent');
        categoryIds = categorysList.map(({ id }) => id);
      }
      if (!tagMapCategorysRef[selectTagId]) {
        tagMapCategorysRef[selectTagId] = categoryIds;
      }
      const eqA = R.eqBy(R.prop('id'));
      // 求差集
      const newSelectTags = R.symmetricDifferenceWith(
        eqA,
        [{ ...tagItem, treePathIds: categoryIds }],
        // [...selectTagsRef.current]
        [...selectTagRows]
      );
      const newSelectTagIds = newSelectTags.map(({ id }) => id);
      const newCategoryIds = newSelectTagIds.reduce((pre, cur) => {
        pre.push(...tagMapCategorysRef[cur]);
        return pre;
      }, []);

      // 去重
      const uniqCategoryIds = R.uniq(newCategoryIds);
      // selectTagsRef.current = newSelectTags;

      onSelectTagRows(newSelectTags);
      onSelectTagIds(newSelectTagIds);
      setSelectCategorys(uniqCategoryIds);
    };

    const tagsLen = useMemo(() => selectTagIds.length, [selectTagIds]);

    return (
      <div className="dhr-dropdown-tree-container">
        <div className="dhr-dropdown-tree" onMouseEnter={handleMouseEnterTag} onMouseLeave={handleMouseLeave}>
          <div
            className={classnames('dhr-tag-select-container', {
              'dhr-tag-select-enter': selectTagIds.length > 0 || tagMouseStatus === 'show',
            })}
          >
            <span>标签分类</span>
            <span>({tagsLen})</span>
            <i className="dhr-menu-submenu-arrow"></i>
          </div>
          <div className="dhr-tag-classification-container">
            <div
              className={classnames('dhr-dropdown-container', {
                'dhr-dropdown-hidden': tagMouseStatus === 'hidden',
                'dhr-slide-up-leave dhr-slide-up-leave-active': tagMouseStatus === 'leave',
                'dhr-slide-up-enter dhr-slide-up-enter-active dhr-slide-up': tagMouseStatus === 'enter',
              })}
              onAnimationEnd={handleAnimationEnd}
            >
              <div className="dhr-dropdown-list-container">
                <div className="dhr-dropdown-menu">
                  <Tree
                    multiple
                    blockNode
                    defaultExpandAll
                    treeData={treeList}
                    titleRender={TreeNode}
                    selectedKeys={selectCategorys}
                    onMouseEnter={handleTreeMouseEnter}
                  />
                </div>
                {hoverTag && (
                  <div className="dhr-dropdown-menu-right-container">
                    {tagList.map(tagsDetail => {
                      return (
                        <div key={tagsDetail.id} className="dhr-dropdown-menu-tags-row">
                          <div className="dhr-dropdown-menu-tags-col-4">
                            <div>
                              <span>{tagsDetail.name}</span>
                            </div>
                          </div>
                          <div className="dhr-dropdown-menu-tags-col-20">
                            {(tagsDetail.tagValueList || []).length > 0 && (
                              <div className="dhr-dropdown-menu-tags-list-container">
                                {(tagsDetail.tagValueList || []).map(tagItem => {
                                  return (
                                    <span
                                      key={tagItem.id}
                                      onClick={() => handleSelectTag(tagItem)}
                                      className={classnames('dhr-dropdown-menu-tag', {
                                        'dhr-dropdown-menu-tag-highlight': selectTagIds.includes(tagItem.id),
                                      })}
                                    >
                                      {tagItem.name}
                                    </span>
                                  );
                                })}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default DropdownTree;
