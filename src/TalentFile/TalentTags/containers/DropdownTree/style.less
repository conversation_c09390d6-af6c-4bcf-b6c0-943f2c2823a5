.dhr-dropdown-tree-container {
  .dhr-dropdown-tree {
    position: relative;
    display: inline-block;
    padding-bottom: 6px;
    .dhr-tag-select-container {
      position: relative;
      cursor: pointer;
      border-radius: 4px;
      line-height: 20px;
      padding: 6px 24px 6px 12px;
      background-color: #f8f8f8;
      &:hover {
        color: var(--antd-dynamic-primary-color);
        background-color: var(--antd-dynamic-primary-underly-color-2);
        .dhr-menu-submenu-arrow {
          transform: translateY(-2px);
          &::before {
            color: var(--antd-dynamic-primary-color);
            transform: rotate(45deg) translate(2.5px);
          }

          &::after {
            color: var(--antd-dynamic-primary-color);
            transform: rotate(-45deg) translate(-2.5px);
          }
        }
      }
      .dhr-menu-submenu-arrow {
        position: absolute;
        top: 50%;
        right: 6px;
        width: 10px;
        color: #000000d9;
        transform: translateY(-50%);
        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        &::before {
          content: '';
          width: 6px;
          height: 1.5px;
          position: absolute;
          border-radius: 2px;
          background-color: currentcolor;
          transform: rotate(-45deg) translate(2.5px);
          transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
            transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
            color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        &::after {
          content: '';
          width: 6px;
          height: 1.5px;
          position: absolute;
          border-radius: 2px;
          background-color: currentcolor;
          transform: rotate(45deg) translate(-2.5px);
          transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
            transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
            color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
      }
    }

    // 高亮
    .dhr-tag-select-enter {
      color: var(--antd-dynamic-primary-color);
      background-color: var(--antd-dynamic-primary-underly-color-2);
      .dhr-menu-submenu-arrow {
        transform: translateY(-2px);
        &::before {
          color: var(--antd-dynamic-primary-color);
          transform: rotate(45deg) translate(2.5px);
        }

        &::after {
          color: var(--antd-dynamic-primary-color);
          transform: rotate(-45deg) translate(-2.5px);
        }
      }
    }

    .dhr-tag-classification-container {
      .dhr-dropdown-container {
        top: 36px;
        left: 0px;
        width: 210px;
        z-index: 1050;
        display: block;
        position: absolute;
        .dhr-dropdown-list-container {
          position: relative;
          background-color: #fff;
          .dhr-dropdown-menu {
            outline: none;
            height: 320px;
            font-size: 14px;
            padding: 8px 4px;
            overflow: overlay;
            border-radius: 8px;
            background-color: #fff;
            color: rgba(0, 0, 0, 0.88);
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12),
              0 9px 28px 8px rgba(0, 0, 0, 0.05);
            & > .ant-tree {
              .ant-tree-list {
                .ant-tree-treenode {
                  width: 100%;
                  .ant-tree-node-content-wrapper {
                    width: 100%;
                    font-size: 14px;
                    padding: 2px 4px 2px 6px;
                    white-space: nowrap;
                    &.ant-tree-node-selected {
                      background-color: #fff;
                      color: var(--antd-dynamic-primary-color);
                    }
                    &:hover {
                      color: var(--antd-dynamic-primary-color);
                      background-color: var(--antd-dynamic-primary-underly-color-2);
                    }
                  }
                }
              }
            }

            &::-webkit-scrollbar {
              width: 4px;
              height: 4px;
            }

            &::-webkit-scrollbar-corner {
              background-color: #f1f1f1;
            }
            &::-webkit-scrollbar-thumb {
              height: 50px;
              background-color: #ddd;
              border-radius: 10px;
            }

            &::-webkit-scrollbar-track-piece {
              background-color: #f1f1f1;
              border-radius: 0;
              -webkit-border-radius: 0;
            }
          }

          // 右侧
          .dhr-dropdown-menu-right-container {
            position: absolute;
            left: 100%;
            top: 0;
            bottom: 0;
            width: 680px;
            font-size: 13px;
            overflow: overlay;
            padding-top: 10px;
            border-radius: 6px;
            padding-left: 18px;
            padding-bottom: 10px;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, 0.06);
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12),
              0 9px 28px 8px rgba(0, 0, 0, 0.05);
            .dhr-dropdown-menu-tags-row {
              display: flex;
              line-height: 2.5;
              padding-top: 10px;

              .dhr-dropdown-menu-tags-col-4 {
                width: 86px;
                color: #000000d9;
                font-weight: 500;
              }
              .dhr-dropdown-menu-tags-col-20 {
                flex: 1;
                .dhr-dropdown-menu-tags-list-container {
                  padding-left: 6px;
                  padding-bottom: 10px;
                  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                  .dhr-dropdown-menu-tag {
                    cursor: pointer;
                    margin-right: 26px;
                    display: inline-block;
                    &:hover {
                      color: var(--antd-dynamic-primary-color);
                    }
                  }
                  // 高亮
                  .dhr-dropdown-menu-tag-highlight {
                    color: var(--antd-dynamic-primary-color);
                  }
                }
              }
            }
          }
        }
      }

      // 隐藏
      .dhr-dropdown-hidden {
        display: none;
        pointer-events: none;
      }

      :where(.dhr-dropdown-container) {
        &.dhr-slide-up-enter {
          opacity: 0;
          transform: scale(0);
          transform-origin: 0% 0%;
          animation-duration: 0.2s;
          animation-fill-mode: both;
          animation-play-state: paused;
          animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
          &.dhr-slide-up-enter-active {
            animation-name: dhr-slideUpIn;
            animation-play-state: running;
          }
        }
      }

      :where(.dhr-dropdown-container) {
        &.dhr-slide-up-leave {
          animation-duration: 0.2s;
          animation-fill-mode: both;
          animation-play-state: paused;
          animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
          &.dhr-slide-up-leave-active {
            animation-name: dhr-slideUpOut;
            animation-play-state: running;
          }
        }
      }

      @keyframes dhr-slideUpIn {
        0% {
          transform: scaleY(0.8);
          transform-origin: 0% 0%;
          opacity: 0;
        }
        100% {
          transform: scaleY(1);
          transform-origin: 0% 0%;
          opacity: 1;
        }
      }

      @keyframes dhr-slideUpOut {
        0% {
          transform: scaleY(1);
          transform-origin: 0% 0%;
          opacity: 1;
        }
        100% {
          transform: scaleY(0.8);
          transform-origin: 0% 0%;
          opacity: 0;
        }
      }
    }
  }
}
