import classnames from 'classnames';
import React, { useCallback, useMemo } from 'react';
import { Dropdown, Space, Typography } from 'antd';

import './style.less';

export interface IAppProps {
  title: string;
  options: any[];
  optKey?: string;
  optName?: string;
  multiple?: boolean;
  onSelect?: (params) => void;
  selectKey: string | string[];
}

const DropSelect: React.FC<IAppProps> = ({
  title,
  options,
  optKey,
  optName,
  selectKey,
  onSelect,
  multiple = false,
}) => {
  const menItems = useMemo(() => {
    if (optKey || optName) {
      const list = (options || []).map(optItem => ({
        ...optItem,
        label: optItem[optName],
        key: optItem[optKey],
      }));
      return list;
    }
    return options;
  }, [optKey, optName, options]);

  const selectNames = useMemo(() => {
    if (selectKey) {
      if (multiple) {
        const newSelectNames = options
          .filter(optItem => selectKey.includes(optItem[optKey || 'key']))
          .map(optItem => optItem[optName || 'label'])
          .join('、');
        return newSelectNames;
      } else {
        const newSelectNames = options
          .filter(optItem => optItem[optKey || 'key'] === selectKey)
          .map(optItem => optItem[optName || 'label'])
          .join('、');
        return newSelectNames;
      }
    } else {
      return '';
    }
  }, [options, optKey, optName, selectKey, multiple]);

  const selectedKeys: any = useMemo(() => {
    const list = multiple ? selectKey : [selectKey];
    return list;
  }, [selectKey, multiple]);

  return (
    <div className="dhr-talent-drop-select-container">
      <Dropdown
        menu={{
          multiple,
          selectedKeys,
          selectable: true,
          onClick: onSelect,
          items: menItems || [],
          className: 'dhr-drop-select-menu-container',
        }}
      >
        <div className={classnames('dhr-tag-select-container', { 'dhr-tag-select-enter': !!selectNames })}>
          <span>{title}</span>
          {selectNames && <span>({selectNames})</span>}
          <i className="dhr-menu-submenu-arrow"></i>
        </div>
      </Dropdown>
    </div>
  );
};

export default DropSelect;
