.dhr-talent-drop-select-container {
  .dhr-tag-select-container {
    position: relative;
    cursor: pointer;
    border-radius: 4px;
    line-height: 20px;
    padding: 6px 24px 6px 12px;
    background-color: #f8f8f8;
    &:hover {
      color: var(--antd-dynamic-primary-color);
      background-color: var(--antd-dynamic-primary-underly-color-2);
      .dhr-menu-submenu-arrow {
        transform: translateY(-2px);
        &::before {
          color: var(--antd-dynamic-primary-color);
          transform: rotate(45deg) translate(2.5px);
        }

        &::after {
          color: var(--antd-dynamic-primary-color);
          transform: rotate(-45deg) translate(-2.5px);
        }
      }
    }
    .dhr-menu-submenu-arrow {
      position: absolute;
      top: 50%;
      right: 6px;
      width: 10px;
      color: #000000d9;
      transform: translateY(-50%);
      transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      &::before {
        content: '';
        width: 6px;
        height: 1.5px;
        position: absolute;
        border-radius: 2px;
        background-color: currentcolor;
        transform: rotate(-45deg) translate(2.5px);
        transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
          transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
          color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      }

      &::after {
        content: '';
        width: 6px;
        height: 1.5px;
        position: absolute;
        border-radius: 2px;
        background-color: currentcolor;
        transform: rotate(45deg) translate(-2.5px);
        transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
          transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
          color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      }
    }
  }
  // 高亮
  .dhr-tag-select-enter {
    color: var(--antd-dynamic-primary-color);
    background-color: var(--antd-dynamic-primary-underly-color-2);
    .dhr-menu-submenu-arrow {
      transform: translateY(-2px);
      &::before {
        color: var(--antd-dynamic-primary-color);
        transform: rotate(45deg) translate(2.5px);
      }

      &::after {
        color: var(--antd-dynamic-primary-color);
        transform: rotate(-45deg) translate(-2.5px);
      }
    }
  }
}

.dhr-drop-select-menu-container {
  .ant-dropdown-menu-item:hover,
  .ant-dropdown-menu-item-selected {
    color: var(--antd-dynamic-primary-color);
    background-color: var(--antd-dynamic-primary-underly-color-2);
  }
}
