import type { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { Row, Col, Tag, Avatar } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import employeeApis from '@apis/employee';
import Icon from '@ant-design/icons';
import axios from 'axios';

import './style.less';

export interface IAppProps {
  data: any;
  onJumpDetail: (params) => void;
}
const TagEmpCard: React.FC<IAppProps> = ({ data, onJumpDetail }) => {
  const { empAccount } = data || {};

  const [avatarUrl, setAvatarUrl] = useState('');
  const ManIconSvg: any = useCallback(
    () => (
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2859" width="14" height="14">
        <path
          p-id="2860"
          fill="#ffffff"
          d="M795.189333 176.917333H682.666667a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V219.946667l-105.866667 105.866666A350.613333 350.613333 0 0 1 821.333333 554.666667c0 194.4-157.6 352-352 352S117.333333 749.066667 117.333333 554.666667s157.6-352 352-352a350.538667 350.538667 0 0 1 221.6 78.506666l104.256-104.256zM469.333333 842.666667c159.061333 0 288-128.938667 288-288S628.394667 266.666667 469.333333 266.666667 181.333333 395.605333 181.333333 554.666667s128.938667 288 288 288z"
        ></path>
      </svg>
    ),
    []
  );

  const WomanIconSvg: any = useCallback(
    () => (
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3789" width="14" height="14">
        <path
          p-id="3790"
          fill="#ffffff"
          d="M485.333333 768v-43.765333C321.077333 710.688 192 573.088 192 405.333333 192 228.597333 335.264 85.333333 512 85.333333c176.736 0 320 143.264 320 320 0 164.106667-123.52 299.349333-282.666667 317.845334V768H640a32 32 0 0 1 0 64h-90.666667v77.333333a32 32 0 0 1-64 0V832H384a32 32 0 0 1 0-64h101.333333zM512 661.333333c141.386667 0 256-114.613333 256-256S653.386667 149.333333 512 149.333333 256 263.946667 256 405.333333s114.613333 256 256 256z"
        ></path>
      </svg>
    ),
    []
  );

  const ManIcon = useCallback(
    (props: Partial<CustomIconComponentProps>) => <Icon component={ManIconSvg} {...props} />,
    [ManIconSvg]
  );

  const WomanIcon = useCallback(
    (props: Partial<CustomIconComponentProps>) => <Icon component={WomanIconSvg} {...props} />,
    [WomanIconSvg]
  );

  // 获取头像
  const onFetchEmployeeAvatar = useCallback(async account => {
    const { baseURL, url } = employeeApis.employeeAvatar;
    axios
      .get(`${baseURL}${url}`, {
        params: {
          type: 'SYSTEM',
          username: account,
        },
        headers: {
          Accept: 'application/octet-stream',
        },
        responseType: 'blob',
      })
      .then(res => {
        const blobData = res?.data;
        const newAvatarUrl = blobData ? URL.createObjectURL(blobData) : '';
        setAvatarUrl(newAvatarUrl);
      });
  }, []);

  useEffect(() => {
    empAccount && onFetchEmployeeAvatar(empAccount);
  }, [empAccount]);

  return (
    <Row key={data.empId} className="dhr-talent-tags-result-item" onClick={() => onJumpDetail(data)}>
      <Col>
        <div className="dhr-talent-tags-avatar-container">
          <div className="dhr-talent-tags-avatar">
            <Avatar size={64} shape="circle" src={avatarUrl}>
              {data.empName}
            </Avatar>
          </div>
        </div>
      </Col>
      <Col span={10}>
        <div className="dhr-talent-tag-emp-name">
          <span>{data.empName}</span>
          {data.empGender && (
            <span className="dhr-talent-tag-emp-icon-container">
              {data.empGender === '1' ? (
                <ManIcon className="dhr-talent-tag-item-gender-man" />
              ) : (
                <WomanIcon className="dhr-talent-tag-item-gender-woman" />
              )}
            </span>
          )}
        </div>
        <div className="dhr-talent-tag-emp-position-name">{data.positionName || '-'}</div>
        <div className="dhr-talent-tag-emp-work-labor-age">
          <span className="dhr-talent-tag-emp-work-age">
            <span>工作年限：</span>
            <span>{data.empWorkAge || '-'}</span>
          </span>
          <span>
            <span>司龄：</span>
            <span>{data.empLaborAge || '-'}</span>
          </span>
        </div>
        <div className="dhr-talent-tag-emp-dept-path-name">
          <span>{data.deptPathName}</span>
        </div>
      </Col>
      <Col span={11}>
        <div className="dhr-talent-tag-container">
          {(data.tagValueList || []).map(tagItem => {
            return (
              <Tag key={tagItem.tag} color={tagItem.color}>
                {tagItem.name}
              </Tag>
            );
          })}
        </div>
      </Col>
    </Row>
  );
};

export default TagEmpCard;
