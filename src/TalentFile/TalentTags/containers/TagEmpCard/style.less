.dhr-talent-tags-result-item {
  border-radius: 10px;
  cursor: pointer;
  margin-bottom: 16px;
  padding: 16px 24px;
  border: 1px solid #eee;
  transition: all 0.2s linear;
  &:hover {
    z-index: 3;
    box-shadow: 0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 4px 10px 4px #00000017;
    .dhr-talent-tag-emp-name {
      color: var(--antd-dynamic-primary-color);
    }
  }

  .dhr-talent-tags-avatar-container {
    width: 100px;
    .dhr-talent-tags-avatar {
      margin-top: 14px;
      position: relative;
      display: inline-block;
      &::after {
        content: '';
        position: absolute;
        top: -8%;
        left: -9%;
        height: 118%;
        width: 118%;
        border-radius: 50%;
        border: 2px solid var(--antd-dynamic-primary-color);
      }
      & > span {
        & > img {
          height: auto;
        }
      }
    }
  }

  .dhr-talent-tag-emp-name {
    color: #222;
    font-weight: 500;
    line-height: 22px;
    font-size: 16px;
    .dhr-talent-tag-emp-icon-container {
      & > .anticon {
        padding: 2px;
        margin-left: 4px;
        border-radius: 50%;
        vertical-align: baseline;
      }
      & > .dhr-talent-tag-item-gender-man {
        background-color: #1296db;
      }
      & > .dhr-talent-tag-item-gender-woman {
        background-color: #d4237a;
      }
    }
  }

  .dhr-talent-tag-emp-position-name {
    margin-top: 12px;
  }

  .dhr-talent-tag-emp-work-labor-age,
  .dhr-talent-tag-emp-dept-path-name {
    margin-top: 10px;
  }

  .dhr-talent-tag-emp-work-age {
    display: inline-block;
    margin-right: 10px;
  }

  .dhr-talent-tag-emp-dept-path-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .dhr-talent-tag-container {
    padding-left: 20px;
    padding-right: 20px;
    .ant-tag {
      padding: 2px 14px;
      border-radius: 4px;
      margin-bottom: 10px;
    }
  }
}
