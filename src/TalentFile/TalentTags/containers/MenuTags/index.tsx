import React, { useCallback, useEffect, useMemo } from 'react';
import type { MenuProps } from 'antd';
import classnames from 'classnames';
import { Menu, Badge } from 'antd';

import './style.less';

export interface IAppProps {
  categoryList: any[];
  selectedKeys: string[];
  selectCountMap: Record<string, any>;
  onSelectMenu: (params: any) => void;
}

const MenuTags: React.FC<IAppProps> = ({ categoryList, selectCountMap, selectedKeys, onSelectMenu }) => {
  const BadgeLabel = useCallback(({ label, count }) => {
    return (
      <Badge size="small" count={count}>
        <span className="dhr-talent-tag-menu-title-badge">{label}</span>
      </Badge>
    );
  }, []);

  const handleAssembleTreeList = useCallback(
    (data, idKey, labelKey, parentIdKey) => {
      // 组装树形结构
      const tree = [];
      // 创建映射表，将所有节点按 id 存储
      const idMap = new Map();
      data.forEach(item => {
        idMap.set(item[idKey], {
          ...item,
          key: item[idKey],
          onTitleClick: onSelectMenu,
          popupClassName: 'dhr-talent-category-sub-menu-container',
          label: <BadgeLabel label={item[labelKey]} count={selectCountMap[item[idKey]] || 0} />,
          children: [],
        });
      });
      data.forEach(item => {
        const node = idMap.get(item[idKey]);
        const parentId = item[parentIdKey];

        if (parentId) {
          // 如果有父级，将当前节点添加到父级的 children 列表中
          const parent = idMap.get(parentId);
          parent && parent.children.push(node);
        } else {
          tree.push(node);
        }
      });

      // 移除叶子节点的 children 属性
      const removeEmptyChildren = node => {
        if (node.children.length === 0) {
          delete node.children;
        } else {
          node.children.forEach(removeEmptyChildren);
        }
      };

      tree.forEach(removeEmptyChildren);
      return tree;
    },
    [selectCountMap]
  );

  const items: MenuProps['items'] = useMemo(() => {
    return handleAssembleTreeList(categoryList, 'id', 'name', 'parent');
  }, [categoryList, selectCountMap]);

  return (
    <div className="dhr-menu-tag-container">
      <Menu items={items} mode="horizontal" onClick={onSelectMenu} selectedKeys={selectedKeys} />
    </div>
  );
};

export default MenuTags;
