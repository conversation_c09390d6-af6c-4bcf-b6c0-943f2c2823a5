.dhr-menu-tag-container {
  margin: 10px 10px 20px 100px;
  .ant-menu-horizontal {
    &:not(.ant-menu-dark) {
      & > .ant-menu-item:hover,
      & > .ant-menu-item-selected,
      & > .ant-menu-submenu:hover,
      & > .ant-menu-submenu-active,
      & > .ant-menu-submenu-open,
      & > .ant-menu-submenu-selected {
        border-width: 0;
        color: #1890ff;
        .ant-badge {
          color: #1890ff;
        }
      }
    }
  }
}

.dhr-talent-category-sub-menu-container {
  .ant-menu-item:hover,
  .ant-menu-item-active,
  .ant-menu-submenu-active,
  .ant-menu-submenu-title:hover,
  .ant-menu-vertical .ant-menu-submenu-selected,
  .ant-menu-vertical-left .ant-menu-submenu-selected,
  .ant-menu-vertical-right .ant-menu-submenu-selected,
  .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open {
    .ant-badge {
      color: #1890ff;
    }
  }
  .ant-menu:not(.ant-menu-horizontal) {
    .ant-menu-item-selected {
      background-color: #e6f7ff;
      .ant-badge {
        color: #1890ff;
      }
    }
  }

  .ant-menu-vertical {
    .ant-menu-submenu-selected {
      .ant-menu-submenu-title {
        background-color: #e6f7ff;
      }
    }
  }
}

.ant-menu-light {
  .ant-menu-item:hover {
    .dhr-talent-tag-menu-title-badge {
      color: #1890ff;
    }
  }
}
.ant-menu:not(.ant-menu-horizontal) {
  .ant-menu-item-selected {
    .dhr-talent-tag-menu-title-badge {
      // color: #1890ff;
    }
  }
}
