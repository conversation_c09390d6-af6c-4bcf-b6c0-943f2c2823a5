import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Row, Col, Pagination, Tag, Spin, Button, Affix } from 'antd';
import axios from 'axios';

import talentApis from '@apis/talent';
import commomApis from '@apis/common';
import list2tree from '@cvte/list2tree';
import useDictCode from '@hooks/useDictCode';
import { IntlWrapper } from '@cvte/cir-lcp-sdk';
import { request as fetchApi } from '@utils/http';

import excelTask from '@components/ExportLargeExcel';
import { IFilterItem } from '@cvte/wuli-antd/src/WULIFilter';
import { WULIFormActions, WULIFilter } from '@cvte/wuli-antd';
import { DICT_CODE_MAP_ID } from '@constants/common';
import DropSelect from './containers/DropSelect';
import TagEmpCard from './containers/TagEmpCard';
import DropdownTree from './containers/DropdownTree';

import './style.less';

const filterFormKey = 'dhrTalentTagFilterFormKey';
const defaultLayout = {
  col: 6,
  labelCol: 7,
  wrapperCol: 16,
};

const defaultPageParams = {
  pageNum: 1,
  pageSize: 20,
};

interface TableDataType {
  list: any[];
  pagination: {
    pages: number;
    total: number;
    pageNum: number;
    pageSize: number;
  };
}

export interface IAppProps {
  systemHelper: {
    history: any;
    menu: {
      currentUrl: string;
    };
  };
}
const TalentTags: React.FC<IAppProps> = props => {
  const { systemHelper } = props;
  // 分类Id映射标签list
  const searchValuesRef = useRef({});
  const categoryMapTagRef = useRef({});
  const dropdownTreeRef = useRef(null);
  const cancelTokenSourceRef = useRef(null);

  const [tableData, setTableData] = useState<TableDataType>({
    list: [],
    pagination: { pages: 0, total: 0, pageNum: 0, pageSize: 0 },
  });

  const [loading, setLoading] = useState(false);

  const [searchValues, setSearchValues] = useState({});

  // 记录全部选择的tag个数
  const [tagIds, setTagIds] = useState([]);
  const [treeList, setTreeList] = useState([]);
  const [selectTagRows, setSelectTagRows] = useState([]);
  // 平铺的分类list
  const [categoryList, setCategoryList] = useState([]);

  const [empOriginOptions, setEmpOriginOptions] = useState([]);

  const { list = [], pagination } = tableData;

  const {
    DHR_STA_GENDER_TYPE = [],
    DHR_STA_MARITAL_STATUS = [],
    DHR_STA_POLITICAL_STATUS = [],
  } = useDictCode([
    DICT_CODE_MAP_ID.DHR_STA_GENDER_TYPE,
    DICT_CODE_MAP_ID.DHR_STA_MARITAL_STATUS,
    DICT_CODE_MAP_ID.DHR_STA_POLITICAL_STATUS,
  ]);

  const maritalStatusOptions = useMemo(() => {
    const list = (DHR_STA_MARITAL_STATUS || []).filter(({ itemValue }) => !['3', '9', '4'].includes(itemValue));
    return list;
  }, [DHR_STA_MARITAL_STATUS]);

  const formActions: IFilterItem[] = useMemo(
    () => [
      {
        type: 'input',
        key: 'tagValueName',
        label: '标签值',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
      {
        type: 'input',
        key: 'empName',
        label: '人员别名',
        configs: {
          allowClear: true,
          placeholder: '请填写',
        },
      },
    ],
    []
  );

  const onFetchTreeData = useCallback(() => {
    fetchApi({
      ...talentApis.tagCateList,
      onSuccess: res => {
        const { tagCateList, tagList } = res || {};
        const idMap = new Map();
        (tagList || []).forEach(tagItem => {
          if (idMap.has(tagItem.tagCate)) {
            const list = idMap.get(tagItem.tagCate);
            list.push(tagItem);
          } else {
            idMap.set(tagItem.tagCate, [tagItem]);
          }
        });
        const treeList = list2tree({
          idKey: 'id',
          parentIdKey: 'parent',
          newKey: {
            key: 'id',
            title: 'name',
          },
        })(tagCateList || []);

        const newCategoryIdMapTag = Object.fromEntries(idMap);
        setTreeList(treeList || []);
        setCategoryList(tagCateList || []);
        categoryMapTagRef.current = newCategoryIdMapTag;
      },
    });
  }, []);

  // 获取员工来源
  const onFetchEmployeeOrigin = useCallback(() => {
    const params = {
      pageSize: 500,
      onlyMain: true,
      keyType: 'CAMEL',
      appId: '96dd1d629bb24193bb8630c9dcb61aad',
      formClassId: 'ed81f25e22124c73a4061504ebcb37ac',
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const options = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.id,
          label: mainData?.cName,
          dataIndex: mainData?.id,
        }));
        setEmpOriginOptions(options);
      },
    });
  }, []);

  const onGetFormValues = () => {
    const formValues = WULIFormActions?.get(filterFormKey)?.getValue();
    const dropSearchValues = searchValuesRef.current;
    return {
      ...formValues,
      ...dropSearchValues,
    };
  };

  const handleSearch = useCallback(
    (params: any = {}) => {
      if (cancelTokenSourceRef.current) {
        cancelTokenSourceRef.current.cancel('取消上次请求');
        cancelTokenSourceRef.current = null;
      }
      setLoading(true);
      const newSearchValues = onGetFormValues();
      cancelTokenSourceRef.current = axios.CancelToken.source();
      fetchApi({
        ...talentApis.tagSearchEmpList,
        data: {
          ...defaultPageParams,
          tagValueIdList: tagIds,
          ...params,
          ...newSearchValues,
        },
        cancelToken: cancelTokenSourceRef.current.token,
      })
        .then(res => {
          cancelTokenSourceRef.current = null;
          setTableData(res || {});
        })
        .finally(() => setLoading(false));
    },
    [tagIds, list?.length]
  );

  useEffect(() => {
    handleSearch();
    onFetchTreeData();
    onFetchEmployeeOrigin();
  }, []);

  const handleJumpDetail = useCallback(record => {
    try {
      const jumpUrl = `/portal/b5cbqdyq/b5cbqdyq_talent_manage_tags_detail/detail?empId=${
        record?.empId || ''
      }&account=${record?.empAccount || ''}&pageName=人才档案详情-${record?.empName || ''}`;
      systemHelper.history.push(jumpUrl);
      console.log('跳转链接：', jumpUrl);
    } catch (error) {
      console.log('跳转失败:', error);
    }
  }, []);

  // 返回顶部
  const onBackTo = useCallback(() => {
    talentRef.current.scrollTo({
      top: 0,
      behavior: 'smooth', // 添加平滑滚动效果
    });
  }, []);

  // 切分页
  const handleChangePagination = useCallback(
    (pageNum, pageSize) => {
      handleSearch({ pageNum, pageSize });
      onBackTo();
    },
    [tagIds]
  );

  const handleSelecttagIds = useCallback(tagIds => {
    setTagIds(tagIds);
    handleSearch({ tagValueIdList: tagIds });
  }, []);

  const handleSelectTagRows = useCallback(newSelectTagRows => {
    setSelectTagRows(newSelectTagRows);
  }, []);

  const handleSearchValue = useCallback(
    (key, value) => {
      const newSearchValues = { ...searchValuesRef.current };
      if (newSearchValues[key] === value) {
        newSearchValues[key] = undefined;
      } else {
        newSearchValues[key] = value;
      }
      setSearchValues(newSearchValues);
      searchValuesRef.current = newSearchValues;
      handleSearch();
    },
    [tagIds]
  );

  const exportColumns = useMemo(
    () => [
      {
        title: '姓名',
        dataIndex: 'empName',
        key: 'empName',
      },
      {
        title: '域账号',
        dataIndex: 'empAccount',
        key: 'empAccount',
      },
      {
        title: '部门全路径',
        dataIndex: 'deptPathName',
        key: 'deptPathName',
        width: 200,
      },
      {
        title: '职位',
        dataIndex: 'positionName',
        key: 'positionName',
      },
      {
        title: '标签',
        dataIndex: 'tagValueList',
        key: 'tagValueList',
        alignment: {
          wrapText: true,
        },
        formatteFn: list => {
          const names = list.map(({ name }) => name).join('\n');
          return names;
        },
      },
    ],
    []
  );

  // 导出
  const handleExport = useCallback(() => {
    const newSearchValues = onGetFormValues();
    const params = {
      fetchParams: {
        ...talentApis.tagSearchEmpList,
        data: {
          tagValueIdList: tagIds,
          ...newSearchValues,
        },
      },
      xlsxName: '人才标签列表',
      configs: {
        columns: exportColumns,
        maxPageSize: 500,
      },
    };

    excelTask.add(params);
  }, []);

  const actions: any[] = useMemo(
    () => [
      {
        key: 'primary',
        type: 'custom',
        content: <Button onClick={handleExport}>导出</Button>,
      },
    ],
    []
  );

  const talentRef = useRef(null);
  const { pageNum, pageSize, total } = pagination;
  return (
    <div className="dhr-talent-tags-list-container" ref={talentRef}>
      <div>
        <div className="structureEmpSearch">
          <WULIFilter
            actions={actions}
            filters={formActions}
            formKey={filterFormKey}
            defaultLayout={defaultLayout}
            onSearch={() => handleSearch()}
            onClear={() => {
              WULIFormActions.get(filterFormKey).reset();
              handleSearch();
            }}
          />
        </div>
        <Row gutter={[12, 12]} className="dhr-talent-drop-select-list">
          <Col>
            <DropdownTree
              treeList={treeList}
              ref={dropdownTreeRef}
              selectTagIds={tagIds}
              categoryList={categoryList}
              selectTagRows={selectTagRows}
              onSelectTagIds={handleSelecttagIds}
              onSelectTagRows={handleSelectTagRows}
              tagMapList={categoryMapTagRef.current}
            />
          </Col>
          <Col>
            <DropSelect
              title="员工来源"
              options={empOriginOptions}
              selectKey={searchValues.source}
              onSelect={({ key }) => handleSearchValue('source', key)}
            />
          </Col>
          <Col>
            <DropSelect
              title="性别"
              optName="name"
              optKey="itemValue"
              options={DHR_STA_GENDER_TYPE}
              selectKey={searchValues.gender}
              onSelect={({ key }) => handleSearchValue('gender', key)}
            />
          </Col>
          <Col>
            <DropSelect
              title="婚姻状况"
              optName="name"
              optKey="itemValue"
              options={maritalStatusOptions}
              selectKey={searchValues.maritalStatus}
              onSelect={({ key }) => handleSearchValue('maritalStatus', key)}
            />
          </Col>
          <Col>
            <DropSelect
              title="政治面貌"
              optName="name"
              optKey="itemValue"
              options={DHR_STA_POLITICAL_STATUS}
              selectKey={searchValues.politicalStatus}
              onSelect={({ key }) => handleSearchValue('politicalStatus', key)}
            />
          </Col>
        </Row>
        {tagIds.length > 0 && (
          <Row className="dhr-talent-tags-action-container">
            <Col>
              <span className="dhr-talent-tags-action-tip"> 当前标签过滤条件:</span>
            </Col>
            <Col span={19}>
              {selectTagRows.map(tagRow => (
                <Tag
                  closable
                  color="processing"
                  key={tagRow.id}
                  onClose={() => dropdownTreeRef.current?.onDelSelectTag(tagRow, tagRow.treePathIds)}
                >
                  {tagRow.name}
                </Tag>
              ))}
              <Tag closable color="error" onClose={() => dropdownTreeRef.current?.onDelAllTags()}>
                清空所有标签
              </Tag>
            </Col>
          </Row>
        )}
        <Spin spinning={loading}>
          <Row className="dhr-talent-tags-search-content-container">
            <Col span={24}>
              <div className="dhr-talent-tags-result-container">
                {(list || []).map(listItem => {
                  return <TagEmpCard key={listItem.id} data={listItem} onJumpDetail={handleJumpDetail} />;
                })}
              </div>
            </Col>
          </Row>
          <div className="dhr-talent-pagination-container">
            <Pagination
              total={total}
              showSizeChanger
              current={pageNum}
              pageSize={pageSize}
              onChange={handleChangePagination}
              showTotal={total => `共有 ${total} 个结果`}
            />
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default IntlWrapper(TalentTags, { forwardRef: true });
