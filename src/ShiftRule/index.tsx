import { Form, Input, Select, Row, Col } from 'antd';
import React, { useMemo } from 'react';

import Block from '@components/Block';
import WholeTable from '@components/WholeTable/AgGrid';

import './style.less';

const { Item: FormItem } = Form;

const formLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const formItemLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 12 },
};

export interface IAppProps {}
const ShiftRule: React.FC<IAppProps> = props => {
  const rulesMap = useMemo(
    () => ({
      input: [{ required: true, message: '请填写' }],
      select: [{ required: true, message: '请选择' }],
    }),
    []
  );

  const columns = useMemo(
    () => [
      {
        title: '一',
        key: 'one',
        dataIndex: 'one',
      },
      {
        title: '二',
        key: 'two',
        dataIndex: 'two',
      },
      {
        title: '三',
        key: 'three',
        dataIndex: 'three',
      },
      {
        title: '四',
        key: 'four',
        dataIndex: 'four',
      },
      {
        title: '五',
        key: 'five',
        dataIndex: 'five',
      },
      {
        title: '六',
        key: 'six',
        dataIndex: 'six',
      },
      {
        title: '七',
        key: 'seven',
        dataIndex: 'seven',
      },
    ],
    []
  );
  return (
    <div className="shiftRuleContainer">
      <Form {...formLayout}>
        <Block title="基本信息">
          <Row>
            <Col span={8}>
              <FormItem name="name" label="轮班规则名称" rules={rulesMap.input}>
                <Input placeholder="请填写" />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem name="status" label="生效状态" rules={rulesMap.select}>
                <Select placeholder="请选择" options={[]} />
              </FormItem>
            </Col>
            <Col span={24}>
              <FormItem name="remark" label="描述" labelCol={{ span: 2 }} wrapperCol={{ span: 20 }}>
                <Input.TextArea placeholder="请填写" autoSize={{ minRows: 4, maxRows: 6 }} />
              </FormItem>
            </Col>
          </Row>
        </Block>
        <Block title="轮班设置" subTitle="(默认按周排班)">
          <WholeTable columns={columns} data={[]} />
        </Block>
        <Block title="节假日处理">
          <Row>
            <Col span={9}>
              <FormItem name="processMode" label="轮班遇到节假日时处理方式" rules={rulesMap.select} {...formItemLayout}>
                <Select placeholder="请选择" options={[]} />
              </FormItem>
            </Col>
            <Col span={9}>
              <FormItem
                name="shiftSkipPeriod"
                label="轮班跳过期间指定OFF班为"
                rules={rulesMap.select}
                {...formItemLayout}
              >
                <Select placeholder="请选择" options={[]} />
              </FormItem>
            </Col>
          </Row>
        </Block>
      </Form>
    </div>
  );
};

export default ShiftRule;
