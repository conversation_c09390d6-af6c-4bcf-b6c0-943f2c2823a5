import { Table, Row, Col, Divider, Ta<PERSON>, Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { Spin, WULIForm } from '@cvte/wuli-antd';
import queryString from 'query-string';

// import useFetch from '@hooks/useFetch';
import cnbApis from '@apis/cnb';
import Block from '@components/Block';
import { defaultObj } from '@utils/tools';
import { request } from '@utils/http';
// import useHistory from 'react-router-dom';

import {
  baseInfoFormItems,
  cnbGroupColumn,
  employeeItemsColumn,
  cnbArchivesStructureHistoryColumn,
  cnbArchivesGroupHistoryColumn,
} from './dataUnit';
import { IOriginalProps } from '../../types/index.d';
import './style.less';

export interface IAppProps extends IOriginalProps {}

const BASE_INFO_FORM_KEY = 'baseInBASE_INFO_FORM';

enum ENUM_PAGE_TYPES {
  LATEST = 'LATEST',
  GROUP_HISTORY = 'GROUP_HISTORY',
  STUCTURE_HISTORY = 'STUCTURE_HISTORY',
}

/** 页面参数
 * empId 员工 ID
 * tyep 详情类型 LATEST 最新详情 GROUP_HISTORY 历史详情 STUCTURE_HISTORY 薪资结构历史详情
 */

// const CUR_PAGE_TYPE = ENUM_PAGE_TYPES.LATEST;

const BaseInfo: React.FC<IAppProps> = props => {
  const [cnbArchivesDetail, setCnbArchivesDetail] = useState<Record<string, any>>({});
  const { systemHelper } = props;
  const {
    type = ENUM_PAGE_TYPES.LATEST,
    empId,
    ...restQueryParams
  } = queryString.parse(window.location.search) as {
    type: ENUM_PAGE_TYPES;
    empId?: string;
    string?: string;
  };
  const requestParams = {
    [ENUM_PAGE_TYPES.LATEST]: {
      ...cnbApis.cnbArchivesDetail,
    },
    [ENUM_PAGE_TYPES.GROUP_HISTORY]: {
      ...cnbApis.cnbArchivesGroupHistory,
    },
    [ENUM_PAGE_TYPES.STUCTURE_HISTORY]: {
      ...cnbApis.cnbArchivesStructureHistory,
    },
  };

  const runAction = () => {
    if (!empId) {
      return;
    }
    return request({
      ...requestParams[type],
      params: { empId },
      onSuccess: data => {
        setCnbArchivesDetail(data);
      },
      onError: err => {
        console.log('err', err);
      },
    });
  };

  useEffect(() => {
    runAction();
  }, [type, empId]);

  const handleJumpSub = (type: ENUM_PAGE_TYPES, pageName: string) => {
    systemHelper.history.push(
      `${window.location.pathname}?${queryString.stringify({
        ...restQueryParams,
        empId,
        type,
        pageFlag: type,
        pageName,
      })}`
    );
  };

  const { groupEmployees = [], employeeItems = [] } = defaultObj(cnbArchivesDetail);
  const hisInfo = [
    {
      key: '01',
      title: '人员薪资组变更历史信息',
      onClick: () =>
        handleJumpSub(
          ENUM_PAGE_TYPES.GROUP_HISTORY,
          `${cnbArchivesDetail.empOtherName || cnbArchivesDetail.empName || ''}-薪资组变更历史信息`
        ),
    },
    {
      key: '02',
      title: '人员固定薪资结构变更历史',
      onClick: () =>
        handleJumpSub(
          ENUM_PAGE_TYPES.STUCTURE_HISTORY,
          `${cnbArchivesDetail.empOtherName || cnbArchivesDetail.empName}-固定薪资结构变更历史`
        ),
    },
  ];
  const historyTabs = [
    {
      label: '历史信息',
      key: 'history',
      children: (
        <div>
          {hisInfo.map(k => (
            <div onClick={k.onClick} key={k.key} style={{ marginBottom: '10px', color: '#3858E6', cursor: 'pointer' }}>
              {k.title}
            </div>
          ))}
        </div>
      ),
    }, // 务必填写 key
  ];
  const isLatest = type === ENUM_PAGE_TYPES.LATEST;

  console.log('cnbArchivesDetail===', cnbArchivesDetail);

  return (
    <Row gutter={20}>
      <Col span={isLatest ? 19 : 24}>
        <Spin spinning={false}>
          <Block title="人员基本信息">
            <WULIForm
              className="cnbArchivesDetailBaseInfoForm"
              formItems={baseInfoFormItems}
              formKey={BASE_INFO_FORM_KEY}
              wuliMode="view"
              initialData={cnbArchivesDetail}
              defaultLayout={{
                col: 8,
                labelCol: 6,
                wrapperCol: 16,
              }}
            />
          </Block>
          {isLatest && (
            <>
              <Block title="人员薪资组信息">
                <Table columns={cnbGroupColumn} dataSource={groupEmployees} pagination={false} />
              </Block>
              <Block title="人员固定薪资结构信息">
                <Table columns={employeeItemsColumn} dataSource={employeeItems} pagination={false} />
              </Block>
            </>
          )}
          {type === ENUM_PAGE_TYPES.GROUP_HISTORY && (
            <Block title="人员薪资组变更历史信息">
              <Table columns={cnbArchivesGroupHistoryColumn} dataSource={groupEmployees} pagination={false} />
            </Block>
          )}
          {type === ENUM_PAGE_TYPES.STUCTURE_HISTORY && (
            <Block title="人员固定薪资结构变动历史">
              <Table columns={cnbArchivesStructureHistoryColumn} dataSource={employeeItems} pagination={false} />
            </Block>
          )}
        </Spin>
      </Col>
      {isLatest && (
        <Col span={5}>
          <Divider type="vertical" />
          <Tabs items={historyTabs} />
        </Col>
      )}
    </Row>
  );
};

export default BaseInfo;
