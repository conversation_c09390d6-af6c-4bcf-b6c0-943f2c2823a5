import React from 'react';
import { IFormItem } from '@cvte/wuli-antd/src/WULIForm';
import { ellipsisTag, toDateFormat } from '@utils/tools';

/** 人员基本信息  */
export const baseInfoFormItems: IFormItem = [
  {
    type: 'custom',
    key: 'empOtherName',
    label: '别名',
  },
  {
    type: 'custom',
    key: 'empCode',
    label: '工号',
  },
  {
    type: 'custom',
    key: 'empDeptFullPathName',
    label: '部门',
    render: data => ellipsisTag(data.value, 10),
  },
  {
    type: 'custom',
    key: 'standard_position',
    label: '标准岗位',
  },
  {
    type: 'custom',
    key: 'empTypeName',
    label: '用工关系类型',
  },
  {
    type: 'custom',
    key: 'empStatusName',
    label: '任职状态',
  },
  {
    type: 'custom',
    key: 'empModeName',
    label: '任职方式',
  },
  {
    type: 'custom',
    key: 'groupLaborDate',
    label: '集团入职时间',
    render: data => {
      const groupLaborDate = data.value;
      return toDateFormat(groupLaborDate) || '-';
    },
  },
  {
    type: 'custom',
    key: 'beginDate',
    label: '入职时间',
    render: data => {
      const beginDate = data.value;
      return toDateFormat(beginDate) || '-';
    },
  },
  {
    type: 'custom',
    key: 'empResidentStatusName',
    label: '居民身份',
  },
  {
    type: 'custom',
    key: 'empLaborName',
    label: '法人公司',
  },
].map(k => ({
  ...k,
  render:
    k.render ||
    (data => {
      return <div>{data.value || '-'}</div>;
    }),
}));

/** 人员薪资组成信息 */
export const cnbGroupColumn = [
  {
    title: '薪资组',
    dataIndex: 'groupName',
  },
  {
    title: '发薪单位',
    dataIndex: 'payUnitName',
  },
  {
    title: '开始时间',
    dataIndex: 'beginDate',
    render: text => toDateFormat(text) || '-',
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    render: text => toDateFormat(text) || '-',
  },
];

/** 人员固定薪资结构信息  */

export const employeeItemsColumn = [
  {
    title: '薪资项目',
    dataIndex: 'itemName',
  },
  {
    title: '值',
    dataIndex: 'value',
  },
  {
    title: '生效时间',
    dataIndex: 'effectiveDate',
    render: date => toDateFormat(date) || '-',
  },
  {
    title: '调薪原因',
    dataIndex: 'reasonName',
  },
  {
    title: '调薪详细原因',
    dataIndex: 'remark',
  },
];

/** 薪酬结构变动历史 */

export const cnbArchivesStructureHistoryColumn = [
  {
    title: '薪酬项目',
    dataIndex: 'itemName',
  },
  {
    title: '关联薪资结构类型',
    dataIndex: 'structureName',
  },
  {
    title: '值',
    dataIndex: 'value',
  },
  {
    title: '生效时间',
    dataIndex: 'effectiveDate',
    render: date => toDateFormat(date) || '-',
  },
  {
    title: '调薪原因',
    dataIndex: 'reasonName',
  },
  {
    title: '调薪详细原因',
    dataIndex: 'remark',
  },
];

/** 员工薪资组变动历史 */

export const cnbArchivesGroupHistoryColumn = [
  {
    title: '薪资组',
    dataIndex: 'groupName',
  },
  {
    title: '发薪单位',
    dataIndex: 'payUnitName',
  },
  {
    title: '开始时间',
    dataIndex: 'beginDate',
    render: date => toDateFormat(date) || '-',
  },
  {
    title: '结束时间',
    dataIndex: 'endDate',
    render: date => toDateFormat(date) || '-',
  },
];
