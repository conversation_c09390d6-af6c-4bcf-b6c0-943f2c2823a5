import React, { useEffect } from 'react';
import { Spin } from 'antd';

import cnbApis from '@apis/cnb';
import useFetch from '@hooks/useFetch';
import { defaultObj } from '@utils/tools';

import TreeSelect from '@components/TreeSelect';
import WULIWholeTable, { DEFAULT_PAGE_SIZE, WULIFormActions } from '@components/WholeTable/AgGrid';

import './style.less';
import '../../styles/atom.less';
import { IOriginalProps } from '../../types/index.d';

const filterFormKey = 'archivesListFormKey';
export interface IAppProps extends IOriginalProps {
  systemHelper: any;
}

const ArchivesList: React.FC<IAppProps> = ({ systemHelper, ...restProps }) => {
  // const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [deptHid, setDeptHid] = React.useState<string>(undefined);
  console.log('restPropsrestProps', restProps);

  /** 请求过程 */
  const {
    Data: cnbArchives,
    Loading: cnbArchivesLoading,
    runAction: runActionOfCnbArchivesList,
  } = useFetch({
    ...cnbApis.cnbArchivesList,
    // params: {},
  });
  const { list: cnbArchivesList = [], pagination = {} } = defaultObj(cnbArchives);
  const handleJumpToNoticeProgress = (data: Record<string, any>) =>
    systemHelper.history.push(
      `/portal/7lr0obbt/7lr0obbt_uqwoobxs_t850iwoc_detail/detail?empId=${data.empId}&pageName=${
        data.empOtherName || data.empName
      }`
    );
  // systemHelper.history.push(
  //   `/7lr0obbt/cmPage?${queryString.stringify({
  //     pageId: data.id,
  //     pageFlag: data.id,
  //     resourceVersion: 'latest',
  //     exposeName: 'CnbArchivesDetail',
  //     resourceName: 'dhr-lcp-custom-comp',
  //     closeOnSave: '0',
  //   })}`
  // );

  const columns = [
    {
      title: '别名',
      dataIndex: 'empOtherName',
      key: 'empOtherName',
      render: ({ data, value }) => (
        <span className="text-primary pointer" onClick={() => handleJumpToNoticeProgress(data)}>
          {value}
        </span>
      ),
    },
    {
      title: '域账号',
      key: 'empAccount',
      dataIndex: 'empAccount',
    },
    {
      title: '工号',
      dataIndex: 'empCode',
      key: 'empCode',
    },
    {
      title: '法人公司',
      dataIndex: 'empLaborName',
      key: 'empLaborName',
    },
    {
      title: '部门',
      dataIndex: 'empDeptFullPathName',
      key: 'empDeptFullPathName',
    },
    // {
    //   title: '标准职位',
    //   dataIndex: 'month',
    //   key: 'month',
    // },
    {
      title: '用工关系类型',
      dataIndex: 'empTypeName',
      key: 'empTypeName',
    },
    {
      title: '任职状态',
      dataIndex: 'empStatusName',
      key: 'empStatusName',
      // render: ({ data, value }) => <span onClick={() => handleJumpToNoticeProgress(data)}>{value}</span>,
    },
  ];

  const filterFormItems = [
    {
      type: 'input',
      key: 'empOtherName',
      label: '人员别名',
      configs: {
        placeholder: '请输入人员别名',
      },
      col: 8,
    },
  ];

  useEffect(() => {
    deptHid && onFetchCnbArchivesList();
  }, [deptHid]);

  const onFetchCnbArchivesList = (data = {}) => {
    const formData = WULIFormActions.get(filterFormKey)?.getValue();
    runActionOfCnbArchivesList({
      params: {
        deptHid,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...formData,
        ...data,
      },
    });
  };

  return (
    <Spin spinning={cnbArchivesLoading}>
      <div className="flex">
        <div
          style={{
            width: '300px',
          }}
        >
          <TreeSelect
            onSelectNode={data => {
              setDeptHid(data[0]);
            }}
          />
        </div>
        <div className="archivesList">
          <WULIWholeTable
            pagination
            rowKey="empId"
            columns={columns}
            data={cnbArchivesList}
            filter={{
              formKey: filterFormKey,
              filters: filterFormItems,
              onSearch: () => onFetchCnbArchivesList(),
              defaultLayout: {
                col: 6,
                labelCol: 6,
                wrapperCol: 18,
              },
              className: 'mb-10',
              onClear: () => {
                WULIFormActions.get(filterFormKey)?.reset();
                onFetchCnbArchivesList();
              },
            }}
            paginationConfig={{
              size: 'small',
              ...pagination,
              current: pagination?.pageNum || 1,
              showSizeChanger: true,
              showQuickJumper: true,
              onChange: (pageNum, pageSize) => {
                onFetchCnbArchivesList({
                  pageNum,
                  pageSize,
                });
              },
            }}
          />
        </div>
      </div>
    </Spin>
  );
};
export default ArchivesList;
