import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Tabs, Dropdown, Space } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';

import tmgApis from '@apis/tmg';
import { request as fetchApi } from '@utils/http';

import commomApis from '@apis/common';
import TaskSlot from '@components/TaskSlot';
import useDictCode from '@hooks/useDictCode';
import EmpTableItem from './containers/EmpTableItem';
import RecordTableItem from './containers/RecordTableItem';
import { LCPDetailTemplate } from '@components/LcpTemplate';

import { DICT_CODE_MAP_ID } from '@constants/common';

import './style.less';

let timeout = null;
export interface IAppProps {
  processId: string;
}
const AttendanceArchive: React.FC<IAppProps> = props => {
  const empComponentRef = useRef(null);

  const { processId } = props;

  const taskRef = useRef(null);
  const templateRef = useRef(null);
  const [empSelectRows, setEmpSelectRows] = useState<any[]>([]);
  const [allDynamicColums, setAllDynamicColums] = useState<any[]>([]);
  const [replayLoading, setReplayLoading] = useState<boolean>(false);

  const dict = useDictCode([DICT_CODE_MAP_ID.DHR_TMG_CALCULATE_CAL_STATUS]);

  const formTemplateConfig = useMemo(
    () => ({
      processId,
      pageId: processId,
      pageFlag: processId,
      appId: '05f5f32003cc41f896b334e526e59de9',
      classId: '417540f373fd4eb0acafb7cc83ba473b',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      metaConfig: {
        contextData: {
          viewType: 'read',
        },
      },
    }),
    [processId]
  );

  const handleEmpSearch = useCallback(() => {
    empComponentRef.current?.onSearch();
  }, [empComponentRef.current]);

  const tabItems = useMemo(
    () => [
      {
        label: '人员明细',
        key: '1',
        children: (
          <EmpTableItem
            dict={dict}
            processId={processId}
            ref={empComponentRef}
            selectRows={empSelectRows}
            onSelectRows={values => setEmpSelectRows(values)}
          />
        ),
      },
      {
        label: '汇总信息',
        key: '2',
        children: <RecordTableItem processId={processId} itemList={allDynamicColums} />,
      },
    ],
    [processId, dict, allDynamicColums.length, empSelectRows.length]
  );

  // 重新计算
  const handleReplay = useCallback(
    (params = {}) => {
      const selectRows = [...empSelectRows];
      const empIds = [...selectRows].map(({ empId }) => empId);
      setReplayLoading(true);
      fetchApi({
        ...tmgApis.attendanceSealProcessAgain,
        data: {
          processId,
          empIds: empIds.length > 0 ? empIds : undefined,
          ...params,
        },
        onSuccess: res => {
          // 清除所有选项
          empComponentRef.current?.onClearAllSelect([...selectRows]);
          taskRef.current?.onReplayTask?.();
        },
      }).finally(() => setReplayLoading(false));
    },
    [processId, empSelectRows]
  );

  // 刷新资源组件的数据
  const onRereshTemplateData = useCallback(() => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
    timeout = setTimeout(() => {
      templateRef.current?.GlobalContext?.pageTools?.refresh();
      clearTimeout(timeout);
      timeout = null;
    }, 500);
  }, []);

  const handleTaskEnd = useCallback(() => {
    handleEmpSearch();
    onRereshTemplateData();
  }, [processId]);

  const items = useMemo(() => {
    const selectRows = [...empSelectRows];
    return [
      {
        label: '重新执行全部',
        key: 'all',
        disabled: replayLoading,
      },
      {
        label: '重新执行已选',
        key: 'selected',
        disabled: selectRows.length === 0 || replayLoading,
      },
      {
        label: '重新执行失败数据',
        key: 'error',
        disabled: replayLoading,
      },
    ];
  }, [processId, replayLoading, empSelectRows]);

  // 获取列的数据
  const onFetchAttendanceItem = useCallback(() => {
    const params = {
      appId: 'e088c2dc19864a748d9c81bdbb6ee5f2',
      formClassId: '90d1e95d7c3d4a3ab3cb92abc50a04dd',
      onlyMain: true,
      keyType: 'CAMEL',
      pageSize: 500,
      mainParamsGroups: [
        {
          paramsList: [
            {
              operator: 'in',
              attrApi: 'C_ITEM_TYPE',
              value: ['DETAIL', 'ALL'],
            },
            {
              operator: '=',
              attrApi: 'C_STATUS',
              value: '1',
            },
            {
              operator: '=',
              attrApi: 'C_NEED_SEAL',
              value: '1',
            },
          ],
        },
      ],
    };

    fetchApi({
      ...commomApis.formTableList,
      data: {
        ...params,
      },
      headers: {
        'x-app-id': params.appId,
      },
      onSuccess: res => {
        const list = res?.content || [];
        const allDynamicColums = list.map(({ mainData }) => ({
          ...mainData,
          key: mainData?.id,
          title: mainData?.cName,
          dataIndex: mainData?.id,
        }));
        setAllDynamicColums(allDynamicColums);
      },
    });
  }, []);

  useEffect(() => {
    onFetchAttendanceItem();
  }, []);

  const handleMenuClick: MenuProps['onClick'] = useCallback(
    e => {
      const key = e.key;
      const fnMap = {
        all: () => handleReplay({ empIds: undefined }),
        selected: () => handleReplay(),
        error: () => handleReplay({ status: 'FAIL' }),
      };
      fnMap[key]?.();
    },
    [processId, replayLoading, empSelectRows]
  );

  const menuProps = useMemo(
    () => ({
      items,
      onClick: handleMenuClick,
    }),
    [processId, replayLoading, empSelectRows]
  );

  return (
    <TaskSlot processId={processId} onOk={handleTaskEnd} ref={taskRef}>
      <div className="dhr-quota-calculation-container">
        <div className="dhr-quota-calculation-btn-action">
          <Dropdown menu={menuProps}>
            <Button type="primary" loading={replayLoading}>
              <Space>
                重新执行
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </div>
        <LCPDetailTemplate {...formTemplateConfig} ref={templateRef} />
        <Tabs items={tabItems} />
      </div>
    </TaskSlot>
  );
};

export default AttendanceArchive;
