import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useRef, useState } from 'react';

import { Spin } from 'antd';

import tmgApis from '@apis/tmg';
import { request as fetchApi } from '@utils/http';
import { exportMultiExcel, IExport } from '@utils/excel';

import excelTask from '@components/ExportLargeExcel';
import WULIWholeTable, { DEFAULT_PAGE_SIZE } from '@components/WholeTable/AgGrid';

const tableRowKey = 'id';

export interface IAppProps {
  dict: any;
  ref?: any;
  selectRows: any[];
  processId: string;
  onSelectRows: (selectedRows: any[]) => void;
}
const EmpTableItem: React.FC<IAppProps> = forwardRef((props, ref) => {
  const { dict, processId, selectRows, onSelectRows } = props;

  const tableRef = useRef(null);
  const filterParamsRef = useRef({});
  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any>({
    list: [],
    pagination: {
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });
  console.log('我打印啦');

  useImperativeHandle(ref, () => ({
    getSelectRows: () => selectRows,
    onSearch: () => handleSearch(),
    onClearAllSelect: list => {
      tableRef.current?.setSelected(
        list.map(selectItem => ({
          rowKey: selectItem[tableRowKey],
          isSelected: false,
        }))
      );
    },
  }));

  const { list: tableList = [], pagination = {} } = tableData;

  const { DHR_TMG_CALCULATE_CAL_STATUS = [] } = dict || {};

  const statusOptions = useMemo(
    () =>
      (DHR_TMG_CALCULATE_CAL_STATUS || []).map(codeItem => ({
        label: codeItem.name,
        value: codeItem.itemValue,
        ...codeItem,
      })),
    [DHR_TMG_CALCULATE_CAL_STATUS]
  );

  const columns = useMemo(
    () => [
      {
        key: 'statusName',
        title: '封存状态',
        dataIndex: 'statusName',
        filterKey: 'status',
        filterType: 'select',
        headerComponentParams: {
          options: statusOptions,
        },
      },
      {
        key: 'empName',
        title: '姓名',
        dataIndex: 'empName',
        filterType: 'input',
      },
      {
        key: 'empAccount',
        title: '域账号',
        dataIndex: 'empAccount',
      },
      {
        title: '异常原因',
        dataIndex: ' errorMsg',
        key: 'errorMsg',
        render: record => {
          const errorMsg = record?.data?.errorMsg;
          return errorMsg || '-';
        },
      },
    ],
    [statusOptions]
  );

  const handleExport = useCallback(() => {
    if (selectRows.length > 0) {
      const exportArr: IExport[] = [
        {
          columns,
          data: selectRows,
          sheetName: 'Sheet1',
        },
      ];
      return exportMultiExcel(exportArr, '人员明细列表.xlsx');
    }
    const filterParams = filterParamsRef.current || {};
    const params = {
      fetchParams: {
        ...tmgApis.attendanceSealProcessEmpList,
        params: {
          processId,
          ...filterParams,
        },
      },
      xlsxName: '人员明细列表',
      configs: {
        columns,
        maxPageSize: 500,
      },
    };
    excelTask.add(params);
  }, [selectRows, processId]);

  const actionBtnItems = useMemo(
    () => [
      {
        key: 'export',
        content: '导出',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleExport,
          disabled: tableList.length === 0,
        },
      },
    ],
    [selectRows, processId, tableList.length]
  );

  const onFetchQuotaProcess = useCallback(params => {
    setLoading(true);
    fetchApi({
      ...tmgApis.attendanceSealProcessEmpList,
      params,
      onSuccess: res => {
        setTableData(res || {});
      },
    }).finally(() => setLoading(false));
  }, []);

  const handleSearch = useCallback(
    (params = {}) => {
      const filterParams = filterParamsRef.current || {};
      onFetchQuotaProcess({
        processId,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...filterParams,
        ...params,
      });
    },
    [processId]
  );

  const handleFilterSearch = values => {
    // onSelectRows([]);
    filterParamsRef.current = values;
    handleSearch(values);
  };

  const paginationConfig = useMemo(
    () => ({
      showSizeChanger: true,
      showQuickJumper: true,
      total: pagination.total,
      current: pagination.pageNum || 1,
      pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
      onChange: (pageNum, pageSize) => handleSearch({ pageNum, pageSize }),
    }),
    [pagination, processId]
  );

  return (
    <Spin spinning={loading}>
      <WULIWholeTable
        canSelect
        pagination
        multiSelect
        height={500}
        ref={tableRef}
        data={tableList}
        columns={columns}
        showSelectedDetail
        rowNameKey="empName"
        rowKey={tableRowKey}
        action={actionBtnItems}
        selectedRows={selectRows}
        onHeaderChange={handleFilterSearch}
        paginationConfig={paginationConfig}
        onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => onSelectRows(_selectedRows)}
      />
    </Spin>
  );
});

export default EmpTableItem;
