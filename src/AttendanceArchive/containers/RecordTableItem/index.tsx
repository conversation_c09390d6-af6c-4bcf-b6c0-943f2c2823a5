import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Spin } from 'antd';

import tmgApis from '@apis/tmg';
import useRowToCols from '@hooks/useRowToCols';
import { request as fetchApi } from '@utils/http';
import { defaultObj, toDateFormat } from '@utils/tools';
import { exportMultiExcel, IExport } from '@utils/excel';

import excelTask from '@components/ExportLargeExcel';
import DisplayItemSettingModal from '@components/DisplayItemSettingModal';
import WULIWholeTable, { DEFAULT_PAGE_SIZE } from '@components/WholeTable/AgGrid';

export interface IAppProps {
  ref?: any;
  processId: string;
  itemList: any[];
}
const RecordTableItem: React.FC<IAppProps> = props => {
  const { processId, itemList } = props;

  const filterParamsRef = useRef({});
  const [loading, setLoading] = useState<boolean>(false);
  const [selectRows, setSelectRows] = useState<any[]>([]);
  const [dispalyItemSettingModalVisible, setDispalyItemSettingModalVisible] = useState(false);
  const [tableData, setTableData] = useState<any>({
    list: [],
    pagination: {
      pageNum: 1,
      pageSize: DEFAULT_PAGE_SIZE,
    },
  });

  useEffect(() => {
    processId && handleSearch();
  }, [processId]);

  const { onSetDisplayItem, assignList, unAssignList } = useRowToCols({
    itemList,
    rowKey: 'id',
    rowNameKey: 'cName',
    defaultShowKeyValue: '1',
    sortKey: 'cSummaryDisplayOrder',
    defaultShowKey: 'cSummaryDefDisplay',
    assignListStorageNamespace: 'attendanceArchiveDetailIds',
    unAssignListStorageNamespace: 'attendanceUnArchiveDetailIds',
    onConfirm: () => {},
  });

  const { pagination = {} } = tableData;

  const tableList = useMemo(() => {
    const { list } = defaultObj(tableData);
    const newList = (list || []).map(listItem => ({
      ...listItem,
      rowId: `${listItem.empId}${listItem.periodDate}`,
    }));
    return newList;
  }, [tableData]);

  // 导出Columns
  const exportColumns: any[] = useMemo(() => {
    const assembleColumns = assignList.map(columnItem => ({
      ...columnItem,
      render: (text, record) => {
        const itemView = record?.itemView || {};
        const value = itemView?.[columnItem.dataIndex]?.value;
        const result = typeof value === 'number' ? value : value || '-';
        return result;
      },
      formatteFn: (text, record) => {
        const itemView = record?.itemView || {};
        const value = itemView?.[columnItem.dataIndex]?.value;
        const result = typeof value === 'number' ? value : value || '-';
        return result;
      },
    }));
    return [
      {
        title: '别名',
        dataIndex: 'empName',
        key: 'empName',
      },
      {
        title: '域账号',
        dataIndex: 'empAccount',
        key: 'empAccount',
      },
      {
        title: '员工类型',
        key: 'empTypeName',
        dataIndex: 'empTypeName',
      },
      {
        title: '工号',
        dataIndex: 'empCode',
        key: 'empCode',
      },
      {
        title: '考勤期间',
        dataIndex: 'periodDate',
        key: 'periodDate',
        render: text => toDateFormat(text),
        formatteFn: text => toDateFormat(text),
      },
      ...assembleColumns,
    ];
  }, [assignList]);

  const columns = useMemo(() => {
    const assembleColumns = assignList.map(columnItem => ({
      ...columnItem,
      render: record => {
        const itemView = record?.data?.itemView || {};
        const value = itemView?.[columnItem.dataIndex]?.value;
        const result = typeof value === 'number' ? value : value || '-';
        return result;
      },
    }));
    return [
      {
        key: 'empName',
        title: '姓名',
        dataIndex: 'empName',
        filterType: 'input',
      },
      {
        key: 'empAccount',
        title: '域账号',
        dataIndex: 'empAccount',
      },
      {
        key: 'empCode',
        title: '员工编号',
        dataIndex: 'empCode',
      },
      {
        key: 'empTypeName',
        title: '员工类型',
        dataIndex: 'empTypeName',
      },
      {
        key: 'periodDate',
        title: '考勤期间',
        dataIndex: 'periodDate',
        render: record => {
          const periodDate = record?.data?.periodDate;
          return toDateFormat(periodDate);
        },
      },
      ...assembleColumns,
    ];
  }, [assignList]);

  const handleExport = useCallback(() => {
    if (selectRows.length > 0) {
      const exportArr: IExport[] = [
        {
          columns: exportColumns,
          data: selectRows,
          sheetName: 'Sheet1',
        },
      ];
      return exportMultiExcel(exportArr, '汇总信息列表.xlsx');
    }
    const filterParams = filterParamsRef.current || {};
    const params = {
      fetchParams: {
        ...tmgApis.attendanceSealProcessRecordList,
        params: {
          processId,
          ...filterParams,
        },
      },
      xlsxName: '汇总信息列表',
      configs: {
        columns: exportColumns,
        maxPageSize: 500,
      },
    };
    excelTask.add(params);
  }, [selectRows, processId, exportColumns]);

  const actionBtnItems = useMemo(
    () => [
      {
        key: 'displaySettings',
        content: '项目设置显示',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: () => setDispalyItemSettingModalVisible(true),
        },
      },
      {
        key: 'export',
        content: '导出',
        type: 'button',
        config: {
          type: 'primary',
          loading: false,
          onClick: handleExport,
          disabled: tableList.length === 0,
        },
      },
    ],
    [selectRows, exportColumns, processId, tableList.length]
  );

  const onFetchQuotaProcess = useCallback(params => {
    setLoading(true);
    fetchApi({
      ...tmgApis.attendanceSealProcessRecordList,
      params,
      onSuccess: res => {
        setTableData(res || {});
      },
    }).finally(() => setLoading(false));
  }, []);

  const handleSearch = useCallback(
    (params = {}) => {
      const filterParams = filterParamsRef.current || {};
      onFetchQuotaProcess({
        processId,
        pageNum: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        ...filterParams,
        ...params,
      });
    },
    [processId]
  );

  const handleFilterSearch = values => {
    setSelectRows([]);
    filterParamsRef.current = values;
    handleSearch(values);
  };

  const paginationConfig = useMemo(
    () => ({
      showSizeChanger: true,
      showQuickJumper: true,
      total: pagination.total,
      current: pagination.pageNum || 1,
      pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
      onChange: (pageNum, pageSize) => handleSearch({ pageNum, pageSize }),
    }),
    [pagination, processId]
  );

  const handleOk = useCallback((_assignList, _unAssignList) => {
    onSetDisplayItem(_assignList, _unAssignList);
    setDispalyItemSettingModalVisible(false);
  }, []);

  return (
    <Spin spinning={loading}>
      <WULIWholeTable
        canSelect
        pagination
        multiSelect
        height={500}
        rowKey="rowId"
        data={tableList}
        columns={columns}
        action={actionBtnItems}
        onHeaderChange={handleFilterSearch}
        paginationConfig={paginationConfig}
        onSelect={(selectedRow, isSelect, selectKeys, _selectedRows) => setSelectRows(_selectedRows)}
      />
      <DisplayItemSettingModal
        rowKey="id"
        rowNameKey="cName"
        title="考勤项目显示设置"
        onConfirm={handleOk}
        assignList={assignList}
        unAssignList={unAssignList}
        open={dispalyItemSettingModalVisible}
        onCancel={() => setDispalyItemSettingModalVisible(false)}
      />
    </Spin>
  );
};

export default RecordTableItem;
