import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo } from 'react';
import type { UploadProps } from 'antd';
import { Form, Upload } from 'antd';

import useAttachment, { ApiConfigParams } from '@hooks/useAttachment';

import './style.less';

const { Dragger } = Upload;
const { Item: FormItem } = Form;

interface IFormItem {
  label?: string;
  name: string;
  type: string;
  header?: string;
  footer?: string;
  uploadConfigs: UploadProps;
  configs: Record<string, any>;
}

export interface IAppProps {
  fields: IFormItem[];
  configs: Record<string, any>;
}
const UserInfoFileUpdateModal: React.FC<IAppProps> = forwardRef((props, ref) => {
  const [form] = Form.useForm();
  const { fields, configs } = props;
  const apiCongiMap = (configs?.context?.apiConfigMap || {}) as ApiConfigParams;
  const { onUpdateApiCongiMap, onPreview, onUploadAttachment } = useAttachment({ manual: true });

  useImperativeHandle(ref, () => ({
    onValidateFields: () =>
      new Promise((resolve, reject) => {
        form
          .validateFields()
          .then(values => {
            resolve(values);
          })
          .catch(error => {
            reject(error);
          });
      }),
  }));

  useEffect(() => {
    apiCongiMap && onUpdateApiCongiMap(apiCongiMap);
  }, [apiCongiMap]);

  /** 自定义上传 */
  const handleCustomRequest = useCallback(
    async ({ file, onSuccess, onError, onProgress }) => {
      try {
        const resp = await onUploadAttachment(
          file,
          { configs },
          {
            onUploadProgress: progressEvent => {
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              onProgress({ percent });
            },
          }
        );
        onSuccess({
          data: {
            result: resp,
          },
          status: '0',
          message: 'success',
        });
      } catch (error) {
        onError(error);
      }
    },
    [configs]
  );

  // 下载附件url prefix
  const downUrlPrefix = useMemo(() => {
    if (apiCongiMap.download) {
      return apiCongiMap.download?.url;
    }
    return '';
  }, [apiCongiMap.download]);

  const handleChange = useCallback(
    async info => {
      if (info.file.status !== 'uploading') {
        const fileIds = [];
        info.fileList.map(file => {
          if (file.response) {
            if (file.response.status !== '0') file.status = 'error';
            const fileId = file.response?.data?.result?.fileIds?.[0];
            file.fileId = fileId;
            if (downUrlPrefix) {
              file.url = `${downUrlPrefix}/${fileId}`;
            }
          }
          file.fileId && fileIds.push(file.fileId);
          return file;
        });
      }
    },
    [downUrlPrefix]
  );

  // 预览附件
  const handlePreview = useCallback(
    file => {
      onPreview({
        fileId: file?.fileId,
        fileName: file.name,
        fileType: file.type,
      });
    },
    [apiCongiMap]
  );

  const uploadProps = useMemo(
    () => ({
      name: 'files',
      onChange: handleChange,
      onPreview: handlePreview,
      customRequest: handleCustomRequest,
      data: {
        categoryId: `/dhr/common/${apiCongiMap?.upload?.params?.categoryId || ''}`,
      },
    }),
    [apiCongiMap, configs]
  );

  const normFile = useCallback((e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  }, []);

  return (
    <div className="dhr-user-info-file-update-modal-content">
      <Form form={form} layout="vertical">
        {fields.map(field => (
          <div key={field.name} style={{ marginBottom: 10 }}>
            {field.header && <span>{field.header}</span>}
            <FormItem
              noStyle
              name={field.name}
              label={field.label}
              valuePropName="fileList"
              getValueFromEvent={normFile}
              rules={[{ required: true, message: '请上传附件' }]}
              {...(field.configs || {})}
            >
              <Dragger {...uploadProps} {...(field.uploadConfigs || {})}>
                <div className="dhr-user-info-file-import-upload-container">
                  <span>拖动文件到此或</span>
                  <span className="dhr-user-info-file-browse-upload-text">浏览</span>
                </div>
              </Dragger>
            </FormItem>
            {field.footer && <span>{field.footer}</span>}
          </div>
        ))}
      </Form>
    </div>
  );
});

export default UserInfoFileUpdateModal;
