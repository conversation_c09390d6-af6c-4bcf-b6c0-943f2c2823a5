import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Table, Pagination, Button } from 'antd';
import queryString from 'query-string';
import Big from 'big.js';

import cnbApis from '@apis/cnb';
import { IntlWrapper } from '@cvte/cir-lcp-sdk';
import { request as fetchApi } from '@utils/http';
import { IFilterItem } from '@cvte/wuli-antd/src/WULIFilter';
import { WULIFormActions, WULIFilter } from '@cvte/wuli-antd';

import excelTask from '@components/ExportLargeExcel';

import { exportMultiExcel, IExport } from '@utils/excel';

import './style.less';

const filterFormKey = 'entranceFlowFormKey';
const defaultLayout = {
  col: 6,
  labelCol: 7,
  wrapperCol: 16,
};

interface SystemHelperType {
  history: {
    push: (url: string) => void;
  };
  menu: {
    currentUrl: string;
    pageUrl: string;
  };
}
export interface IAppProps {
  systemHelper: SystemHelperType;
}
const StructureEmpList: React.FC<IAppProps> = props => {
  const { systemHelper } = props;
  const routerParams = props.systemHelper?.menu;
  const { currentUrl, pageUrl } = routerParams || {};
  const detailUrl = (currentUrl || '').replace(pageUrl, '/detail');
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<{
    list: any[];
    pagination: {
      total: number;
      pageNum: number;
      pageSize: number;
    };
  }>({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 20,
    },
  });

  const formActions: IFilterItem[] = [
    {
      type: 'input',
      key: 'empOtherName',
      label: '人员别名',
      configs: {
        placeholder: '请填写',
      },
    },
    {
      type: 'input',
      key: 'empTypeName',
      label: '用工关系类型',
      configs: {
        placeholder: '请填写',
      },
    },
    {
      type: 'input',
      key: 'empStatusName',
      label: '任职状态',
      configs: {
        placeholder: '请填写',
      },
    },
  ];

  const handleJumpDetail = values => {
    systemHelper.history.push(
      `${detailUrl}?${queryString.stringify({
        pageId: values.id,
        pageFlag: values.id,
      })}`
    );
  };

  const columns = useMemo(
    () => [
      {
        width: 150,
        title: '别名',
        key: 'empOtherName',
        dataIndex: 'empOtherName',
        onCell: record => ({ rowSpan: record.rowSpan }),
        render: (text, values) => {
          return (
            <span className="dhr-table-cell-link" onClick={() => handleJumpDetail(values)}>
              {text}
            </span>
          );
        },
      },
      {
        title: '域账号',
        key: 'empAccount',
        dataIndex: 'empAccount',
        onCell: record => ({ rowSpan: record.rowSpan }),
      },
      {
        width: 150,
        title: '工号',
        key: 'empCode',
        dataIndex: 'empCode',
        onCell: record => ({ rowSpan: record.rowSpan }),
      },
      {
        title: '部门',
        ellipsis: true,
        key: 'empDeptFullPathName',
        dataIndex: 'empDeptFullPathName',
        onCell: record => ({ rowSpan: record.rowSpan }),
      },
      {
        ellipsis: true,
        title: '当前薪资组',
        key: 'groupName',
        dataIndex: 'groupName',
        onCell: record => ({ rowSpan: record.rowSpan }),
      },
      {
        width: 200,
        ellipsis: true,
        title: '当前固定薪资结构类型',
        key: 'structureName',
        dataIndex: 'structureName',
        onCell: record => ({ rowSpan: record.rowSpan }),
      },
      {
        // width: 130,
        title: '用工关系类型',
        key: 'empTypeName',
        dataIndex: 'empTypeName',
        onCell: record => ({ rowSpan: record.rowSpan }),
      },
      {
        // width: 150,
        title: '任职状态',
        key: 'empStatusName',
        dataIndex: 'empStatusName',
        onCell: record => ({ rowSpan: record.rowSpan }),
        render: text => text || '-',
      },
      {
        width: 150,
        title: '薪资项目',
        dataIndex: 'itemName',
        key: 'itemName',
      },
      {
        width: 130,
        title: '金额',
        key: 'value',
        dataIndex: 'value',
      },
    ],
    []
  );

  // 处理合并数据
  const handleAssembletableList = useCallback(list => {
    if (!Array.isArray(list)) {
      return [];
    }
    const assembleTableList = [];
    list.forEach(listItem => {
      const { employeeItems } = listItem;
      // 如果为空 则直接push
      if ((employeeItems || []).length === 0) {
        return assembleTableList.push({ ...listItem });
      }

      if ((employeeItems || []).length === 1) {
        const detail = employeeItems.pop() || {};
        return assembleTableList.push({
          ...listItem,
          itemId: detail.itemId,
          itemName: detail.itemName,
          value: detail.value,
        });
      }
      const newListItem = {
        ...listItem,
        employeeItems: undefined,
      };
      // 初始化总额
      let bigTotal = new Big(0);

      employeeItems.forEach((employeeItem, index) => {
        // 加
        bigTotal = bigTotal.plus(employeeItem.value || 0);
        assembleTableList.push({
          ...newListItem,
          value: employeeItem.value,
          itemId: employeeItem.itemId,
          itemName: employeeItem.itemName,
          // + 1 多一个总额
          rowSpan: index === 0 ? employeeItems.length + 1 : 0,
        });
      });
      // 塞入总额
      assembleTableList.push({
        ...newListItem,
        value: Number(bigTotal.toString() || 0),
        itemId: undefined,
        itemName: '总额',
        // + 1 有一个总额
        rowSpan: 0,
      });
    });

    return assembleTableList;
  }, []);

  const onFetchTableData = (params = {}) => {
    setLoading(true);
    fetchApi({
      ...cnbApis.structureEmpList,
      params,
      onSuccess: res => {
        const list = res.list || [];
        const result = handleAssembletableList(list);
        setLoading(false);
        setTableData({
          ...(res || {}),
          list: result,
        });
      },
      onError: err => {
        console.log('err===', err);
        setLoading(false);
      },
    });
  };

  const handleSearch = (params = {}) => {
    const formValues = WULIFormActions.get(filterFormKey).getValue();
    onFetchTableData({
      pageNum: 1,
      pageSize: 50,
      ...params,
      ...formValues,
    });
  };

  useEffect(() => {
    handleSearch();
  }, []);

  const handleChangeTable = useCallback(newSelectedRowKeys => setSelectedRowKeys(newSelectedRowKeys), []);

  // 合并checkout
  const handleRenderCell = useCallback(
    (checked, record, index, originNode) => {
      if (record.rowSpan === 0) {
        return {
          props: { rowSpan: 0, hidden: true },
        };
      }
      if (record.rowSpan) {
        return {
          children: originNode,
          props: { rowSpan: record.rowSpan },
        };
      }
      return originNode;
    },
    [tableData]
  );

  // 导出
  const handleExport = () => {
    if (selectedRowKeys.length > 0) {
      const selectIds = selectedRowKeys.map(selectItem => JSON.parse(selectItem)?.id);
      const { list } = tableData;
      const filterList = list.filter(({ id }) => selectIds.includes(id));
      const exportArr: IExport[] = [
        {
          columns,
          data: filterList,
          sheetName: 'Sheet1',
        },
      ];
      return exportMultiExcel(exportArr, '人员固定薪资结构管理列表.xlsx');
    }

    const formValues = WULIFormActions.get(filterFormKey).getValue();
    const params = {
      fetchParams: {
        ...cnbApis.structureEmpList,
        params: {
          ...formValues,
        },
      },
      xlsxName: '人员固定薪资结构管理列表',
      configs: {
        columns,
        extraHandle: handleAssembletableList,
      },
    };
    excelTask.add(params);
  };

  const { list: dataSource, pagination } = tableData;
  console.log('props===', props);

  return (
    <div className="structureEmpList">
      <div className="structureEmpSearch">
        <WULIFilter
          formKey={filterFormKey}
          filters={formActions}
          defaultLayout={defaultLayout}
          onSearch={() => handleSearch()}
          onClear={() => {
            WULIFormActions.get(filterFormKey).reset();
            handleSearch();
          }}
        />
      </div>
      <section className="structureEmpContent">
        <div className="structureEmpTableAction">
          <div className="tableAction">
            <Button type="primary" onClick={handleExport}>
              导出
            </Button>
          </div>
        </div>
        <div className="structureEmpTableContainer">
          <Table
            size="small"
            columns={columns}
            loading={loading}
            pagination={false}
            rowKey={record => JSON.stringify(record)}
            rowSelection={{
              selectedRowKeys,
              renderCell: handleRenderCell,
              onChange: handleChangeTable,
            }}
            dataSource={dataSource}
            scroll={{ y: 'calc(100vh - 362px)' }}
          />
        </div>
        <div className="structureEmpPagination">
          <Pagination
            showSizeChanger
            {...pagination}
            current={pagination.pageNum}
            showTotal={total => `共 ${total} 条`}
            onChange={(pageNum, pageSize) => handleSearch({ pageNum, pageSize })}
          />
        </div>
      </section>
    </div>
  );
};

export default IntlWrapper(StructureEmpList, { forwardRef: true });
