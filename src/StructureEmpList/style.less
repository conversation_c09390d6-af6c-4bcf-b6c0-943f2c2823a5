.structureEmpList {
  padding: 0;
  height: 100%;
  .structureEmpSearch {
    height: 86px;
    border-bottom: 1px solid rgb(232, 232, 232);
    padding: var(--dynamic-primary-spacing-normal, 20px);
    .wuli-filter {
      position: relative;
      .actions {
        top: 6px;
        right: 16px;
        position: absolute;
      }
    }
  }
  .structureEmpContent {
    height: calc(100% - 86px);
    padding: var(--dynamic-primary-spacing-normal, 20px);
    .structureEmpTableAction {
      padding-bottom: 10px;
      .tableAction {
        height: 45px;
        border-radius: 6px;
        position: relative;
        display: inline-block;
        padding: 10px 10px 10px 20px;
        background-color: var(--antd-dynamic-primary-underly-color-1, rgba(56, 88, 230, 0.04));
        &::before {
          content: '';
          left: 0;
          width: 2px;
          top: 12px;
          bottom: 12px;
          position: absolute;
          background-color: var(--antd-dynamic-primary-color, #3858e6);
        }
      }
    }
    .structureEmpTableContainer {
      overflow: hidden;
      height: calc(100% - 40px - 45px);
      .ant-table-container {
        .dhr-table-cell-link {
          cursor: pointer;
          color: var(--antd-dynamic-primary-color);
        }
        .ant-table-tbody {
          tr {
            &.ant-table-row-selected {
              & > td {
                background-color: var(--ag-header-background-color, #f8f8f8);
              }
              &:hover {
                td {
                  background-color: var(--ag-header-background-color, #f8f8f8);
                }
              }
            }
          }
        }
        // .ant-table-content {
        //   .ant-table-thead {
        //     height: 32px;
        //     min-height: 32px;
        //     tr {
        //       & > th {
        //         padding: 0 4px;
        //         line-height: 24px;
        //         background-color: var(--ag-header-background-color, #f8f8f8);
        //       }
        //     }
        //   }
        // }
      }
    }

    .structureEmpPagination {
      padding-top: 8px;
      height: 40px;
      text-align: right;
    }
  }
}
