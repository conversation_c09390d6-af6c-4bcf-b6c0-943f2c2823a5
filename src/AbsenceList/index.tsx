import React, { forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import { List, Button, InfiniteScroll, DotLoading } from 'antd-mobile';
import { FilterOutline } from 'antd-mobile-icons';
import { Cache as LcpCache } from '@cvte/cir-lcp-sdk';
import moment from 'moment';
import { request } from '@utils/http';
import commomApis from '@apis/common';
import tmgApis from '@apis/tmg';
import { ABSENCE_CATEGORY } from '@constants/absence';
import { DICT_CODE_MAP_ID } from '@constants/common';
import useDictCode from '@hooks/useDictCode';
import { formListDataTransform } from '@utils/tz';

import { IOriginalProps } from '../types';
import Filter from './container/Filter';

import './style.less';
import '../styles/atom.less';
import { roundToOneDecimals } from '@utils/tools';

const createLink = `//${location.hostname}/portal/y51o6js7/cmPage?classId=695ee45fbcc84a0093180f490aca79d5&appId=6712ca8886ad4cd28d13d6e0c04fb7ca&pageName=%E8%AF%B7%E5%81%87%E6%B5%81%E7%A8%8B&pageFlag=695ee45fbcc84a0093180f490aca79d5&resourceName=lcp-data-object&exposeName=ObjectLinkView&tenantId=undefined&closeOnSave=0&__sourceTargetCode=VIEW_AREA&__sourceScopeFlag=v9cp5mwz_20240520091045800%3A1i2gee15-1294-4a41-b0b7-6b00705h409i&__sourceSubject=v9cp5mwz_20240520091045800&metaConfig=%7B%22contextData%22%3A%7B%7D%7D`;

const DEFAULT_PAGE_SIZE = 20;
const BASE_PAGINATION = {
  pageNum: 0,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

export interface IAppProps extends IOriginalProps {}
const AbsenceList: React.FC<IAppProps> = forwardRef((props, ref) => {
  const [absenceList, setAbsenceList] = useState<Record<string, any>[]>([]);
  const [absenceCategoryList, setAbsenceCategoryList] = useState<IObject[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const hasMoreRef = useRef(true);
  const [filterVisible, setFilterVisible] = useState(false);
  // const [pageCache, setPageCache] = useState({});
  const [pagination, setPagination] = useState({
    ...BASE_PAGINATION,
  });

  const [serachValues, setSerachValues] = useState<{
    flowStatus: string[];
    categary: string[];
  }>({ flowStatus: [], categary: [] });
  const userInfo = window['form-common-context']?.get('form-common-context-user').value || {};
  console.log('userInfo======', userInfo);
  console.log('hasMore', hasMoreRef.current);
  const { DHR_TMG_LEAVE_FLOW_BIZ_STATUS = [] } = useDictCode([DICT_CODE_MAP_ID.DHR_TMG_LEAVE_FLOW_BIZ_STATUS]);

  const fetchAbsenceCategaryList = async () => {
    const _absenceCategaryList = await request({
      ...tmgApis.absenceCategaryList,
      params: {
        empId: userInfo.id,
        queryDate: Date.now(),
      },
    });
    Array.isArray(_absenceCategaryList) && setAbsenceCategoryList(_absenceCategaryList);
  };

  useEffect(() => {
    fetchAbsenceCategaryList();
  }, []);

  // 获取列的数据
  const loadMore = async (isFilter?: boolean) => {
    if (!hasMoreRef.current) {
      return true;
    }

    const _pagination = isFilter ? { ...BASE_PAGINATION } : pagination;
    const advConditions = [];

    if (Array.isArray(serachValues.flowStatus) && serachValues.flowStatus.length > 0) {
      advConditions.push({
        $or: serachValues.flowStatus?.map(status => ({
          $eq: {
            $formatType: '',
            $val: status,
            $var: '85a2b30f21134645a22f1e2c563d1470', // C_FLOW_BIZ_STATUS ID
            $varType: 'SELECT',
            valueType: 'value',
          },
        })),
      });
    }

    if (Array.isArray(serachValues.categary) && serachValues.categary.length > 0) {
      advConditions.push({
        $or: serachValues.categary?.map(status => ({
          $contains: {
            $formatType: '',
            $val: status,
            $var: 'd97a8d7f5af943a58f6e63b8f2c8640d', // C_FLOW_BIZ_STATUS ID
            $varType: 'TEXT',
            valueType: 'value',
          },
        })),
      });
      // mainParamsGroups.push({
      //   paramsList: serachValues.categary?.map(categaryItem => ({
      //     operator: 'like',
      //     attrApi: 'C_CONTAIN_LEAVE_TYPE',
      //     value: categaryItem,
      //     andOr: 'or',
      //   })),
      // });
      // cContainLeaveType
    }

    console.log('advConditions', advConditions);

    const params = {
      viewDatasourceId: '695ee45fbcc84a0093180f490aca79d5',
      viewId: 'd3c1cf635f214c01b562adbc19971017',
      isAsyncPage: '0',
      isCustomerView: '0',
      advConditions: { $and: advConditions },
      appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
    };
    const res = await request({
      ...commomApis.tzFormDataList,
      data: {
        ...params,
        pageSize: _pagination.pageSize,
        pageNum: _pagination.pageNum + 1,
      },
      headers: {
        'x-app-id': params.appId,
      },
    }).catch(e => {
      hasMoreRef.current = false;
      setHasMore(false);
    });

    const _absenceList = formListDataTransform(res.list, res.columnList);
    setAbsenceList(isFilter ? [..._absenceList] : [...absenceList, ..._absenceList]);
    if (res?.total) {
      const { pageNum, pageSize, total } = res;
      setPagination({
        pageNum,
        pageSize,
        total,
      });
      hasMoreRef.current = !(pageNum * pageSize >= total);
      setHasMore(!(pageNum * pageSize >= total));
    } else {
      hasMoreRef.current = false;
      setHasMore(false);
    }
    isFilter && setFilterVisible(false);
  };

  useEffect(() => {
    console.log('触发了这里');
    loadMore(true);
  }, [serachValues]);
  const genTitle = (containLeaveType: string) => {
    return containLeaveType
      ?.split(',')
      ?.map((leaveType: string) => ABSENCE_CATEGORY[leaveType])
      .filter(k => k)
      .join('、');
  };

  const handleViewDetail = (absenceDetail: Record<string, any>) => {
    const parmas = {
      exposeName: 'LCPDetailTemplate',
      resourceName: 'tz-render',
      apiName: 'TB_TMG_LEAVE_FLOW',
      appId: '6712ca8886ad4cd28d13d6e0c04fb7ca',
      tenantId: 'c1e09cc9-a0ba-4487-a2a0-eae7d99e74b3',
      classId: '695ee45fbcc84a0093180f490aca79d5',
      pageId: absenceDetail.objId,
      pageName: absenceDetail.C_WF_NAME,
    };
    window.location.href = `/portal/y51o6js7/cmPage?${new URLSearchParams(parmas).toString()}`;
  };

  const handleJumpCreetaPage = () => {
    window.location.href = createLink;
  };

  return (
    <div className="mAbsenceList">
      <div className="section-block flex justify-between items-center">
        <span className="text-14" onClick={() => setFilterVisible(true)}>
          <FilterOutline className="mr-6" />
          <span>筛选</span>
        </span>
        <Button color="primary" size="small" onClick={handleJumpCreetaPage}>
          + 新增请假
        </Button>
      </div>
      <div className="section-block mt-10 px-0">
        <List>
          {absenceList.map(k => (
            <List.Item key={k.objId} onClick={() => handleViewDetail(k)}>
              <div className="flex justify-between items-center text-14">
                <div className="text-left">
                  <span>
                    {genTitle(k.C_CONTAIN_LEAVE_TYPE)} - {k.C_EMP_NAME}
                  </span>
                  <div className="text-12">创建时间：{moment(k.CRT_TIME).format('YYYY-MM-DD HH:mm')}</div>
                </div>
                <div className="text-right">
                  <span>共 {roundToOneDecimals(k.C_JUDGE_EXCEPT_TOTAL_DURATION)} 天</span>
                  <div className="text-12 text-primary">{k.CURRENT_NODE_NAME}</div>
                </div>
              </div>
            </List.Item>
          ))}
        </List>
        <InfiniteScroll
          loadMore={async () => {
            await loadMore();
          }}
          hasMore={hasMore}
        >
          <>
            {hasMore ? (
              <>
                <span>Loading</span>
                <DotLoading />
              </>
            ) : (
              <span>--- 我是有底线的 ---</span>
            )}
          </>
        </InfiniteScroll>
        {/* <InfiniteScroll loadMore={() => {}} hasMore={hasMoreRef.current} /> */}
      </div>
      <Filter
        visible={filterVisible}
        title="筛选"
        flowStatusList={DHR_TMG_LEAVE_FLOW_BIZ_STATUS}
        absenceCategoryList={absenceCategoryList}
        onCancel={() => setFilterVisible(false)}
        // onReset={() => (entity[entity.curFilterType] = [])}
        onOk={_values => {
          hasMoreRef.current = true;
          setHasMore(true);
          setSerachValues(_values || {});
          // // setHasMore(true);
          // console.log('触发一次');
          // hasMoreRef.current = true;
          // setTimeout(() => {
          //   loadMore(serachValues, true);
          // }, 0);
        }}
      />
    </div>
  );
});

export default AbsenceList;
