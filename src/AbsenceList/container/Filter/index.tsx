import React, { useMemo } from 'react';
import { Popup, SearchBar, Form, Selector, DatePicker } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import { PopoverProps } from 'antd-mobile/es/components/popover';
import classnames from 'classnames';

// import { List as VirtualizedList, AutoSizer } from 'react-virtualized/dist/es';
// import BottomAction from '@components/BottomAction';
import './style.less';

export interface IAppProps extends PopoverProps {
  visible: boolean;
  title: string;
  absenceCategoryList: IObject[];
  flowStatusList: IObject[];
  // filterType: FilterType;
  onCancel?: () => void;
  onOk?: (values: IObject) => void;
  // onReset?: () => void;
  dict: IObject[];
}
const FilterPopup: React.FC<IAppProps> = ({ visible, onCancel, onOk, title, absenceCategoryList, flowStatusList }) => {
  const [form] = Form.useForm();
  const _flowStatusList = useMemo(
    () => flowStatusList.map(k => ({ ...k, label: k.name, value: k.itemValue })),
    [flowStatusList]
  );

  const _absenceCategoryList = useMemo(
    () => absenceCategoryList.map(k => ({ ...k, label: k.name, value: k.code })),
    [absenceCategoryList]
  );

  const handleConfirm = () => {
    const values = form.getFieldsValue();
    onOk && onOk(values);
  };

  // const selectIds = R.defaultTo([], selectPositions).map(selectedItem => selectedItem.itemValue);
  return (
    <Popup visible={visible} className="absenceListFilter" bodyStyle={{ height: '80vh', paddingBottom: '100px' }}>
      <div className="mb-20 relative box-border">
        <p className="text-center text-black text-18 text-bold mb-10 mt-10">{title}</p>
        <span className="absolute inline-block top-0 right-4 text-16" onClick={() => onCancel && onCancel()}>
          <CloseOutline />
        </span>
      </div>
      <Form name="form" form={form}>
        <Form.Item name="flowStatus" label="流程状态">
          <Selector columns={3} multiple options={_flowStatusList} />
        </Form.Item>
        <Form.Item name="categary" label="假种">
          <Selector columns={3} multiple options={_absenceCategoryList} />
        </Form.Item>
        {/* <Form.Item name="status" label="申请时间">
          <DatePicker />
        </Form.Item> */}
      </Form>
      <div className="absenceListFilterBottom">
        <div className={classnames('actionButton', 'actionButtonPrimary')} onClick={handleConfirm}>
          确认
          {/* {action.loading ? <DotLoading /> : action.content} */}
        </div>
        <div
          className={classnames('actionButton', 'actionButtonOutline')}
          onClick={() => {
            form.resetFields();
            onOk && onOk({});
          }}
        >
          重置
        </div>
      </div>
    </Popup>
  );
};

export default FilterPopup;
