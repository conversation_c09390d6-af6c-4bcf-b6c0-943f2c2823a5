.absenceListFilter {
  position: relative;
  .searchBar {
    width: 90%;
  }
  .shadowTop {
    box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
  }
  .adm-form {
    padding-bottom: 100px;
    overflow-y: scroll;
  }
  .absenceListFilterBottom {
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    width: 100%;
    height: 64px;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    z-index: 99;
    .actionButton {
      width: 50%;
      height: 48px;
      text-align: center;
      line-height: 48px;
      border-radius: 4px;
      box-sizing: border-box;
      margin: 0 2px;
      font-size: 16px;
    }
    .actionButtonPrimary {
      background-color: var(--adm-color-primary);
      color: #fff;
    }
    .actionButtonOutline {
      background-color: #fff;
      color: var(--adm-color-primary);
      border: 1px solid var(--adm-color-primary);
    }
  }
}
