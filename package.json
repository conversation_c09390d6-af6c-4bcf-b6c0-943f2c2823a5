{"name": "@cvte/standard-resource", "version": "1.0.0", "main": "index.js", "scripts": {"doc": "typedoc --options typedoc.json", "clean:dist": "rm -rf dist && mkdir dist", "test": "jest --config jest.config.js", "dist": "npm run doc && npm run clean:dist && cp package.json dist && tsc --outDir dist/ && copyfiles -u 1 \"src/**/*.d.ts\" \"src/**/*.less\" dist/src", "build": "release-it && npm run dist", "build:patch": "release-it -i patch && npm run dist", "build:minor": "release-it -i minor && npm run dist", "build:major": "release-it -i major && npm run dist", "demo": "parcel demo/index.html -d demo/dist", "demo:dist": "npm run dist && parcel demo/index.dist.html -d demo/dist", "rc": "node --max_old_space_size=10240 /usr/local/lib/node_modules/@cvte/resource-center-cli/bin/rc", "rc:dev": "LOCAL=true node --max_old_space_size=8172 /usr/local/lib/node_modules/@cvte/resource-center-cli/bin/rc", "new:tpl": "node shortcut.js"}, "publishConfig": {"registry": "http://artifactory.gz.cvte.cn/artifactory/api/npm/cvte-npm-registry"}, "repository": {"type": "git"}, "keywords": ["cvte"], "author": "resource-center", "license": "ISC", "devDependencies": {"@ant-design/icons": "4.7.0", "@cvte/cir-page-generator-resource-types": "^2.0.3-huangyunzhen-20231218105538-rc", "@cvte/kylin-tools": "^1.1.17", "@cvte/wuli-antd": "^3.0.14", "@release-it/conventional-changelog": "3.3.0", "@types/jest": "27.0.1", "@types/react-router-dom": "5.1.0", "@typescript-eslint/eslint-plugin": "4.30.0", "@typescript-eslint/parser": "4.30.0", "ag-grid-community": "28.2.1", "ag-grid-react": "28.2.1", "antd": "4.24.8", "antd-mobile": "5.24.0", "antd-mobile-icons": "0.3.0", "big.js": "6.2.1", "copyfiles": "^2.4.1", "cz-conventional-changelog": "2.1.0", "eslint": "7.32.0", "eslint-config-airbnb": "18.2.1", "eslint-config-prettier": "8.3.0", "eslint-import-resolver-typescript": "2.4.0", "eslint-plugin-import": "2.24.2", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-react": "7.25.2", "eslint-plugin-react-hooks": "4.2.0", "inquirer": "8.0.0", "jest": "27.1.1", "jest-html-reporter": "3.4.1", "js-cookie": "3.0.5", "parcel": "1.12.3", "prettier": "2.4.1", "react": "17.0.2", "react-dom": "17.0.2", "release-it": "14.11.5", "ts-jest": "27.0.5", "typedoc": "0.21.9", "typedoc-plugin-markdown": "3.10.4", "typescript": "4.6.4", "uuid": "8.3.2"}, "dependencies": {"@cvte/cir-core-net": "^1.0.5", "@cvte/cir-lcp-sdk": "2.1.21-7", "@cvte/list2tree": "^0.0.5", "@cvte/resource-center-sdk": "^1.7.34", "@cvte/wuli-ag-grid": "^4.0.9", "@cvte/xioo-ag-table": "^1.14.7", "@uiw/react-amap-api-loader": "7.1.3", "@uiw/react-amap-circle": "7.1.3", "@uiw/react-amap-map": "7.1.3", "@uiw/react-amap-marker": "7.1.3", "@uiw/react-amap-scale-control": "7.1.3", "@uiw/react-amap-tool-bar-control": "7.1.3", "@vis-tree/react": "^0.1.7", "acorn": "^8.11.3", "acorn-walk": "^8.3.2", "axios": "^1.6.3", "classnames": "^2.5.1", "codemirror": "5.5.0", "dayjs": "^1.11.10", "escodegen": "^2.1.0", "estree-parent": "^0.3.1", "exceljs": "4.4.0", "html2pdf.js": "^0.10.3", "moment-timezone": "^0.5.46", "nunjucks": "^3.2.4", "path-to-regexp": "^6.2.1", "pdf-lib": "1.17.1", "query-string": "^8.1.0", "quill": "2.0.0-dev.4", "quill-better-table": "1.2.10", "quill-image-uploader": "^1.3.0", "quill2-image-uploader": "^1.0.4", "react-codemirror2": "^7.3.0", "react-image-crop": "^11.0.7", "react-qr-code": "^2.0.15", "react-quill-v2.0": "2.0.0", "react-router-dom": "5.0.0"}, "remoteDependencies": {}, "resourceCenter": {"name": "dhr-lcp-custom-comp", "group": "dhr", "type": "js", "version": "1.0.3", "author": "柯剑烽"}}