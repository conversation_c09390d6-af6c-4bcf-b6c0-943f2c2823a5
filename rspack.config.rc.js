const path = require('path');

async function override(config) {
  const __config = { ...config };
  __config.devtool = 'source-map';
  __config.resolve.alias = {
    ...(__config.resolve.alias || {}),
    '@utils': path.resolve(__dirname, './src/utils'),
    '@components': path.resolve(__dirname, './src/components'),
    '@constants': path.resolve(__dirname, './src/constants'),
    '@hooks': path.resolve(__dirname, './src/hooks'),
    '@types': path.resolve(__dirname, './src/types'),
    '@apis': path.resolve(__dirname, './src/apis'),
    '@src': path.resolve(__dirname, './src'),
  };
  if (process.env.LOCAL) {
    // 关闭代码压缩，方便调试
    __config.optimization.minimize = false;
  }
  return __config;
}

module.exports = override;
