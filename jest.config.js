/** @type {import('ts-jest/dist/types').InitialOptionsTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  verbose: false,
  testRegex: '(/__test__/cases/.*|(\\.|/))(test|spec).(ts|tsx)?$',
  collectCoverage: true,
  coverageDirectory: '<rootDir>/__test__/coverage',
  reporters: [
    'default',
    [
      '<rootDir>/node_modules/jest-html-reporter',
      {
        // 输出页面标题
        pageTitle: 'Test Report',
        // 插件将会输出的HTML报告的路径。
        outputPath: '<rootDir>/__test__/report/JesttestReport.html',
        // 为每个失败的测试输出详细的失败消息。
        includeFailureMsg: true,
      },
    ],
  ],
};
