{"compilerOptions": {"baseUrl": ".", "module": "ES2022", "target": "ES6", "noImplicitAny": false, "sourceMap": false, "jsx": "react", "experimentalDecorators": true, "declaration": true, "lib": ["scripthost", "dom", "es6", "ES2015", "ES2017"], "skipLibCheck": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "paths": {"@utils/*": ["src/utils/*"], "@components/*": ["src/components/*"], "@constants/*": ["src/constants/*"], "@hooks/*": ["src/hooks/*"], "@types/*": ["src/types/*"], "@apis/*": ["src/apis/*"], "@src/*": ["src/*"]}}, "exclude": ["./node_modules", "./__test__", "dist"]}