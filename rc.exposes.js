const configs = {
  shared: {
    react: { isSingleton: true, versions: '17.0.2' },
    'react-dom': { isSingleton: true, version: '17.0.2' },
    antd: { isSingleton: true, version: '4.24.8' },
  },
  SalaryFormulaEditor: {
    file: './src/SalaryFormulaEditor/index.tsx',
    name: '算薪公式编辑器',
    desc: 'dhr薪酬算薪公式编辑器',
  },
  SalaryFormulaEditorConfigs: {
    file: './src/SalaryFormulaEditor/exportConfig.ts',
    name: '算薪公式编辑器配置',
    desc: 'dhr薪酬算薪公式编辑器配置',
  },
  MoveBtnInDetailList: {
    file: './src/MoveBtnInDetailList/index.tsx',
    name: '上下移动按钮',
    desc: '明细表表项上下移动按钮',
  },
  MoveBtnInDetailListConfigs: {
    file: './src/MoveBtnInDetailList/exportConfig.ts',
    name: '上下移动按钮配置',
    desc: '明细表表项上下移动按钮配置',
  },
  TreeSelectComp: {
    file: './src/components/TreeSelect/index.tsx',
    name: '树形选择',
    desc: '树形选择',
  },
  TreeSelectCompConfigs: {
    file: './src/components/TreeSelect/exportConfig.ts',
    name: '树形选择配置',
    desc: '树形选择配置',
  },
  ArchivesList: {
    file: './src/CnbArchives/ArchivesList',
    name: '薪酬档案列表',
    desc: '薪酬档案列表',
  },
  CnbArchivesDetail: {
    file: './src/CnbArchives/ArchivesDetail',
    name: '薪酬档案详情组件',
    desc: '薪酬档案详情组件',
  },
  PaySlipTemplate: {
    file: './src/PaySlipTemplate/index.tsx',
    name: '工资条模板',
    desc: '工资条模板',
  },
  CnbPayProcess: {
    file: './src/CnbPayProcess/index.tsx',
    name: '薪酬发放流程',
    desc: '薪酬发放流程',
  },
  PayrollDetail: {
    file: './src/PayrollDetail/index.tsx',
    name: '薪资组管理详情',
    desc: '薪资组管理详情',
  },
  StructureEmpList: {
    file: './src/StructureEmpList/index.tsx',
    name: '人员当前固定薪酬',
    desc: '人员当前固定薪酬',
  },
  StructureEmpDetail: {
    file: './src/StructureEmpDetail/index.tsx',
    name: '查看人员固定薪资结构',
    desc: '查看人员固定薪资结构',
  },
  PayrollPublish: {
    file: './src/PayrollPublish/index.tsx',
    name: '工资条发布',
    desc: '工资条发布',
  },
  PayrollNoticeProgess: {
    file: './src/PayrollPublish/NoticeProgess/index.tsx',
    name: '工资条通知进度',
    desc: '工资条通知进度',
  },
  CnbEmpPay: {
    file: './src/CnbEmpPay/index.tsx',
    name: '发薪人员列表',
    desc: '发薪人员列表',
  },
  SalaryFormulaList: {
    file: './src/SalaryFormulaList/index.tsx',
    name: '薪酬项目公式管理',
    desc: '薪酬项目公式管理',
  },
  AttendanceRuleEditor: {
    file: './src/AttendanceRuleEditor/index.tsx',
    name: '考勤规则编辑器',
    desc: '考勤规则编辑器',
  },
  AttendanceRuleEditorConfigs: {
    file: './src/AttendanceRuleEditor/exportConfig.ts',
    name: '考勤规则编辑器',
    desc: '考勤规则编辑器',
  },
  BatchSelectModal: {
    file: './src/components/BatchSelectModal/index.tsx',
    name: '批量数据选择弹窗',
    desc: '批量数据选择弹窗',
  },
  RichTextEditor: {
    file: './src/RichTextEditor/index.tsx',
    name: '富文本编辑器',
    desc: '富文本编辑器',
  },
  RichTextEditorConfigs: {
    file: './src/RichTextEditor/exportConfig.ts',
    name: '富文本编辑器配置',
    desc: '富文本编辑器配置',
  },
  ShiftRule: {
    file: './src/ShiftRule/index.tsx',
    name: '轮班规则',
    desc: '轮班规则',
  },
  CustomTimePicker: {
    file: './src/CustomTimePicker/index.tsx',
    name: '时间选择',
    desc: '时间选择',
  },
  CustomTimePickerConfigs: {
    file: './src/CustomTimePicker/exportConfig.ts',
    name: '时间选择',
    desc: '时间选择',
  },
  ShiftTable: {
    file: './src/ShiftTable/index.tsx',
    name: '排班明细表',
    desc: '排班明细表',
  },
  ShiftTableConfigs: {
    file: './src/ShiftTable/exportConfig.ts',
    name: '轮班列表配置',
    desc: '轮班列表配置',
  },
  AttendanceFile: {
    file: './src/AttendanceFile/index.tsx',
    name: '考勤档案',
    desc: '考勤档案详情',
  },
  QuotaDetail: {
    file: './src/QuotaDetail/index.tsx',
    name: '定额明细(未使用，待1.1.0上线前看是否需要删除)',
    desc: '定额明细',
  },
  QuotaSummary: {
    file: './src/QuotaSummary/index.tsx',
    name: '定额汇总(未使用，待1.1.0上线前看是否需要删除)',
    desc: '定额汇总',
  },
  EmployeeInfoCard: {
    file: './src/EmployeeInfoCard/index.tsx',
    name: '个人信息卡片',
    desc: '个人信息卡片',
  },
  EmployeeInfoCardConfigs: {
    file: './src/EmployeeInfoCard/exportConfig.ts',
    name: '个人信息卡片',
    desc: '个人信息卡片',
  },
  WorkSchedule: {
    file: './src/WorkSchedule/index.tsx',
    name: '工作日程表',
    desc: '工作日程表',
  },
  'AttendanceRecord.PeriodSummary': {
    file: './src/AttendanceRecord/PeriodSummary/index.tsx',
    name: '考勤记录-期间汇总',
    desc: '考勤记录-期间汇总',
  },
  'AttendanceRecord.DayDetail': {
    file: './src/AttendanceRecord/DayDetail/index.tsx',
    name: '考勤记录-日明细',
    desc: '考勤记录-日明细',
  },
  'AttendanceRecord.SealSummary': {
    file: './src/AttendanceRecord/SealSummary/index.tsx',
    name: '考勤记录-封存汇总',
    desc: '考勤记录-封存汇总',
  },
  'AttendanceRecord.AccountingProcess': {
    file: './src/AttendanceRecord/DayDetail/AccountingProcess/index.tsx',
    name: '考勤记录-日明细-核算步骤',
    desc: '考勤记录-日明细-核算步骤',
  },
  TextDisplay: {
    file: './src/TextDisplay/index.tsx',
    name: '文本展示',
    desc: '文本展示',
  },
  TextDisplayConfigs: {
    file: './src/TextDisplay/exportConfig.ts',
    name: '文本展示配置',
    desc: '文本展示配置',
  },
  OrgSelectConfigs: {
    file: './src/OrgSelect/exportConfig.ts',
    name: 'DHR组织单选',
    desc: 'DHR组织单选',
  },
  OrgSelect: {
    file: './src/OrgSelect/index.tsx',
    name: 'DHR组织单选',
    desc: 'DHR组织单选',
  },
  OrgSelectTranslate: {
    file: './src/OrgSelect/Render/batchTranslate.ts',
    name: 'DHR组织单选批量翻译',
    desc: 'DHR组织单选批量翻译',
  },
  AbsenceDatePickerConfigs: {
    file: './src/AbsenceDatePicker/exportConfig.ts',
    name: '请假日期选择配置',
    desc: '请假日期选择配置',
  },
  AbsenceDatePicker: {
    file: './src/AbsenceDatePicker/index.tsx',
    name: '请假日期选择',
    desc: '请假日期选择',
  },
  TalentFileList: {
    file: './src/TalentFile/TalentList',
    name: '人才档案列表',
    desc: '人才档案列表',
  },
  TalentFileDetail: {
    file: './src/TalentFile/TalentDetail',
    name: '人才档案详情',
    desc: '人才档案详情',
  },
  TalentDetailArchives: {
    file: './src/TalentFile/TalentDetailArchives',
    name: '人才档案详情(带权限)',
    desc: '人才档案详情(带权限)',
  },
  TalentDetailTag: {
    file: './src/TalentFile/TalentDetailTag',
    name: '人才标签的人才档案详情(带权限)',
    desc: '人才标签的人才档案详情(带权限)',
  },
  TalentTags: {
    file: './src/TalentFile/TalentTags',
    name: '人才标签管理',
    desc: '人才标签管理',
  },
  QuotaCalculation: {
    file: './src/QuotaCalculation/index.tsx',
    name: '定额核算详情',
    desc: '定额核算详情',
  },
  DHRSingleSelectConfigs: {
    file: './src/DHRSingleSelect/exportConfig.ts',
    name: 'DHR单选',
    desc: 'DHR单选',
  },
  DHRSingleSelect: {
    file: './src/DHRSingleSelect/index.tsx',
    name: 'DHR单选',
    desc: 'DHR单选',
  },
  DHRPersonSelectConfigs: {
    file: './src/DHRPersonSelect/exportConfig.ts',
    name: 'DHR人员单选',
    desc: 'DHR人员单选',
  },
  DHRPersonSelect: {
    file: './src/DHRPersonSelect/index.tsx',
    name: 'DHR人员单选',
    desc: 'DHR人员单选',
  },
  DHRPersonSelectTranslate: {
    file: './src/DHRPersonSelect/Render/batchTranslate.ts',
    name: 'DHR单选翻译',
    desc: 'DHR单选翻译',
  },
  AssociatedAuthority: {
    name: '关联权限',
    desc: '关联权限',
    file: './src/AssociatedAuthority/index.tsx',
  },
  AssociatedAuthorityConfigs: {
    name: '关联权限',
    desc: '关联权限',
    file: './src/AssociatedAuthority/exportConfig.ts',
  },
  AttendanceCalculation: {
    name: '考勤核算过程详情',
    desc: '考勤核算过程详情',
    file: './src/AttendanceCalculation/index.tsx',
  },
  AttendanceArchive: {
    name: '考勤封存过程详情',
    desc: '考勤封存过程详情',
    file: './src/AttendanceArchive/index.tsx',
  },
  LeaveProcessList: {
    name: '请假流程数据',
    desc: '请假流程数据',
    file: './src/LeaveProcessList/index.tsx',
  },
  MaternityLeaveList: {
    name: '产假流程数据',
    desc: '产假流程数据',
    file: './src/MaternityLeaveList/index.tsx',
  },
  AbsenceList: {
    name: '移动端请假列表',
    desc: '移动端请假列表',
    file: './src/AbsenceList/index.tsx',
  },
  SystemNotice: {
    name: '系统公告',
    desc: '系统公告',
    file: './src/SystemNotice/index.tsx',
  },
  EarlyReturnWorkMaternityLeaveList: {
    name: '提前返岗产假流程数据',
    desc: '提前返岗产假流程数据',
    file: './src/EarlyReturnWorkMaternityLeaveList/index.tsx',
  },
  'CareerPath.CareerPathMain': {
    file: './src/CareerPath/CareerPathMain/index.tsx',
    name: '职业发展',
    desc: '职业发展',
  },
  'CareerPath.CareerStandard': {
    file: './src/CareerPath/CareerStandard/index.tsx',
    name: '职业标准',
    desc: '职业标准',
  },
  'AuditLine.Adjust': {
    name: '审核线调整',
    desc: '审核线调整',
    file: './src/AuditLine/Adjust/index.tsx',
  },
  'AuditLine.EditModal': {
    name: '修改审核人',
    desc: '修改审核人',
    file: './src/AuditLine/EditModal/index.tsx',
  },
  BatchUpload: {
    name: '批量附件上传',
    desc: '批量附件上传',
    file: './src/components/BatchUpload/index.tsx',
  },
  XLSXImportModal: {
    name: '批量导入组件',
    desc: '批量导入组件',
    file: './src/components/XLSXImportModal/index.tsx',
  },
  OrgUnitList: {
    name: '组织单元列表',
    desc: '组织单元列表',
    file: './src/OrgUnit/OrgUnitList/index.tsx',
  },
  OrgUnitLabor: {
    name: '法人组织单元',
    desc: '法人组织单元',
    file: './src/OrgUnit/OrgUnitLabor/index.tsx',
  },
  DHRCustomUpload: {
    file: './src/CustomUpload/index.tsx',
    name: 'DHR附件上传',
    desc: 'DHR附件上传',
  },
  DHRCustomUploadConfigs: {
    file: './src/CustomUpload/exportConfig.ts',
    name: 'DHR附件上传配置',
    desc: 'DHR附件上传配置',
  },
  DHRCustomUploadTranslate: {
    file: './src/CustomUpload/Render/batchTranslate.ts',
    name: 'DHR上传附件翻译',
    desc: 'DHR上传附件翻译',
  },
  DHRAddressSearch: {
    file: './src/DHRAddressSearch/index.tsx',
    name: 'DHR地址搜索',
    desc: 'DHR地址搜索',
  },
  DHRAddressSearchConfigs: {
    file: './src/DHRAddressSearch/exportConfig.ts',
    name: 'DHR地址搜索配置',
    desc: 'DHR地址搜索配置',
  },
  DHRFileParse: {
    file: './src/DHRFileParse/index.tsx',
    name: 'DHR附件上传解析',
    desc: 'DHR附件上传解析',
  },
  DHRFileParseConfigs: {
    file: './src/DHRFileParse/exportConfig.ts',
    name: 'DHR附件上传解析配置',
    desc: 'DHR附件上传解析配置',
  },
  DHRFileParseTranslate: {
    file: './src/DHRFileParse/Render/batchTranslate.ts',
    name: 'DHR上传解析附件翻译',
    desc: 'DHR上传解析附件翻译',
  },
  MessageTemplateConfig: {
    name: '消息模版配置',
    desc: '消息模版配置',
    file: './src/MessageTemplateConfig/index.tsx',
  },
  DHREntryAvatarBlock: {
    file: './src/DHREntryAvatarBlock/index.tsx',
    name: '入职登记表头像',
    desc: '入职登记表头像',
  },
  DHREntryAvatarBlockConfigs: {
    file: './src/DHREntryAvatarBlock/exportConfig.ts',
    name: '入职登记表头像配置',
    desc: '入职登记表头像配置',
  },
  DHRAmapSearch: {
    file: './src/DHRAmapSearch/index.tsx',
    name: '高德地图搜索',
    desc: '高德地图搜索',
  },
  DHRAmapSearchConfigs: {
    file: './src/DHRAmapSearch/exportConfig.ts',
    name: '高德地图搜索配置',
    desc: '高德地图搜索配置',
  },
  DHRActRenderer: {
    file: './src/DHREntryRender/index.tsx',
    name: 'DHR活动渲染',
    desc: 'DHR活动渲染',
  },
  DHREntryHome: {
    file: './src/EntryHome/index.tsx',
    name: 'DHR入职活动首页',
    desc: 'DHR入职活动首页',
  },
  DHREntryRegistrationForm: {
    file: './src/DHREntryRegistrationForm/index.tsx',
    name: '入职登记表',
    desc: '入职登记表',
  },
  ReportPunch: {
    file: './src/ReportPunch/index.tsx',
    name: '报到打卡',
    desc: '报到打卡',
  },
  DHRValidConfirmBox: {
    file: './src/DHRValidConfirmBox/index.tsx',
    name: '校验确认框',
    desc: '校验确认框',
  },
  DHRValidConfirmBoxConfigs: {
    file: './src/DHRValidConfirmBox/exportConfig.ts',
    name: '校验确认框配置',
    desc: '校验确认框配置',
  },
  PhysicalExaminationAppointment: {
    file: './src/PhysicalExaminationAppointment/index.tsx',
    name: '体检预约日期',
    desc: '体检预约日期',
  },
  PhysicalExaminationAppointmentConfigs: {
    file: './src/PhysicalExaminationAppointment/exportConfig.ts',
    name: '体检预约日期配置',
    desc: '体检预约日期配置',
  },
  EntryApplication: {
    file: './src/EntryApplication/index.tsx',
    name: '入职申报',
    desc: '入职申报',
  },
  DocumentSigning: {
    file: './src/DocumentSigning/index.tsx',
    name: '文件签署',
    desc: '文件签署',
  },
  WULIFormModal: {
    file: './src/components/RenderFormModal/index.tsx',
    name: 'form弹窗',
    desc: 'form弹窗',
  },
  CustomEntryDate: {
    file: './src/CustomEntryDate/index.tsx',
    name: '时间选择',
    desc: '时间选择',
  },
  CustomEntryDateConfigs: {
    file: './src/CustomEntryDate/exportConfig.ts',
    name: '时间选择',
    desc: '时间选择',
  },
  DHRImagePreview: {
    file: './src/DHRImagePreview/index.tsx',
    name: '图片预览',
    desc: '图片预览组件',
  },
  PositionInfoModal: {
    file: './src/PositionInfoModal/index.tsx',
    name: '岗位新增弹窗(待对接)',
    desc: '岗位新增弹窗(待对接)',
  },
  NoticeAppointmentModal: {
    file: './src/NoticeAppointmentModal/index.tsx',
    name: '定岗通知弹窗',
    desc: '定岗通知弹窗',
  },
  ReplyModal: {
    file: './src/NoticeAppointmentModal/containers/ReplyModal/index.tsx',
    name: '定岗沟通HR弹窗',
    desc: '定岗沟通HR弹窗',
  },
  DHRJobHandover: {
    file: './src/DHRJobHandover/index.tsx',
    name: '工作交接',
    desc: '工作交接组件',
  },
  DHRJobHandoverConfigs: {
    file: './src/DHRJobHandover/exportConfig.ts',
    name: '工作交接配置',
    desc: '工作交接组件配置',
  },
  UserInfoFileUpdateModal: {
    file: './src/UserInfoFileUpdateModal/index.tsx',
    name: '用户信息附件更新',
    desc: '用户信息附件更新',
  },
  HeaderSearch: {
    file: './src/HeaderSearch/index.tsx',
    name: '头部搜索',
    desc: '头部搜索',
  },
  DHRFileSign: {
    file: './src/DHRFileSign/index.tsx',
    name: '扫码签署',
    desc: '扫码签署组件',
  },
  DHRFileSignConfigs: {
    file: './src/DHRFileSign/exportConfig.ts',
    name: '扫码签署配置',
    desc: '扫码签署组件配置',
  },
  InternReallocateEvaluationHrModal: {
    file: './src/InternReallocateEvaluationHrModal/index.tsx',
    name: '重新分配Hr弹窗',
    desc: '重新分配Hr弹窗',
  },
  DHRLeaveCalculate: {
    file: './src/DHRLeaveCalculate/index.tsx',
    name: '离职测算展示组件',
    desc: '离职测算展示组件',
  },
  DHRLeaveCalculateConfigs: {
    file: './src/DHRLeaveCalculate/exportConfig.ts',
    name: '离职测算展示组件配置',
    desc: '离职测算展示组件配置',
  },
};

export default configs;
