{
  "env": {
    "browser": true,
    "es2021": true
  },
  "extends": ["plugin:react/recommended", "plugin:@typescript-eslint/recommended", "prettier"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 12,
    "sourceType": "module"
  },
  "plugins": ["react", "react-hooks", "@typescript-eslint", "prettier"],
  "rules": {
    "consistent-return": "off",
    "max-len": [
      "error",
      {
        "code": 120
      }
    ],
    "no-undef": [
      "off",
      {
        "ignore": "__test__"
      }
    ],
    "no-eval": "off",
    "no-bitwise": "off",
    "no-shadow": "off",
    "import/prefer-default-export": "off",
    "no-async-promise-executor": "off",
    "no-return-await": "off",
    "no-use-before-define": "off",
    "no-underscore-dangle": "off",
    "no-unused-vars": "off",
    "no-unused-expressions": [
      "error",
      {
        "allowShortCircuit": true,
        "allowTernary": true
      }
    ],
    "no-plusplus": [
      "error",
      {
        "allowForLoopAfterthoughts": true
      }
    ],
    "no-param-reassign": ["off"],
    "radix": ["error", "as-needed"],
    "@typescript-eslint/no-shadow": ["off"],
    "@typescript-eslint/no-unused-vars": ["error"],
    "@typescript-eslint/no-use-before-define": ["error"],
    "prettier/prettier": "error",
    "react-hooks/rules-of-hooks": "error",
    "react/prop-types": "off",
    "react/require-default-props": "off",
    // "react-hooks/exhaustive-deps": "warn",
    "react/jsx-filename-extension": [
      "warn",
      {
        "extensions": [".tsx"]
      }
    ],
    "react/jsx-props-no-spreading": [
      "error",
      {
        "custom": "ignore"
      }
    ],
    "jsx-a11y/click-events-have-key-events": "off",
    "jsx-a11y/no-static-element-interactions": "off",
    "jsx-a11y/anchor-is-valid": "off",
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never"
      }
    ],
    "import/no-extraneous-dependencies": [
      "error",
      {
        "devDependencies": true,
        "peerDependencies": true
      }
    ],
    "no-continue": "off",
    "import/no-unresolved": "off",
    "no-restricted-syntax": "off",
    "guard-for-in": "off",
    "no-irregular-whitespace": "off"
  },
  "settings": {
    "import/resolver": {
      "typescript": {},
      "node": {
        "extensions": [".tsx", ".ts", ".d.ts", ".js", ".jsx", "d.ts"]
      }
    }
  }
}
