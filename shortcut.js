const inquirer = require('inquirer');
const fs = require('fs');
const path = require('path');
const prettier = require('prettier');
const copyfiles = require('copyfiles');

const TYPES_CODE = {
  formDesign: 'formDesign',
  wholeTale: 'wholeTale',
  simplePage: 'simplePage',
};

/** 组件类型 */
const TYPES_OTPIONS = [
  /** 表单设计组件模板 */
  {
    name: '表单设计组件',
    code: TYPES_CODE.formDesign,
    template: ['./src/Templates/FormDesignComp/**/*'],
  },
  /** wholeTale页面模板 */
  {
    name: 'wholeTale组件',
    code: TYPES_CODE.wholeTale,
    template: ['./src/Templates/WholeTable/*'],
  },
  /** 简单资源组件模板 */
  {
    name: '简单组件',
    code: TYPES_CODE.simplePage,
    template: ['./src/Templates/SimplePage/*'],
  },
];

const componentsDirectory = './src'; // 组件文件夹路径

const questions = [
  {
    type: 'list',
    name: 'type',
    message: '请选择需要生成的组件类型',
    choices: TYPES_OTPIONS.map(k => k.name),
  },
  {
    type: 'input',
    name: 'name',
    message: '请输入需要生成的组件中文名字, 如: 列表组件',
  },
  {
    type: 'input',
    name: 'componentName',
    message: '请输入需要生成的组件英文名字,首字母大写 如: ArchivesList',
    validate: input => {
      if (/^[a-zA-Z0-9_-]+$/.test(input)) {
        return true; // 验证输入是否合法，这里假设只允许字母、数字、下划线和破折号
      }
      return '组件名只能包含字母、数字、下划线和破折号';
    },
  },
  {
    type: 'input',
    name: 'desc',
    message: '请描述该组件用途',
  },
];

inquirer.prompt(questions).then(answers => {
  const { type, name, componentName, desc } = answers;
  const targetTpl = TYPES_OTPIONS.find(k => k.name === type);

  const componentDir = `${componentsDirectory}/${componentName}`;
  const isFinish = new Promise(resolve => {
    copyfiles(
      [...targetTpl.template, componentDir],
      {
        all: true,
        up: 3,
      },
      err => {
        if (err) {
          resolve(false);
          console.error('复制过程中发生错误:', err);
        } else {
          resolve(true);
          console.log('文件夹和文件已成功复制到目标目录。');
        }
      }
    );
  });
  if (!isFinish) {
    console.log('文件夹和文件复制失败');
  }

  // 读取文件内容
  // const rcExposesPath = path.join(__dirname, 'rc.exposes.js');
  // const fileContent = fs.readFileSync(rcExposesPath, 'utf8');

  // 解析文件内容并添加新的导出组件
  // const newContent = fileContent.replace('export default', 'global.obj = '); // 在对象末尾添加新组件
  // 动态创建一个模块，并返回导出的对象
  // @ts-ignore
  // new Function(newContent)();

  /** 导出组件 */
  // global.obj[componentName] = {
  //   file: `./src/${componentName}/index.tsx`,
  //   name,
  //   desc,
  // };
  /** 表单设计组件才需要加这个配置 */
  // if (targetTpl.code === TYPES_CODE.formDesign) {
  //   /** 导出组件配置 */
  //   global.obj[`${componentName}Configs`] = {
  //     file: `./src/${componentName}/exportConfig.ts`,
  //     name,
  //     desc,
  //   };
  // }

  /**
   * global.obj内容覆盖rc.exposes.js文件
   */
  // const fsContent = `export default ${JSON.stringify(global.obj)};`;

  // 使用 prettier 格式化代码
  // const formattedCode = prettier.format(fsContent, {
  //   singleQuote: true,
  //   semi: true,
  //   endOfLine: 'auto',
  //   arrowParens: 'avoid',
  //   jsxBracketSameLine: false,
  //   proseWrap: 'never',
  //   printWidth: 120,
  // });
  // fs.writeFileSync(rcExposesPath, formattedCode, 'utf8');
});
