# DHR 低代码自定义组件

## 组件列表

| 组件名称       | 说明                       | 备注                       |
| -------------- | -------------------------- | -------------------------- |
| 薪酬公式编辑器 | 用于编辑算薪需要的一些公式 | 自定义开发，托管在资源中心 |

## 快速生成组件

### 执行命令：

```
npm run new:tpl
```

### 特性

#### 1. 选择生成不同类似的资源组件

生成类型：

- 表单设计组件【天周云表单设计组件专用】
- wholeTale 组件 【表单搜索+列表页】
- 简单页面组件 【一个简单的页面模板】

#### 2. 自动往 rc.exposes.js 添加导出配置

- 表单设计组件导出两个配置，组件以及组件 Configs
- 常规组件导出一个配置

#### hooks-通用方法

- useDebounce 防抖
- useDictCode 获取字典列表
- useViews 获取角色按钮权限列表
- useFetch 请求

#### components - 通用组件

- Block 标题块容器
- FreeCard 可展开/收缩的卡片
- QuillEditor 富文本编辑器
- XLSXImportModal 导入 xlsx（支持两步/一步导入、支持不上传直接导入）
- UploadModal 附件上传（可直接使用 hooks-useAttachment）
- WholeTable/AgGrid 可配置的表格+按钮+搜索
- ExportLargeExcel 导出表格-下载器

## Ai提示
### 生成组件
```
根据截图，帮我开发一个表单组件。
主要作用是
1、初始状态判断 传入的value是否有值，有值展示附件组件
2、若value为空，展示扫码签署按钮。点击签署按钮，弹出弹窗，弹出内容如图所示。

该组件命名为：DHRFileSign
表单组件的结构参考：
@OrgSelect 

开发完成后需要在 @rc.exposes.js 中添加导出


```
